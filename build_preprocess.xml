<?xml version="1.0"?>
<!-- ======================================================================
 	 Copyright (c) 2005, 2008 IBM Corporation and others.\
 	 This program and the accompanying materials 
 	 are made available under the terms of the Eclipse Public License 2.0
 	 which accompanies this distribution, and is available at
 	 https://www.eclipse.org/legal/epl-2.0/
 	 
 	 SPDX-License-Identifier: EPL-2.0
 
 	 Contributors:
    	 IBM Corporation - initial API and implementation
         Wind River Systems - adopted to use with DSF

     EclipseCon    
     DSF Tutorial Exercises
     ====================================================================== -->
<project name="EclipseCon" default="generateAll">
	<description>
            DSF Tutorial Exercises
    </description>

	<taskdef name="preprocess" classname="org.eclipse.cdt.examples.ant.tasks.PreProcessor" classpath="bin" />
	<property name="$workspace" location="c:\eclipse-dev\dev-3.4" />
	<property name="srcBase" location="${workspace}/org.eclipse.cdt.examples.dsf/src_preprocess" />
	<property name="destBase" location="${workspace}/org.eclipse.cdt.examples.dsf/src" />

	<!-- ================================= 
          target: generateAll              
         ================================= -->
	<target name="generateAll" description="--> DSF Tutorial Exercises">
		
    <!-- = = = = = = = = = = = = = = = = =
          macrodef: process          
         = = = = = = = = = = = = = = = = = -->
    <macrodef name="process">
        <attribute name="packagedir"/>
        <sequential>
            <delete dir="src/@{packagedir}"/>
            <mkdir dir="src/@{packagedir}"/>
            <mkdir dir="src/@{packagedir}/answers"/>
        	<preprocess destdir="src/@{packagedir}" symbols="exercises">
        		<fileset dir="src_preprocess/@{packagedir}"/>
        	</preprocess>
        	<preprocess destdir="src/@{packagedir}/answers" symbols="answers">
        		<fileset dir="src_preprocess/@{packagedir}"/>
        	</preprocess>
        </sequential>
    </macrodef>

	<process packagedir="org/eclipse/cdt/examples/dsf/requestmonitor"/>
    <process packagedir="org/eclipse/cdt/examples/dsf/dataviewer"/>
	</target>

</project>

