package com.chipon32.hex.ui.editors;

import java.io.File;
import java.io.InputStream;

import org.eclipse.core.resources.IFile;
import org.eclipse.jface.resource.ImageDescriptor;
import org.eclipse.ui.IEditorInput;
import org.eclipse.ui.IPersistableElement;
import org.eclipse.ui.part.FileEditorInput;

import com.chipon32.hex.core.HexFileParser;
import com.chipon32.hex.core.Memory;
import com.chipon32.hex.core.util.IConfigurationProvider;
/**  hex编辑器输入对象，复位 HexEditor
 * 
 * <AUTHOR>
 *
 */
public class HexFileEditorInput extends FileEditorInput implements IEditorInput {

	private long fileNameTime;
	// 编辑器的 输入 文件流
	private InputStream fileStream;
	// 芯片的整个hex映射对象 
	private Memory memory;
	// 芯片的信息提供者
	private IConfigurationProvider configurationProvider;
	// 文件操作类
	private final File f;
	//-------------------------------------------------------------------------------------
	
	// 基于 文件名，芯片信息提供者构造该类
	public HexFileEditorInput(IFile file, IConfigurationProvider configurationProvider)
	{
		super(file);
		this.f = file.getRawLocation().toFile();
		this.fileNameTime = file.getModificationStamp();
		this.configurationProvider = configurationProvider;
		// 初始化
		init();
	}

	//-------------------------------------------------------------------------------------
	private void init() {
		// 解析hex的结果
		HexFileParser parser = new HexFileParser(f, getIFile().getName(), configurationProvider);
		parser.parse();
		memory = parser.getMemory();
	}
	public void FileUpdate2Memory()
	{
		if(this.getIFile()==null)return;
		if(this.configurationProvider==null)return;
		if(this.fileNameTime < f.lastModified() ){
			init();
			this.fileNameTime = f.lastModified();
		}
	}

	//-------------------------------------------------------------------------------------
	// 可对外提供信息提供者
	public IConfigurationProvider getConfigurationProvider() {
		return configurationProvider;
	}
	// 外部信息提供者设入
	public void setConfigurationProvider(
			IConfigurationProvider configurationProvider) {
		this.configurationProvider = configurationProvider;
	}
	// 结果hex对外提供
	public Memory getMemory() {
		return memory;
	}
	// 结果hex直接设入
	public void setMemory(Memory memory) {
		this.memory = memory;
	}
	//--------判断是否存在的方法就是判断memory是否为空
	@Override
	public boolean exists() {
		if(memory == null)
			return false;
		else
			return true;		
	}
	// 
	@Override
	public ImageDescriptor getImageDescriptor() {
		
		return null;
	}

	@Override
	public IPersistableElement getPersistable() {
		return null;
	}

	public IFile getIFile() {
		return getFile();
	}
}
