package com.chipon32.hex.ui.editors;


import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.eclipse.jface.dialogs.MessageDialog;
import org.eclipse.jface.viewers.TableViewer;

import com.chipon32.hex.core.GroupAddrVauleData;
import com.chipon32.hex.core.util.ByteConvertor;
import com.chipon32.util.ui.DoubleClickCellModifier;
// 组化的单元格内容修改类
public class GroupMemoryCellModifier extends DoubleClickCellModifier { //双击修改单元格事件
	public String showformat="16bits";
	// 
	public GroupMemoryCellModifier(TableViewer tableViewer) {
		super(tableViewer);
		// TODO Auto-generated constructor stub
	}
	
	//是否允许修改         1
	@Override
	public boolean allowModify(Object element, int columnIndex) {
		if(element instanceof GroupAddrVauleData)
		{
			GroupAddrVauleData groupRomData = (GroupAddrVauleData) element;
			if(columnIndex >= 1 && columnIndex <= 16 && groupRomData.getAddress() >= 0)
			{
				if(groupRomData.getAddrVauleDatasDataList() != null)
				if(groupRomData.getAddrVauleDatasDataList().size()>=16)
				if(groupRomData.getAddrVauleDatasDataList().get(columnIndex-1)!=null)
					return true;
			}
//			if(columnIndex==0)
//			{
//				return true;
//			}			
		}
		return false;
	}
	
	// 执行修改          3
	@Override
	public void doModify(Object element, int columnIndex, Object value){
		
		if(element instanceof GroupAddrVauleData)
		{
			GroupAddrVauleData groupRomData = (GroupAddrVauleData)element;
			if(columnIndex >= 1 && columnIndex <= 16 && groupRomData.getAddress() >= 0)
			{
				// 修改的结果目标值
				String tmp = value.toString().toUpperCase();		
				if( tmp.length()==0)
					return;
				//###########################################				
				if(tmp.length()<2){
					StringBuffer zeroNum = new StringBuffer();
					for(int i=0;i<(2-tmp.length());i++){
						zeroNum.append("0"); //$NON-NLS-1$
					}
					tmp = zeroNum.append(tmp).toString();					
				}
				//###########################################################################
				// 规则匹配
				Pattern pattern = Pattern.compile("[0-9a-fA-F]*");
				Matcher match = pattern.matcher(tmp);
				int onelength =ChiponMemoryServiceClass.getFormatBytes(showformat);
				if(!match.matches()	|| tmp.length() > onelength*2 )
				{
					String errorAddress = "addr:0x"+ByteConvertor.toEight(Long.toString(groupRomData.getAddress()+(columnIndex-1), 16));
					
					String errorInfo = "\n" + errorAddress +"\n\"" + tmp + "\"";
					MessageDialog.openError(null, "ERROR", errorInfo);
					return;
				}
				//###########################################################################
				if(groupRomData.getAddrVauleDatasDataList().size()>0) // 16个元素存在
				{					
						
						tmp = ChiponMemoryServiceClass.toLength(tmp,onelength);
						columnIndex = columnIndex-1;
						for(int j=0;j<onelength;j++)
						{		
							String command= tmp.substring(tmp.length()-2);
							groupRomData.setCommand(columnIndex+j , command);		
							tmp = tmp.substring(0,tmp.length()-2);
						}
				}
				// 结果的刷新
				tableViewer.refresh();		
			}
		}

	}
	// 获取当前行 某个下标的结果            2
	@Override
	public Object getColumnValue(Object element, int columnIndex) {
		if(element instanceof GroupAddrVauleData)
		{
			// 
			GroupAddrVauleData groupaddrcommData = (GroupAddrVauleData)element;
			if(columnIndex >= 1 && columnIndex <= 16 && groupaddrcommData.getAddress() >= 0)
			{
//				AddrVauleData romData  = groupaddrcommData.getAddrVauleDatasDataList().get((columnIndex - 1)*2+0);
//				AddrVauleData romData1 = groupaddrcommData.getAddrVauleDatasDataList().get((columnIndex - 1)*2+1);
//				return romData1.getVaule() + romData.getVaule();
				columnIndex =columnIndex-1 ; // 0-15
				String valueOut="";
				int onelength =ChiponMemoryServiceClass.getFormatBytes(showformat);
				if(columnIndex%onelength ==0)
				{
					for(int i=0; i<onelength;i++ )
					{
						valueOut = groupaddrcommData.getAddrVauleDatasDataList().get(columnIndex+i).getVaule().toUpperCase() + valueOut;
					}
				}
				else
				{
					return "*";
				}
				
				//------------------------
				return ChiponMemoryServiceClass.ValueStringCovGet(showformat,valueOut);
			}
		}
		return null;
	}
	

}
