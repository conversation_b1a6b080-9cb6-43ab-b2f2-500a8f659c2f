package com.chipon32.hex.ui.editors;

import org.eclipse.jface.viewers.ITableLabelProvider;
import org.eclipse.jface.viewers.LabelProvider;
import org.eclipse.swt.graphics.Image;

import com.chipon32.hex.core.GroupAddrVauleData;
// 行内容类型解析
public class GroupMemoryTableLabelProvider extends LabelProvider implements ITableLabelProvider{
	public String showformat="16bits";

	@Override
	public Image getColumnImage(Object element, int columnIndex) {
		return null;
	}
	// 获取内容 
	@Override
	public String getColumnText(Object element, int columnIndex) {
		
		if(element instanceof GroupAddrVauleData)
		{
			// 格式化为  行n个指令的形式
			GroupAddrVauleData groupaddrcommData = (GroupAddrVauleData)element;
			
			// -1 -2 -3表示的值的意义在GroupMemory类中给出
			// 地址-1为行列表额外追加的为 段内容
			if(groupaddrcommData.getAddress() == -1){   
				switch(columnIndex)
				{
				// 地址位置
				case 0:					
					return "**Flash**";
				// 指令位置
				default:
					return  "----";			
					
				}
			}
			// 区别的编码 用户区为地址-2
			else if(groupaddrcommData.getAddress() == -2){
				
				switch(columnIndex)
				{
				case 0:
					return "**Data**";
				
				default:
					return "----";
					
				}
			}
			// 区别的编码 数据区为地址-3
			else if(groupaddrcommData.getAddress() == -3){
				
				switch(columnIndex)
				{
				case 0:
					return "**EEprom**";
				
				default:
					return "----";
					
				}
			}
			// 省略  地址为 -4
			else if(groupaddrcommData.getAddress() == -4){
				
				switch(columnIndex)
				{
				case 0:
					return ".... ... .";
				
				default:
					return "....";
					
				}
			}
			// 起始地址下的 序号
			else
			{
				switch(columnIndex)
				{
				// 地址显示为  地址+":"
					case 0:		
						String addrbuf=groupaddrcommData.getAddressHexValue();
						addrbuf =addrbuf.substring(0,4) + " " +addrbuf.substring(4);
						addrbuf=addrbuf.substring(0,addrbuf.length()-1)+"0:";
						return addrbuf;	
				// 内容的 ascii码内容
					case 17:
						return groupaddrcommData.getAscii();		
				// 1-16的16个地址的指令结果
					default:
					{
//						AddrVauleData addrcodeData  =  groupaddrcommData.getAddrVauleDatasDataList().get((columnIndex - 1)*2+0);
//						AddrVauleData addrcodeData1 =  groupaddrcommData.getAddrVauleDatasDataList().get((columnIndex - 1)*2+1);
//						return addrcodeData1.getVaule() + addrcodeData.getVaule();
						columnIndex =columnIndex-1 ; // 0-15
						String valueOut="";
						int onelength =ChiponMemoryServiceClass.getFormatBytes(showformat);
						if(columnIndex%onelength ==0)
						{
							if(groupaddrcommData.getAddrVauleDatasDataList() != null)
							if(groupaddrcommData.getAddrVauleDatasDataList().size()>=16)
							if(groupaddrcommData.getAddrVauleDatasDataList().get(columnIndex)!=null){
									for(int i=0; i<onelength;i++ )
									{
										valueOut = groupaddrcommData.getAddrVauleDatasDataList().get(columnIndex+i).getVaule().toUpperCase() + valueOut;
									}
							}
							
						}
						else
						{
							return "*";
						}
						//------------------------
						return ChiponMemoryServiceClass.ValueStringCovGet(showformat,valueOut);
					}					
				}
			}			
		}
		// 其他情况 ，返回对象的文本
		return element.toString();
	}
}
