package com.chipon32.hex.ui.editors;

import org.eclipse.core.resources.IFile;
import org.eclipse.core.resources.IProject;
import org.eclipse.core.runtime.IAdapterFactory;
import org.eclipse.ui.IFileEditorInput;

import com.chipon32.chiponide.core.utils.ChipNameManager;
import com.chipon32.hex.core.util.IConfigurationProvider;
import com.chipon32.util.provider.ConfigurationFactory;

public class HexFileEditorInputAdapterFactory implements IAdapterFactory {

	/**
	 * 基于当前工程获取对应的芯片信息获取变量
	 */
	public IConfigurationProvider getChipConfigElement(IProject project) {
		String chipName = ChipNameManager.getChipName(project);
		return ConfigurationFactory.getProvider(chipName);
	}

	@SuppressWarnings("unchecked")
	@Override
	public <T> T getAdapter(Object adaptableObject, Class<T> adapterType) {
		if (adaptableObject instanceof IFileEditorInput fileInput) {
			if (adapterType.equals(HexFileEditorInput.class)) {
				IFile file = fileInput.getFile();
				HexFileEditorInput hexInput;
				hexInput = new HexFileEditorInput(file, getChipConfigElement(file.getProject()));
				return (T) hexInput;
			}
		}
		return null;
	}

	@SuppressWarnings("rawtypes")
	@Override
	public Class[] getAdapterList() {
		return new Class[] { HexFileEditorInput.class };
	}

}
