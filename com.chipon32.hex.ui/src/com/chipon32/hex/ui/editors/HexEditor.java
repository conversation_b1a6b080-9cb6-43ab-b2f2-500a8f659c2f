package com.chipon32.hex.ui.editors;

import org.eclipse.core.resources.IFile;
import org.eclipse.core.resources.IProject;
import org.eclipse.core.runtime.IProgressMonitor;
import org.eclipse.jface.dialogs.MessageDialog;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.ModifyEvent;
import org.eclipse.swt.events.ModifyListener;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.events.SelectionListener;
import org.eclipse.swt.graphics.GC;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Button;
import org.eclipse.swt.widgets.Combo;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Event;
import org.eclipse.swt.widgets.Group;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.Listener;
import org.eclipse.swt.widgets.Table;
import org.eclipse.swt.widgets.TableColumn;
import org.eclipse.swt.widgets.Text;
import org.eclipse.ui.IEditorInput;
import org.eclipse.ui.IEditorPart;
import org.eclipse.ui.IEditorSite;
import org.eclipse.ui.IFileEditorInput;
import org.eclipse.ui.PartInitException;
import org.eclipse.ui.PlatformUI;
import org.eclipse.ui.part.EditorPart;
import org.eclipse.wb.swt.SWTResourceManager;

import com.chipon32.chiponide.core.properties.ChipOnProjectProperties;
import com.chipon32.chiponide.core.properties.ProjectPropertyManager;
import com.chipon32.hex.core.GroupAddrVauleData;
import com.chipon32.hex.core.GroupMemory;
import com.chipon32.hex.core.Memory;
import com.chipon32.hex.core.util.IConfigurationProvider;
import com.chipon32.util.provider.ConfigurationFactory;
import com.chipon32.util.ui.HexTableViewer;
import com.chipon32.util.ui.UIUtil;

/**
 * HEX 编辑器 PRO 主界面实现 或 IDE 打开 HEX的界面实现
 * 
 * <AUTHOR>
 *
 */
public class HexEditor extends EditorPart {

	public static final String ID = "com.chipon32.hex.ui.HexEditor"; //$NON-NLS-1$
	//
	// 表对象，按表显示的
	private Table table;
	// hex表视图
	private HexTableViewer tableViewer;
	// 芯片hex的结果实体 ---芯片信息
	private Memory memory;
	private Combo ShowDataFormat;

	public Combo getShowDataFormat() {
		return ShowDataFormat;
	}

	private Text showStart;
	private Text showStop;

	private Button showFlashSelect;
	private Button showUserSelect;

	private Button show_Button;
	private Button UP_Button;
	private Button DOWN_Button;

	private Label labelShowInput;

	public Label getLabelShowInput() {
		return labelShowInput;
	}

	// ------------------------------------------------------------------------------------------------
	// 空构造
	public HexEditor() {
		// TODO Auto-generated constructor stub
	}

	// 保存方法
	@Override
	public void doSave(IProgressMonitor monitor) {
		// TODO Auto-generated method stub

	}

	// 另存为方法
	@Override
	public void doSaveAs() {
		// TODO Auto-generated method stub

	}

	// ------------------------------------------------------------------------------------------------
	// 初始化
	@Override
	public void init(IEditorSite site, IEditorInput input) throws PartInitException {
		this.setSite(site);
		this.setInput(input);
	}

	@Override
	public boolean isDirty() {
		return false;
	}

	@Override
	public boolean isSaveAsAllowed() {
		return false;
	}

	@Override
	protected void setInput(IEditorInput i) {
		if (i instanceof IFileEditorInput fileEditorInput) {
			try {
				IFile f = fileEditorInput.getFile();
				System.out.println(f.getFullPath().toString());

				IProject project = f.getProject();

				ProjectPropertyManager ppm = ProjectPropertyManager.getPropertyManager(project);
				ChipOnProjectProperties properties = ppm.getProjectProperties();

				String chipName = properties.getChipName();
				IConfigurationProvider provider = ConfigurationFactory.getProvider(chipName);

				HexFileEditorInput input = new HexFileEditorInput(f, provider);
				memory = input.getMemory();
				super.setInput(input);
			} catch (Exception ignored) {
			}
		} else if (i instanceof HexFileEditorInput input) {
			memory = input.getMemory();
			super.setInput(input);
		}
	}

	@Override
	public void createPartControl(Composite parent) {
		// default show
		Memory.setEditShowstart(0);
		Memory.setEditShowstop(1024 - 1);
		// 基于原可重定位窗的对象构建 窗显示对象
		final Composite container = new Composite(parent, SWT.NONE);
		container.setLayout(new GridLayout(1, false));

		Group group = new Group(container, SWT.NONE);
		group.setLayout(new GridLayout(7, false));
		group.setText(Messages.HexEditor_group); // $NON-NLS-1$

		Label labe0 = new Label(group, SWT.NONE);
		labe0.setFont(SWTResourceManager.getFont("System", 9, SWT.NORMAL)); //$NON-NLS-1$
		labe0.setText(Messages.HexEditor_0);

		ShowDataFormat = new Combo(group, SWT.FILL | SWT.BORDER);
		ShowDataFormat.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, false, 1, 1));
		ShowDataFormat.setItems(ChiponMemoryServiceClass.datetype);
		ShowDataFormat.select(1); // 8 16 32

		Label label = new Label(group, SWT.NONE);
		label.setFont(SWTResourceManager.getFont("System", 9, SWT.NORMAL)); //$NON-NLS-1$
		label.setText(Messages.HexEditor_5);

		showStart = new Text(group, SWT.FILL | SWT.BORDER);
		showStart.setText("0"); //$NON-NLS-1$
		showStart.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, false, 1, 1));

		Label label2 = new Label(group, SWT.NONE);
		label2.setFont(SWTResourceManager.getFont("System", 9, SWT.NORMAL)); //$NON-NLS-1$
		label2.setText(Messages.HexEditor_6); // $NON-NLS-1$

		showStop = new Text(group, SWT.FILL | SWT.BORDER);
		showStop.setText("1024"); //$NON-NLS-1$
		showStop.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, false, 1, 1));
		new Label(group, SWT.NONE);

		// @@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
		showFlashSelect = new Button(group, SWT.CHECK);
		showFlashSelect.setFont(org.eclipse.wb.swt.SWTResourceManager.getFont("System", 9, SWT.NORMAL)); //$NON-NLS-1$
		showFlashSelect.setLayoutData(new GridData(SWT.LEFT, SWT.FILL, true, false, 1, 1));
		showFlashSelect.setText("Flash"); //$NON-NLS-1$
		showFlashSelect.setSelection(true);

		showUserSelect = new Button(group, SWT.CHECK);
		showUserSelect.setFont(org.eclipse.wb.swt.SWTResourceManager.getFont("System", 9, SWT.NORMAL)); //$NON-NLS-1$
		showUserSelect.setLayoutData(new GridData(SWT.LEFT, SWT.FILL, true, false, 1, 1));
		showUserSelect.setText("Data"); //$NON-NLS-1$
		showUserSelect.setSelection(false);

		show_Button = new Button(group, SWT.None);
		show_Button.setEnabled(true);
		show_Button.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, false, 1, 1));
		show_Button.setText(Messages.HexEditor_1);
		show_Button.addSelectionListener(new SelectionListener() {
			@Override
			public void widgetSelected(SelectionEvent e) {
				String bufget = ""; //$NON-NLS-1$
				int start = 0;
				int stop = 0;

				try {
					// ######################################################
					bufget = showStart.getText().trim().replace(" ", "");

					if (bufget.startsWith("0x") || bufget.startsWith("0X")) // format strict to avoid exception //$NON-NLS-1$ //$NON-NLS-2$
					{
						bufget = bufget.substring(2);
						start = Integer.parseInt(bufget, 16);
					} else if (bufget.endsWith("H") || bufget.endsWith("h")) {
						bufget = bufget.substring(0, bufget.length() - 1);
						start = Integer.parseInt(bufget, 16);
					} else if (bufget.contains("K") || bufget.contains("k")) //$NON-NLS-1$ //$NON-NLS-2$
					{
						bufget = bufget.substring(0, bufget.length() - 1);
						start = Integer.parseInt(bufget, 10) * 1024;
					} else
						start = Integer.parseInt(bufget, 10);

					bufget = showStop.getText().trim();
					if (bufget.isEmpty() || bufget.equalsIgnoreCase("0")) //$NON-NLS-1$
					{
						showStop.setText("512"); //$NON-NLS-1$
						bufget = "512"; //$NON-NLS-1$
					}
					if (bufget.startsWith("0x") || bufget.startsWith("0X")) // format strict to avoid exception //$NON-NLS-1$ //$NON-NLS-2$
					{
						bufget = bufget.substring(2);
						stop = Integer.parseInt(bufget, 16);
					} else if (bufget.endsWith("H") || bufget.endsWith("h")) {
						bufget = bufget.substring(0, bufget.length() - 1);
						stop = Integer.parseInt(bufget, 16);
					} else if (bufget.contains("K") || bufget.contains("k")) //$NON-NLS-1$ //$NON-NLS-2$
					{
						bufget = bufget.substring(0, bufget.length() - 1);
						stop = Integer.parseInt(bufget, 10) * 1024;
					} else
						stop = Integer.parseInt(bufget, 10);

//				if(stop<start)
//				{
//					showStop.setText(showStart.getText().trim());stop=start;
//				}
					stop = start + stop - 1;

				} catch (Exception e1) {
					MessageDialog.openError(null, Messages.HexEditor_3, Messages.HexEditor_28);
					return;
				}
				Memory.setEditShowstart(start);
				Memory.setEditShowstop(stop);

				if (getEditorInput() instanceof HexFileEditorInput input) {
					input.FileUpdate2Memory();
					memory = input.getMemory();
				}

				GroupMemory groupMemory = new GroupMemory(memory);
				tableViewer.setHexInput(groupMemory);
			}

			@Override
			public void widgetDefaultSelected(SelectionEvent e) {
			}
		});

		UP_Button = new Button(group, SWT.None);
		UP_Button.setEnabled(true);
		UP_Button.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, false, 1, 1));
		UP_Button.setText(Messages.HexEditor_2);
		UP_Button.addSelectionListener(new SelectionListener() {
			@Override
			public void widgetSelected(SelectionEvent e) {
				String bufget = ""; //$NON-NLS-1$
				int start = 0;
				int stop = 0;

				try {
					// ######################################################
					bufget = showStart.getText().trim();

					if (bufget.startsWith("0x") || bufget.startsWith("0X")) // format strict to avoid exception //$NON-NLS-1$ //$NON-NLS-2$
					{
						bufget = bufget.substring(2);
						start = Integer.parseInt(bufget, 16);
					} else if (bufget.endsWith("H") || bufget.endsWith("h")) {
						bufget = bufget.substring(0, bufget.length() - 1);
						start = Integer.parseInt(bufget, 16);
					} else if (bufget.contains("K") || bufget.contains("k")) //$NON-NLS-1$ //$NON-NLS-2$
					{
						bufget = bufget.substring(0, bufget.length() - 1);
						start = Integer.parseInt(bufget, 10) * 1024;
					} else
						start = Integer.parseInt(bufget, 10);

					bufget = showStop.getText().trim();
					if (bufget.isEmpty() || bufget.equalsIgnoreCase("0")) //$NON-NLS-1$
					{
						showStop.setText("512"); //$NON-NLS-1$
						bufget = "512"; //$NON-NLS-1$
					}
					if (bufget.startsWith("0x") || bufget.startsWith("0X")) // format strict to avoid exception //$NON-NLS-1$ //$NON-NLS-2$
					{
						bufget = bufget.substring(2);
						stop = Integer.parseInt(bufget, 16);
					} else if (bufget.endsWith("H") || bufget.endsWith("h")) {
						bufget = bufget.substring(0, bufget.length() - 1);
						stop = Integer.parseInt(bufget, 16);
					} else if (bufget.contains("K") || bufget.contains("k")) //$NON-NLS-1$ //$NON-NLS-2$
					{
						bufget = bufget.substring(0, bufget.length() - 1);
						stop = Integer.parseInt(bufget, 10) * 1024;
					} else
						stop = Integer.parseInt(bufget, 10);

//					if(stop<start)
//					{
//						showStop.setText(showStart.getText().trim());stop=start;
//					}
					if (start > stop)
						start -= stop;
					else
						start = 0;
					showStart.setText("0x" + Integer.toHexString(start).toUpperCase()); //$NON-NLS-1$
					stop = start + stop - 1;
					// ######################################################
				} catch (Exception e1) {
					// TODO: handle exception
					MessageDialog.openError(null, Messages.HexEditor_3, Messages.HexEditor_28);
					return;
				}
				//

				Memory.setEditShowstart(start);
				Memory.setEditShowstop(stop);

				IEditorPart part = PlatformUI.getWorkbench().getActiveWorkbenchWindow().getActivePage().getActiveEditor();
				if (part != null && part instanceof HexEditor) {
					HexFileEditorInput input = null;
					if (getEditorInput() instanceof HexFileEditorInput) {
						// 输入对象是 hex编辑器的输入，提供各种内容的获取
						input = (HexFileEditorInput) getEditorInput();
						// 这里获取memory结果
						memory = input.getMemory();
					} else if (getEditorInput().getAdapter(HexFileEditorInput.class) != null) {
						// hex编辑器输入对象类的反射构建对象
						input = getEditorInput().getAdapter(HexFileEditorInput.class);
						memory = input.getMemory();
					}

					GroupMemory groupMemory = new GroupMemory(memory);
					tableViewer.setHexInput(groupMemory);
				}

			}

			@Override
			public void widgetDefaultSelected(SelectionEvent e) {
			}
		});

		DOWN_Button = new Button(group, SWT.None);
		DOWN_Button.setEnabled(true);
		DOWN_Button.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, false, 1, 1));
		DOWN_Button.setText(Messages.HexEditor_4);
		DOWN_Button.addSelectionListener(new SelectionListener() {
			@Override
			public void widgetSelected(SelectionEvent e) {
				String bufget = ""; //$NON-NLS-1$
				int start = 0;
				int stop = 0;

				try {
					bufget = showStart.getText().trim();

					if (bufget.startsWith("0x") || bufget.startsWith("0X")) // format strict to avoid exception //$NON-NLS-1$ //$NON-NLS-2$
					{
						bufget = bufget.substring(2);
						start = Integer.parseInt(bufget, 16);
					} else if (bufget.endsWith("H") || bufget.endsWith("h")) {
						bufget = bufget.substring(0, bufget.length() - 1);
						start = Integer.parseInt(bufget, 16);
					} else if (bufget.contains("K") || bufget.contains("k")) //$NON-NLS-1$ //$NON-NLS-2$
					{
						bufget = bufget.substring(0, bufget.length() - 1);
						start = Integer.parseInt(bufget, 10) * 1024;
					} else
						start = Integer.parseInt(bufget, 10);

					bufget = showStop.getText().trim();
					if (bufget.isEmpty() || bufget.equalsIgnoreCase("0")) //$NON-NLS-1$
					{
						showStop.setText("512"); //$NON-NLS-1$
						bufget = "512"; //$NON-NLS-1$
					}
					if (bufget.startsWith("0x") || bufget.startsWith("0X")) // format strict to avoid exception //$NON-NLS-1$ //$NON-NLS-2$
					{
						bufget = bufget.substring(2);
						stop = Integer.parseInt(bufget, 16);
					} else if (bufget.endsWith("H") || bufget.endsWith("h")) {
						bufget = bufget.substring(0, bufget.length() - 1);
						stop = Integer.parseInt(bufget, 16);
					} else if (bufget.contains("K") || bufget.contains("k")) //$NON-NLS-1$ //$NON-NLS-2$
					{
						bufget = bufget.substring(0, bufget.length() - 1);
						stop = Integer.parseInt(bufget, 10) * 1024;
					} else
						stop = Integer.parseInt(bufget, 10);

					start += stop;
					showStart.setText("0x" + Integer.toHexString(start).toUpperCase()); //$NON-NLS-1$
					stop = start + stop - 1;
				} catch (Exception e1) {
					MessageDialog.openError(null, Messages.HexEditor_3, Messages.HexEditor_28);
					return;
				}
				Memory.setEditShowstart(start);
				Memory.setEditShowstop(stop);

				IEditorPart part = PlatformUI.getWorkbench().getActiveWorkbenchWindow().getActivePage().getActiveEditor();
				if (part instanceof HexEditor) {
					HexFileEditorInput input = null;
					if (getEditorInput() instanceof HexFileEditorInput) {
						// 输入对象是 hex编辑器的输入，提供各种内容的获取
						input = (HexFileEditorInput) getEditorInput();
						// 这里获取memory结果
						memory = input.getMemory();
					} else if (getEditorInput().getAdapter(HexFileEditorInput.class) != null) {
						// hex编辑器输入对象类的反射构建对象
						input = getEditorInput().getAdapter(HexFileEditorInput.class);
						memory = input.getMemory();
					}

					GroupMemory groupMemory = new GroupMemory(memory);
					tableViewer.setHexInput(groupMemory);
				}

			}

			@Override
			public void widgetDefaultSelected(SelectionEvent e) {
			}
		});

		labelShowInput = new Label(group, SWT.NONE);
		labelShowInput.setFont(SWTResourceManager.getFont("System", 9, SWT.NORMAL)); //$NON-NLS-1$
		labelShowInput.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, false, 2, 1));
		labelShowInput.setText("0x 1H 2 1K <2048K"); //$NON-NLS-1$
		// @@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
		// 表显示对象，结果中的表格
		tableViewer = new HexTableViewer(container, SWT.BORDER | SWT.FULL_SELECTION);
		table = tableViewer.getTable();
		// 表格的字体定义
		table.setFont(SWTResourceManager.getFont("System", 10, SWT.NORMAL)); //$NON-NLS-1$
		// 不显示表格分割线
		table.setLinesVisible(false);
		// 显示表头
		table.setHeaderVisible(true);

		// 网格对象
		GridData gd_table = new GridData(SWT.FILL, SWT.FILL, true, true, 1, 1);
		// 网格高度 310
		gd_table.heightHint = 310;
		// 表设定网格对象
		table.setLayoutData(gd_table);

		// ------------------基于首元素确认行高的监听使整个表格适应-----------------------
		table.addListener(SWT.MeasureItem, new Listener() {
			@Override
			public void handleEvent(Event event) {
				// 事件行数据对象
				Object obj = event.item.getData();
				if (obj instanceof GroupAddrVauleData rd) {
					// 行对象转为的 GroupAddrCommData 变量
					// 非内容差异之间的实际代码
					if (rd.getAddress() >= 0) {
						// 首个元素的指令结果，根据结果调整行高
						if (rd.getAddrVauleDatasDataList() != null && rd.getAddrVauleDatasDataList().get(0) != null) {
							String command = rd.getAddrVauleDatasDataList().get(0).getVaule();
							GC gc = new GC(Display.getDefault());
							int y = gc.stringExtent(command).y;
							// 基于结果更新高度 ，额外多 13个像素
							event.height = y + 13;
							gc.dispose();
						}
					}
				}
			}
		});
		// ------------------表格列内容结果--------------------------
		// 表格第一列 的内容 为 地址的结果
		TableColumn tblclmnAddress = new TableColumn(table, SWT.NONE);
		tblclmnAddress.setWidth(100);
		tblclmnAddress.setText("Address"); //$NON-NLS-1$
		tblclmnAddress.setAlignment(SWT.CENTER);
		tblclmnAddress.setResizable(true);// 可以拖拽重设置宽度高度
		// 表格第二列的内容为
		TableColumn tblclmn00 = new TableColumn(table, SWT.CENTER);
		tblclmn00.setWidth(40);
		tblclmn00.setText("00"); //$NON-NLS-1$

		TableColumn tblclmn01 = new TableColumn(table, SWT.CENTER);
		tblclmn01.setWidth(40);
		tblclmn01.setText("01"); //$NON-NLS-1$

		TableColumn tblclmn02 = new TableColumn(table, SWT.CENTER);
		tblclmn02.setWidth(40);
		tblclmn02.setText("02"); //$NON-NLS-1$

		TableColumn tblclmn03 = new TableColumn(table, SWT.CENTER);
		tblclmn03.setWidth(40);
		tblclmn03.setText("03"); //$NON-NLS-1$

		TableColumn tblclmn04 = new TableColumn(table, SWT.CENTER);
		tblclmn04.setWidth(40);
		tblclmn04.setText("04"); //$NON-NLS-1$

		TableColumn tblclmn05 = new TableColumn(table, SWT.CENTER);
		tblclmn05.setWidth(40);
		tblclmn05.setText("05"); //$NON-NLS-1$

		TableColumn tblclmn06 = new TableColumn(table, SWT.CENTER);
		tblclmn06.setWidth(40);
		tblclmn06.setText("06"); //$NON-NLS-1$

		TableColumn tblclmn07 = new TableColumn(table, SWT.CENTER);
		tblclmn07.setWidth(40);
		tblclmn07.setText("07"); //$NON-NLS-1$

		TableColumn tblclmn08 = new TableColumn(table, SWT.CENTER);
		tblclmn08.setWidth(40);
		tblclmn08.setText("08"); //$NON-NLS-1$

		TableColumn tblclmn09 = new TableColumn(table, SWT.CENTER);
		tblclmn09.setWidth(40);
		tblclmn09.setText("09"); //$NON-NLS-1$

		TableColumn tblclmn10 = new TableColumn(table, SWT.CENTER);
		tblclmn10.setWidth(40);
		tblclmn10.setText("0A"); //$NON-NLS-1$

		TableColumn tblclmn11 = new TableColumn(table, SWT.CENTER);
		tblclmn11.setWidth(40);
		tblclmn11.setText("0B"); //$NON-NLS-1$

		TableColumn tblclmn12 = new TableColumn(table, SWT.CENTER);
		tblclmn12.setWidth(40);
		tblclmn12.setText("0C"); //$NON-NLS-1$

		TableColumn tblclmn13 = new TableColumn(table, SWT.CENTER);
		tblclmn13.setWidth(40);
		tblclmn13.setText("0D"); //$NON-NLS-1$

		TableColumn tblclmn14 = new TableColumn(table, SWT.CENTER);
		tblclmn14.setWidth(40);
		tblclmn14.setText("0E"); //$NON-NLS-1$

		TableColumn tblclmn15 = new TableColumn(table, SWT.CENTER);
		tblclmn15.setWidth(40);
		tblclmn15.setText("0F"); //$NON-NLS-1$

		TableColumn tblclmnASCII = new TableColumn(table, SWT.CENTER);
		tblclmnASCII.setWidth(140);
		tblclmnASCII.setText("ASCII"); //$NON-NLS-1$

		/**
		 * tableViewer的setInput参数对象是Object类型的，也就是说它可以接受任何参数
		 * 不过它最常接受的还是java的Collection或者数组
		 * 那么tableViewer是怎么知道如何来显示这么多输入格式千差万别的数据呢？就是依靠内容器和标签器
		 * 即setLabelProvider和setContentProvider
		 * 
		 */
		tableViewer.setLabelProvider(new GroupMemoryTableLabelProvider()); // 标签器--- 对如输入表格中的数据做处理
		tableViewer.setContentProvider(new GroupMemoryContentProvider()); // 内容器 --- 对输入表格种的数据进行筛选和转化

		// 列数量的设入
		UIUtil.setDefaultProperties(tableViewer);
		HexFileEditorInput input = null;
		// 正常情况下对hex文件进行显示
		if (getEditorInput() instanceof HexFileEditorInput) {
			// 输入对象是 hex编辑器的输入，提供各种内容的获取
			input = (HexFileEditorInput) getEditorInput();
			// 这里获取memory结果
			memory = input.getMemory();
			String showdisplay = "0x 1H 2 1K <";
			int sizeflash = memory.getFlashSize();
			sizeflash /= 1024;
			showdisplay += sizeflash + "K     ";
			getLabelShowInput().setText(showdisplay);
			GroupMemory groupMemory = new GroupMemory(memory);
			tableViewer.setHexInput(groupMemory);
		} else {
			System.out.println("???");
		}
		if (input != null && input.getIFile() != null) {
			this.setPartName(input.getIFile().getName());
		}
		for (int i = 0; i < 2; i++) {
			int[] ws = ChiponMemoryServiceClass.GetFormatTableChange("16bits"); //$NON-NLS-1$
			table.getColumn(1 + 8 * i).setWidth(ws[0]);
			table.getColumn(2 + 8 * i).setWidth(ws[1]);
			table.getColumn(3 + 8 * i).setWidth(ws[2]);
			table.getColumn(4 + 8 * i).setWidth(ws[3]);
			table.getColumn(5 + 8 * i).setWidth(ws[4]);
			table.getColumn(6 + 8 * i).setWidth(ws[5]);
			table.getColumn(7 + 8 * i).setWidth(ws[6]);
			table.getColumn(8 + 8 * i).setWidth(ws[7]);
		}

		ShowDataFormat.addModifyListener(new ModifyListener() {
			@Override
			public void modifyText(ModifyEvent e) {
				String format = ShowDataFormat.getText();

				if (tableViewer.getContentProvider() instanceof GroupMemoryContentProvider cp) {
					cp.showformat = format;
				}
				if (tableViewer.getLabelProvider() instanceof GroupMemoryTableLabelProvider lp) {
					lp.showformat = format;
				}
				if (tableViewer.getCellModifier() instanceof GroupMemoryCellModifier mp) {
					mp.showformat = format;
				}
				// ************
				for (int i = 0; i < 2; i++) {
					int[] ws = ChiponMemoryServiceClass.GetFormatTableChange(format);
					table.getColumn(1 + 8 * i).setWidth(ws[0]);
					table.getColumn(2 + 8 * i).setWidth(ws[1]);
					table.getColumn(3 + 8 * i).setWidth(ws[2]);
					table.getColumn(4 + 8 * i).setWidth(ws[3]);
					table.getColumn(5 + 8 * i).setWidth(ws[4]);
					table.getColumn(6 + 8 * i).setWidth(ws[5]);
					table.getColumn(7 + 8 * i).setWidth(ws[6]);
					table.getColumn(8 + 8 * i).setWidth(ws[7]);
				}
				// ************
				tableViewer.refresh();
			}
		});
		showFlashSelect.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent e) {
				if (tableViewer.getContentProvider() instanceof GroupMemoryContentProvider cp) {
					cp.isShowFlash = showFlashSelect.getSelection();
					tableViewer.refresh();

				} else {
					showFlashSelect.setSelection(!showFlashSelect.getSelection());
				}
				if (!showFlashSelect.getSelection()) {
					showStart.setEnabled(false);
					showStop.setEnabled(false);

					show_Button.setEnabled(false);
					UP_Button.setEnabled(false);
					DOWN_Button.setEnabled(false);
				} else {
					showStart.setEnabled(true);
					showStop.setEnabled(true);

					show_Button.setEnabled(true);
					UP_Button.setEnabled(true);
					DOWN_Button.setEnabled(true);
				}
			}
		});
		showUserSelect.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent e) {
				if (tableViewer.getContentProvider() instanceof GroupMemoryContentProvider cp) {
					cp.isShowDate = showUserSelect.getSelection();
					tableViewer.refresh();

				} else {
					showUserSelect.setSelection(!showUserSelect.getSelection());
				}
			}
		});
	}

	@Override
	public void setFocus() {
		tableViewer.getControl().setFocus();
	}

	public HexTableViewer getTableViewer() {
		return tableViewer;
	}

}
