package com.chipon32.hex.ui.editors;

public class ChiponMemoryServiceClass {
	
	static public String[] datetype = new String[]{
			"8bits",				
			"16bits",	
			"32bits",
			"64bits"
		  };
	
	
	static public int getFormatBytes(String format)
	{
		if(format.equalsIgnoreCase("8bits"))
			return 1;
		
		else if(format.equalsIgnoreCase("16bits"))
			return 2;
		
		else if(format.equalsIgnoreCase("32bits"))
			return 4;		
		else if(format.equalsIgnoreCase("64bits"))
			return 8;	
		else
			return 1;
	}
	
	static public boolean getIsSigned(String format)
	{		
		return false;
	}
	

	
// romlist内存16进制多自己字符串值解析到界面显示值.................romlist charshex to ui show	
	static public String ValueStringCovGet(String format,String inHex)
	{
		if(inHex==null || inHex.isEmpty())	inHex="0";
		//---------------------------------------------------
		if(format.equalsIgnoreCase("8bits"))
			return /*"0x"+*/inHex;	
		else 		if(format.equalsIgnoreCase("16bits"))
			return /*"0x"+*/inHex;	
		else 		if(format.equalsIgnoreCase("32bits"))
			return /*"0x"+*/inHex;	
		else 		if(format.equalsIgnoreCase("64bits"))
			return /*"0x"+*/inHex;	
		else 
		//---------------------------------------------------
			return /*"0x"+*/inHex;
	}
	// ui show for gdb  set 
	static public String ValueStringCovSet(String format,String inHex)
	{
		if(inHex==null || inHex.isEmpty())	inHex="0";
		//---------------------------------------------------
		if(format.equalsIgnoreCase("8bits"))
			return   "0x"+inHex;	
		else 		if(format.equalsIgnoreCase("16bits"))
			return   "0x"+inHex;	
		else 		if(format.equalsIgnoreCase("32bits"))
			return   "0x"+inHex;	
		else 		if(format.equalsIgnoreCase("64bits"))
			return   "0x"+inHex;	
		else 
			return 	 "0x"+inHex;
		//---------------------------------------------------
	}
	// for ui   re  to rom ele  to charhex ele
	static public String ValueStringCovReSet(String format,String inStr)
	{
		if(inStr==null || inStr.isEmpty())	inStr="0";
		
		if(format.equalsIgnoreCase("8bits"))
			return   toLength(inStr.toLowerCase(),1);	
		else 		if(format.equalsIgnoreCase("16bits"))
			return   toLength(inStr.toLowerCase(),2);	
		
		else 		if(format.equalsIgnoreCase("32bits"))
			return   toLength(inStr.toLowerCase(),4);	
		else 		if(format.equalsIgnoreCase("64bits"))
			return   toLength(inStr.toLowerCase(),8);	
		
		else
			return inStr;
	}
	
	static public String toLength(String instr,int bytes)
	{
		if(instr==null || instr.isEmpty())	instr="0";
		
		while(instr.length() < bytes*2)
			instr = "0"+ instr;
		return instr.substring(instr.length()-(bytes)*2);
	}
	
	static int [] GetFormatTableChange(String format)
	{
		int [] ws=new int[]{0,0,0,0,0,0,0,0};
		if(format==null || format.isEmpty())	format="0";
		//---------------------
		if(format.equalsIgnoreCase("8bits"))
		{
			ws[0]=40;	ws[1]=40;	ws[2]=40;	ws[3]=40;	ws[4]=40;	ws[5]=40;	ws[6]=40;	ws[7]=40;
		}
		else 		if(format.equalsIgnoreCase("16bits"))
		{
			ws[0]=60;	ws[1]=0;	ws[2]=80;	ws[3]=0;	ws[4]=60;	ws[5]=0;	ws[6]=80;	ws[7]=0;
		}
		else 		if(format.equalsIgnoreCase("32bits"))
		{
			ws[0]=100;	ws[1]=0;	ws[2]=0;	ws[3]=0;	ws[4]=100;	ws[5]=0;	ws[6]=0;	ws[7]=0;
		}
		else 		if(format.equalsIgnoreCase("64bits"))
		{
			ws[0]=200;	ws[1]=0;	ws[2]=0;	ws[3]=0;	ws[4]=0;	ws[5]=0;	ws[6]=0;	ws[7]=0;
		}
		else 
		{
			ws[0]=40;	ws[1]=40;	ws[2]=40;	ws[3]=40;	ws[4]=40;	ws[5]=40;	ws[6]=40;	ws[7]=40;
		}
		//--------------------
		return ws;
	}
	
	static long MyLongGetFromHex(String inhex)
	{
		if(inhex.length()<16)
			return Long.parseLong(inhex, 16);
		else if(inhex.length()==16)
		{
			char c= inhex.charAt(0);
			// 0-7
			if( c<'8')
				return Long.parseLong(inhex, 16);
			else
			{
				// 8 9
				if(c<='9')
				{
					if(c == '8')
						inhex= inhex.substring(1);
					else
						inhex= "1"+inhex.substring(1);					
					return Long.parseLong(inhex, 16) + Long.MIN_VALUE;
				}
				else
				{
				 // A-F a-f
					c=  (char) (c&0xDF);
					c=(char) (c-15);// a-f to 2-7
					inhex= c+inhex.substring(1);
					return Long.parseLong(inhex, 16) + Long.MIN_VALUE;
				}
			}	
		}
		else
		{
			return	Long.parseLong(inhex, 16);// let  intel errot 
		}
		
	}
}
