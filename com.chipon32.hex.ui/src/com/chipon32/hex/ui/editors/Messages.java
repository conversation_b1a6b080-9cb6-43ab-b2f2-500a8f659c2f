package com.chipon32.hex.ui.editors;

import org.eclipse.osgi.util.NLS;

public class Messages extends NLS {
	private static final String BUNDLE_NAME = "com.chipon32.hex.ui.editors.messages"; //$NON-NLS-1$
	
	public static String HexEditor_0;
	public static String HexEditor_1;
	public static String HexEditor_2;
	public static String HexEditor_28;
	public static String HexEditor_3;
	public static String HexEditor_4;
	public static String HexEditor_5;
	public static String HexEditor_6;
	
	public static String HexEditor_group;
	static {
		// initialize resource bundle
		NLS.initializeMessages(BUNDLE_NAME, Messages.class);
	}

	private Messages() {
	}
}
