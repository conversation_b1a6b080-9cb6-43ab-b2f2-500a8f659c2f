package com.chipon32.hex.ui.editorsmcu;

import org.eclipse.jface.viewers.IStructuredContentProvider;
import org.eclipse.jface.viewers.Viewer;


// 行对象解析
public class GroupMemoryMcuContentProvider implements IStructuredContentProvider {

	@Override
	public void dispose() {

	}

	@Override
	public void inputChanged(Viewer viewer, Object oldInput, Object newInput) {

	}
	//  获取不同的对象
	@Override
	public Object[] getElements(Object inputElement) {
		//  
		if(inputElement instanceof GroupMemory2)
		{
			// 当前行 
			GroupMemory2 lMemory = (GroupMemory2)inputElement;
			// 当前hex
				//		lMemory.getMemory();
			// 对象列表
			Object[] dateList = lMemory.getGroupDataList().toArray();
		
			// 线性统一数量下管理：顺序 falsh  userflash  eeprom config debugprogramflag protect  rom ram
			int dateonlylen	=dateList.length;
		

			Object[] obj=new Object[dateonlylen];
			
			for(int i=0;i<dateonlylen;i++)
			{
				if(i<dateonlylen)
					obj[i]=dateList[i-0];
				
			}
			//
			return obj;			
		}
		return new Object[0];
	}
	
}	
	
	
	


