package com.chipon32.hex.ui.editorsmcu;   //  校准值界面服务

import java.util.ArrayList;
import java.util.List;

import com.chipon32.hex.core.Memory;

// 分段式内容
public class GroupMemory2 {
	//~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
	// 整个芯片资源映射
	private Memory memory;
	// 行的段结果
	private List<Group4AddrVauleData> groupDataList;
	//~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
	//  从 memory中获取到对象 的构建
	public GroupMemory2(Memory memory)
	{
		this.memory = memory;
		// 初始化即是分类
		init();
	}
	// 分类
	private void init() 
	{
		//-------------------------------------------------
		// 构建 行内容 容器，多1行，首行为 内容标签
		groupDataList  = new ArrayList<Group4AddrVauleData>(memory.getOtherDates().length/4);
		//  XX区按照行 追加到内容中
		for(int i = 0; i < memory.getOtherDates().length/4; i++)
		{
			Group4AddrVauleData groupData = new Group4AddrVauleData();	
			groupData.setAddress(0+i*4);			
			
			for(int j = 0; j < 4; j++)
			{
				groupData.addRomData(memory.getOtherDates(i * 4 + j));
			}
			groupDataList.add(groupData);			
		}		
	}
	//~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
	//  获取段
	public List<Group4AddrVauleData> getGroupDataList() 
	{
		return groupDataList;
	}
	

	//~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
	// 对外提供整个内存	
	public Memory getMemory() {
		return memory;
	}
	//~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
}
