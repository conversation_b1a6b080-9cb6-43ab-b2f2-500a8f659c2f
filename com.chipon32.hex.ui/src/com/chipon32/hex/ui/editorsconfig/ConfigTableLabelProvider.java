package com.chipon32.hex.ui.editorsconfig;

import org.eclipse.jface.viewers.ITableLabelProvider;
import org.eclipse.jface.viewers.LabelProvider;
import org.eclipse.swt.graphics.Image;

import com.chipon32.hex.core.ConfigMessage;

// 行内容类型解析
public class ConfigTableLabelProvider extends LabelProvider implements ITableLabelProvider{
	// 

	@Override
	public Image getColumnImage(Object element, int columnIndex) {
		
		return null;
	}
	// 获取内容 
	@Override
	public String getColumnText(Object element, int columnIndex) {
		
		if(element instanceof ConfigMessage)
		{			
			ConfigMessage commData = (ConfigMessage)element;
			switch(columnIndex)
			{
			case 0:	// 该配置信息名字缩写
				return commData.getName();
			case 1: // Combo列表下的内容	
				return commData.getSelelementtext();
			case 2:	// 详细作用注解	
				return commData.getRemark(); 
			}		
		}
		// 其他情况 ，返回对象的文本
		return element.toString();
	}

}
