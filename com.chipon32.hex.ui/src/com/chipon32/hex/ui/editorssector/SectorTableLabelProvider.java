package com.chipon32.hex.ui.editorssector;

import org.eclipse.jface.viewers.ITableLabelProvider;
import org.eclipse.jface.viewers.LabelProvider;
import org.eclipse.swt.graphics.Image;

import com.chipon32.hex.core.SectorElementVaule;
import com.chipon32.hex.ui.HexUiActivator;

// 行内容类型解析
public class SectorTableLabelProvider extends LabelProvider implements ITableLabelProvider{
	// 
	static String  PathImageFile1= HexUiActivator.getFilePathFromPlugin("sel.gif");
	static String  PathImageFile2= HexUiActivator.getFilePathFromPlugin("nosel.gif");
	private Image[]  imgs=new Image[]
			{
			 new Image(null,	PathImageFile1),
	         new Image(null,	PathImageFile2)			
			};
	@Override
	public Image getColumnImage(Object element, int columnIndex) {
		if(element instanceof SectorElementVaule)
		{			

			SectorElementVaule commData = (SectorElementVaule)element;
			switch(columnIndex)
			{
			case 0:
				return null;
			case 1:
				return null;
			case 2:
				return null;				
			case 3:
				if(commData.getIsCanRead())
					return imgs[0];
				else
					return imgs[1];
			case 4:
				if(commData.getIsCanWrite())
					return imgs[0];
				else
					return imgs[1];		
			}		
		}	
		
		
		
		return null;
	}
	// 获取内容 
	@Override
	public String getColumnText(Object element, int columnIndex) {
		
		if(element instanceof SectorElementVaule)
		{			
			SectorElementVaule commData = (SectorElementVaule)element;
			switch(columnIndex)
			{
			case 0:
				return "Page"+commData.getNum();
			case 1:
				String strbuf="0x"+commData.getAddressHexValue();
				return strbuf;
			case 2:
				{
					int len=commData.getLen();
					if(len<1024)
						return Integer.toString(len)+"B";
					else {
						return Integer.toString(len/1024)+"K";
					}
					
				}
								
			case 3:
				return Boolean.toString(commData.getIsCanRead());			
			case 4:
				return Boolean.toString(commData.getIsCanWrite());				
			}		
		}
		// 其他情况 ，返回对象的文本
		return element.toString();
	}
	@Override
	public void dispose() {
		// TODO Auto-generated method stub
		for(Image image:imgs)
		{
			image.dispose();
		}

	}
}
