package com.chipon32.hex.ui.editorssector;

import org.eclipse.jface.viewers.IStructuredContentProvider;
import org.eclipse.jface.viewers.Viewer;

import com.chipon32.hex.core.SectorElementVaule;

// 行对象解析
public class SectorContentProvider implements IStructuredContentProvider {

	@Override
	public void dispose() {

	}

	@Override
	public void inputChanged(Viewer viewer, Object oldInput, Object newInput) {

	}
	//  获取不同的对象
	@Override
	public Object[] getElements(Object inputElement) {
		//  
		if(inputElement instanceof SectorElementVaule[])
		{
			// 当前行 
			SectorElementVaule[] sector = (SectorElementVaule[])inputElement;

			Object[] obj=new Object[sector.length];
			
			for(int i=0;i<sector.length;i++)
			{				
					obj[i]=sector[i];				
			}
			//
			return obj;			
		}
		return new Object[0];
	}
	
}	
	
	
	


