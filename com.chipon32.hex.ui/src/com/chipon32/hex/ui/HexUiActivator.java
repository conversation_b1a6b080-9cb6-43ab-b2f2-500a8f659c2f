package com.chipon32.hex.ui;

import java.net.URL;
import java.util.Enumeration;

import org.eclipse.core.runtime.FileLocator;
import org.eclipse.core.runtime.IPath;
import org.eclipse.core.runtime.Path;
import org.eclipse.ui.plugin.AbstractUIPlugin;
import org.osgi.framework.BundleContext;


/**
 * The activator class controls the plug-in life cycle
 */
public class HexUiActivator extends AbstractUIPlugin {

	// The plug-in ID
	public static final String PLUGIN_ID = "com.chipon32.hex.ui"; //$NON-NLS-1$

	// The shared instance
	private static HexUiActivator plugin;
	
	/**
	 * The constructor
	 */
	public HexUiActivator() {
	}

	/*
	 * (non-Javadoc)
	 * @see org.eclipse.ui.plugin.AbstractUIPlugin#start(org.osgi.framework.BundleContext)
	 */
	@Override
	public void start(BundleContext context) throws Exception {
		super.start(context);
		plugin = this;
	}
    public IPath getDataLocation() {
        try {
            return getStateLocation();
        } catch (IllegalStateException e) {
            // This occurs if -data=@none is explicitly specified, so ignore this silently.
            // Is this OK? See bug 85071.
            return null;
        }
    }
	/*
	 * (non-Javadoc)
	 * @see org.eclipse.ui.plugin.AbstractUIPlugin#stop(org.osgi.framework.BundleContext)
	 */
	@Override
	public void stop(BundleContext context) throws Exception {
		plugin = null;
		super.stop(context);
	}

	/**
	 * Returns the shared instance
	 *
	 * @return the shared instance
	 */
	public static HexUiActivator getDefault() {
		return plugin;
	}
	
	/**
	   * Returns the absolut path of a entrie from the plugin's directory.
	   * 
	   * @param entrie a file or directory (don't use "dir1\dir2" or "dir1\file1")
	   * 
	   * @return Returns the path from the plugin.
	   */
	public static String getFilePathFromPlugin(String entrie)
	{
		URL url = null;
	    IPath path = null;
	    String result = "";

	    Enumeration<URL> enu = HexUiActivator.getDefault().getBundle().findEntries("/", entrie, true);
	    if(enu == null){
	    	result = "";
	    }else{
			if (enu.hasMoreElements()) {
				url = enu.nextElement();
			}
			try {
				path = new Path(FileLocator.toFileURL(url).getPath());
				result = path.makeAbsolute().toOSString();
			} catch (Exception e) {
				result = "";
			}
	    }	   
	    return result;
	  }

}
