<?xml version="1.0" encoding="UTF-8"?>
<?eclipse version="3.0"?>
<plugin>
    <!-- ///////////////////////////////////////////
                        PREFERENCE PAGE
    /////////////////////////////////////////// -->
    <extension
            point="org.eclipse.ui.preferencePages">
        <page
                class="com.chipon32.os.freertos.preferences.PreferencesPage"
                id="com.chipon32.os.freertos.preferences.TadPreferences"
                name="FreeRTOS">
        </page>
    </extension>
    <extension point="org.eclipse.core.runtime.preferences">
        <initializer class="com.chipon32.os.freertos.preferences.PreferencesInitializer"/>
    </extension>

    <!-- ///////////////////////////////////////////
                        COMMANDS
    /////////////////////////////////////////// -->
    <extension
            point="org.eclipse.ui.commands">
        <!--	VIEWS -->
        <command
                id="com.chipon32.os.freertos.taskListView"
                name="Task List View">
        </command>
<!--        <command-->
<!--                id="com.chipon32.os.freertos.taskNotificationsView"-->
<!--                name="Task Notifications View">-->
<!--        </command>-->
        <command
                id="com.chipon32.os.freertos.queueListView"
                name="Queue List View">
        </command>
        <command
                id="com.chipon32.os.freertos.timerListView"
                name="Timer List View">
        </command>
        <command
                id="com.chipon32.os.freertos.heapUsageView"
                name="Heap Usage View">
        </command>
    </extension>
    <!-- ///////////////////////////////////////////
                        VIEWS
    /////////////////////////////////////////// -->
    <extension
            point="org.eclipse.ui.views">
        <category
                id="com.chipon32.os.freertos.freeRTOSCategory"
                name="FreeRTOS">
        </category>
        <view
                category="com.chipon32.os.freertos.freeRTOSCategory"
                class="com.chipon32.os.freertos.view.tasklist.TaskListView"
                icon="icons/freertos.png"
                id="com.chipon32.os.freertos.taskListView"
                name="Tasks"
                restorable="true">
        </view>
        <!--      <view-->
        <!--            category="com.chipon32.os.freertos.freeRTOSCategory"-->
        <!--            class="tasknotifications.view.com.chipon32.os.freertos.TaskNotificationsView"-->
        <!--            icon="icons/freertos.png"-->
        <!--            id="com.chipon32.os.freertos.taskNotificationsView"-->
        <!--            name="Task Notifications"-->
        <!--            restorable="true">-->
        <!--      </view>-->
        <view
                category="com.chipon32.os.freertos.freeRTOSCategory"
                class="com.chipon32.os.freertos.view.queuelist.QueueListView"
                icon="icons/freertos.png"
                id="com.chipon32.os.freertos.queueListView"
                name="Queues"
                restorable="true">
        </view>
        <view
                category="com.chipon32.os.freertos.freeRTOSCategory"
                class="com.chipon32.os.freertos.view.timerlist.TimerListView"
                icon="icons/freertos.png"
                id="com.chipon32.os.freertos.timerListView"
                name="Timers"
                restorable="true">
        </view>
        <view
                category="com.chipon32.os.freertos.freeRTOSCategory"
                class="com.chipon32.os.freertos.view.heapusage.HeapUsageView"
                icon="icons/freertos.png"
                id="com.chipon32.os.freertos.heapUsageView"
                name="Heap Usage"
                restorable="true">
        </view>
    </extension>
    <!-- ///////////////////////////////////////////
                        MENUS
    /////////////////////////////////////////// -->
    <extension
            point="org.eclipse.ui.menus">
        <menuContribution
                allPopups="false"
                locationURI="menu:org.eclipse.ui.main.menu?after=additions">
            <menu label="FreeRTOS">
                <visibleWhen checkEnabled="false">
                    <with variable="activeWorkbenchWindow.activePerspective">
                        <or>
                            <equals
                                    value="org.eclipse.debug.ui.DebugPerspective">
                            </equals>
                        </or>
                    </with>
                </visibleWhen>

                <!-- TASKS -->
                <command
                        commandId="com.chipon32.os.freertos.taskListView"
                        icon="icons/freertos.png"
                        label="Task List"
                        style="push">
                </command>
                <!-- TASK NOTIFICATIONS -->
<!--                <command-->
<!--                        commandId="com.chipon32.os.freertos.taskNotificationsView"-->
<!--                        icon="icons/freertos.png"-->
<!--                        label="Task Notifications"-->
<!--                        style="push">-->
<!--                </command>-->
                <!-- QUEUES -->
                <command
                        commandId="com.chipon32.os.freertos.queueListView"
                        icon="icons/freertos.png"
                        label="Queue List"
                        style="push">
                </command>
                <!-- TIMERS -->
                <command
                        commandId="com.chipon32.os.freertos.timerListView"
                        icon="icons/freertos.png"
                        label="Timer List"
                        style="push">
                </command>
                <!-- HEAP -->
                <command
                        commandId="com.chipon32.os.freertos.heapUsageView"
                        icon="icons/freertos.png"
                        label="Heap Usage"
                        style="push">
                </command>
            </menu>
        </menuContribution>
    </extension>
    <!-- ///////////////////////////////////////////
                        STARTUP
    /////////////////////////////////////////// -->
    <extension point="org.eclipse.ui.startup">
        <startup class="com.chipon32.os.freertos.model.FreeRtosTadModelInit"/>
    </extension>
    <!-- ///////////////////////////////////////////
                        HANDLERS
    /////////////////////////////////////////// -->
    <extension
            point="org.eclipse.ui.handlers">
        <handler
                class="com.chipon32.os.common.handlers.ShowTadView"
                commandId="com.chipon32.os.freertos.taskListView">
        </handler>
        <handler
                class="com.chipon32.os.common.handlers.ShowTadView"
                commandId="com.chipon32.os.freertos.queueListView">
        </handler>
        <handler
                class="com.chipon32.os.common.handlers.ShowTadView"
                commandId="com.chipon32.os.freertos.taskNotificationsView">
        </handler>
        <handler
                class="com.chipon32.os.common.handlers.ShowTadView"
                commandId="com.chipon32.os.freertos.timerListView">
        </handler>
        <handler
                class="com.chipon32.os.common.handlers.ShowTadView"
                commandId="com.chipon32.os.freertos.heapUsageView">
        </handler>
    </extension>
    <!-- ///////////////////////////////////////////
                        CONSOLE
    /////////////////////////////////////////// -->
    <extension
            point="org.eclipse.ui.console.consoleFactories">
        <consoleFactory
                class="com.chipon32.os.freertos.model.console.FreeRTOSConsoleFactory"
                icon="icons/freertos.png"
                label="FreeRTOS Task Aware Debugger Console">
        </consoleFactory>
    </extension>

</plugin>
