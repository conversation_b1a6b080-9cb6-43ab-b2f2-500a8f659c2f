# Info
Info.ViewDataEmpty=%s view data is empty.
Info.FreeBlock=Free
Info.Used=Used
Info.AllocatedBlock=Allocated
Info.Enabled=Enabled
Info.Disabled=Disabled
Info.HeapNoInfo=No information is available when heap 3 or static memory allocation is used. 
Info.HeapNewlibNoInfo=No information is available for heap using Newlib (are symbols \"_pvHeapStart\", \"_pvHeapLimit\" defined?). 
Info.Heap5RegionsUndef=Heap 5 regions (e.g. xHeapRegions) are not defined.
Info.HeapVariable=Heap variable %s is equal to \"0x%08x\".
Info.HeapTypeIdentifyFromVariable=TAD will try to determine heap type (memory scheme) from available variables.
Info.HeapTypeIdentifyHelp=Make sure you have correctly defined \"configFRTOS_MEMORY_SCHEME\" macro in FreeRTOSConfig.h. \
	TAD determines heap type either from MCUXpresso IDE's \"FreeRTOSDebugConfig\" structure (which uses \"configFRTOS_MEMORY_SCHEME\" define) \
	or user defined variable \"static const uint8_t freeRTOSMemoryScheme = <heap_type_used>;\".
Info.HeapIdentifiedFrom=Heap type (memory allocation scheme) has been identified from %s as %s. 
Info.VersionIdentifiedFrom=FreeRTOS version has been identified from %s as %s.
Info.StackGrowth=Stack grows %s
Info.CouldNotReadEndOfStack=Could not read pxEndOfStack variable needed for determining correct stack size! \n\t\
	Please enable \"configRECORD_STACK_HIGH_ADDRESS\" macro in FreeRTOSConfig.h.
Info.FreeRTOSNotUsed=FreeRTOS has not been detected.
Info.FreeRTOSUnknown=Could not determine FreeRTOS' presence.
Info.DebugConfigStructureV14=FreeRTOS kernel version 11 (or newer) detected but \"FreeRTOSDebugConfig\" structure version is still older than v1.4. \
	Please update \"FreeRTOSDebugConfig\" structure to help MCUXpresso IDE correctly deal with SMP-specific features.
# Labels
Label.Task=Task
Label.Task.TCB=TCB
Label.Task.Stack=Stack
Label.Queue=Queue
# Exceptions
Exception.CouldNotGetSymbol=could not get symbol on address 0x%08x.
Exception.CouldNotOpenURL=Could not open \"%s\"
# Errors
Error.DataNotValid=\"%s\" data are not valid - not all items are TadItem!
Error.KeyNotFound=Key \"%s\" not found!
Error.CouldNotGetHeapFactoryData=Could not load data for \"%s\" view probably due to missing heap related variable (try to define \
	heap variables as "volatile" so they are not optimized out) or due to incorrect heap type identification (detected heap #%d).
Error.CouldNotGetSize=Could not get size of \"%s\"!
Error.CannotDisplayReadyTaskList=Cannot display tasks from ready task list!
Error.CannotReadFrom=Cannot read information from \"%s\".
Error.TaskStackDefault=Stack memory block was not read properly, stack has default values.
Error.Overflow=Overflow!
Error.None=None
Error.Empty=Empty
Error.Disabled=Disabled
Error.QuestionMark=?
Error.Unknown=Unknown
# Warning messages
Warning.Dependency.Queue=Queue registry size is zero! Each queue must be registered with vQueueAddToRegistry() and configQUEUE_REGISTRY_SIZE macro \n\
	must be greater than zero (found in FreeRTOSConfig.h)
# TAD state
State.None=TAD state init (NONE)
# TAD log
Log.TadConsole=FreeRTOS Task Aware Debugger Console version %s
Log.DirName=FreeRTOS_TAD_logs
Log.FileExtension=.log
Log.Saved=TAD log was saved in \"%s\"
Log.SaveName=TAD_log_
Log.SaveAs=Save TAD log as
# TAD preferences
Pref.Title=FreeRTOS TAD preferences
Pref.Desc=FreeRTOS Task Aware Debugger plug-in preferences
Pref.ReadTimeout=FreeRTOS memory read timeout (ms)
# Task Notifications view
TaskNotifications.StateNotWaiting=Not waiting
TaskNotifications.StateWaiting=Waiting
TaskNotifications.StateReceived=Received
TaskNotifications.StateUnknown=Unknown (0x%02x)
TaskNotifications.TCBnum=TCB#
TaskNotifications.TCBnumTooltip=Task number (user or TCB)
TaskNotifications.TaskName=Task Name
TaskNotifications.TaskNameTooltip=Name assigned to task
TaskNotifications.TaskHandle=Task Handle
TaskNotifications.TaskHandleTooltip=Task handle address
TaskNotifications.NumNotificationsReceived=# Received
TaskNotifications.NumNotificationsReceivedTooltip=Number of notifications received by task
TaskNotifications.NumNotificationsWaiting=# Waiting
TaskNotifications.NumNotificationsWaitingTooltip=Number of notifications on which task is waiting
TaskNotifications.NotificationsWaitingFor=Waiting for...
TaskNotifications.NotificationsWaitingForTooltip=Indexes of notifications that are being waited
TaskNotifications.NotificationsWaitingForNone=None
TaskNotifications.Index=#
TaskNotifications.IndexTooltip=Notification index
TaskNotifications.NotificationsAddress=Address
TaskNotifications.NotificationsAddressTooltip=Address of notification data item
TaskNotifications.NotificationsState=State
TaskNotifications.NotificationsStateTooltip=Notification state
TaskNotifications.NotificationsDataBytes=Data [DEC]
TaskNotifications.NotificationsDataBytesTooltip=Notification data as decimal number
TaskNotifications.NotificationsDataHex=Data [HEX]
TaskNotifications.NotificationsDataHexTooltip=Notification data as hexadecimal number
TaskNotifications.NotificationsDataBin=Data [BIN]
TaskNotifications.NotificationsDataBinTooltip=Notification data as binary number
TaskNotifications.NotificationsDataText=Data [ASCII]
TaskNotifications.NotificationsDataTextTooltip=Notification data as ASCII text
TaskNotifications.ErrorReadingNotificationsData=Could not read notifications data for task "%s"
# Tasks view
Tasks.TotalRunTimeNotAvailable=ulTotalRunTime not available
Tasks.TaskNumber=Task Number
Tasks.CoreAffinityMask=Core Affinity Mask
Tasks.StackBase=Stack Base
Tasks.StackTop=Stack Top
Tasks.StackHighWaterMark=Stack High Water Mark
Tasks.Unknown=Unknown
