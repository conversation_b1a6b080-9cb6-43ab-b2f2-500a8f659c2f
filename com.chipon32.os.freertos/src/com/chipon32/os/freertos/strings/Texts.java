package com.chipon32.os.freertos.strings;

import java.util.ResourceBundle;

import com.chipon32.os.freertos.Activator;

public class Texts {
	private static final ResourceBundle TEXT_BUNDLE = ResourceBundle.getBundle("com.chipon32.os.freertos.strings.Texts");

	public static String get(String key) {
		if (TEXT_BUNDLE.containsKey(key)) {
			return TEXT_BUNDLE.getString(key);
		} else {
			Activator.getDefault().getLogger().error(String.format(TEXT_BUNDLE.getString("Error.KeyNotFound"), key));
			return "";
		}
	}
}
