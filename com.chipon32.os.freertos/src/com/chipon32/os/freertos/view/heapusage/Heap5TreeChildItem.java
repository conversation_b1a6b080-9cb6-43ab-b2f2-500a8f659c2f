package com.chipon32.os.freertos.view.heapusage;

import java.util.LinkedList;
import java.util.List;

import com.chipon32.os.common.messages.Separator;
import com.chipon32.os.common.model.Auxiliary;
import com.chipon32.os.common.view.column.ITadColumnId;
import com.chipon32.os.common.view.items.TadItem;
import com.chipon32.os.freertos.model.view.heapusage.HeapBlock;
import com.chipon32.os.freertos.model.view.heapusage.HeapRegion;
import com.chipon32.os.freertos.view.column.FreeRtosTadColumnId;

public class Heap5TreeChildItem extends TadItem {

	private final HeapRegion region;
	private final List<HeapBlockItem> heapBlockItems;

	public Heap5TreeChildItem(int index, HeapRegion region) {
		super(index);
		this.region = region;
		int i = 0;
		this.heapBlockItems = new LinkedList<>();

		for (HeapBlock block : region.getHeapBlocks()) {
			++i;
			this.heapBlockItems.add(new HeapBlockItem(block, i));
		}

	}

	public List<? extends TadItem> getChildItems() {
		return this.heapBlockItems;
	}

	public String toString(Separator separator) {
		return super.toString(new FreeRtosTadColumnId[] { FreeRtosTadColumnId.ADDRESS_START, FreeRtosTadColumnId.ADDRESS_END, FreeRtosTadColumnId.ADDRESS_RANGE,
				FreeRtosTadColumnId.USAGE, FreeRtosTadColumnId.FREE_SPACE, FreeRtosTadColumnId.USAGE_GRAPH }, separator);
	}

	public String getText(ITadColumnId column) {
		FreeRtosTadColumnId freeRtosColumn = (FreeRtosTadColumnId) column;
		return switch (freeRtosColumn) {
			case ADDRESS_START -> String.format("0x%08x", this.region.getStartAddress());
			case ADDRESS_END -> String.format("0x%08x", this.region.getEndAddress());
			case TYPE -> String.format("#%d Reg.", this.index);
			case USAGE -> String.format("%s / %s", formatBytes(this.region.getUsed()), formatBytes(this.region.getSize()));
			case USAGE_GRAPH -> String.format("%.2f%%", Auxiliary.getPercentage(this.region.getUsed(), this.region.getSize()));
			case FREE_SPACE -> {
				double percentage = (double) 100.0F - Auxiliary.getPercentage(this.region.getUsed(), this.region.getSize());
				yield String.format("%.2f%% (%s)", Math.max(percentage, 0.0F), formatBytes(Math.max(this.region.getFree(), 0L)));
			}
			default -> null;
		};
	}

	public Long getNumValue(ITadColumnId column) {
		FreeRtosTadColumnId freeRtosColumn = (FreeRtosTadColumnId) column;
		return switch (freeRtosColumn) {
			case ADDRESS_START -> this.region.getStartAddress();
			case ADDRESS_END -> this.region.getEndAddress();
			case USAGE, USAGE_GRAPH -> this.region.getSize();
			default -> null;
		};
	}

	public HeapRegion getRegion() {
		return this.region;
	}
}
