package com.chipon32.os.freertos.view.heapusage;

import org.eclipse.jface.viewers.StyledCellLabelProvider;
import org.eclipse.jface.viewers.TreeViewer;
import org.eclipse.swt.widgets.Composite;

import com.chipon32.os.common.controller.TadViewController;
import com.chipon32.os.common.rtos.IRtosConfig;
import com.chipon32.os.common.view.TadDoubleViewWeight;
import com.chipon32.os.common.view.TadView;
import com.chipon32.os.common.view.TadViewType;
import com.chipon32.os.common.view.TadViewViewerType;
import com.chipon32.os.common.view.column.TadColumn;
import com.chipon32.os.common.view.column.TadColumnCopyable;
import com.chipon32.os.common.view.column.TadColumnWeight;
import com.chipon32.os.common.view.providers.TadItemColumnLabelProvider;
import com.chipon32.os.freertos.Activator;
import com.chipon32.os.freertos.controller.FreeRtosTadViewController;
import com.chipon32.os.freertos.model.freertos.FreeRTOSConfig;
import com.chipon32.os.freertos.view.column.FreeRtosTadColumnId;
import com.chipon32.os.freertos.view.providers.FreeRtosTadItemColumnLabelProvider;

public class HeapUsageView extends TadView {
	public HeapUsageView() {
		super(Activator.getDefault().getTadModel(), TadViewType.DOUBLE, TadDoubleViewWeight.CHILD);
		this.parentColumns.add(new TadColumn(FreeRtosTadColumnId.TYPE, "Type", "Heap implementation", TadColumnWeight.SMALL, TadColumnCopyable.READ_ONLY,
				(StyledCellLabelProvider) null, FreeRTOSConfig.MEMORY_SCHEME));
		this.parentColumns.add(new TadColumn(FreeRtosTadColumnId.USAGE_GRAPH, "Usage (%)", "How many bytes of heap is used (in graph)", TadColumnWeight.WIDE,
				TadColumnCopyable.COPYABLE, new HeapUsageGraphLabelProvider(), (IRtosConfig) null));
		this.parentColumns.add(
				new TadColumn(FreeRtosTadColumnId.USAGE, "Used", "How many bytes of heap is used (in user friendly format)", TadColumnWeight.NORMAL, TadColumnCopyable.COPYABLE));
		this.parentColumns.add(new TadColumn(FreeRtosTadColumnId.FREE_SPACE, "Free", "Free heap space in bytes", TadColumnWeight.NORMAL, TadColumnCopyable.COPYABLE));
		this.parentColumns.add(new TadColumn(FreeRtosTadColumnId.ADDRESS_RANGE, "Address Range", "Starting address of heap", TadColumnWeight.NORMAL, TadColumnCopyable.COPYABLE));
		this.childColumns.add(new TadColumn(FreeRtosTadColumnId.ID, "#", "Heap block number", TadColumnWeight.NARROW, TadColumnCopyable.READ_ONLY));
		this.childColumns.add(new TadColumn(FreeRtosTadColumnId.DETAILS, "Details", "If heap block is free or allocated", TadColumnWeight.NORMAL, TadColumnCopyable.COPYABLE));
		this.childColumns.add(new TadColumn(FreeRtosTadColumnId.ADDRESS_START, "Block Start", "Starting address of heap block", TadColumnWeight.SMALL, TadColumnCopyable.COPYABLE));
		this.childColumns.add(new TadColumn(FreeRtosTadColumnId.ADDRESS_END, "Block End", "Ending address of heap block", TadColumnWeight.SMALL, TadColumnCopyable.COPYABLE));
		this.childColumns.add(new TadColumn(FreeRtosTadColumnId.SIZE, "Size", "Size of heap block", TadColumnWeight.SMALL, TadColumnCopyable.COPYABLE));
	}

	public TadView getTadView() {
		return this;
	}

	public String getTitle() {
		return "Heap Usage";
	}

	protected TadViewController createController() {
		return new FreeRtosTadViewController(this.getTadView());
	}

	protected TadItemColumnLabelProvider createColumnLabelProvider(TadColumn column) {
		return new FreeRtosTadItemColumnLabelProvider(column);
	}

	public void createPartControl(Composite parent) {
		super.createPartControl(parent);
		((TreeViewer) this.getTreeViewers().get(TadViewViewerType.PARENT)).getControl().setData(Activator.SWTBOT_DEFAULT_KEY, "freertos.heapUsage_upper");
		((TreeViewer) this.getTreeViewers().get(TadViewViewerType.CHILD)).getControl().setData(Activator.SWTBOT_DEFAULT_KEY, "freertos.heapUsage_lower");
	}
}
