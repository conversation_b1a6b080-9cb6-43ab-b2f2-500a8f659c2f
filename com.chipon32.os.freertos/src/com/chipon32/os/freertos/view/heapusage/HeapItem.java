package com.chipon32.os.freertos.view.heapusage;

import java.util.LinkedList;
import java.util.List;

import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.widgets.Display;

import com.chipon32.os.common.messages.Separator;
import com.chipon32.os.common.model.Auxiliary;
import com.chipon32.os.common.view.column.ITadColumnId;
import com.chipon32.os.common.view.items.TadItem;
import com.chipon32.os.freertos.model.view.heapusage.Heap;
import com.chipon32.os.freertos.model.view.heapusage.HeapBlock;
import com.chipon32.os.freertos.model.view.heapusage.HeapRegion;
import com.chipon32.os.freertos.model.view.heapusage.HeapType;
import com.chipon32.os.freertos.strings.Texts;
import com.chipon32.os.freertos.view.column.FreeRtosTadColumnId;

public class HeapItem extends TadItem {
	private Heap heap;
	private List<HeapBlockItem> heapBlockItems;
	private List<TadItem> treeChildren;

	public HeapItem(Heap heap) {
		super(1);
		this.heap = heap;
		int i = 0;
		this.heapBlockItems = new LinkedList<>();

		for (HeapBlock block : heap.getHeapBlocks()) {
			++i;
			this.heapBlockItems.add(new HeapBlockItem(block, i));
		}

		this.treeChildren = new LinkedList<>();
		if (heap.getType() == HeapType.HEAP_5) {
			i = 0;

			for (HeapRegion region : heap.getHeap5regions()) {
				++i;
				this.treeChildren.add(new Heap5TreeChildItem(i, region));
			}
		}

	}

	public List<? extends TadItem> getTreeChildren() {
		return this.treeChildren;
	}

	public List<? extends TadItem> getChildItems() {
		return this.heapBlockItems;
	}

	public Color getTextColour() {
		return !this.hasChildItems() ? Display.getDefault().getSystemColor(16) : super.getTextColour();
	}

	public String toString(Separator separator) {
		return super.toString(new FreeRtosTadColumnId[] { FreeRtosTadColumnId.TYPE, FreeRtosTadColumnId.ADDRESS_START, FreeRtosTadColumnId.ADDRESS_END,
				FreeRtosTadColumnId.ADDRESS_RANGE, FreeRtosTadColumnId.USAGE, FreeRtosTadColumnId.FREE_SPACE, FreeRtosTadColumnId.USAGE_GRAPH }, separator);
	}

	public String getText(ITadColumnId column) {
		FreeRtosTadColumnId freeRtosColumn = (FreeRtosTadColumnId) column;
		switch (freeRtosColumn) {
			case ADDRESS_START:
				return String.format("0x%08x", this.heap.getHeapStart());
			case ADDRESS_END:
				return String.format("0x%08x", this.heap.getHeapEnd());
			case ADDRESS_RANGE:
				String var10000 = String.format("0x%08x", this.heap.getHeapStart());
				return var10000 + " - " + String.format("0x%08x", this.heap.getHeapEnd());
			case TYPE:
				return String.format("%s", this.heap.getType().toString());
			case USAGE:
				return String.format("%s / %s", this.heap.getUsed() != null ? formatBytes(this.heap.getUsed()) : Texts.get("Error.QuestionMark"), formatBytes(this.heap.getSize()));
			case USAGE_GRAPH:
				if (this.heap.getUsed() == null) {
					return null;
				}

				return String.format("%.2f%%", Auxiliary.getPercentage(this.heap.getUsed(), this.heap.getSize()));
			case FREE_SPACE:
				if (this.heap.getUsed() == null && this.heap.getFree() == 0L) {
					return Texts.get("Error.Unknown");
				}

				return String.format("%.2f%% (%s)", (double) 100.0F - Auxiliary.getPercentage(this.heap.getUsed(), this.heap.getSize()), formatBytes(this.heap.getFree()));
			default:
				return null;
		}
	}

	public Long getNumValue(ITadColumnId column) {
		FreeRtosTadColumnId freeRtosColumn = (FreeRtosTadColumnId) column;
		switch (freeRtosColumn) {
			case ADDRESS_START:
				return this.heap.getHeapStart();
			case ADDRESS_END:
				return this.heap.getHeapEnd();
			case ADDRESS_RANGE:
			case TYPE:
			case STATE:
			case SIZE:
			case NUMBER:
			default:
				return null;
			case USAGE:
			case USAGE_GRAPH:
				return this.heap.getUsed();
		}
	}

	public Heap getHeap() {
		return this.heap;
	}
}
