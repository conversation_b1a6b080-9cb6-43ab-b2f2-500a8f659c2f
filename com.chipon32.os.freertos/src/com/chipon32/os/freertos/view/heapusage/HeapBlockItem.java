package com.chipon32.os.freertos.view.heapusage;

import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.widgets.Display;

import com.chipon32.os.common.messages.Separator;
import com.chipon32.os.common.model.Auxiliary;
import com.chipon32.os.common.view.column.ITadColumnId;
import com.chipon32.os.common.view.items.TadItem;
import com.chipon32.os.freertos.model.view.heapusage.HeapBlock;
import com.chipon32.os.freertos.strings.Texts;
import com.chipon32.os.freertos.view.column.FreeRtosTadColumnId;

class HeapBlockItem extends TadItem {
	private final HeapBlock block;

	HeapBlockItem(HeapBlock block, int index) {
		super(index);
		this.block = block;
	}

	public Color getTextColour() {
		switch (this.block.getStatus()) {
			case FREE:
				return Display.getDefault().getSystemColor(SWT.COLOR_DARK_GREEN);
			case ALLOCATED:
				if (this.block.getDetails() == null) {
					return Display.getDefault().getSystemColor(SWT.COLOR_DARK_GRAY);
				}
			default:
				return super.getTextColour();
		}
	}

	public String toString(Separator separator) {
		return super.toString(new FreeRtosTadColumnId[] { FreeRtosTadColumnId.ID, FreeRtosTadColumnId.DETAILS, FreeRtosTadColumnId.ADDRESS_START, FreeRtosTadColumnId.ADDRESS_END,
				FreeRtosTadColumnId.SIZE }, separator);
	}

	public String getText(ITadColumnId column) {
		FreeRtosTadColumnId freeRtosColumn = (FreeRtosTadColumnId) column;
		return switch (freeRtosColumn) {
			case ID -> String.format("%d", this.index);
			case ADDRESS_START -> String.format("0x%08x", this.block.getStartAddress());
			case ADDRESS_END -> String.format("0x%08x", this.block.getEndAddress());
			case SIZE -> {
				if (this.block.getSize() == 0L) {
					yield Texts.get("Error.Empty");
				}

				yield String.format("0x%x (%s)", this.block.getSize(), formatBytes(this.block.getSize()));
			}
			case DETAILS -> this.block.getDetails() == null ? Auxiliary.capitalize(this.block.getStatus().toString()) : this.block.getDetails();
			default -> null;
		};
	}

	public Long getNumValue(ITadColumnId column) {
		FreeRtosTadColumnId freeRtosColumn = (FreeRtosTadColumnId) column;
		return switch (freeRtosColumn) {
			case ADDRESS_START -> this.block.getStartAddress();
			case ADDRESS_END -> this.block.getEndAddress();
			case SIZE -> this.block.getSize();
			default -> null;
		};
	}
}
