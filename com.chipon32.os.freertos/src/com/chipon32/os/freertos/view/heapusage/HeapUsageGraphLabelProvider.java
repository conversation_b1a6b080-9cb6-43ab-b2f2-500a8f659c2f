package com.chipon32.os.freertos.view.heapusage;

import java.util.List;

import org.eclipse.jface.viewers.TreeViewerColumn;
import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.graphics.GC;
import org.eclipse.swt.graphics.RGBA;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Event;

import com.chipon32.os.common.model.Auxiliary;
import com.chipon32.os.freertos.model.view.heapusage.HeapBlock;
import com.chipon32.os.freertos.model.view.heapusage.HeapBlockStatus;
import com.chipon32.os.freertos.model.view.heapusage.HeapRegion;
import com.chipon32.os.freertos.strings.Texts;
import com.chipon32.os.freertos.view.providers.FreeRtosTadBarGraphProvider;

class HeapUsageGraphLabelProvider extends FreeRtosTadBarGraphProvider {

	protected void paint(Event event, Object element) {
		if (element != null) {
			HeapRegion region;
			if (element instanceof Heap5TreeChildItem) {
				region = ((Heap5TreeChildItem) element).getRegion();
			} else {
				if (!(element instanceof HeapItem)) {
					return;
				}

				region = ((HeapItem) element).getHeap();
			}

			long size = region.getSize();
			Long used = region.getUsed();
			long free = region.getFree();
			List<HeapBlock> blocks = region.getHeapBlocks();
			if (used != null || free != 0L) {
				GC gc = event.gc;
				int fullHeight = event.height - 1;
				int fullWidth = ((TreeViewerColumn) this.getColumn()).getColumn().getWidth() - 1;
				Color oldColour = gc.getBackground();
				Color whiteColor = gc.getDevice().getSystemColor(SWT.COLOR_WHITE);
				if (size > 0L) {
					if (!blocks.isEmpty()) {
						double barWidth = (double) 0.0F;

						for (HeapBlock block : blocks) {
							double percentage = Auxiliary.getPercentage(block.getSize(), size);
							double width = (double) fullWidth * (percentage / (double) 100.0F);
							if (width > (double) 0.0F) {
								float hue = (float) (percentage > (double) 90.0F ? 0
										: (percentage > (double) 75.0F ? 30 : (percentage > (double) 50.0F ? 55 : (percentage > (double) 30.0F ? 90 : 125))));
								gc.setBackground(
										block.getStatus() == HeapBlockStatus.FREE ? Auxiliary.WHITE_COLOUR : new Color(Display.getDefault(), new RGBA(hue, 1.0F, 1.0F, 120.0F)));
								gc.fillRectangle((int) Math.ceil(barWidth) + event.x, event.y, (int) Math.ceil(width), fullHeight);
								barWidth += width;
							}
						}
					} else {
						double percentage = Auxiliary.getPercentage(used, size);
						int width = (int) Math.ceil((double) fullWidth * (percentage / (double) 100.0F));
						float hue = (float) (percentage > (double) 90.0F ? 0
								: (percentage > (double) 75.0F ? 30 : (percentage > (double) 50.0F ? 55 : (percentage > (double) 30.0F ? 90 : 125))));
						gc.setBackground(new Color(Display.getDefault(), new RGBA(hue, 1.0F, 1.0F, 120.0F)));
						gc.fillRectangle(event.x, event.y, width, fullHeight);
						gc.setBackground(whiteColor);
						gc.fillRectangle(width + event.x, event.y, fullWidth - width, fullHeight);
					}
				}

				gc.setFont(Display.getDefault().getSystemFont());
				gc.setForeground(Auxiliary.getContrastColor(gc.getBackground()));
				String percentageString = String.format("%.2f%% " + Texts.get("Info.Used"), Auxiliary.getPercentage(used, size));
				gc.drawString(percentageString, event.x + fullWidth / 2 - gc.textExtent(percentageString).x / 2, event.y, true);
				gc.setBackground(oldColour);
			}
		}
	}
}
