package com.chipon32.os.freertos.view.providers;

import com.chipon32.os.common.view.column.TadColumn;
import com.chipon32.os.common.view.providers.TadItemColumnLabelProvider;
import com.chipon32.os.freertos.Activator;

public class FreeRtosTadItemColumnLabelProvider extends TadItemColumnLabelProvider {
	public FreeRtosTadItemColumnLabelProvider(TadColumn column) {
		super(column);
		this.rtos = Activator.getDefault().getTadModel().getRTOS();
	}
}
