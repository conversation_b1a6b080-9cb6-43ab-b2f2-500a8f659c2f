package com.chipon32.os.freertos.view.providers;

import com.chipon32.os.common.messages.Messages;
import com.chipon32.os.common.model.Auxiliary;
import com.chipon32.os.common.view.providers.TadBarGraphProvider;

public abstract class FreeRtosTadBarGraphProvider extends TadBarGraphProvider {
	public String getToolTipText(Object element) {
		return this.showWarning ? String.format(Messages.Warning_Dependency, this.dependency.toString(), Auxiliary.capitalize(this.columnName)) : null;
	}
}
