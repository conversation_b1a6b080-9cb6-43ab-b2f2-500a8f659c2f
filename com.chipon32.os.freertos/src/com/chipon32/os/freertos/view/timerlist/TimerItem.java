package com.chipon32.os.freertos.view.timerlist;

import com.chipon32.os.common.messages.Separator;
import com.chipon32.os.common.view.column.ITadColumnId;
import com.chipon32.os.common.view.items.TadItem;
import com.chipon32.os.freertos.model.view.timerlist.Timer;
import com.chipon32.os.freertos.view.column.FreeRtosTadColumnId;

public class TimerItem extends TadItem {
	private Timer timer;

	public TimerItem(Timer timer, int index) {
		super(index);
		this.timer = timer;
	}

	public String toString(Separator separator) {
		return super.toString(new FreeRtosTadColumnId[] { FreeRtosTadColumnId.ID, FreeRtosTadColumnId.NAME, FreeRtosTadColumnId.NUMBER, FreeRtosTadColumnId.PERIOD,
				FreeRtosTadColumnId.STATUS, FreeRtosTadColumnId.CALLBACK }, separator);
	}

	public String getText(ITadColumnId column) {
		FreeRtosTadColumnId freeRtosColumn = (FreeRtosTadColumnId) column;
		switch (freeRtosColumn) {
			case ID:
				return String.format("0x%x", this.timer.getId());
			case NAME:
				return this.timer.getName();
			case NUMBER:
				if (this.timer.getNumber() != null) {
					return String.format("0x%x", this.timer.getNumber());
				}

				return null;
			case PERIOD:
				return String.format("%d", this.timer.getPeriod());
			case STATUS:
				return this.timer.getStatus();
			case CALLBACK:
				return String.format("%s (0x%08x)", this.timer.getCallback().getName(), this.timer.getCallback().getAddress());
			default:
				return null;
		}
	}

	public Long getNumValue(ITadColumnId column) {
		FreeRtosTadColumnId freeRtosColumn = (FreeRtosTadColumnId) column;
		switch (freeRtosColumn) {
			case ID:
				return (long) this.timer.getId();
			case NUMBER:
				if (this.timer.getNumber() != null) {
					return this.timer.getNumber();
				}

				return null;
			case CALLBACK:
				return this.timer.getCallback() != null ? this.timer.getCallback().getAddress() : 0L;
			default:
				return null;
		}
	}
}
