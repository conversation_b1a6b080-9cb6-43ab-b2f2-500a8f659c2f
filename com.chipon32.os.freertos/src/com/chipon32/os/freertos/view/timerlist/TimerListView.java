package com.chipon32.os.freertos.view.timerlist;

import org.eclipse.jface.viewers.StyledCellLabelProvider;
import org.eclipse.jface.viewers.TreeViewer;
import org.eclipse.swt.widgets.Composite;

import com.chipon32.os.common.controller.TadViewController;
import com.chipon32.os.common.view.TadView;
import com.chipon32.os.common.view.TadViewType;
import com.chipon32.os.common.view.TadViewViewerType;
import com.chipon32.os.common.view.column.TadColumn;
import com.chipon32.os.common.view.column.TadColumnCopyable;
import com.chipon32.os.common.view.column.TadColumnWeight;
import com.chipon32.os.common.view.providers.TadItemColumnLabelProvider;
import com.chipon32.os.freertos.Activator;
import com.chipon32.os.freertos.controller.FreeRtosTadViewController;
import com.chipon32.os.freertos.model.freertos.FreeRTOSConfig;
import com.chipon32.os.freertos.view.column.FreeRtosTadColumnId;
import com.chipon32.os.freertos.view.providers.FreeRtosTadItemColumnLabelProvider;

public class TimerListView extends TadView {
	public TimerListView() {
		super(Activator.getDefault().getTadModel(), TadViewType.SINGLE);
		this.parentColumns.add(new TadColumn(FreeRtosTadColumnId.ID, "ID", "Timer's ID", TadColumnWeight.SMALL, TadColumnCopyable.COPYABLE));
		this.parentColumns.add(new TadColumn(FreeRtosTadColumnId.NAME, "Timer Name", "User defined timer name", TadColumnWeight.NORMAL, TadColumnCopyable.COPYABLE));
		this.parentColumns.add(new TadColumn(FreeRtosTadColumnId.PERIOD, "Period [ticks]", "Timer period in ticks", TadColumnWeight.SMALL, TadColumnCopyable.COPYABLE));
		this.parentColumns.add(new TadColumn(FreeRtosTadColumnId.STATUS, "Status", "Timer status", TadColumnWeight.NORMAL, TadColumnCopyable.COPYABLE));
		this.parentColumns.add(new TadColumn(FreeRtosTadColumnId.NUMBER, "Timer Number", "Timer's ID assigned by trace tools (e.g. FreeRTOS+Trace)", TadColumnWeight.NORMAL,
				TadColumnCopyable.COPYABLE, (StyledCellLabelProvider) null, FreeRTOSConfig.USE_TRACE));
		this.parentColumns.add(new TadColumn(FreeRtosTadColumnId.CALLBACK, "Callback function", "Function that will be called when the timer expires", TadColumnWeight.WIDE,
				TadColumnCopyable.COPYABLE));
	}

	protected TadView getTadView() {
		return this;
	}

	public String getTitle() {
		return "Timer List";
	}

	protected TadViewController createController() {
		return new FreeRtosTadViewController(this.getTadView());
	}

	protected TadItemColumnLabelProvider createColumnLabelProvider(TadColumn column) {
		return new FreeRtosTadItemColumnLabelProvider(column);
	}

	public void createPartControl(Composite parent) {
		super.createPartControl(parent);
		((TreeViewer) this.getTreeViewers().get(TadViewViewerType.PARENT)).getControl().setData(Activator.SWTBOT_DEFAULT_KEY, "freertos.timerList");
	}
}
