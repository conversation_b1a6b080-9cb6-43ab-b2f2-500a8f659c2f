package com.chipon32.os.freertos.view.items;

import com.chipon32.os.common.messages.Separator;
import com.chipon32.os.common.view.column.ITadColumnId;
import com.chipon32.os.common.view.items.TadItem;
import com.chipon32.os.common.view.items.TadTreeChildItem;
import com.chipon32.os.freertos.view.column.FreeRtosTadColumnId;

public class FreeRtosTadTreeChildItem extends TadTreeChildItem {
	public FreeRtosTadTreeChildItem(TadItem parent, int index, String key, String value) {
		super(parent, index, key, value);
	}

	public String toString(Separator separator) {
		return super.toString(new ITadColumnId[] { FreeRtosTadColumnId.NAME, FreeRtosTadColumnId.ADDRESS }, separator);
	}

	public String getText(ITadColumnId column) {
		FreeRtosTadColumnId freeRtosColumn = (FreeRtosTadColumnId) column;
		switch (freeRtosColumn) {
			case NAME -> {
				return this.key + ":";
			}
			case ADDRESS -> {
				return this.value;
			}
			default -> {
				return null;
			}
		}
	}
}
