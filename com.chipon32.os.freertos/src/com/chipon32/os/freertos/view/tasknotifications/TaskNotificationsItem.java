package com.chipon32.os.freertos.view.tasknotifications;

import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;

import com.chipon32.os.common.messages.Messages;
import com.chipon32.os.common.messages.Separator;
import com.chipon32.os.common.view.column.ITadColumnId;
import com.chipon32.os.common.view.items.TadItem;
import com.chipon32.os.freertos.Activator;
import com.chipon32.os.freertos.model.view.tasknotifications.TaskNotifications;
import com.chipon32.os.freertos.model.view.tasknotifications.TaskNotificationsData;
import com.chipon32.os.freertos.strings.Texts;
import com.chipon32.os.freertos.view.column.FreeRtosTadColumnId;

public class TaskNotificationsItem extends TadItem {
	private TaskNotifications taskNotifications;
	private List<TaskNotificationsDataItem> taskNotificationsItems;

	public TaskNotificationsItem(TaskNotifications taskNotifications, int index) {
		super(index);
		this.taskNotifications = taskNotifications;
		this.taskNotificationsItems = new LinkedList<>();
		if (taskNotifications.getNotificationsData() != null) {
			for (TaskNotificationsData item : taskNotifications.getNotificationsData()) {
				if (item.hasData()) {
					this.taskNotificationsItems.add(new TaskNotificationsDataItem(item));
				}
			}
		}

	}

	public String getText(ITadColumnId column) {
		FreeRtosTadColumnId freeRtosColumn = (FreeRtosTadColumnId) column;

		try {
			switch (freeRtosColumn) {
				case ID:
					if (this.taskNotifications.getTCBNumber() != null) {
						return this.taskNotifications.getTCBNumber().toString();
					}

					return null;
				case NAME:
					return this.taskNotifications.getTaskName() != null ? this.taskNotifications.getTaskName() : Texts.get("Error.Unknown");
				case ADDRESS:
					return String.format("0x%08x", this.taskNotifications.getTaskHandle());
				case NUM_RECEIVED:
					return String.valueOf(this.taskNotifications.getNumReceived());
				case NUM_WAITING:
					return String.valueOf(this.taskNotifications.getNumWaiting());
				case WAITING_FOR:
					String waitingFor = (String) this.taskNotifications.getWaitingFor().stream().map(String::valueOf).collect(Collectors.joining(", "));
					if (waitingFor.isEmpty()) {
						waitingFor = Texts.get("TaskNotifications.NotificationsWaitingForNone");
					}

					return waitingFor;
				default:
					return null;
			}
		} catch (Exception e) {
			Activator.getDefault().getLogger().exception(e, String.format(Messages.Exception_WhileGetTextColumn, column.toString()));
			return null;
		}
	}

	public String toString(Separator separator) {
		return super.toString(new FreeRtosTadColumnId[] { FreeRtosTadColumnId.ID, FreeRtosTadColumnId.NAME, FreeRtosTadColumnId.ADDRESS, FreeRtosTadColumnId.NUM_RECEIVED,
				FreeRtosTadColumnId.NUM_WAITING, FreeRtosTadColumnId.WAITING_FOR }, separator);
	}

	public List<? extends TadItem> getChildItems() {
		return this.taskNotificationsItems;
	}

	public List<? extends TadItem> getTreeChildren() {
		return Collections.emptyList();
	}

	public Long getNumValue(ITadColumnId column) {
		FreeRtosTadColumnId freeRtosColumn = (FreeRtosTadColumnId) column;
		switch (freeRtosColumn) {
			case ADDRESS -> {
				return this.taskNotifications.getTaskHandle();
			}
			default -> {
				return null;
			}
		}
	}
}
