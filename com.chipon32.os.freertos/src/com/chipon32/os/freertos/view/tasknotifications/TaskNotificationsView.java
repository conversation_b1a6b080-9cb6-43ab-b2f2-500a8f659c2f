package com.chipon32.os.freertos.view.tasknotifications;

import org.eclipse.jface.viewers.StyledCellLabelProvider;

import com.chipon32.os.common.controller.TadViewController;
import com.chipon32.os.common.view.TadDoubleViewWeight;
import com.chipon32.os.common.view.TadView;
import com.chipon32.os.common.view.TadViewType;
import com.chipon32.os.common.view.column.TadColumn;
import com.chipon32.os.common.view.column.TadColumnCopyable;
import com.chipon32.os.common.view.column.TadColumnWeight;
import com.chipon32.os.common.view.providers.TadItemColumnLabelProvider;
import com.chipon32.os.freertos.Activator;
import com.chipon32.os.freertos.controller.FreeRtosTadViewController;
import com.chipon32.os.freertos.model.freertos.FreeRTOSConfig;
import com.chipon32.os.freertos.strings.Texts;
import com.chipon32.os.freertos.view.column.FreeRtosTadColumnId;
import com.chipon32.os.freertos.view.providers.FreeRtosTadItemColumnLabelProvider;

public class TaskNotificationsView extends TadView {
	public TaskNotificationsView() {
		super(Activator.getDefault().getTadModel(), TadViewType.DOUBLE, TadDoubleViewWeight.PARENT);
		this.parentColumns.add(new TadColumn(FreeRtosTadColumnId.ID, Texts.get("TaskNotifications.TCBnum"), Texts.get("TaskNotifications.TCBnumTooltip"), TadColumnWeight.SMALL,
				TadColumnCopyable.READ_ONLY, (StyledCellLabelProvider) null, FreeRTOSConfig.USE_TRACE));
		this.parentColumns.add(new TadColumn(FreeRtosTadColumnId.NAME, Texts.get("TaskNotifications.TaskName"), Texts.get("TaskNotifications.TaskNameTooltip"),
				TadColumnWeight.WIDE, TadColumnCopyable.COPYABLE));
		this.parentColumns.add(new TadColumn(FreeRtosTadColumnId.ADDRESS, Texts.get("TaskNotifications.TaskHandle"), Texts.get("TaskNotifications.TaskHandleTooltip"),
				TadColumnWeight.NORMAL, TadColumnCopyable.COPYABLE));
		this.parentColumns.add(new TadColumn(FreeRtosTadColumnId.NUM_RECEIVED, Texts.get("TaskNotifications.NumNotificationsReceived"),
				Texts.get("TaskNotifications.NumNotificationsReceivedTooltip"), TadColumnWeight.SMALL, TadColumnCopyable.COPYABLE));
		this.parentColumns.add(new TadColumn(FreeRtosTadColumnId.NUM_WAITING, Texts.get("TaskNotifications.NumNotificationsWaiting"),
				Texts.get("TaskNotifications.NumNotificationsWaitingTooltip"), TadColumnWeight.SMALL, TadColumnCopyable.COPYABLE));
		this.parentColumns.add(new TadColumn(FreeRtosTadColumnId.WAITING_FOR, Texts.get("TaskNotifications.NotificationsWaitingFor"),
				Texts.get("TaskNotifications.NotificationsWaitingForTooltip"), TadColumnWeight.NORMAL, TadColumnCopyable.COPYABLE));
		this.childColumns.add(new TadColumn(FreeRtosTadColumnId.NUMBER, Texts.get("TaskNotifications.Index"), Texts.get("TaskNotifications.IndexTooltip"), TadColumnWeight.NARROW,
				TadColumnCopyable.COPYABLE));
		this.childColumns.add(new TadColumn(FreeRtosTadColumnId.ADDRESS, Texts.get("TaskNotifications.NotificationsAddress"),
				Texts.get("TaskNotifications.NotificationsAddressTooltip"), TadColumnWeight.SMALL, TadColumnCopyable.COPYABLE));
		this.childColumns.add(new TadColumn(FreeRtosTadColumnId.STATE, Texts.get("TaskNotifications.NotificationsState"), Texts.get("TaskNotifications.NotificationsStateTooltip"),
				TadColumnWeight.SMALL, TadColumnCopyable.COPYABLE));
		this.childColumns.add(new TadColumn(FreeRtosTadColumnId.DATA_BYTES, Texts.get("TaskNotifications.NotificationsDataBytes"),
				Texts.get("TaskNotifications.NotificationsDataBytesTooltip"), TadColumnWeight.SMALL, TadColumnCopyable.COPYABLE));
		this.childColumns.add(new TadColumn(FreeRtosTadColumnId.DATA_HEX, Texts.get("TaskNotifications.NotificationsDataHex"),
				Texts.get("TaskNotifications.NotificationsDataHexTooltip"), TadColumnWeight.SMALL, TadColumnCopyable.COPYABLE));
		this.childColumns.add(new TadColumn(FreeRtosTadColumnId.DATA_BIN, Texts.get("TaskNotifications.NotificationsDataBin"),
				Texts.get("TaskNotifications.NotificationsDataBinTooltip"), TadColumnWeight.NORMAL, TadColumnCopyable.COPYABLE));
		this.childColumns.add(new TadColumn(FreeRtosTadColumnId.DATA_TEXT, Texts.get("TaskNotifications.NotificationsDataText"),
				Texts.get("TaskNotifications.NotificationsDataTextTooltip"), TadColumnWeight.SMALL, TadColumnCopyable.COPYABLE));
	}

	protected TadView getTadView() {
		return this;
	}

	public String getTitle() {
		return "Task Notifications";
	}

	protected TadViewController createController() {
		return new FreeRtosTadViewController(this.getTadView());
	}

	protected TadItemColumnLabelProvider createColumnLabelProvider(TadColumn column) {
		return new FreeRtosTadItemColumnLabelProvider(column);
	}
}
