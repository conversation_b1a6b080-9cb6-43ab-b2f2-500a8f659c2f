package com.chipon32.os.freertos.view.tasknotifications;

import com.chipon32.os.common.messages.Messages;
import com.chipon32.os.common.messages.Separator;
import com.chipon32.os.common.model.view.queues.GenericQueueDataTreeChildItem;
import com.chipon32.os.common.view.column.ITadColumnId;
import com.chipon32.os.common.view.items.TadItem;
import com.chipon32.os.freertos.Activator;
import com.chipon32.os.freertos.model.view.tasknotifications.TaskNotificationsData;
import com.chipon32.os.freertos.view.column.FreeRtosTadColumnId;
import com.chipon32.os.freertos.view.queuelist.QueueDataItem;

public class TaskNotificationsDataItem extends QueueDataItem {
	private TaskNotificationsData taskNotificationsData;

	public TaskNotificationsDataItem(TaskNotificationsData taskNotificationsData) {
		super(taskNotificationsData);
		this.taskNotificationsData = taskNotificationsData;
	}

	public GenericQueueDataTreeChildItem createChildItem(TadI<PERSON> parent, long address, long data, int size) {
		return null;
	}

	public String toString(Separator separator) {
		return super.toString(new FreeRtosTadColumnId[] { FreeRtosTadColumnId.NUMBER, FreeRtosTadColumnId.ADDRESS, FreeRtosTadColumnId.STATE, FreeRtosTadColumnId.DATA_BYTES,
				FreeRtosTadColumnId.DATA_HEX, FreeRtosTadColumnId.DATA_BIN, FreeRtosTadColumnId.DATA_TEXT }, separator);
	}

	public String getText(ITadColumnId column) {
		FreeRtosTadColumnId freeRtosColumn = (FreeRtosTadColumnId) column;

		try {
			switch (freeRtosColumn) {
				case ADDRESS -> {
					return String.format("0x%08x", this.taskNotificationsData.getAddress());
				}
				case STATE -> {
					return this.taskNotificationsData.getStateStr();
				}
				case NUMBER -> {
					return Integer.toString(this.taskNotificationsData.getId());
				}
				default -> {
					return super.getText(column);
				}
			}
		} catch (Exception e) {
			Activator.getDefault().getLogger().exception(e, String.format(Messages.Exception_WhileGetTextColumn, column.toString()));
			return null;
		}
	}

	public Long getNumValue(ITadColumnId column) {
		FreeRtosTadColumnId freeRtosColumn = (FreeRtosTadColumnId) column;
		switch (freeRtosColumn) {
			case ADDRESS -> {
				return this.taskNotificationsData.getAddress();
			}
			case NUMBER -> {
				return (long) this.taskNotificationsData.getId();
			}
			default -> {
				return super.getNumValue(column);
			}
		}
	}
}
