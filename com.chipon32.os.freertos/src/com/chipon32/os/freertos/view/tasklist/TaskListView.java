package com.chipon32.os.freertos.view.tasklist;

import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Event;

import com.chipon32.os.common.controller.TadFactoryData;
import com.chipon32.os.common.controller.TadViewController;
import com.chipon32.os.common.model.Auxiliary;
import com.chipon32.os.common.model.view.tasks.GenericTaskStack;
import com.chipon32.os.common.view.TadView;
import com.chipon32.os.common.view.TadViewType;
import com.chipon32.os.common.view.TadViewViewerType;
import com.chipon32.os.common.view.column.TadColumn;
import com.chipon32.os.common.view.column.TadColumnCopyable;
import com.chipon32.os.common.view.column.TadColumnWeight;
import com.chipon32.os.common.view.providers.TadItemColumnLabelProvider;
import com.chipon32.os.freertos.Activator;
import com.chipon32.os.freertos.controller.FreeRtosTadViewController;
import com.chipon32.os.freertos.model.freertos.FreeRTOS;
import com.chipon32.os.freertos.model.freertos.FreeRTOSConfig;
import com.chipon32.os.freertos.model.view.tasklist.Task;
import com.chipon32.os.freertos.view.column.FreeRtosTadColumnId;
import com.chipon32.os.freertos.view.providers.FreeRtosTadBarGraphProvider;
import com.chipon32.os.freertos.view.providers.FreeRtosTadItemColumnLabelProvider;

import java.util.List;

public class TaskListView extends TadView {
	public TaskListView() {
		super(Activator.getDefault().getTadModel(), TadViewType.SINGLE);
		this.parentColumns.add(
				new TadColumn(FreeRtosTadColumnId.ID, "TCB#", "Task number (user or TCB)", TadColumnWeight.SMALL, TadColumnCopyable.READ_ONLY, null, FreeRTOSConfig.USE_TRACE));
		this.parentColumns.add(new TadColumn(FreeRtosTadColumnId.NAME, "Task Name", "Text name assigned to task", TadColumnWeight.WIDE, TadColumnCopyable.COPYABLE));
		this.parentColumns.add(new TadColumn(FreeRtosTadColumnId.ADDRESS, "Task Handle", "Task handle address", TadColumnWeight.NORMAL, TadColumnCopyable.COPYABLE));
		this.parentColumns.add(new TadColumn(FreeRtosTadColumnId.STATE, "Task State", "Current task state", TadColumnWeight.NORMAL, TadColumnCopyable.COPYABLE));
		this.parentColumns.add(new TadColumn(FreeRtosTadColumnId.PRIORITY, "Priority", "Current task priority (Actual / Base)", TadColumnWeight.SMALL, TadColumnCopyable.COPYABLE));
		this.parentColumns.add(new TadColumn(FreeRtosTadColumnId.USAGE_GRAPH, "Stack Usage", "Stack High Water Mark / Stack Size", TadColumnWeight.WIDE, TadColumnCopyable.COPYABLE,
				new FreeRtosTadBarGraphProvider() {
					protected void paint(Event event, Object element) {
						if (element instanceof TaskItem taskItem) {
							if (taskItem.getTask() != null) {
								GenericTaskStack stack = taskItem.getTask().getStack();
								FreeRTOS rtos = (FreeRTOS) Activator.getDefault().getTadModel().getRTOS();
								super.paintBarGraph(event, Auxiliary.getPercentage(stack.getUsage(), stack.getSize()), taskItem.getText(FreeRtosTadColumnId.USAGE_GRAPH),
										!rtos.isMacroEnabled(FreeRTOSConfig.RECORD_STACK_HIGH_ADDRESS));
							}
						}

					}
				}, FreeRTOSConfig.RECORD_STACK_HIGH_ADDRESS));
		this.parentColumns.add(new TadColumn(FreeRtosTadColumnId.EVENT_OBJ, "Event Object", "The name (or address) of queue on which is task blocked", TadColumnWeight.WIDE,
				TadColumnCopyable.COPYABLE));
		this.parentColumns.add(new TadColumn(FreeRtosTadColumnId.RUNTIME, "Runtime", "Task active time in percent of total runtime", TadColumnWeight.NORMAL,
				TadColumnCopyable.COPYABLE, new FreeRtosTadBarGraphProvider() {
					protected void paint(Event event, Object element) {
						if (element instanceof TaskItem taskItem) {
							if (taskItem.getTask() != null) {
								super.paintBarGraph(event, Auxiliary.getPercentage(taskItem.getTask().getTaskRuntime(), taskItem.getTask().getRuntime()),
										taskItem.getText(FreeRtosTadColumnId.RUNTIME), taskItem.isParent() && this.dependency != null && taskItem.getText(this.columnId) == null);
							}
						}

					}
				}, FreeRTOSConfig.RUNTIME_STATS));
	}

	protected TadView getTadView() {
		return this;
	}

	public String getTitle() {
		return "Task List";
	}

	protected TadViewController createController() {
		return new FreeRtosTadViewController(this.getTadView());
	}

	protected TadItemColumnLabelProvider createColumnLabelProvider(TadColumn column) {
		return new FreeRtosTadItemColumnLabelProvider(column);
	}

	public void createPartControl(Composite parent) {
		super.createPartControl(parent);
		this.getTreeViewers().get(TadViewViewerType.PARENT).getControl().setData(Activator.SWTBOT_DEFAULT_KEY, "freertos.taskList");

		//TODO DELETE TEST DATA
		getController().dataReady(new TadFactoryData(
			tadModel,
			List.of(
				new Task("")
			)
		));
	}
}
