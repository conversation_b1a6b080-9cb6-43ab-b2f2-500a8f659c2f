package com.chipon32.os.freertos.view.queuelist;

import org.eclipse.jface.viewers.StyledCellLabelProvider;
import org.eclipse.jface.viewers.TreeViewer;
import org.eclipse.swt.widgets.Composite;

import com.chipon32.os.common.controller.TadViewController;
import com.chipon32.os.common.view.TadDoubleViewWeight;
import com.chipon32.os.common.view.TadView;
import com.chipon32.os.common.view.TadViewType;
import com.chipon32.os.common.view.TadViewViewerType;
import com.chipon32.os.common.view.column.TadColumn;
import com.chipon32.os.common.view.column.TadColumnCopyable;
import com.chipon32.os.common.view.column.TadColumnWeight;
import com.chipon32.os.common.view.providers.TadItemColumnLabelProvider;
import com.chipon32.os.freertos.Activator;
import com.chipon32.os.freertos.controller.FreeRtosTadViewController;
import com.chipon32.os.freertos.model.freertos.FreeRTOSConfig;
import com.chipon32.os.freertos.view.column.FreeRtosTadColumnId;
import com.chipon32.os.freertos.view.providers.FreeRtosTadItemColumnLabelProvider;

public class QueueListView extends TadView {
	public QueueListView() {
		super(Activator.getDefault().getTadModel(), TadViewType.DOUBLE, TadDoubleViewWeight.PARENT);
		this.parentColumns.add(new TadColumn(FreeRtosTadColumnId.ID, "#", "Queue number", TadColumnWeight.SMALL, TadColumnCopyable.READ_ONLY));
		this.parentColumns.add(new TadColumn(FreeRtosTadColumnId.NAME, "Queue Name", "Text name assigned to queue", TadColumnWeight.WIDE, TadColumnCopyable.COPYABLE));
		this.parentColumns.add(new TadColumn(FreeRtosTadColumnId.ADDRESS, "Address", "Address of the queue", TadColumnWeight.NORMAL, TadColumnCopyable.COPYABLE));
		this.parentColumns.add(new TadColumn(FreeRtosTadColumnId.LENGTH, "Length", "Current and maximum length of queue", TadColumnWeight.SMALL, TadColumnCopyable.COPYABLE));
		this.parentColumns.add(new TadColumn(FreeRtosTadColumnId.SIZE, "Item Size", "Size of queue item", TadColumnWeight.NORMAL, TadColumnCopyable.COPYABLE));
		this.parentColumns.add(
				new TadColumn(FreeRtosTadColumnId.TX_WAITING, "# Tx Waiting", "Number of tasks waiting to send to the queue", TadColumnWeight.SMALL, TadColumnCopyable.COPYABLE));
		this.parentColumns.add(new TadColumn(FreeRtosTadColumnId.RX_WAITING, "# Rx Waiting", "Number of tasks waiting to receive from the queue", TadColumnWeight.SMALL,
				TadColumnCopyable.COPYABLE));
		this.parentColumns.add(new TadColumn(FreeRtosTadColumnId.TYPE, "Queue Type", "Type of queue (regular queue or synchronization object)", TadColumnWeight.WIDE,
				TadColumnCopyable.COPYABLE, (StyledCellLabelProvider) null, FreeRTOSConfig.USE_TRACE));
		this.childColumns.add(new TadColumn(FreeRtosTadColumnId.ID, "#", "Number of queue item", TadColumnWeight.NARROW, TadColumnCopyable.COPYABLE));
		this.childColumns.add(new TadColumn(FreeRtosTadColumnId.ADDRESS, "Address", "Address of the queue data", TadColumnWeight.SMALL, TadColumnCopyable.COPYABLE));
		this.childColumns.add(new TadColumn(FreeRtosTadColumnId.DATA_BYTES, "Queue Data [DEC]", "Queue data as decimal number", TadColumnWeight.SMALL, TadColumnCopyable.COPYABLE));
		this.childColumns
				.add(new TadColumn(FreeRtosTadColumnId.DATA_HEX, "Queue Data [HEX]", "Queue data as hexadecimal number", TadColumnWeight.SMALL, TadColumnCopyable.COPYABLE));
		this.childColumns.add(new TadColumn(FreeRtosTadColumnId.DATA_BIN, "Queue Data [BIN]", "Queue data as binary number", TadColumnWeight.NORMAL, TadColumnCopyable.COPYABLE));
		this.childColumns.add(new TadColumn(FreeRtosTadColumnId.DATA_TEXT, "Queue Data [ASCII]", "Queue data as ASCII text", TadColumnWeight.SMALL, TadColumnCopyable.COPYABLE));
	}

	protected TadView getTadView() {
		return this;
	}

	public String getTitle() {
		return "Queue List";
	}

	protected TadViewController createController() {
		return new FreeRtosTadViewController(this.getTadView());
	}

	protected TadItemColumnLabelProvider createColumnLabelProvider(TadColumn column) {
		return new FreeRtosTadItemColumnLabelProvider(column);
	}

	public void createPartControl(Composite parent) {
		super.createPartControl(parent);
		((TreeViewer) this.getTreeViewers().get(TadViewViewerType.PARENT)).getControl().setData(Activator.SWTBOT_DEFAULT_KEY, "freertos.queueList_upper");
		((TreeViewer) this.getTreeViewers().get(TadViewViewerType.CHILD)).getControl().setData(Activator.SWTBOT_DEFAULT_KEY, "freertos.queueList_lower");
	}
}
