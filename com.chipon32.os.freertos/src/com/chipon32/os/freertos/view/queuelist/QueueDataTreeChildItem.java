package com.chipon32.os.freertos.view.queuelist;

import com.chipon32.os.common.messages.Separator;
import com.chipon32.os.common.model.Auxiliary;
import com.chipon32.os.common.model.view.queues.GenericQueueDataTreeChildItem;
import com.chipon32.os.common.view.column.ITadColumnId;
import com.chipon32.os.common.view.items.TadItem;
import com.chipon32.os.freertos.view.column.FreeRtosTadColumnId;

public class QueueDataTreeChildItem extends GenericQueueDataTreeChildItem {
	public QueueDataTreeChildItem(TadItem parent, long address, long data, int size) {
		super(parent, address, data, size);
	}

	public String toString(Separator separator) {
		return super.toString(new FreeRtosTadColumnId[] { FreeRtosTadColumnId.ADDRESS, FreeRtosTadColumnId.DATA_BYTES, FreeRtosTadColumnId.DATA_HEX, FreeRtosTadColumnId.DATA_BIN,
				FreeRtosTadColumnId.DATA_TEXT }, separator);
	}

	public String getText(ITadColumnId column) {
		FreeRtosTadColumnId freeRtosColumn = (FreeRtosTadColumnId) column;
		switch (freeRtosColumn) {
			case ADDRESS:
				return String.format("0x%08x", this.address);
			case DATA_BYTES:
				return String.format("%d", this.data);
			case DATA_HEX:
				String hexStr = "0x%0" + 2 * this.size + "x";
				return String.format(hexStr, this.data);
			case DATA_BIN:
				String binStr = "%" + 8 * this.size + "s";
				return String.format(binStr, Long.toBinaryString(this.data)).replace(' ', '0');
			case DATA_TEXT:
				return Auxiliary.toASCII(this.data, this.size);
			default:
				return null;
		}
	}

	public Long getNumValue(ITadColumnId column) {
		FreeRtosTadColumnId freeRtosColumn = (FreeRtosTadColumnId) column;
		switch (freeRtosColumn) {
			case ADDRESS:
				return this.address;
			case DATA_BYTES:
			case DATA_HEX:
			case DATA_BIN:
			case DATA_TEXT:
				return this.data;
			default:
				return null;
		}
	}
}
