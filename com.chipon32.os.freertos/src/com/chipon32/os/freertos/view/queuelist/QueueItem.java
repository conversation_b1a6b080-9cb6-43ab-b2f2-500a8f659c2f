package com.chipon32.os.freertos.view.queuelist;

import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.widgets.Display;

import com.chipon32.os.common.messages.Separator;
import com.chipon32.os.common.model.view.queues.GenericQueueData;
import com.chipon32.os.common.model.view.queues.GenericQueueDataItem;
import com.chipon32.os.common.view.column.ITadColumnId;
import com.chipon32.os.common.view.items.TadItem;
import com.chipon32.os.freertos.model.view.queuelist.Queue;
import com.chipon32.os.freertos.model.view.queuelist.QueueType;
import com.chipon32.os.freertos.model.view.queuelist.QueueWaitingTask;
import com.chipon32.os.freertos.model.view.queuelist.QueueWaitingTaskOperation;
import com.chipon32.os.freertos.strings.Texts;
import com.chipon32.os.freertos.view.column.FreeRtosTadColumnId;
import com.chipon32.os.freertos.view.items.FreeRtosTadTreeChildItem;

public class QueueItem extends TadItem {
	private Queue queue;
	private List<TadItem> treeChildren;
	private List<GenericQueueDataItem> queueDataItems;
	private Map<QueueWaitingTaskOperation, QueueWaitingTask> queueWaitingTasks;

	public QueueItem(Queue queue, int index) {
		super(index);
		this.queue = queue;
		this.queueWaitingTasks = queue.getWaitingTasks();
		this.treeChildren = new LinkedList<>();
		if (queue.getHead() != 0L && queue.getTail() != 0L && queue.getReadFrom() != 0L && queue.getWriteTo() != 0L) {
			this.treeChildren.add(new FreeRtosTadTreeChildItem(this, 1, "Head", String.format("0x%08x", queue.getHead())));
			this.treeChildren.add(new FreeRtosTadTreeChildItem(this, 2, "Tail", String.format("0x%08x", queue.getTail())));
			this.treeChildren.add(new FreeRtosTadTreeChildItem(this, 3, "Read from", String.format("0x%08x", queue.getReadFrom())));
			this.treeChildren.add(new FreeRtosTadTreeChildItem(this, 4, "Write to", String.format("0x%08x", queue.getWriteTo())));
		}

		this.queueDataItems = new LinkedList<>();
		if (queue.getData() != null) {
			for (GenericQueueData item : queue.getData()) {
				if (item.hasData()) {
					this.queueDataItems.add(new QueueDataItem(item));
				}
			}
		}

	}

	public List<? extends TadItem> getTreeChildren() {
		return this.treeChildren;
	}

	public List<? extends TadItem> getChildItems() {
		return this.queueDataItems;
	}

	public Color getTextColour() {
		return this.queue.getCurrentLength() == this.queue.getMaxLength() && this.queue.getType() != QueueType.MUTEX ? Display.getDefault().getSystemColor(4)
				: super.getTextColour();
	}

	public String toString(Separator separator) {
		return super.toString(new FreeRtosTadColumnId[] { FreeRtosTadColumnId.ID, FreeRtosTadColumnId.NAME, FreeRtosTadColumnId.ADDRESS, FreeRtosTadColumnId.LENGTH,
				FreeRtosTadColumnId.SIZE, FreeRtosTadColumnId.TX_WAITING, FreeRtosTadColumnId.RX_WAITING, FreeRtosTadColumnId.TYPE }, separator);
	}

	public String getText(ITadColumnId column) {
		FreeRtosTadColumnId freeRtosColumn = (FreeRtosTadColumnId) column;
		switch (freeRtosColumn) {
			case ID:
				return String.format("%d", this.index);
			case NAME:
				return this.queue.getName() != null ? this.queue.getName() : Texts.get("Error.Unknown");
			case ADDRESS:
				return String.format("0x%08x", this.queue.getAddress());
			case ADDRESS_START:
			case ADDRESS_END:
			case ADDRESS_RANGE:
			case STATE:
			case NUMBER:
			case USAGE:
			case USAGE_GRAPH:
			case HIGHWATERMARK:
			case HIGHWATERMARK_GRAPH:
			case PRIORITY:
			case EVENT_OBJ:
			case RUNTIME:
			default:
				return null;
			case TYPE:
				return this.queue.getType() != null ? this.queue.getType().toString() : null;
			case SIZE:
				if (this.queue.getDataItemSize() == 0) {
					return Texts.get("Error.Empty");
				}

				return String.format("0x%x (%s)", this.queue.getDataItemSize(), formatBytes((long) this.queue.getDataItemSize()));
			case LENGTH:
				return String.format("%d/%d", this.queue.getCurrentLength(), this.queue.getMaxLength());
			case TX_WAITING:
				if (this.queueWaitingTasks.containsKey(QueueWaitingTaskOperation.TX)) {
					return String.format("%d", ((QueueWaitingTask) this.queueWaitingTasks.get(QueueWaitingTaskOperation.TX)).getCount());
				}

				return "0";
			case RX_WAITING:
				return this.queueWaitingTasks.containsKey(QueueWaitingTaskOperation.RX)
						? String.format("%d", ((QueueWaitingTask) this.queueWaitingTasks.get(QueueWaitingTaskOperation.RX)).getCount())
						: "0";
		}
	}

	public Long getNumValue(ITadColumnId column) {
		FreeRtosTadColumnId freeRtosColumn = (FreeRtosTadColumnId) column;
		switch (freeRtosColumn) {
			case ADDRESS -> {
				return this.queue.getAddress();
			}
			case SIZE -> {
				return (long) this.queue.getDataItemSize();
			}
			case LENGTH -> {
				return this.queue.getCurrentLength();
			}
			default -> {
				return null;
			}
		}
	}
}
