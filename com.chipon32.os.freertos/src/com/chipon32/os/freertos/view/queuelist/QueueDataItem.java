package com.chipon32.os.freertos.view.queuelist;

import com.chipon32.os.common.messages.Separator;
import com.chipon32.os.common.model.view.queues.GenericQueueData;
import com.chipon32.os.common.model.view.queues.GenericQueueDataItem;
import com.chipon32.os.common.model.view.queues.GenericQueueDataTreeChildItem;
import com.chipon32.os.common.model.view.queues.GenericQueueDataType;
import com.chipon32.os.common.view.column.ITadColumnId;
import com.chipon32.os.common.view.items.TadItem;
import com.chipon32.os.freertos.view.column.FreeRtosTadColumnId;

public class QueueDataItem extends GenericQueueDataItem {
	public QueueDataItem(GenericQueueData queueData) {
		super(queueData);
	}

	public GenericQueueDataTreeChildItem createChildItem(TadItem parent, long address, long data, int size) {
		return new QueueDataTreeChildItem(parent, address, data, size);
	}

	public String toString(Separator separator) {
		return super.toString(new FreeRtosTadColumnId[] { FreeRtosTadColumnId.ID, FreeRtosTadColumnId.ADDRESS, FreeRtosTadColumnId.DATA_BYTES, FreeRtosTadColumnId.DATA_HEX,
				FreeRtosTadColumnId.DATA_BIN, FreeRtosTadColumnId.DATA_TEXT }, separator);
	}

	public String getText(ITadColumnId column) {
		FreeRtosTadColumnId freeRtosColumn = (FreeRtosTadColumnId) column;
		switch (freeRtosColumn) {
			case ID:
				return String.format("%d", this.queueData.getId());
			case ADDRESS:
				return String.format("0x%08x", this.queueData.getAddress());
			case DATA_BYTES:
				return this.queueData.toString(GenericQueueDataType.DECIMAL);
			case DATA_HEX:
				return this.queueData.toString(GenericQueueDataType.HEXADECIMAL);
			case DATA_BIN:
				String binStrFormat = "%" + 8 * this.queueData.getSize() + "s";
				String binStr = this.queueData.toString(GenericQueueDataType.BINARY);
				return String.format(binStrFormat, binStr).replace(' ', '0');
			case DATA_TEXT:
				return this.queueData.toString(GenericQueueDataType.ASCII);
			default:
				return null;
		}
	}

	public Long getNumValue(ITadColumnId column) {
		FreeRtosTadColumnId freeRtosColumn = (FreeRtosTadColumnId) column;
		switch (freeRtosColumn) {
			case ID:
				return (long) this.queueData.getId();
			case NAME:
			default:
				return null;
			case ADDRESS:
				return this.queueData.getAddress();
		}
	}
}
