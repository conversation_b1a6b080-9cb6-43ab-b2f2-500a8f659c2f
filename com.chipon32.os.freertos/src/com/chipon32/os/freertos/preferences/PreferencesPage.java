package com.chipon32.os.freertos.preferences;

import org.eclipse.jface.preference.IntegerFieldEditor;
import org.eclipse.ui.IWorkbench;

import com.chipon32.os.common.preferences.TadPreferences;
import com.chipon32.os.freertos.Activator;
import com.chipon32.os.freertos.strings.Texts;

public class PreferencesPage extends TadPreferences {
	public PreferencesPage() {
		super(1);
	}

	public void init(IWorkbench arg0) {
		this.setPreferenceStore(Activator.getDefault().getPreferenceStore());
		this.setTitle(Texts.get("Pref.Title"));
	}

	protected void createFieldEditors() {
		this.addField(new IntegerFieldEditor("freeRtosTimeout", IPreferencesConstants.L_RTOS_OPERATION_TIMEOUT, this.getFieldEditorParent()));
		this.createLoggingFieldEditors();
	}

	protected String getPreferencesPrefix() {
		return "freeRTOS";
	}
}
