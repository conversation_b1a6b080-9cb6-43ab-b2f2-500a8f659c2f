package com.chipon32.os.freertos.preferences;

import org.eclipse.core.runtime.preferences.AbstractPreferenceInitializer;
import org.eclipse.jface.preference.IPreferenceStore;

import com.chipon32.os.freertos.Activator;

public class PreferencesInitializer extends AbstractPreferenceInitializer {
	public void initializeDefaultPreferences() {
		IPreferenceStore store = Activator.getDefault().getPreferenceStore();
		store.setDefault("freeRtosTimeout", 4000);
		store.setDefault("freeRTOSrtosLoggingEnablement", true);
		store.setDefault("freeRTOSrtosLoggingLocation", "rtosLoggingWorkspace");
		store.setDefault("freeRTOSrtosLoggingLocationCustomPath", "");
		store.setDefault("FreeRTOSConfigPath", "");
		store.setDefault("FreeRTOSMemoryScheme", "freeRTOSMemoryScheme");
	}
}
