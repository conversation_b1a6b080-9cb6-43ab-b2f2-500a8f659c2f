package com.chipon32.os.freertos.controller;

import java.util.LinkedList;
import java.util.List;

import com.chipon32.os.common.controller.TadFactoryData;
import com.chipon32.os.common.controller.TadFactoryDataStatus;
import com.chipon32.os.common.controller.TadViewController;
import com.chipon32.os.common.model.view.generic.TadObject;
import com.chipon32.os.common.view.TadView;
import com.chipon32.os.common.view.items.TadItem;
import com.chipon32.os.freertos.Activator;
import com.chipon32.os.freertos.model.view.heapusage.Heap;
import com.chipon32.os.freertos.model.view.queuelist.Queue;
import com.chipon32.os.freertos.model.view.tasklist.Task;
import com.chipon32.os.freertos.model.view.tasknotifications.TaskNotifications;
import com.chipon32.os.freertos.model.view.timerlist.Timer;
import com.chipon32.os.freertos.view.heapusage.HeapItem;
import com.chipon32.os.freertos.view.queuelist.QueueItem;
import com.chipon32.os.freertos.view.tasklist.TaskItem;
import com.chipon32.os.freertos.view.tasknotifications.TaskNotificationsItem;
import com.chipon32.os.freertos.view.timerlist.TimerItem;

public class FreeRtosTadViewController extends TadViewController {
	public FreeRtosTadViewController(TadView view) {
		super(Activator.getDefault().getTadModel(), view);
	}

	public void dataReady(TadFactoryData data) {
		if (view != null && data != null && data.isValid() && data.getStatus() == TadFactoryDataStatus.SUCCESS) {
			if (previousData == data) {
				return;
			}

			previousData = data;
			List<TadItem> items = new LinkedList<>();
			int index = 1;

			for (TadObject obj : data.getTadObjects()) {
				switch (view.getTitle()) {
					case "Task Notifications":
						items.add(new TaskNotificationsItem((TaskNotifications) obj, index));
						break;
					case "Timer List":
						items.add(new TimerItem((Timer) obj, index));
						break;
					case "Queue List":
						items.add(new QueueItem((Queue) obj, index));
						break;
					case "Heap Usage":
						items.add(new HeapItem((Heap) obj));
						break;
					case "Task List":
						items.add(new TaskItem((Task) obj, index));
				}

				++index;
			}

			boolean shouldBeEnabled = !items.isEmpty();
			view.refreshData(items, shouldBeEnabled);
			view.showTadView();
			view.enableAllViewers(shouldBeEnabled);
		}

	}
}
