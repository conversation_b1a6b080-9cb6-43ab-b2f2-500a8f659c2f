package com.chipon32.os.freertos.controller;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import com.chipon32.os.common.ReadingException;
import com.chipon32.os.common.controller.TadFactory;
import com.chipon32.os.common.controller.TadFactoryData;
import com.chipon32.os.common.controller.TadFactoryDataStatus;
import com.chipon32.os.common.logger.Logger;
import com.chipon32.os.common.model.TadModel;
import com.chipon32.os.common.model.readers.MemoryReader;
import com.chipon32.os.common.model.readers.VariableReader;
import com.chipon32.os.common.model.view.generic.TadDataCache;
import com.chipon32.os.freertos.Activator;
import com.chipon32.os.freertos.model.freertos.FreeRTOS;
import com.chipon32.os.freertos.model.freertos.FreeRTOSConfig;
import com.chipon32.os.freertos.model.view.tasklist.LightweightTask;
import com.chipon32.os.freertos.model.view.tasklist.TaskState;
import com.chipon32.os.freertos.model.view.tasknotifications.TaskNotifications;
import com.chipon32.os.freertos.model.view.tasknotifications.TaskNotificationsData;
import com.chipon32.os.freertos.strings.Texts;

public class TaskNotificationsFactory extends TaskFactory {
	public TaskNotificationsFactory(TadModel tadModel, MemoryReader memoryReader, VariableReader variableReader, FreeRTOS freeRTOS, TadDataCache dataCache) {
		super(tadModel, memoryReader, variableReader, freeRTOS, dataCache);
	}

	public TadFactoryData getData() {
		List<TaskNotifications> items = new ArrayList<>();
		Logger logger = Activator.getDefault().getLogger();
		long listSize = 0L;
		long listStructSize = 0L;
		long numberOfReadyTasks = 0L;

		try {
			listSize = this.getSize("pxReadyTasksLists");
			listStructSize = this.getStructSize(this.freeRTOS.getStruct("list_item"));
			numberOfReadyTasks = listSize > 0L && listStructSize > 0L ? listSize / listStructSize : 0L;
		} catch (ReadingException e) {
			logger.error(String.format(Texts.get("Error.CouldNotGetSize"), e.getArgs()));
			logger.error(Texts.get("Error.CannotDisplayReadyTaskList"));
		}

		for (int i = 0; (long) i < numberOfReadyTasks; ++i) {
			String readFrom = "pxReadyTasksLists[" + i + "]";

			try {
				items.addAll(this.readTasksFromList(readFrom, TaskState.READY));
			} catch (ReadingException var17) {
				logger.error(String.format(Texts.get("Error.CannotReadFrom"), readFrom));
			}
		}

		for (Map.Entry<TaskState, String[]> listItem : this.taskLists.entrySet()) {
			String[] var14;
			for (String listName : var14 = (String[]) listItem.getValue()) {
				try {
					items.addAll(this.readTasksFromList(listName, (TaskState) listItem.getKey()));
				} catch (ReadingException var16) {
					logger.error(String.format(Texts.get("Error.CannotReadFrom"), listName));
				}
			}
		}

		Collections.sort(items, new Comparator<TaskNotifications>() {
			public int compare(TaskNotifications task1, TaskNotifications task2) {
				if (task1.getTCBNumber() != null && task2.getTCBNumber() != null) {
					if (task1.getTCBNumber() < task2.getTCBNumber()) {
						return -1;
					}

					if (task1.getTCBNumber() > task2.getTCBNumber()) {
						return 1;
					}
				}

				return 0;
			}
		});
		return new TadFactoryData(this.getTadModel(), items, TadFactoryDataStatus.SUCCESS, (String) null);
	}

	private synchronized List<TaskNotifications> readTasksFromList(String listName, TaskState state) throws ReadingException {
		List<TaskNotifications> taskNotifications = new ArrayList<>();
		if (!this.freeRTOS.isMacroEnabled(FreeRTOSConfig.USE_TASK_NOTIFICATIONS)) {
			return taskNotifications;
		} else {
			int sizeOfVals = 0;
			int sizeOfStates = 0;
			int sizeOfValItem = 0;
			int sizeOfStateItem = 0;
			String structTCB = this.freeRTOS.getStruct("task_control_block");
			String structNotifVal = "(((struct " + structTCB + " *) 0x0)->ulNotifiedValue)";
			String structNotifState = "(((struct " + structTCB + " *) 0x0)->ucNotifyState)";

			try {
				String sizeOfValsStr = this.variableReader.getSize(structNotifVal);
				String sizeOfStatesStr = this.variableReader.getSize(structNotifState);
				if (!TadFactory.isTextValid(sizeOfValsStr) || !TadFactory.isTextValid(sizeOfStatesStr)) {
					throw new ReadingException();
				}

				sizeOfVals = Integer.decode(sizeOfValsStr);
				sizeOfStates = Integer.decode(sizeOfStatesStr);
				if (this.freeRTOS.getVersion() < 10.4) {
					sizeOfValItem = sizeOfVals;
					sizeOfStateItem = sizeOfStates;
				} else {
					String sizeOfValStr = this.variableReader.getSize(structNotifVal + "[0]");
					String sizeOfStateStr = this.variableReader.getSize(structNotifState + "[0]");
					if (!TadFactory.isTextValid(sizeOfValStr) || !TadFactory.isTextValid(sizeOfStateStr)) {
						throw new ReadingException();
					}

					sizeOfValItem = Integer.decode(sizeOfValStr);
					sizeOfStateItem = Integer.decode(sizeOfStateStr);
				}
			} catch (NumberFormatException | ReadingException var33) {
				this.freeRTOS.enableMacro(FreeRTOSConfig.USE_TASK_NOTIFICATIONS, false);
			} finally {
				if (sizeOfValItem <= 0) {
					sizeOfValItem = 1;
				}

				if (sizeOfStateItem <= 0) {
					sizeOfStateItem = 1;
				}

			}

			for (LightweightTask task : this.readTasksFromList(listName, state, true)) {
				List<TaskNotificationsData> notificationsData = new LinkedList<>();
				List<Long> notificationValues = null;
				List<Long> notificationStates = null;

				long startAddrOfValues;
				try {
					startAddrOfValues = this.readAddressAtAddress(task.getAddress(), structTCB, "ulNotifiedValue");
					long startAddrOfStates = this.readAddressAtAddress(task.getAddress(), structTCB, "ucNotifyState");
					notificationValues = this.readMemoryBlock(startAddrOfValues, (long) sizeOfVals, sizeOfValItem);
					notificationStates = this.readMemoryBlock(startAddrOfStates, (long) (sizeOfStates / sizeOfStateItem), sizeOfStateItem);
				} catch (ReadingException e) {
					Activator.getDefault().getLogger().exception(e, e.getSummary());
					continue;
				}

				if (notificationValues != null && notificationStates != null) {
					long numWaiting = 0L;
					long numReceived = 0L;
					List<Integer> waitingFor = new LinkedList<>();

					for (int stateIndx = 0; stateIndx < notificationStates.size(); ++stateIndx) {
						if ((Long) notificationStates.get(stateIndx) == 1L) {
							++numWaiting;
							waitingFor.add(stateIndx);
						} else if ((Long) notificationStates.get(stateIndx) == 2L) {
							++numReceived;
						}
					}

					long currentAddr = startAddrOfValues;

					for (int i = 0; i < notificationValues.size(); currentAddr += 4L) {
						notificationsData.add(new TaskNotificationsData(i, ((Long) notificationStates.get(i)).intValue(), currentAddr, sizeOfValItem,
								Arrays.asList((Long) notificationValues.get(i))));
						++i;
					}

					TaskNotifications taskNotificationsItem = new TaskNotifications(task.getName(), task.getAddress(), task.getTCBNumber(), numReceived, numWaiting, waitingFor);
					taskNotificationsItem.setNotificationsData(notificationsData);
					taskNotifications.add(taskNotificationsItem);
				} else {
					Activator.getDefault().getLogger().error(String.format(Texts.get("TaskNotifications.ErrorReadingNotificationsData"), task.getName()));
				}
			}

			return taskNotifications;
		}
	}
}
