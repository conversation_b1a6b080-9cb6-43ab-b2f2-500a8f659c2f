package com.chipon32.os.freertos.controller;

import java.util.LinkedList;
import java.util.List;

import com.chipon32.os.common.ReadingException;
import com.chipon32.os.common.controller.TadFactoryData;
import com.chipon32.os.common.controller.TadFactoryDataStatus;
import com.chipon32.os.common.messages.Messages;
import com.chipon32.os.common.model.TadModel;
import com.chipon32.os.common.model.readers.MemoryReader;
import com.chipon32.os.common.model.readers.VariableReader;
import com.chipon32.os.common.model.view.queues.GenericQueueData;
import com.chipon32.os.freertos.Activator;
import com.chipon32.os.freertos.model.freertos.FreeRTOS;
import com.chipon32.os.freertos.model.freertos.FreeRTOSConfig;
import com.chipon32.os.freertos.model.view.queuelist.Queue;
import com.chipon32.os.freertos.model.view.queuelist.QueueType;
import com.chipon32.os.freertos.model.view.queuelist.QueueWaitingTask;
import com.chipon32.os.freertos.model.view.queuelist.QueueWaitingTaskOperation;
import com.chipon32.os.freertos.strings.Texts;

public class QueueFactory extends FreeRtosTadFactory {
	public QueueFactory(TadModel tadModel, MemoryReader memoryReader, VariableReader variableReader, FreeRTOS freeRTOS) {
		super(tadModel, memoryReader, variableReader, freeRTOS);
	}

	public synchronized TadFactoryData getData() {
		LinkedList<Queue> list = new LinkedList<>();
		if (this.freeRTOS.getQueueRegistrySize() == null) {
			try {
				this.freeRTOS.setQueueRegistrySize(this.getSize("xQueueRegistry"));
			} catch (ReadingException var11) {
				Activator.getDefault().getLogger().error(Texts.get("Warning.Dependency.Queue"));
				this.freeRTOS.setQueueRegistrySize(0L);
			}
		}

		if (this.freeRTOS.getQueueRegistrySize() != 0L && this.readAddressOfVariableSafely("xQueueRegistry") >= 1L) {
			try {
				long queueRegistryItemSize = this.getStructSize(this.freeRTOS.getStruct("queue_registr_item"));
				if (queueRegistryItemSize > 0L) {
					long numberOfQueues = this.freeRTOS.getQueueRegistrySize() / queueRegistryItemSize;

					for (int i = 0; (long) i < numberOfQueues; ++i) {
						String readFrom = "xQueueRegistry[" + i + "]";
						long queueAddress = this.readAddressOfVariableSafely(readFrom);
						if (queueAddress > 0L) {
							Queue queue = this.getQueue(queueAddress);
							if (queue != null) {
								list.add(queue);
							}
						} else {
							Activator.getDefault().getLogger().error(String.format(Texts.get("Error.CannotReadFrom"), readFrom));
						}
					}
				}
			} catch (ReadingException e) {
				Activator.getDefault().getLogger().exception(e, e.getSummary());
				return new TadFactoryData(this.getTadModel(), list, TadFactoryDataStatus.ERROR, String.format(Messages.Error_CouldNotGetFactoryData, "Queue List"));
			}

			return new TadFactoryData(this.getTadModel(), list, TadFactoryDataStatus.SUCCESS, (String) null);
		} else {
			return new TadFactoryData(this.getTadModel(), list, TadFactoryDataStatus.WARNING, Texts.get("Warning.Dependency.Queue"));
		}
	}

	private synchronized Queue getQueue(long queueAddress) throws ReadingException {
		long queueHandle;
		long pcQueueName;
		try {
			queueHandle = this.readLongAtAddress(queueAddress, this.freeRTOS.getStruct("queue_registr_item"), "xHandle");
			pcQueueName = this.readLongAtAddress(queueAddress, this.freeRTOS.getStruct("queue_registr_item"), "pcQueueName");
		} catch (ReadingException var32) {
			return null;
		}

		if (queueHandle != 0L && pcQueueName != 0L) {
			String queueName = this.readTextAtAddress(pcQueueName);
			long maxLength = this.readLongAtAddressSafely(queueHandle, this.freeRTOS.getStruct("queue"), "uxLength");
			long currentLength = this.readLongAtAddressSafely(queueHandle, this.freeRTOS.getStruct("queue"), "uxMessagesWaiting");
			int itemSize = this.readIntAtAddressSafely(queueHandle, this.freeRTOS.getStruct("queue"), "uxItemSize");
			long queueHead = this.readLongAtAddressSafely(queueHandle, this.freeRTOS.getStruct("queue"), "pcHead");
			long writeTo = this.readLongAtAddressSafely(queueHandle, this.freeRTOS.getStruct("queue"), "pcWriteTo");
			long queueTail = this.readLongAtAddressSafely(queueHandle, this.freeRTOS.getStruct("queue"), this.freeRTOS.getVersion() < 10.1 ? "pcTail" : "u.xQueue.pcTail");
			long readFrom = this.readLongAtAddressSafely(queueHandle, this.freeRTOS.getStruct("queue"), this.freeRTOS.getVersion() < 10.1 ? "u.pcReadFrom" : "u.xQueue.pcReadFrom");
			Queue queue = new Queue(queueHandle, queueName, maxLength, currentLength, itemSize, queueHead, queueTail, readFrom, writeTo);
			if (this.freeRTOS.isMacroEnabled(FreeRTOSConfig.USE_TRACE)) {
				try {
					int queueTypeNum = this.readIntAtAddress(queueHandle, this.freeRTOS.getStruct("queue"), "ucQueueType");
					queue.setType(this.determineQueueType(queue, queueTypeNum));
				} catch (ReadingException var31) {
					this.freeRTOS.enableMacro(FreeRTOSConfig.USE_TRACE, false);
				}
			}

			QueueWaitingTaskOperation[] var26;
			for (QueueWaitingTaskOperation operation : var26 = QueueWaitingTaskOperation.values()) {
				int waitingCount = this.readIntAtAddressSafely(queueHandle, this.freeRTOS.getStruct("queue"), operation.getValue() + ".uxNumberOfItems");
				if (waitingCount > 0) {
					queue.addWaitingTask(operation,
							new QueueWaitingTask(this.readAddressAtAddress(queueHandle, this.freeRTOS.getStruct("queue"), operation.getValue()),
									this.readTaskName(this.readLongAtAddress(queueHandle, this.freeRTOS.getStruct("queue"), operation.getValue() + ".xListEnd.pxNext.pvOwner")),
									waitingCount));
				}
			}

			if (queue.getDataSize() > 0 && queue.getHead() > 0L) {
				int messageBlockSize = Math.min(queue.getDataItemSize(), 4);
				List<GenericQueueData> data = new LinkedList<>();

				for (int i = 0; (long) i < queue.getCurrentLength(); ++i) {
					long itemStartOffset = (long) (i * queue.getDataItemSize());
					long itemStartAddr = queue.getHead() + itemStartOffset;
					List<Long> blocks = this.readMemoryBlock(itemStartAddr, (long) queue.getDataItemSize(), messageBlockSize);
					if (blocks != null && !blocks.isEmpty()) {
						data.add(new GenericQueueData(i + 1, itemStartAddr, messageBlockSize, blocks));
					}
				}

				queue.setData(data);
			}

			return queue;
		} else {
			return null;
		}
	}

	private QueueType determineQueueType(Queue queue, int type) {
		switch (type) {
			case 1:
				return QueueType.MUTEX;
			case 2:
				return QueueType.COUNTING_SEMAPHORE;
			case 3:
				return QueueType.BINARY_SEMAPHORE;
			case 4:
				return QueueType.RECURSIVE_MUTEX;
			default:
				if (queue.getDataItemSize() == 0) {
					return queue.getHead() == 0L ? QueueType.MUTEX : QueueType.BINARY_SEMAPHORE;
				} else {
					return QueueType.QUEUE;
				}
		}
	}
}
