package com.chipon32.os.freertos.controller;

import com.chipon32.os.common.ReadingException;
import com.chipon32.os.common.controller.TadFactory;
import com.chipon32.os.common.model.TadModel;
import com.chipon32.os.common.model.readers.MemoryReader;
import com.chipon32.os.common.model.readers.VariableReader;
import com.chipon32.os.freertos.Activator;
import com.chipon32.os.freertos.model.freertos.FreeRTOS;
import com.chipon32.os.freertos.strings.Texts;

public abstract class FreeRtosTadFactory extends TadFactory {
	protected FreeRTOS freeRTOS;

	public FreeRtosTadFactory(TadModel tadModel, MemoryReader memoryReader, VariableReader variableReader, FreeRTOS freeRTOS) {
		super(tadModel, memoryReader, variableReader);
		this.freeRTOS = freeRTOS;
	}

	public synchronized String readTaskName(long address) throws ReadingException {
		if (address > 0L) {
			long nameOffset = this.getOffset(this.freeRTOS.getStruct("task_control_block"), "pcTaskName");
			return this.readTextAtAddress(address + nameOffset);
		} else {
			return Texts.get("Error.Unknown");
		}
	}

	public TadModel getTadModel() {
		return Activator.getDefault().getTadModel();
	}
}
