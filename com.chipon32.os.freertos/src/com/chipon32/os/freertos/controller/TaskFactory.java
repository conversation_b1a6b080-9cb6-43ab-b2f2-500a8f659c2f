package com.chipon32.os.freertos.controller;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.chipon32.os.common.ReadingException;
import com.chipon32.os.common.controller.TadFactoryData;
import com.chipon32.os.common.controller.TadFactoryDataStatus;
import com.chipon32.os.common.logger.Logger;
import com.chipon32.os.common.messages.Messages;
import com.chipon32.os.common.model.TadModel;
import com.chipon32.os.common.model.readers.MemoryReader;
import com.chipon32.os.common.model.readers.VariableReader;
import com.chipon32.os.common.model.view.generic.TadDataCache;
import com.chipon32.os.common.model.view.generic.TadObject;
import com.chipon32.os.common.model.view.tasks.GenericTaskStack;
import com.chipon32.os.freertos.Activator;
import com.chipon32.os.freertos.model.freertos.FreeRTOS;
import com.chipon32.os.freertos.model.freertos.FreeRTOSConfig;
import com.chipon32.os.freertos.model.view.queuelist.Queue;
import com.chipon32.os.freertos.model.view.queuelist.QueueWaitingTask;
import com.chipon32.os.freertos.model.view.queuelist.QueueWaitingTaskOperation;
import com.chipon32.os.freertos.model.view.tasklist.LightweightTask;
import com.chipon32.os.freertos.model.view.tasklist.Task;
import com.chipon32.os.freertos.model.view.tasklist.TaskEventObject;
import com.chipon32.os.freertos.model.view.tasklist.TaskPriority;
import com.chipon32.os.freertos.model.view.tasklist.TaskState;
import com.chipon32.os.freertos.strings.Texts;

public class TaskFactory extends FreeRtosTadFactory {
	private static final int STACK_FILL_BYTE = 165;
	private static final int TASK_NOT_RUNNING = -1;
	protected Map<TaskState, String[]> taskLists;
	private List<? extends TadObject> queues;
	private final TadDataCache dataCache;
	private long[] runningTaskPtrs;

	public TaskFactory(TadModel tadModel, MemoryReader memoryReader, VariableReader variableReader, FreeRTOS freeRTOS, TadDataCache dataCache) {
		super(tadModel, memoryReader, variableReader, freeRTOS);
		this.dataCache = dataCache;
		this.taskLists = new HashMap<>();
		this.taskLists.put(TaskState.BLOCKED, new String[] { "pxDelayedTaskList", "pxOverflowDelayedTaskList" });
		this.taskLists.put(TaskState.READY, new String[] { "xPendingReadyList" });
		this.taskLists.put(TaskState.SUSPENDED, new String[] { "xSuspendedTaskList" });
	}

	public synchronized TadFactoryData getData() {
		Logger logger = Activator.getDefault().getLogger();
		List<Task> list = new LinkedList<>();
		long listSize;
		long listStructSize;
		long numberOfReadyTasks = 0L;

		try {
			listSize = this.getSize("pxReadyTasksLists");
			listStructSize = this.getStructSize(this.freeRTOS.getStruct("list_item"));
			numberOfReadyTasks = listSize > 0L && listStructSize > 0L ? listSize / listStructSize : 0L;
		} catch (ReadingException e) {
			logger.error(String.format(Texts.get("Error.CouldNotGetSize"), e.getArgs()));
			logger.error(Texts.get("Error.CannotDisplayReadyTaskList"));
		}

		this.runningTaskPtrs = new long[this.freeRTOS.getSMPNumberOfCores()];
		if (this.freeRTOS.getSMPNumberOfCores() == 1) {
			this.runningTaskPtrs[0] = this.readLongVariableSafely("pxCurrentTCB");
		} else {
			for (int i = 0; i < this.freeRTOS.getSMPNumberOfCores(); ++i) {
				String currentTCB = String.format("%s[%d]", "pxCurrentTCBs", i);

				try {
					this.runningTaskPtrs[i] = this.readLongVariable(currentTCB);
				} catch (ReadingException var18) {
					logger.error(String.format(Texts.get("Error.CannotReadFrom"), currentTCB));
				}
			}
		}

		if (this.freeRTOS.isMacroEnabled(FreeRTOSConfig.RUNTIME_STATS)) {
			try {
				long[] totalRuntime = new long[this.freeRTOS.getSMPNumberOfCores()];
				if (this.freeRTOS.getVersion() < (double) 11.0F) {
					totalRuntime[0] = this.readLongVariable("ulTotalRunTime");
				} else {
					for (int i = 0; i < this.freeRTOS.getSMPNumberOfCores(); ++i) {
						totalRuntime[i] = this.readLongVariable("ulTotalRunTime[" + i + "]");
					}
				}

				this.freeRTOS.setRuntime(totalRuntime);
			} catch (ReadingException var19) {
				logger.error(Texts.get("Tasks.TotalRunTimeNotAvailable"));
			}
		}

		this.queues = this.dataCache.getViewTadObjects("Queue List");

		for (int i = 0; (long) i < numberOfReadyTasks; ++i) {
			String readFrom = "pxReadyTasksLists[" + i + "]";

			try {
				list.addAll(this.readTasksFromList(readFrom, TaskState.READY));
			} catch (ReadingException var17) {
				logger.error(String.format(Texts.get("Error.CannotReadFrom"), readFrom));
			}
		}

		for (Map.Entry<TaskState, String[]> listItem : this.taskLists.entrySet()) {
			for (String listName : listItem.getValue()) {
				try {
					list.addAll(this.readTasksFromList(listName, listItem.getKey()));
				} catch (ReadingException var16) {
					logger.error(String.format(Texts.get("Error.CannotReadFrom"), listName));
				}
			}
		}

		if (!this.freeRTOS.isMacroEnabled(FreeRTOSConfig.RECORD_STACK_HIGH_ADDRESS)) {
			logger.error(Texts.get("Info.CouldNotReadEndOfStack"));
		}

		list.sort((task1, task2) -> {
			if (task1.getTCBNumber() != null && task2.getTCBNumber() != null) {
				if (task1.getTCBNumber() < task2.getTCBNumber()) {
					return -1;
				}

				if (task1.getTCBNumber() > task2.getTCBNumber()) {
					return 1;
				}
			}

			return 0;
		});
		return new TadFactoryData(this.getTadModel(), list, TadFactoryDataStatus.SUCCESS, null);
	}

	private synchronized List<Task> readTasksFromList(String listName, TaskState state) throws ReadingException {
		List<LightweightTask> tasks = this.readTasksFromList(listName, state, false);
		return tasks.stream().filter((t) -> t instanceof Task).map((t) -> (Task) t).collect(Collectors.toList());
	}

	protected synchronized List<LightweightTask> readTasksFromList(String listName, TaskState state, boolean retrieveLightweightTask) throws ReadingException {
		ArrayList<LightweightTask> list = new ArrayList<>();

		try {
			long listAddress = this.readLongVariable(listName);
			if (listAddress <= 0L) {
				return list;
			}
		} catch (ReadingException var30) {
		}

		int numberOfItems = this.readIntVariable(listName + ".uxNumberOfItems");
		long listAddress = 0L;
		if (numberOfItems > 0) {
			listAddress = this.readLongVariable(listName + ".xListEnd.pxNext");
		}

		boolean isSchedulerRunning = this.readLongVariableSafely("xSchedulerRunning") == 1L;

		for (int i = 0; i < numberOfItems && listAddress > 0L; ++i) {
			long taskPtr = this.readLongAtAddress(listAddress, this.freeRTOS.getStruct("list_item"), "pvOwner");
			if (taskPtr == 0L) {
				break;
			}

			String name = this.readTaskName(taskPtr);
			LightweightTask lightweightTask = retrieveLightweightTask ? new LightweightTask(name, taskPtr, this.freeRTOS) : null;
			Task task = retrieveLightweightTask ? null : new Task(name, taskPtr, this.freeRTOS);
			if (!retrieveLightweightTask) {
				int basePriority = 0;
				if (this.freeRTOS.isMacroEnabled(FreeRTOSConfig.USE_MUTEXES)) {
					try {
						basePriority = this.readIntAtAddress(taskPtr, this.freeRTOS.getStruct("task_control_block"), "uxBasePriority");
					} catch (ReadingException var29) {
						this.freeRTOS.enableMacro(FreeRTOSConfig.USE_MUTEXES, false);
					}
				}

				task.setPriority(new TaskPriority(this.readIntAtAddress(taskPtr, this.freeRTOS.getStruct("task_control_block"), "uxPriority"), basePriority));
				long stackBaseAddress = this.readLongAtAddress(taskPtr, this.freeRTOS.getStruct("task_control_block"), "pxStack");
				long stackEndAddress = this.readLongAtAddress(taskPtr, this.freeRTOS.getStruct("task_control_block"), "pxTopOfStack");
				if (this.freeRTOS.isMacroEnabled(FreeRTOSConfig.RECORD_STACK_HIGH_ADDRESS)) {
					try {
						stackEndAddress = this.readLongAtAddress(taskPtr, this.freeRTOS.getStruct("task_control_block"), "pxEndOfStack");
					} catch (ReadingException var28) {
						this.freeRTOS.enableMacro(FreeRTOSConfig.RECORD_STACK_HIGH_ADDRESS, false);
					}
				}

				List<Integer> stackMemoryBlock = this.readMemoryBlock(stackBaseAddress, stackEndAddress - stackBaseAddress);
				if (stackMemoryBlock != null && !stackMemoryBlock.isEmpty()) {
					task.setStack(new GenericTaskStack(stackBaseAddress, stackEndAddress, stackBaseAddress + GenericTaskStack.getStackHighWaterMarkBytes(stackMemoryBlock, 165),
							GenericTaskStack.isStackOverflown(stackMemoryBlock, 165)));
				} else {
					Activator.getDefault().getLogger().error(Messages.Error_TaskStackDefault);
					task.setStack(new GenericTaskStack(stackBaseAddress, stackEndAddress, -1L, false));
				}

				if (this.freeRTOS.isMacroEnabled(FreeRTOSConfig.USE_CORE_AFFINITY)) {
					try {
						task.setCoreAffinityMask(this.readLongAtAddress(taskPtr, this.freeRTOS.getStruct("task_control_block"), "uxCoreAffinityMask"));
					} catch (ReadingException var27) {
						this.freeRTOS.enableMacro(FreeRTOSConfig.USE_CORE_AFFINITY, false);
					}
				}
			} else {
				this.readIntAtAddress(taskPtr, this.freeRTOS.getStruct("task_control_block"), "uxPriority");
			}

			if (this.freeRTOS.isMacroEnabled(FreeRTOSConfig.USE_TRACE)) {
				try {
					LightweightTask taskRef = retrieveLightweightTask ? lightweightTask : task;
					taskRef.setNumber(this.readLongAtAddress(taskPtr, this.freeRTOS.getStruct("task_control_block"), "uxTaskNumber"),
							this.readLongAtAddress(taskPtr, this.freeRTOS.getStruct("task_control_block"), "uxTCBNumber"));
				} catch (ReadingException var26) {
					this.freeRTOS.enableMacro(FreeRTOSConfig.USE_TRACE, false);
				}
			}

			if (!retrieveLightweightTask) {
				long eventObjPtr = 0L;
				String fieldName = this.freeRTOS.getVersion() < 10.1 ? "pvContainer" : "pxContainer";

				try {
					eventObjPtr = this.readLongAtAddress(taskPtr, this.freeRTOS.getStruct("task_control_block"), "xEventListItem." + fieldName);
				} catch (ReadingException var31) {
					if (this.freeRTOS.getVersion() >= 10.1) {
						try {
							eventObjPtr = this.readLongAtAddress(taskPtr, this.freeRTOS.getStruct("task_control_block"), "xEventListItem.pvContainer");
						} catch (ReadingException var25) {
						}
					}
				}

				if (eventObjPtr != 0L) {
					TaskEventObject eventObject = new TaskEventObject();
					eventObject.setAddress(eventObjPtr);
					if (this.queues != null && !this.queues.isEmpty()) {
						for (TadObject obj : this.queues) {
							if (obj instanceof Queue queue) {

								for (Map.Entry<QueueWaitingTaskOperation, QueueWaitingTask> waitingTask : queue.getWaitingTasks().entrySet()) {
									if (waitingTask.getValue().getAddress() == eventObjPtr) {
										eventObject.setName(queue.getName());
										eventObject.setOperation(waitingTask.getKey());
										break;
									}
								}
							}
						}
					}

					task.setEventObject(eventObject);
				}

				if (this.freeRTOS.isMacroEnabled(FreeRTOSConfig.RUNTIME_STATS)) {
					try {
						task.setTaskRuntime(this.readLongAtAddress(taskPtr, this.freeRTOS.getStruct("task_control_block"), "ulRunTimeCounter"));
					} catch (ReadingException var24) {
						this.freeRTOS.enableMacro(FreeRTOSConfig.RUNTIME_STATS, false);
					}
				}

				long taskRunState = -1L;
				if (this.freeRTOS.getSMPNumberOfCores() > 1) {
					taskRunState = this.readLongAtAddress(taskPtr, this.freeRTOS.getStruct("task_control_block"), "xTaskRunState");
				}

				task.setState(isSchedulerRunning && this.isTaskRunning(taskPtr, taskRunState, this.runningTaskPtrs) ? TaskState.RUNNING : state);
			}

			list.add(retrieveLightweightTask ? lightweightTask : task);
			listAddress = this.readLongAtAddress(listAddress, this.freeRTOS.getStruct("list_item"), "pxNext");
		}

		return list;
	}

	private boolean isTaskRunning(long taskPtr, long taskRunState, long[] runningTaskPtrs) {
		if (this.freeRTOS.getSMPNumberOfCores() == 1) {
			return taskPtr == runningTaskPtrs[0];
		} else {
			return taskRunState >= 0L && taskRunState < (long) this.freeRTOS.getSMPNumberOfCores();
		}
	}
}
