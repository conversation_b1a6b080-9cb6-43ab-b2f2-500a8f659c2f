package com.chipon32.os.freertos.controller;

import java.util.Collections;
import java.util.Comparator;
import java.util.LinkedList;
import java.util.List;

import com.chipon32.os.common.ReadingException;
import com.chipon32.os.common.controller.TadFactoryData;
import com.chipon32.os.common.controller.TadFactoryDataStatus;
import com.chipon32.os.common.logger.Logger;
import com.chipon32.os.common.model.TadModel;
import com.chipon32.os.common.model.readers.MemoryReader;
import com.chipon32.os.common.model.readers.VariableReader;
import com.chipon32.os.common.model.view.generic.TadDataCache;
import com.chipon32.os.common.model.view.generic.TadObject;
import com.chipon32.os.freertos.Activator;
import com.chipon32.os.freertos.model.freertos.FreeRTOS;
import com.chipon32.os.freertos.model.freertos.FreeRTOSConfig;
import com.chipon32.os.freertos.model.view.heapusage.Heap;
import com.chipon32.os.freertos.model.view.heapusage.HeapBlock;
import com.chipon32.os.freertos.model.view.heapusage.HeapBlockStatus;
import com.chipon32.os.freertos.model.view.heapusage.HeapRegion;
import com.chipon32.os.freertos.model.view.heapusage.HeapType;
import com.chipon32.os.freertos.model.view.queuelist.Queue;
import com.chipon32.os.freertos.model.view.tasklist.Task;
import com.chipon32.os.freertos.strings.Texts;

public class HeapFactory extends FreeRtosTadFactory {
	private final TadDataCache dataCache;
	private long ucHeapAddress;

	public HeapFactory(TadModel tadModel, MemoryReader memoryReader, VariableReader variableReader, FreeRTOS freeRTOS, TadDataCache dataCache) {
		super(tadModel, memoryReader, variableReader, freeRTOS);
		this.dataCache = dataCache;
	}

	private synchronized HeapType identifyHeapType() {
		long heapStructSize = this.readIntVariableSafely("xHeapStructSize");
		if (this.ucHeapAddress != -1L) {
			Logger logger = Activator.getDefault().getLogger();
			logger.info(String.format(Texts.get("Info.HeapVariable"), "ucHeap", this.ucHeapAddress));
			if (heapStructSize != -1L) {
				logger.info(String.format(Texts.get("Info.HeapVariable"), "xHeapStructSize", heapStructSize));
				return HeapType.HEAP_4;
			} else {
				try {
					this.readIntVariable("heapSTRUCT_SIZE");
					return HeapType.HEAP_2;
				} catch (ReadingException var5) {
					return HeapType.HEAP_1;
				}
			}
		} else if (heapStructSize != -1L) {
			try {
				this.getSize("xHeapRegions");
				return HeapType.HEAP_5;
			} catch (ReadingException var6) {
				return HeapType.HEAP_4;
			}
		} else if (this.readAddressOfVariableSafely("_pvHeapStart") != -1L) {
			return HeapType.HEAP_NEWLIB;
		} else {
			return this.freeRTOS.isMacroEnabled(FreeRTOSConfig.STATIC_AND_DYNAMIC_ALLOCATION) ? HeapType.STATIC_MEMORY : HeapType.HEAP_3;
		}
	}

	public synchronized TadFactoryData getData() {
		List<Heap> list = new LinkedList<>();

		try {
			HeapType heapType = this.freeRTOS.getHeapType();
			if (heapType == HeapType.HEAP_3) {
				TadModel var47 = this.getTadModel();
				TadFactoryDataStatus var48 = TadFactoryDataStatus.INFORMATION;
				String var49 = Texts.get("Info.HeapNoInfo");
				return new TadFactoryData(var47, list, var48, var49 + "\n\nNOTE: " + Texts.get("Info.HeapTypeIdentifyHelp"));
			}

			this.ucHeapAddress = this.readAddressOfVariableSafely("ucHeap");
			if (heapType == HeapType.UNKNOWN) {
				Logger logger = Activator.getDefault().getLogger();
				int typeNum = this.readIntVariableSafely("freeRTOSMemoryScheme");
				if (typeNum != -1 && HeapType.valueOf(typeNum) != HeapType.UNKNOWN) {
					this.freeRTOS.enableMacro(FreeRTOSConfig.MEMORY_SCHEME, true);
					heapType = HeapType.valueOf(typeNum);
					logger.info(String.format(Texts.get("Info.HeapIdentifiedFrom"), "freeRTOSMemoryScheme variable (configFRTOS_MEMORY_SCHEME)", heapType.toString()));
				}

				if (heapType == HeapType.UNKNOWN) {
					logger.info(Texts.get("Info.HeapTypeIdentifyFromVariable"));
					logger.info(Texts.get("Info.HeapTypeIdentifyHelp"));
					this.freeRTOS.enableMacro(FreeRTOSConfig.MEMORY_SCHEME, false);
					heapType = this.identifyHeapType();
					logger.info(String.format(Texts.get("Info.HeapIdentifiedFrom"), "available heap variables", heapType.toString()));
				}

				this.freeRTOS.setHeapType(heapType);
			}

			Heap heap = new Heap(heapType);
			long heapSize;
			switch (this.freeRTOS.getHeapType()) {
				case UNKNOWN:
				case STATIC_MEMORY:
				case HEAP_3:
					TadModel var10002 = this.getTadModel();
					TadFactoryDataStatus var10004 = TadFactoryDataStatus.INFORMATION;
					String var10005 = Texts.get("Info.HeapNoInfo");
					return new TadFactoryData(var10002, list, var10004, var10005 + "\n\nNOTE: " + Texts.get("Info.HeapTypeIdentifyHelp"));
				case HEAP_1:
					heapSize = this.getSize("ucHeap");
					heap.setHeapStart(this.ucHeapAddress);
					heap.setHeapEnd(this.ucHeapAddress + heapSize);
					long nextFreeByte = this.readLongVariable("xNextFreeByte");
					heap.setFree(heapSize - nextFreeByte);
					heap.setUsed(heapSize - heap.getFree());
					break;
				case HEAP_2:
				case HEAP_4:
				case HEAP_5:
					heapSize = 0L;
					long xStartAddress = this.readAddressOfVariable("xStart");
					long free = this.readIntVariable("xFreeBytesRemaining");
					heap.setFree(free);
					if (heapType != HeapType.HEAP_5) {
						heap.setHeapStart(this.ucHeapAddress);
						heapSize = this.getSize("ucHeap");
						heap.setHeapEnd(this.ucHeapAddress + heapSize);
					} else {
						long heapRegionsSize = 0L;

						try {
							heapRegionsSize = this.getSize("xHeapRegions");
						} catch (ReadingException var22) {
							return new TadFactoryData(this.getTadModel(), list, TadFactoryDataStatus.INFORMATION, Texts.get("Info.Heap5RegionsUndef"));
						}

						long numberOfHeapRegions = heapRegionsSize / this.getStructSize(this.freeRTOS.getStruct("heap_region"));
						List<HeapRegion> regions = new LinkedList<>();

						for (int i = 0; (long) i < numberOfHeapRegions; ++i) {
							long heapRegionAddress = this.readAddressOfVariable("xHeapRegions[" + i + "]");
							long size = this.readLongAtAddress(heapRegionAddress, this.freeRTOS.getStruct("heap_region"), "xSizeInBytes");
							if (size > 0L) {
								heapSize += size;
								long startAddress = this.readLongAtAddress(heapRegionAddress, this.freeRTOS.getStruct("heap_region"), "pucStartAddress");
								regions.add(new HeapRegion(startAddress, size));
							}
						}

						heap.setHeap5regions(this.sortHeapRegionsByAddress(regions));
						long heapStart = !regions.isEmpty() ? regions.get(0).getStartAddress() : xStartAddress;
						heap.setHeapStart(heapStart);
						long heapEnd = !regions.isEmpty() ? regions.get(regions.size() - 1).getEndAddress() : heapStart + heapSize;
						heap.setHeapEnd(heapEnd);
					}

					heap.setUsed(heapSize - free);
					if (heapType != HeapType.HEAP_2) {
						heap.setMinFree(this.readIntVariable("xMinimumEverFreeBytesRemaining"));
					}

					if (xStartAddress > 0L) {
						long xStartPtr = this.readLongAtAddress(xStartAddress, this.freeRTOS.getStruct("heap_block"), "pxNextFreeBlock");
						if (xStartPtr != 0L && heap.getSize() > 0L) {
							List<HeapBlock> blocks = this.sortHeapBlocksByAddress(this.getHeapBlocks(xStartPtr, heap.getHeapEnd()));
							blocks = this.sortHeapBlocksByAddress(this.addAllocatedBlocks(blocks, heap.getHeapStart()));
							if (heapType == HeapType.HEAP_5) {
								for (HeapRegion region : heap.getHeap5regions()) {
									long usage = 0L;
									List<HeapBlock> regionBlocks = new LinkedList<>();

									for (HeapBlock block : blocks) {
										if (block.getStartAddress() > region.getEndAddress()) {
											break;
										}

										if (block.getEndAddress() >= region.getStartAddress()) {
											long overflow = 0L;
											if (block.getStartAddress() >= region.getStartAddress() && block.getEndAddress() <= region.getEndAddress()) {
												regionBlocks.add(block);
											} else if (block.getEndAddress() > region.getEndAddress()) {
												overflow = block.getEndAddress() - region.getEndAddress();
												regionBlocks.add(new HeapBlock(block.getStartAddress(), block.getSize() - overflow, block.getStatus()));
											} else {
												overflow = region.getStartAddress() - block.getStartAddress();
												regionBlocks.add(new HeapBlock(region.getStartAddress(), block.getSize() - overflow, block.getStatus()));
											}

											if (block.getStatus() == HeapBlockStatus.ALLOCATED) {
												usage += block.getSize() - overflow;
											}
										}
									}

									regionBlocks = this.addAllocatedBlocks(regionBlocks, region.getStartAddress());
									region.setUsed(usage);
									region.setHeapBlocks(regionBlocks);
								}

								blocks.clear();

								for (HeapRegion region : heap.getHeap5regions()) {
									blocks.addAll(region.getHeapBlocks());
								}
							}

							heap.setHeapBlocks(this.sortHeapBlocksByAddress(blocks));
						}
					}
					break;
				case HEAP_NEWLIB:
					long heapBase = this.readAddressOfVariableSafely("_pvHeapStart");
					long heapEnd = this.readAddressOfVariableSafely("_pvHeapLimit");
					int remainingBytes = this.readIntVariableSafely("heapBytesRemaining");
					if (heapBase == -1L || heapEnd == -1L || remainingBytes == -1) {
						return new TadFactoryData(this.getTadModel(), list, TadFactoryDataStatus.INFORMATION, Texts.get("Info.HeapNewlibNoInfo"));
					}

					heap.setHeapStart(heapBase);
					heap.setHeapEnd(heapEnd);
					heap.setFree(remainingBytes);
					heap.setUsed(heapEnd - heapBase - heap.getFree());
					break;
				default:
					throw new IllegalStateException("Unexpected value: " + this.freeRTOS.getHeapType());
			}

			list.add(heap);
		} catch (ReadingException e) {
			Activator.getDefault().getLogger().exception(e, e.getSummary());
			return new TadFactoryData(this.getTadModel(), list, TadFactoryDataStatus.WARNING, String
					.format(Texts.get("Error.CouldNotGetHeapFactoryData") + "\n" + Texts.get("Info.HeapTypeIdentifyHelp"), "Heap Usage", this.freeRTOS.getHeapType().getType()));
		}

		return new TadFactoryData(this.getTadModel(), list, TadFactoryDataStatus.SUCCESS, null);
	}

	private List<HeapBlock> sortHeapBlocksByAddress(List<HeapBlock> blocks) {
		Collections.sort(blocks, new Comparator<HeapBlock>() {
			public int compare(HeapBlock block1, HeapBlock block2) {
				if (block1.getStartAddress() < block2.getStartAddress()) {
					return -1;
				} else {
					return block1.getStartAddress() > block2.getStartAddress() ? 1 : 0;
				}
			}
		});
		return blocks;
	}

	private List<HeapRegion> sortHeapRegionsByAddress(List<HeapRegion> regions) {
		Collections.sort(regions, new Comparator<HeapRegion>() {
			public int compare(HeapRegion region1, HeapRegion region2) {
				if (region1.getStartAddress() < region2.getStartAddress()) {
					return -1;
				} else {
					return region1.getStartAddress() > region2.getStartAddress() ? 1 : 0;
				}
			}
		});
		return regions;
	}

	private List<HeapBlock> addAllocatedBlocks(List<HeapBlock> blocks, long heapStart) {
		if (blocks != null && blocks.size() > 0) {
			for (int i = 0; i < blocks.size() - 1; ++i) {
				HeapBlock block = blocks.get(i);
				HeapBlock nextBlock = blocks.get(i + 1);
				if (block.getEndAddress() + 1L < nextBlock.getStartAddress()) {
					++i;
					blocks.add(i, new HeapBlock(block.getEndAddress() + 1L, nextBlock.getStartAddress() - block.getEndAddress() - 1L, HeapBlockStatus.ALLOCATED));
				}
			}

			HeapBlock firstBlock = blocks.get(0);
			if (heapStart + 1L < firstBlock.getStartAddress()) {
				blocks.add(0, new HeapBlock(heapStart, firstBlock.getStartAddress() - heapStart, HeapBlockStatus.ALLOCATED));
			}
		}

		return blocks;
	}

	private synchronized List<HeapBlock> getHeapBlocks(long nextFreeBlock, long heapEnd) throws ReadingException {
		List<HeapBlock> blocks;
		for (blocks = new LinkedList<>(); nextFreeBlock != 0L
				&& nextFreeBlock < heapEnd; nextFreeBlock = this.readLongAtAddress(nextFreeBlock, this.freeRTOS.getStruct("heap_block"), "pxNextFreeBlock")) {
			long blockSize = this.readLongAtAddress(nextFreeBlock, this.freeRTOS.getStruct("heap_block"), "xBlockSize");
			if (blockSize > 0L && blockSize <= heapEnd - nextFreeBlock) {
				blocks.add(new HeapBlock(nextFreeBlock, blockSize, HeapBlockStatus.FREE));
			}
		}

		List<? extends TadObject> tasks = this.dataCache.getViewTadObjects("Task List");
		if (tasks != null && !tasks.isEmpty()) {
			long sizeOfTCB = this.getStructSize(this.freeRTOS.getStruct("task_control_block"));

			for (TadObject obj : tasks) {
				Task task = (Task) obj;
				long var10003 = task.getAddress();
				HeapBlockStatus var10005 = HeapBlockStatus.ALLOCATED;
				String var10006 = task.getName();
				blocks.add(new HeapBlock(var10003, sizeOfTCB, var10005, var10006 + " (" + Texts.get("Label.Task") + " " + Texts.get("Label.Task.TCB") + ")"));
				var10003 = task.getStack().getBaseAddress();
				long var10004 = task.getStack().getSize();
				var10005 = HeapBlockStatus.ALLOCATED;
				var10006 = task.getName();
				blocks.add(new HeapBlock(var10003, var10004, var10005, var10006 + " (" + Texts.get("Label.Task") + " " + Texts.get("Label.Task.Stack") + ")"));
			}
		}

		List<? extends TadObject> queues = this.dataCache.getViewTadObjects("Queue List");
		if (queues != null && !queues.isEmpty()) {
			long queueStructSize = this.getStructSize(this.freeRTOS.getStruct("queue"));

			for (TadObject obj : queues) {
				Queue queue = (Queue) obj;
				long var18 = queue.getAddress();
				long var19 = queueStructSize + (long) queue.getDataSize();
				HeapBlockStatus var21 = HeapBlockStatus.ALLOCATED;
				String var23 = queue.getName();
				blocks.add(new HeapBlock(var18, var19, var21, var23 + " (" + Texts.get("Label.Queue") + ")"));
			}
		}

		return blocks;
	}
}
