package com.chipon32.os.freertos.controller;

import java.util.Collections;
import java.util.Comparator;
import java.util.LinkedList;
import java.util.List;

import com.chipon32.os.common.ReadingException;
import com.chipon32.os.common.controller.TadFactoryData;
import com.chipon32.os.common.controller.TadFactoryDataStatus;
import com.chipon32.os.common.messages.Messages;
import com.chipon32.os.common.model.TadModel;
import com.chipon32.os.common.model.readers.MemoryReader;
import com.chipon32.os.common.model.readers.VariableReader;
import com.chipon32.os.common.model.view.generic.TadValue;
import com.chipon32.os.freertos.Activator;
import com.chipon32.os.freertos.model.freertos.FreeRTOS;
import com.chipon32.os.freertos.model.freertos.FreeRTOSConfig;
import com.chipon32.os.freertos.model.view.timerlist.Timer;
import com.chipon32.os.freertos.model.view.timerlist.TimerCallback;
import com.chipon32.os.freertos.model.view.timerlist.TimerStatus;

public class TimerFactory extends FreeRtosTadFactory {
	private final String[] timerLists = new String[] { "pxCurrentTimerList", "pxOverflowTimerList" };

	public TimerFactory(TadModel tadModel, MemoryReader memoryReader, VariableReader variableReader, FreeRTOS freeRTOS) {
		super(tadModel, memoryReader, variableReader, freeRTOS);
	}

	public TadFactoryData getData() {
		List<Timer> list = new LinkedList<>();

		try {
			String[] var5;
			for (String listName : var5 = this.timerLists) {
				list.addAll(this.readTimersFromList(listName));
			}

			Collections.sort(list, new Comparator<Timer>() {
				public int compare(Timer timer1, Timer timer2) {
					if (timer1.getId() < timer2.getId()) {
						return -1;
					} else {
						return timer1.getId() > timer2.getId() ? 1 : 0;
					}
				}
			});
		} catch (ReadingException e) {
			Activator.getDefault().getLogger().exception(e, e.getSummary());
			return new TadFactoryData(this.getTadModel(), list, TadFactoryDataStatus.ERROR, String.format(Messages.Error_CouldNotGetFactoryData, "Timer List"));
		}

		return new TadFactoryData(this.getTadModel(), list, TadFactoryDataStatus.SUCCESS, (String) null);
	}

	private synchronized List<Timer> readTimersFromList(String listName) throws ReadingException {
		List<Timer> list = new LinkedList<>();
		int numberOfItems = this.readIntVariableSafely(listName + ".uxNumberOfItems");
		long listAddress = this.readLongVariableSafely(listName + ".xListEnd.pxNext");
		if (numberOfItems > 0 && listAddress > 0L) {
			for (int i = 0; i < numberOfItems; ++i) {
				long address = this.readLongAtAddress(listAddress, this.freeRTOS.getStruct("list_item"), "pvOwner");
				long nameAddress = this.readLongAtAddress(address, this.freeRTOS.getStruct("timer"), "pcTimerName");
				Long number = null;
				if (this.freeRTOS.isMacroEnabled(FreeRTOSConfig.USE_TRACE)) {
					try {
						number = this.readLongAtAddress(address, this.freeRTOS.getStruct("timer"), "uxTimerNumber");
					} catch (ReadingException var15) {
						this.freeRTOS.enableMacro(FreeRTOSConfig.USE_TRACE, false);
					}
				}

				TadValue callback = this.readLongAndContextAtAddress(address, this.freeRTOS.getStruct("timer"), "pxCallbackFunction");
				TimerStatus status;
				if (this.freeRTOS.getVersion() < 10.2) {
					boolean uxAutoReload = this.readIntAtAddress(address, this.freeRTOS.getStruct("timer"), "uxAutoReload") == 1;
					status = new TimerStatus(false, false, uxAutoReload);
				} else {
					int ucStatus = this.readIntAtAddress(address, this.freeRTOS.getStruct("timer"), "ucStatus");
					status = new TimerStatus(ucStatus);
				}

				list.add(new Timer(address, this.readTextAtAddress(nameAddress), this.readIntAtAddress(address, this.freeRTOS.getStruct("timer"), "pvTimerID"), number,
						this.readLongAtAddress(address, this.freeRTOS.getStruct("timer"), "xTimerPeriodInTicks"), status,
						new TimerCallback(callback.getValue(), callback.getContext())));
				listAddress = this.readLongAtAddress(listAddress, this.freeRTOS.getStruct("list_item"), "pxNext");
			}

			return list;
		} else {
			return list;
		}
	}
}
