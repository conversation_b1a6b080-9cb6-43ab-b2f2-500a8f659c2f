package com.chipon32.os.freertos.logger;

import org.eclipse.ui.console.MessageConsole;

import com.chipon32.os.common.logger.Logger;
import com.chipon32.os.freertos.Activator;
import com.chipon32.os.freertos.strings.Texts;

public class FreeRtosLogger extends Logger {
	public FreeRtosLogger(MessageConsole messageConsole) {
		super(messageConsole);
	}

	protected String getDefaultDirName() {
		return Texts.get("Log.DirName");
	}

	protected String getDirName() {
		return this.getDirName(Activator.getDefault().getPreferenceStore());
	}

	public boolean isLoggingEnabled() {
		return this.isLoggingEnabled(Activator.getDefault().getPreferenceStore());
	}

	protected boolean mustSaveLogs() {
		return this.mustSaveLogs(Activator.getDefault().getPreferenceStore());
	}

	protected String getPreferencesPrefix() {
		return "freeRTOS";
	}
}
