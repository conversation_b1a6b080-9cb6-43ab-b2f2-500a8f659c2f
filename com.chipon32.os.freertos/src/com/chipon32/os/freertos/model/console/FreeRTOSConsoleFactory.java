package com.chipon32.os.freertos.model.console;

import org.eclipse.jface.resource.ImageDescriptor;

import com.chipon32.os.common.model.console.TadConsoleFactory;
import com.chipon32.os.freertos.Activator;
import com.chipon32.os.freertos.strings.Texts;

public class FreeRTOSConsoleFactory extends TadConsoleFactory {
	private static final ImageDescriptor FREERTOS_IMG = Activator.getDefault().getImageDescriptor("freertos");

	protected String getConsoleName() {
		return String.format(Texts.get("Log.TadConsole"), Activator.getDefault().getVersionAsString());
	}

	protected ImageDescriptor getConsoleImage() {
		return FREERTOS_IMG;
	}
}
