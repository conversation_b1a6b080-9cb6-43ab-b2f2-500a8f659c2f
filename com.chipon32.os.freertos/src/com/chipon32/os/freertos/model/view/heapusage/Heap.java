package com.chipon32.os.freertos.model.view.heapusage;

import java.util.LinkedList;
import java.util.List;

public class Heap extends HeapRegion {
	private HeapType type;
	private long end;
	private long free;
	private long minFree;
	private List<HeapRegion> heap5regions;

	public Heap(HeapType type) {
		this.type = type;
		this.heapBlocks = new LinkedList<>();
		this.heap5regions = new LinkedList<>();
		this.free = 0L;
		this.minFree = 0L;
	}

	public HeapType getType() {
		return this.type;
	}

	public long getHeapStart() {
		return this.start;
	}

	public void setHeapStart(long heapStart) {
		this.start = heapStart;
	}

	public long getHeapEnd() {
		return this.end;
	}

	public void setHeapEnd(long heapEnd) {
		this.end = heapEnd;
	}

	public long getFree() {
		return this.free;
	}

	public void setFree(long free) {
		this.free = free;
	}

	public void setMinFree(long minFree) {
		this.minFree = minFree;
	}

	public long getMinFree() {
		return this.minFree;
	}

	public long getSize() {
		return this.used + this.free;
	}

	public List<HeapRegion> getHeap5regions() {
		return this.heap5regions;
	}

	public void setHeap5regions(List<HeapRegion> heap5regions) {
		this.heap5regions = heap5regions;
	}
}
