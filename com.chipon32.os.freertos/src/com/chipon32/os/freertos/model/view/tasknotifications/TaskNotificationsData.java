package com.chipon32.os.freertos.model.view.tasknotifications;

import java.util.List;

import com.chipon32.os.common.model.view.queues.GenericQueueData;
import com.chipon32.os.freertos.strings.Texts;

public class TaskNotificationsData extends GenericQueueData {
	public static final int STATE_NOT_WAITING = 0;
	public static final int STATE_WAITING = 1;
	public static final int STATE_RECEIVED = 2;
	private int stateVal;

	public TaskNotificationsData(int id, int stateVal, long address, int size, List<Long> data) {
		super(id, address, size, data);
		this.stateVal = stateVal;
	}

	public int getStateVal() {
		return this.stateVal;
	}

	public String getStateStr() {
		switch (this.stateVal) {
			case 0 -> {
				return Texts.get("TaskNotifications.StateNotWaiting");
			}
			case 1 -> {
				return Texts.get("TaskNotifications.StateWaiting");
			}
			case 2 -> {
				return Texts.get("TaskNotifications.StateReceived");
			}
			default -> {
				return String.format(Texts.get("TaskNotifications.StateUnknown"), this.stateVal);
			}
		}
	}
}
