package com.chipon32.os.freertos.model.view.heapusage;

public enum HeapType {
	UNKNOWN(-1, "Unknown"),
	STATIC_MEMORY(0, "Static memory"),
	HEAP_1(1, "Heap #1"),
	HEAP_2(2, "Heap #2"),
	HEAP_3(3, "Heap #3"),
	HEAP_4(4, "Heap #4"),
	HEAP_5(5, "Heap #5"),
	HEAP_NEWLIB(6, "Newlib");

	private int type;
	private String desc;

	private HeapType(int type, String desc) {
		this.type = type;
		this.desc = desc;
	}

	public String toString() {
		return String.format("%s", this.desc);
	}

	public static HeapType valueOf(int num) {
		switch (num) {
			case 0 -> {
				return STATIC_MEMORY;
			}
			case 1 -> {
				return HEAP_1;
			}
			case 2 -> {
				return HEAP_2;
			}
			case 3 -> {
				return HEAP_3;
			}
			case 4 -> {
				return HEAP_4;
			}
			case 5 -> {
				return HEAP_5;
			}
			case 6 -> {
				return HEAP_NEWLIB;
			}
			default -> {
				return UNKNOWN;
			}
		}
	}

	public int getType() {
		return this.type;
	}
}
