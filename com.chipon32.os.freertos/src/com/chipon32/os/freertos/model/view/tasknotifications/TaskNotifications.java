package com.chipon32.os.freertos.model.view.tasknotifications;

import java.util.List;

import com.chipon32.os.common.model.view.generic.TadObject;

public class TaskNotifications extends TadObject {
	private String taskName;
	private long taskHandle;
	private Long tcbNumber;
	private long numReceived;
	private long numWaiting;
	private List<Integer> waitingFor;
	private List<TaskNotificationsData> notificationsData;

	public TaskNotifications(String taskName, long taskHandle, Long tcbNumber, long numReceived, long numWaiting, List<Integer> waitingFor) {
		this.taskName = taskName;
		this.taskHandle = taskHandle;
		this.tcbNumber = tcbNumber;
		this.numReceived = numReceived;
		this.numWaiting = numWaiting;
		this.waitingFor = waitingFor;
	}

	public String getTaskName() {
		return this.taskName;
	}

	public long getTaskHandle() {
		return this.taskHandle;
	}

	public Long getTCBNumber() {
		return this.tcbNumber;
	}

	public long getNumReceived() {
		return this.numReceived;
	}

	public long getNumWaiting() {
		return this.numWaiting;
	}

	public List<Integer> getWaitingFor() {
		return this.waitingFor;
	}

	public List<TaskNotificationsData> getNotificationsData() {
		return this.notificationsData;
	}

	public void setNotificationsData(List<TaskNotificationsData> notificationsData) {
		this.notificationsData = notificationsData;
	}
}
