package com.chipon32.os.freertos.model.view.tasklist;

import com.chipon32.os.common.model.view.generic.TadObject;
import com.chipon32.os.freertos.model.freertos.FreeRTOS;
import com.chipon32.os.freertos.strings.Texts;

public class LightweightTask extends TadObject {
	protected FreeRTOS freeRTOS;
	protected String name;
	private long address;
	private Long number;
	private Long TCBNumber;

	public LightweightTask(String name, long address, FreeRTOS freeRTOS) {
		this.freeRTOS = freeRTOS;
		this.name = name != null && name.length() > 0 ? name : Texts.get("Error.Unknown");
		this.address = address;
		this.number = null;
		this.TCBNumber = null;
	}

	public String getName() {
		return this.name;
	}

	public long getAddress() {
		return this.address;
	}

	public Long getTCBNumber() {
		return this.TCBNumber;
	}

	public Long getTaskNumber() {
		return this.number;
	}

	public void setNumber(long number, long TCBNumber) {
		this.number = number;
		this.TCBNumber = TCBNumber;
	}
}
