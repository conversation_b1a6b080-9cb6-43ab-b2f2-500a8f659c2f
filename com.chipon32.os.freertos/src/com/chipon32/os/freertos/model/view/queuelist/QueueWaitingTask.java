package com.chipon32.os.freertos.model.view.queuelist;

public class QueueWaitingTask {
	private long address;
	private String name;
	private int count;

	public QueueWaitingTask(long address, String name, int count) {
		this.address = address;
		this.name = name;
		this.count = count;
	}

	public QueueWaitingTask() {
		this(0L, (String) null, 0);
	}

	public long getAddress() {
		return this.address;
	}

	public int getCount() {
		return this.count;
	}

	public String getName() {
		return this.name;
	}
}
