package com.chipon32.os.freertos.model.view.timerlist;

public class TimerStatus {
	boolean active;
	boolean staticallyAllocated;
	boolean autoReload;

	public TimerStatus(boolean active, boolean staticallyAllocated, boolean autoReload) {
		this.active = active;
		this.staticallyAllocated = staticallyAllocated;
		this.autoReload = autoReload;
	}

	public TimerStatus(int status) {
		this.active = (status & 1) > 0;
		this.staticallyAllocated = (status & 2) > 0;
		this.autoReload = (status & 4) > 0;
	}
}
