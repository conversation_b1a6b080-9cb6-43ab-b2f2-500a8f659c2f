package com.chipon32.os.freertos.model.view.tasklist;

import java.util.Arrays;

import com.chipon32.os.common.model.view.tasks.GenericTaskStack;
import com.chipon32.os.freertos.model.freertos.FreeRTOS;

public class Task extends LightweightTask {
	private TaskState state;
	private GenericTaskStack stack;
	private TaskPriority priority;
	private Long taskRuntime;
	private TaskEventObject eventObject;
	private TaskType type;
	private Long coreAffinityMask;

	public Task(String name, long address, FreeRTOS freeRTOS) {
		super(name, address, freeRTOS);
		this.state = TaskState.BLOCKED;
		this.eventObject = new TaskEventObject();
		this.type = TaskType.USER;
		this.coreAffinityMask = Long.MAX_VALUE;
	}

	public TaskPriority getPriority() {
		return this.priority;
	}

	public void setPriority(TaskPriority priority) {
		this.priority = priority;
	}

	public TaskState getState() {
		return this.state;
	}

	public void setState(TaskState state) {
		this.state = state;
	}

	public boolean isActive() {
		return this.state == TaskState.RUNNING;
	}

	public GenericTaskStack getStack() {
		return this.stack;
	}

	public void setStack(GenericTaskStack stack) {
		this.stack = stack;
	}

	public Long getTaskRuntime() {
		return this.taskRuntime;
	}

	public long getRuntime() {
		return Arrays.stream(this.freeRTOS.getRuntime()).sum();
	}

	public void setTaskRuntime(Long taskRuntime) {
		this.taskRuntime = taskRuntime;
	}

	public TaskStackGrowth getTaskStackGrowth() {
		return this.freeRTOS.getTaskStackGrowth();
	}

	public TaskEventObject getEventObject() {
		return this.eventObject;
	}

	public void setEventObject(TaskEventObject eventObject) {
		this.eventObject = eventObject;
	}

	public TaskType getType() {
		return this.type;
	}

	public long getCoreAffinityMask() {
		return this.coreAffinityMask;
	}

	public void setCoreAffinityMask(Long coreAffinityMask) {
		this.coreAffinityMask = coreAffinityMask;
	}
}
