package com.chipon32.os.freertos.model.view.heapusage;

public class HeapBlock {
	private String details;
	private long start;
	private long end;
	private long size;
	private HeapBlockStatus status;

	public HeapBlock(long start, long size, HeapBlockStatus status, String details) {
		this.start = start;
		this.end = start + size - (long) (size == 0L ? 0 : 1);
		this.size = size;
		this.status = status;
		this.details = details;
	}

	public HeapBlock(long start, long size, HeapBlockStatus status) {
		this(start, size, status, (String) null);
	}

	public String getDetails() {
		return this.details;
	}

	public long getStartAddress() {
		return this.start;
	}

	public void setStartAddress(long address) {
		this.start = address;
	}

	public long getEndAddress() {
		return this.end;
	}

	public void setEndAddress(long address) {
		this.end = address;
	}

	public long getSize() {
		return this.size;
	}

	public void setSize(long size) {
		this.size = size;
	}

	public HeapBlockStatus getStatus() {
		return this.status;
	}

	public void setStatus(HeapBlockStatus status) {
		this.status = status;
	}
}
