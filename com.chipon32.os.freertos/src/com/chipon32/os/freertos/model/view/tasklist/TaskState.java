package com.chipon32.os.freertos.model.view.tasklist;

import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.graphics.Image;

import com.chipon32.os.common.SystemRGB;
import com.chipon32.os.freertos.Activator;

public enum TaskState {

	RUNNING(new Color(SystemRGB.COLOR_DARK_GREEN.getRGB()), "running"),
	READY(new Color(SystemRGB.COLOR_BLACK.getRGB()), "ready"),
	BLOCKED(new Color(SystemRGB.COLOR_BLACK.getRGB()), "blocked"),
	SUSPENDED(new Color(SystemRGB.COLOR_DARK_GRAY.getRGB()), "suspended");

	private final Image image;
	private final Color colour;

	TaskState(Color colour, String icon) {
		this.colour = colour;
		this.image = Activator.getDefault().getImage(icon);
	}

	public Image getImage() {
		return this.image;
	}

	public Color getColour() {
		return this.colour;
	}

}
