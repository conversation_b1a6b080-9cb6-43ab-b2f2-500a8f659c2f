package com.chipon32.os.freertos.model.view.queuelist;

import java.util.HashMap;
import java.util.Map;

import com.chipon32.os.common.model.view.queues.GenericQueue;

public class Queue extends GenericQueue {
	private QueueType type;
	private final Map<QueueWaitingTaskOperation, QueueWaitingTask> waitingTasks = new HashMap<>();

	public Queue(long handleAddress, String name, long maxLength, long currentLength, int dataItemSize, long headAddress, long tailAddress, long readFromAddress,
			long writeToAddress) {
		super(handleAddress, name, maxLength, currentLength, dataItemSize, headAddress, tailAddress, readFromAddress, writeToAddress);
	}

	public void setType(QueueType type) {
		this.type = type;
	}

	public QueueType getType() {
		return this.type;
	}

	public Map<QueueWaitingTaskOperation, QueueWaitingTask> getWaitingTasks() {
		return this.waitingTasks;
	}

	public void addWaitingTask(QueueWaitingTaskOperation operation, QueueWaitingTask waitingTask) {
		this.waitingTasks.put(operation, waitingTask);
	}
}
