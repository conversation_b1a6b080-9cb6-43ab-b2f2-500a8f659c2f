package com.chipon32.os.freertos.model.view.timerlist;

import java.util.LinkedList;
import java.util.List;

import com.chipon32.os.common.model.Auxiliary;
import com.chipon32.os.common.model.view.generic.TadObject;

public class Timer extends TadObject {
	private long address;
	private String name;
	private int id;
	private Long number;
	private long periodInTicks;
	private TimerStatus status;
	private TimerCallback callback;

	public Timer(long address, String name, int id, Long number, long periodInTicks, TimerStatus status, TimerCallback callback) {
		this.address = address;
		this.name = name;
		this.id = id;
		this.number = number;
		this.periodInTicks = periodInTicks;
		this.status = status;
		this.callback = callback;
	}

	public long getAddress() {
		return this.address;
	}

	public String getName() {
		return this.name;
	}

	public int getId() {
		return this.id;
	}

	public Long getNumber() {
		return this.number;
	}

	public long getPeriod() {
		return this.periodInTicks;
	}

	public String getStatus() {
		List<String> str = new LinkedList<>();
		if (this.status.active) {
			str.add("active");
		}

		if (this.status.staticallyAllocated) {
			str.add("static");
		}

		if (this.status.autoReload) {
			str.add("autoreload");
		}

		return str.size() > 0 ? Auxiliary.capitalize(String.join(", ", str)) : "Unknown";
	}

	public TimerCallback getCallback() {
		return this.callback;
	}
}
