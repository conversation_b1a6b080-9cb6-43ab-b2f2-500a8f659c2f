package com.chipon32.os.freertos.model.view.heapusage;

import java.util.List;

import com.chipon32.os.common.model.view.generic.TadObject;

public class HeapRegion extends TadObject {
	protected long start;
	protected long size;
	protected Long used;
	protected List<HeapBlock> heapBlocks;

	public HeapRegion() {
		this.used = null;
	}

	public HeapRegion(long startAddress, long size) {
		this.start = startAddress;
		this.size = size;
	}

	public long getStartAddress() {
		return this.start;
	}

	public long getEndAddress() {
		return this.start + this.size - 1L;
	}

	public long getSize() {
		return this.size;
	}

	public long getFree() {
		return this.size - this.used;
	}

	public void setUsed(long usage) {
		this.used = usage;
	}

	public Long getUsed() {
		return this.used;
	}

	public void setHeapBlocks(List<HeapBlock> heapBlocks) {
		this.heapBlocks = heapBlocks;
	}

	public List<HeapBlock> getHeapBlocks() {
		return this.heapBlocks;
	}
}
