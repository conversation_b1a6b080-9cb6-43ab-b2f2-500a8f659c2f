package com.chipon32.os.freertos.model.view.tasklist;

import com.chipon32.os.freertos.model.view.queuelist.QueueWaitingTaskOperation;

public class TaskEventObject {
	private long address;
	private String name;
	private QueueWaitingTaskOperation operation;

	public long getAddress() {
		return this.address;
	}

	public void setAddress(long address) {
		this.address = address;
	}

	public String getName() {
		return this.name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public QueueWaitingTaskOperation getOperation() {
		return this.operation;
	}

	public void setOperation(QueueWaitingTaskOperation operation) {
		this.operation = operation;
	}
}
