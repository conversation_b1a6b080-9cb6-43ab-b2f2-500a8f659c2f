package com.chipon32.os.freertos.model.freertos;

import com.chipon32.os.common.rtos.IRtosConfig;
import com.chipon32.os.freertos.strings.Texts;

public enum FreeRTOSConfig implements IRtosConfig {
	USE_TRACE("configUSE_TRACE_FACILITY", (String) null),
	RUNTIME_STATS("configGENERATE_RUN_TIME_STATS", (String) null),
	USE_MUTEXES("configUSE_MUTEXES", (String) null),
	RECORD_STACK_HIGH_ADDRESS("configRECORD_STACK_HIGH_ADDRESS", (String) null),
	MEMORY_SCHEME("configFRTOS_MEMORY_SCHEME", Texts.get("Info.HeapTypeIdentifyHelp")),
	STATIC_AND_DYNAMIC_ALLOCATION("configSUPPORT_STATIC_ALLOCATION", (String) null),
	USE_TASK_NOTIFICATIONS("configUSE_TASK_NOTIFICATIONS", (String) null),
	USE_CORE_AFFINITY("configUSE_CORE_AFFINITY", (String) null);

	private String macroName;
	private String info;

	private FreeRTOSConfig(String macroName, String info) {
		this.macroName = macroName;
		this.info = info;
	}

	public String getInfo() {
		return this.info;
	}

	public String toString() {
		return this.macroName;
	}
}
