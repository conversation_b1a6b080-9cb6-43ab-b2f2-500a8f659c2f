package com.chipon32.os.freertos.model.freertos;

import java.util.Arrays;

import com.chipon32.os.common.messages.Messages;
import com.chipon32.os.common.rtos.IRtosConfig;
import com.chipon32.os.common.rtos.Rtos;
import com.chipon32.os.common.rtos.RtosAvailability;
import com.chipon32.os.freertos.Activator;
import com.chipon32.os.freertos.model.view.heapusage.HeapType;
import com.chipon32.os.freertos.model.view.tasklist.TaskStackGrowth;
import com.chipon32.os.freertos.strings.Texts;

public class FreeRTOS extends Rtos {
	public static final String READY_TASK_LISTS = "pxReadyTasksLists";
	public static final String PENDING_READY_TASK_LIST = "xPendingReadyList";
	public static final String SUSPENDED_TASK_LIST = "xSuspendedTaskList";
	public static final String DELAYED_TASK_LIST_1 = "pxDelayedTaskList";
	public static final String DELAYED_TASK_LIST_2 = "pxOverflowDelayedTaskList";
	public static final String TIMER_LIST_1 = "pxCurrentTimerList";
	public static final String TIMER_LIST_2 = "pxOverflowTimerList";
	public static final String CURRENT_TCB = "pxCurrentTCB";
	public static final String CURRENT_TCBS = "pxCurrentTCBs";
	public static final String TASK_SCHEDULER_ACTIVE = "xSchedulerRunning";
	public static final long TASK_SCHEDULER_ACTIVE_VALUE = 1L;
	public static final String TASK_NOTIFICATIONS_VALUES = "ulNotifiedValue";
	public static final String TASK_NOTIFICATIONS_STATES = "ucNotifyState";
	public static final String HEAP_STRUCT_SIZE = "xHeapStructSize";
	public static final String HEAP2_STRUCT_SIZE = "heapSTRUCT_SIZE";
	public static final String HEAP_ARRAY = "ucHeap";
	public static final String HEAP_START_LINKER = "_pvHeapStart";
	public static final String HEAP_END_LINKER = "_pvHeapLimit";
	public static final String HEAP_BYTES_REMAINING = "heapBytesRemaining";
	public static final String HEAP_MIN_FREE_BYTES = "xMinimumEverFreeBytesRemaining";
	public static final String HEAP_FREE_BYTES = "xFreeBytesRemaining";
	public static final String HEAP_NEXT_FREE_BYTE = "xNextFreeByte";
	public static final String HEAP_START_FREE_BLOCKS = "xStart";
	public static final String HEAP_REGIONS = "xHeapRegions";
	public static final String TASK_CONTROL_BLOCK_10_1_0 = "TaskControlBlock_t";
	public static final double FREERTOS_VERSION_10_1_0 = 10.1;
	public static final double FREERTOS_VERSION_10_2_0 = 10.2;
	public static final double FREERTOS_VERSION_10_4_0 = 10.4;
	public static final double FREERTOS_VERSION_11_0_0 = (double) 11.0F;
	public static final String FREERTOS_DEBUG_CONFIG = "FreeRTOSDebugConfig";
	public static final int DEBUG_CONFIG_VERSION_MAJOR_OFFSET = 0;
	public static final int DEBUG_CONFIG__VERSION_MINOR_OFFSET = 1;
	public static final int FREERTOS_VERSION_MAJOR_OFFSET = 2;
	public static final int FREERTOS_VERSION_MINOR_OFFSET = 3;
	public static final int FREERTOS_VERSION_BUILD_OFFSET = 4;
	public static final int FREERTOS_MEMORY_SCHEME_OFFSET = 5;
	public static final int FREERTOS_NUMBER_OF_CORES_OFFSET = 19;
	public static final String LIST_ITEM = "list_item";
	public static final String TASK_CONTROL_BLOCK = "task_control_block";
	public static final String QUEUE = "queue";
	public static final String QUEUE_REGISTRY_ITEM = "queue_registr_item";
	public static final String QUEUE_POINTERS = "queue_pointers";
	public static final String TIMER = "timer";
	public static final String HEAP_BLOCK = "heap_block";
	public static final String HEAP_REGION = "heap_region";
	private long[] runtime;
	private HeapType heapType;
	private Long queueRegistrySize;
	private int numberOfCores;
	private TaskStackGrowth taskStackGrowth;

	public void init() {
		super.init();
		this.heapType = HeapType.UNKNOWN;
		this.queueRegistrySize = null;
		this.taskStackGrowth = TaskStackGrowth.UNKNOWN;
		this.numberOfCores = 1;
		this.runtime = new long[this.numberOfCores];
		this.config.put(FreeRTOSConfig.RUNTIME_STATS, true);
		this.config.put(FreeRTOSConfig.USE_MUTEXES, true);
		this.config.put(FreeRTOSConfig.USE_TRACE, true);
		this.config.put(FreeRTOSConfig.RECORD_STACK_HIGH_ADDRESS, true);
		this.config.put(FreeRTOSConfig.MEMORY_SCHEME, true);
		this.config.put(FreeRTOSConfig.STATIC_AND_DYNAMIC_ALLOCATION, false);
		this.config.put(FreeRTOSConfig.USE_TASK_NOTIFICATIONS, true);
		this.config.put(FreeRTOSConfig.USE_CORE_AFFINITY, true);
	}

	public void initStructs(double version) {
		super.initStructs(version);
		this.structs.put("list_item", "xLIST_ITEM");
		this.structs.put("queue_registr_item", "QUEUE_REGISTRY_ITEM");
		this.structs.put("heap_block", "A_BLOCK_LINK");
		this.structs.put("heap_region", "HeapRegion");
		if (version == 10.1) {
			this.structs.put("task_control_block", "TaskControlBlock_t");
			this.structs.put("queue", "QueueDef_t");
			this.structs.put("timer", "TimerDef_t");
			this.structs.put("queue_pointers", "QueuePointers");
		} else {
			this.structs.put("task_control_block", "tskTaskControlBlock");
			this.structs.put("queue", "QueueDefinition");
			this.structs.put("timer", "tmrTimerControl");
		}

	}

	public long[] getRuntime() {
		return this.runtime;
	}

	public void setRuntime(long[] runtime) {
		this.runtime = Arrays.copyOf(runtime, runtime.length);
	}

	public HeapType getHeapType() {
		return this.heapType;
	}

	public void setHeapType(HeapType heapType) {
		this.heapType = heapType;
	}

	public Long getQueueRegistrySize() {
		return this.queueRegistrySize;
	}

	public void setQueueRegistrySize(Long queueRegistrySize) {
		this.queueRegistrySize = queueRegistrySize;
	}

	public TaskStackGrowth getTaskStackGrowth() {
		return this.taskStackGrowth;
	}

	public void setTaskStackGrowth(TaskStackGrowth taskStackGrowth) {
		this.taskStackGrowth = taskStackGrowth;
	}

	public void enableMacro(IRtosConfig macro, boolean enable) {
		if (!enable) {
			Activator.getDefault().getLogger().info(String.format(Messages.Info_MacroDisabled, macro.toString()));
		}

		this.config.put(macro, enable);
	}

	public void setAvailability(RtosAvailability availability) {
		if (availability != RtosAvailability.AVAILABLE) {
			Activator.getDefault().getLogger().info(Texts.get("Info.FreeRTOSNotUsed"));
		}

		super.setAvailability(availability);
	}

	public int getSMPNumberOfCores() {
		return this.numberOfCores;
	}

	public void setSMPNumberOfCores(int numberOfCores) {
		this.numberOfCores = numberOfCores;
	}
}
