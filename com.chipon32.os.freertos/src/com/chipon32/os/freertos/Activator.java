package com.chipon32.os.freertos;

import java.net.URL;

import org.eclipse.core.runtime.FileLocator;
import org.eclipse.core.runtime.IPath;
import org.eclipse.core.runtime.Path;
import org.eclipse.jface.resource.ImageDescriptor;
import org.eclipse.jface.resource.ImageRegistry;
import org.osgi.framework.Bundle;
import org.osgi.framework.BundleContext;
import org.osgi.framework.Version;

import com.chipon32.os.common.AbstractCommonActivator;
import com.chipon32.os.common.IconsUtils;
import com.chipon32.os.common.logger.Logger;
import com.chipon32.os.common.messages.Messages;
import com.chipon32.os.common.model.TadModel;
import com.chipon32.os.freertos.model.FreeRtosTadModel;

public class Activator extends AbstractCommonActivator {
	public static final String PLUGIN_ID = "com.chipon32.os.freertos";
	public static final String IMG_FREERTOS = "freertos";
	public static final String IMG_RUNNING = "running";
	public static final String IMG_SUSPENDED = "suspended";
	public static final String IMG_READY = "ready";
	public static final String IMG_BLOCKED = "blocked";
	private static Activator plugin;
	private FreeRtosTadModel tadModel;
	public static final String SWTBOT_DEFAULT_KEY = System.getProperty("org.eclipse.swtbot.search.defaultKey", "org.eclipse.swtbot.widget.key");
	public static final String FREERTOS_TASKLIST = "freertos.taskList";
	public static final String FREERTOS_QUEUELIST_UPPER = "freertos.queueList_upper";
	public static final String FREERTOS_QUEUELIST_LOWER = "freertos.queueList_lower";
	public static final String FREERTOS_HEAPUSAGE_UPPER = "freertos.heapUsage_upper";
	public static final String FREERTOS_HEAPUSAGE_LOWER = "freertos.heapUsage_lower";
	public static final String FREERTOS_TIMERLIST = "freertos.timerList";

	protected void registerImage(ImageRegistry registry, String key, String fileName) {
		try {
			IPath path = new Path("icons/" + fileName);
			URL url = FileLocator.find(this.getBundle(), path, null);
			if (url != null) {
				ImageDescriptor desc = ImageDescriptor.createFromURL(url);
				registry.put(key, desc);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

	}

	protected void initializeImageRegistry(ImageRegistry registry) {
		super.initializeImageRegistry(registry);
		this.registerImage(registry, "freertos", "freertos.png");
		this.registerImage(registry, "ready", "ready.gif");
		IconsUtils.registerImage(registry, "blocked", "org.eclipse.debug.ui", "full/elcl16/suspend_co.png");
		IconsUtils.registerImage(registry, "running", "org.eclipse.ui.browser", "elcl16/nav_go.png");
		IconsUtils.registerImage(registry, "suspended", "org.eclipse.ui.ide", "full/obj16/incomplete_tsk.png");
	}

	public void start(BundleContext context) throws Exception {
		super.start(context);
		plugin = this;
		this.tadModel = new FreeRtosTadModel();
	}

	public void stop(BundleContext context) throws Exception {
		plugin = null;
		this.tadModel = null;
		super.stop(context);
	}

	public static Activator getDefault() {
		return plugin;
	}

	public TadModel getTadModel() {
		return this.tadModel;
	}

	public Logger getLogger() {
		return this.getTadModel().getLogger();
	}

	public String getVersionAsString() {
		Bundle BUNDLE = getDefault().getBundle();
		if (BUNDLE != null) {
			Version v = BUNDLE.getVersion();
			return String.format("%d.%d.%d (%s)", v.getMajor(), v.getMinor(), v.getMicro(), v.getQualifier());
		} else {
			return Messages.Error_Unknown;
		}
	}

	public void post_start(BundleContext context) {
	}
}
