package com.chipon32.hex.core;

import java.util.List;

import com.chipon32.hex.core.util.ByteConvertor;

public class CheckSUMaCalculateLibrary {	
		
	public static long 	VALUE_0XFFFFFFFF=Long.parseLong("FFFFFFFF",16);
	public static int 	VALUE_0XFFFF		=Integer.parseInt("FFFF",16);
	public static long 	VALUE_0X80000000=Long.parseLong("80000000",16);

//check 8 16 32 result oK
//	0x03,0x5F,0x11,0xD0,0x60,0x99,0x50,0x10,0x31,0x10,0x63,0x10,0x0A,0x04,0xF9,0xD4,
//	0x06,0xF0,0x7E,0xDB,0x77,0x68,0x27,0xD5,0x7A,0xDB,0xE4,0xD7,0x09,0x7C,0xAD,0xC0,
//	0x10,0x38,0xF6,0xF1,0x44,0xA1,0x03,0x5E,0x1D,0x5C,0x00,0x00,0x41,0x10,0x24,0xDA,
//	0x06,0x45,0x24,0x68,0x04,0x04,0x35,0x82,0xDA,0xD4,0x53,0x85,0x35,0x82,0xDC,0xD4,
//	0x1B,0xDC,0x30,0x38,0xF9,0xF1,0x1D,0x5C,0x8C,0x00,0x20,0x40,0x83,0x5F,0x61,0x10,
//	0xB5,0x7B,0x06,0x58,0x11,0x10,0x06,0x47,0x07,0x5C,0x06,0x58,0x10,0x10,0x07,0x5C,
//	0x06,0x58,0x11,0x10,0x03,0x45,0x05,0x5C,0x83,0x5E,0x00,0x00,0x94,0x05,0x00,0x00,
//	0x7C,0x05,0x00,0x00,0x18,0x45,0x25,0x82,0x48,0x99,0xD8,0x99,0xE1,0xD6,0x08,0x99,
//	0x64,0xD6,0x98,0x99,0xE4,0xD6,0xD8,0x98,0x9E,0x7B,0xE4,0xD6,0x18,0x9A,0x9D,0x7A,
//	0xE4,0xD6,0x12,0x43,0xDA,0xD4,0xE4,0xD6,0x54,0x85,0x30,0x82,0x60,0x98,0x30,0x38,
//	0x0B,0xF0,0x90,0x98,0x20,0x38,0x04,0xF0,0x55,0x99,0x9B,0xD6,0x04,0x04,0x55,0x99,
//	0x33,0x68,0x9B,0xD4,0x6B,0xA1,0x40,0x38,0x0C,0xF0,0x98,0x98,0x06,0x45,0x30,0x38,
//	0x04,0xF0,0x9D,0x99,0xE4,0xD6,0x04,0x04,0x9D,0x99,0x44,0x68,0xE4,0xD4,0xAC,0xA1,
//	0x1D,0x5C,0x00,0x00,0x80,0x0E,0x00,0x40,0xF0,0xD3,0x01,0xBF,0x04,0x45,0x00,0x38,
//	0x03,0xF0,0xAF,0x4D,0x02,0x04,0xAF,0x4F,0x1D,0x5C,0x00,0x00,0x80,0x0E,0x00,0x40,
//	0x03,0x5F,0x10,0x38,0x0D,0xF0,0x1E,0x45,0x60,0x11,0xA0,0xC3,0x22,0x7A,0x35,0xE9,
//	0x30,0x38,0x03,0xF0,0x89,0xD7,0x03,0x04,0x11,0x68,0x89,0xD5,0x29,0xEF,0x42,0xD1,
//	0x40,0x38,0x15,0xF0,0x63,0x10,0xB0,0xD5,0x33,0x7A,0x15,0x45,0x72,0x10,0xC8,0xDD,
//	0x18,0x29,0x30,0x38,0x06,0xF0,0xC9,0xDB,0x7D,0xE8,0xA4,0xDB,0xE4,0xD7,0x06,0x04,
//	0x0A,0x7A,0x7D,0xE8,0xA4,0xDB,0x44,0x68,0xE4,0xD5,0x6C,0xEE,0x90,0x7C,0x20,0x38,
//	0x13,0xF0,0x43,0x10,0x20,0xD5,0x23,0x7A,0x09,0x45,0x02,0x7C,0x0A,0x29,0x02,0x7A,
//	0x30,0x38,0x05,0xF0,0x1D,0xE8,0x22,0xDB,0xE4,0xD6,0x05,0x04,0x1D,0xE8,0x22,0xDB,
//	0x44,0x68,0xE4,0xD4,0x2C,0xEE,0x03,0x5E,0x1D,0x5C,0x00,0x00,0x80,0x0E,0x00,0x40,
//	0x16,0x45,0x05,0x70,0x29,0xF0,0x24,0x10,0xA8,0xDC,0x5F,0x39,0x0D,0xF8,0x4F,0x10,
//	0x00,0xD5,0x68,0xDB,0x12,0x44,0x50,0x38,0x04,0xF0,0xC4,0x98,0x2D,0xD6,0x02,0x04,
//	0xEC,0x98,0xE5,0xA0,0x19,0x04,0x3F,0x10,0x20,0x12,0xA5,0xC6,0xC0,0xD4,0x5E,0x3A,
//	0x0A,0xF8,0x20,0xDB,0x0A,0x45,0x40,0x38,0x04,0xF0,0x05,0x99,0x24,0xD6,0x0B,0x04,
//	0x25,0x99,0x09,0x04,0x00,0xDB,0x06,0x45,0x25,0x99,0x00,0x38,0x04,0xF0,0x00,0xD7,
//	0x28,0xA1,0x02,0x04,0x2C,0xA1,0x1D,0x5C,0xF0,0x0F,0x00,0x00,0x80,0x0E,0x00,0x40,
//	0x04,0x45,0x00,0x38,0x03,0xF0,0xAB,0x4C,0x02,0x04,0xAB,0x4E,0x1D,0x5C,0x00,0x00,
//	0x88,0x0E,0x00,0x40,0x04,0x45,0x00,0x38,0x03,0xF0,0xAC,0x4D,0x02,0x04,0xAC,0x4F,
//	0x1D,0x5C,0x00,0x00,0x80,0x0E,0x00,0x40,0x04,0x7C,0x51,0x10,0x05,0xDA,0x05,0x45,
/**
 * 	
 * @param value
 * @return
 */
	public static byte ReverseByte(byte value)
	{
		byte out=0;
		for(int i=0;i<8;i++)
		{
			out /=2;
			if((value&0x80) !=0)
			{
				out |=0x80;
			}
			value *=2;
		}		
		return out;
	}
	public static int ReverseByte2Int(byte value)
	{
		int out=0;
		for(int i=0;i<8;i++)
		{
			out /=2;
			if((value&0x80) !=0)
			{
				out |=0x80;
			}
			value *=2;
		}		
		return out;
	}
	public static int ReverseShort(int value)
	{		
		int out=0;
		for(int i=0;i<16;i++)
		{
			out /=2;
			if((value&0x8000) !=0)
			{
				out |=0x8000;
			}
			value *=2;
		}		
		return out;		
	}
	public static long ReverseLong(long value)
	{		
		long out=0;
		for(int i=0;i<32;i++)
		{
			out /=2;
			if((value& Long.parseLong("80000000",16)) !=0)
			{
				out |= Long.parseLong("80000000",16);
			}
			value *=2;
		}		
		return out;			
	}
	public static long ReverseLongByte (long value)
	{
		long out=0;
		for(int i=0;i<8;i++)
		{
			out *=2;
			if((value& 1) !=0)
			{
				out |= 1;
			}
			value /=2;
		}		
		return out;	
	}
	
	/******************************************************************************
	 * Name:    CRC-4/ITU           x4+x+1
	 * Poly:    0x03
	 * Init:    0x00
	 * Refin:   True
	 * Refout:  True
	 * Xorout:  0x00
	 * Note:
	 *****************************************************************************/
//	 uint8_t crc8 = 0;                // Initial value
// 0x0C = (reverse 0x03)>>(8-4)
	public static byte crc4_itu(List<String> data , byte crc8){
	    int i;
	    int length=data.size();
	    for(int j=0;j<length;j++)
	    {
	    	crc8 ^= ByteConvertor.hexToByte(data.get(j));                 // crc ^= *data; data++;
	        for (i = 0; i < 8; ++i)
	        {
	            if ((crc8 & 1) !=0)
	            	crc8 = (byte) ((crc8 >> 1) ^ 0x0C);// 0x0C = (reverse 0x03)>>(8-4)
	            else
	            	crc8 = (byte) (crc8 >> 1);
	        }
	    }
	    return crc8;}
	 /******************************************************************************
	 * Name:    CRC-5/EPC           x5+x3+1
	 * Poly:    0x09
	 * Init:    0x09
	 * Refin:   False
	 * Refout:  False
	 * Xorout:  0x00
	 * Note:
	 *****************************************************************************/
	//uint8_t crc8 = 0x48;        // Initial value: 0x48 = 0x09<<(8-5)
	  // 0x48 = 0x09<<(8-5)
	public static byte crc5_epc(List<String> data, byte crc8,byte Polynomial){
		int i;
	    
	    int length=data.size();
	    for(int j=0;j<length;j++)
	    {
	    	crc8 ^= ByteConvertor.hexToByte(data.get(j));     // crc ^= *data; data++;
	        for ( i = 0; i < 8; i++ )
	        {
	            if ( (crc8 & 0x80)!=0)
	            	crc8 = (byte) ((crc8 << 1) ^ Polynomial);      
	            else
	            	crc8 <<= 1;
	        }
	    }
	    return (byte) (crc8 >> 3);}
	 /******************************************************************************
	 * Name:    CRC-5/ITU           x5+x4+x2+1
	 * Poly:    0x15
	 * Init:    0x00
	 * Refin:   True
	 * Refout:  True
	 * Xorout:  0x00
	 * Note:
	 *****************************************************************************/
//	uint8_t crc8 = 0;                // Initial value
//	0x15 = (reverse 0x15)>>(8-5)
	public static byte crc5_itu(List<String> data, byte crc8,byte Polynomial){
	    int i;
	    int length=data.size();
	    for(int j=0;j<length;j++)
	    {
	    	crc8 ^= ByteConvertor.hexToByte(data.get(j));               // crc ^= *data; data++;
	        for (i = 0; i < 8; ++i)
	        {
	            if ((crc8 & 1)!=0)
	            	crc8 = (byte) ((crc8 >> 1) ^ 0x15);// 0x15 = (reverse 0x15)>>(8-5)
	            else
	            	crc8 = (byte) (crc8 >> 1);
	        }
	    }
	    return crc8;}
	 /******************************************************************************
	 * Name:    CRC-5/USB           x5+x2+1
	 * Poly:    0x05
	 * Init:    0x1F
	 * Refin:   True
	 * Refout:  True
	 * Xorout:  0x1F
	 * Note:
	 *****************************************************************************/
//	uint8_t crc8 = 0x1F;                // Initial value
// 0x14 = (reverse 0x05)>>(8-5)
//	return (byte) (crc8 ^ 0x1F);}
	public static byte crc5_usb(List<String> data, byte crc8,byte Polynomial ){
		int i;
	    
	    int length=data.size();
	    for(int j=0;j<length;j++)
	    {
	    	crc8 ^= ByteConvertor.hexToByte(data.get(j));                 // crc ^= *data; data++;
	        for (i = 0; i < 8; ++i)
	        {
	            if ((crc8 & 1)!=0)
	            	crc8 = (byte) ((crc8 >> 1) ^ 0x14);// 0x14 = (reverse 0x05)>>(8-5)
	            else
	            	crc8 = (byte) (crc8 >> 1);
	        }
	    }
	    return (byte) (crc8 & 0x1F);}
	 /******************************************************************************
	 * Name:    CRC-6/ITU           x6+x+1
	 * Poly:    0x03
	 * Init:    0x00
	 * Refin:   True
	 * Refout:  True
	 * Xorout:  0x00
	 * Note:
	 *****************************************************************************/
//	 uint8_t crc8 = 0;         // Initial value
//	 0x30 = (reverse 0x03)>>(8-6)
	public static byte crc6_itu(List<String> data, byte crc8,byte Polynomial){
		int i;
	   
	    int length=data.size();
	    for(int j=0;j<length;j++)
	    {
	    	crc8 ^= ByteConvertor.hexToByte(data.get(j));        // crc ^= *data; data++;
	        for (i = 0; i < 8; ++i)
	        {
	            if ((crc8 & 1)!=0)
	            	crc8 = (byte) ((crc8 >> 1) ^ 0x30);// 0x30 = (reverse 0x03)>>(8-6)
	            else
	            	crc8 = (byte) (crc8 >> 1);
	        }
	    }
	    return crc8;}
	 /******************************************************************************
	 * Name:    CRC-7/MMC           x7+x3+1
	 * Poly:    0x09
	 * Init:    0x00
	 * Refin:   False
	 * Refout:  False
	 * Xorout:  0x00
	 * Use:     MultiMediaCard,SD,ect.
	 *****************************************************************************/
//	uint8_t crc8 = 0;        // Initial value
//	 0x12 = 0x09<<(8-7)
	public static byte crc7_mmc(List<String> data, byte crc8,byte Polynomial){
		int i;
	    
	    int length=data.size();
	    for(int j=0;j<length;j++)
	    {
	    	crc8 ^= ByteConvertor.hexToByte(data.get(j));        // crc ^= *data; data++;
	        for ( i = 0; i < 8; i++ )
	        {
	            if (( crc8 & 0x80 )!=0)
	            	crc8 = (byte) ((crc8 << 1) ^ 0x12);        // 0x12 = 0x09<<(8-7)
	            else
	            	crc8 <<= 1;
	        }
	    }
	    return (byte) (crc8 >> 1);}	
	 /******************************************************************************
	 * Name:    CRC-8               x8+x2+x+1
	 * Poly:    0x07
	 * Init:    0x00
	 * Refin:   False
	 * Refout:  False
	 * Xorout:  0x00
	 * Note:
	 *****************************************************************************/
//    uint8_t crc8 = 0;        // Initial value
//	 0x07;
	public static int crc8(List<String> data, byte incrc8,byte Polynomial){
		int i;
		int crc8 = incrc8&0xFF;
	    int length=data.size();
	    for(int j=0;j<length;j++)
	    {
	    	int bytein =Integer.parseInt(data.get(j) ,16 );
	    	crc8 ^= bytein;        // crc ^= *data; data++;
	        for ( i = 0; i < 8; i++ )
	        {
	            if (( crc8 & 0x80 )!=0)
	            	crc8 =  (crc8 << 1) ^ Polynomial;
	            else
	            	crc8 <<= 1;
	        }
	    }
	    return crc8&0xFF;}
	 /******************************************************************************
	 * Name:    CRC-8/ITU           x8+x2+x+1
	 * Poly:    0x07
	 * Init:    0x00
	 * Refin:   False
	 * Refout:  False
	 * Xorout:  0x55
	 * Alias:   CRC-8/ATM
	 *****************************************************************************/
//    uint8_t crc8 = 0;        // Initial value
//	0x07;
//	return crc8 ^ 0x55;}
	public static int crc8_itu(List<String> data, byte incrc8,byte Polynomial){
		int i;
		int crc8 = incrc8&0xFF;
	    int length=data.size();
	    for(int j=0;j<length;j++)
	    {
	    	int bytein =Integer.parseInt(data.get(j) ,16 );
	    	crc8 ^= bytein;        // crc ^= *data; data++;
	        for ( i = 0; i < 8; i++ )
	        {
	            if (( crc8 & 0x80 )!=0)
	            	crc8 =  (crc8 << 1) ^ Polynomial;
	            else
	            	crc8 <<= 1;
	        }
	    }
	    return crc8&0xFF;}
	 /******************************************************************************
	 * Name:    CRC-8/ROHC          x8+x2+x+1
	 * Poly:    0x07
	 * Init:    0xFF
	 * Refin:   True
	 * Refout:  True
	 * Xorout:  0x00
	 * Note:
	 *****************************************************************************/
//    uint8_t crc8 = 0xFF;         // Initial value
	 // 0xE0 = reverse 0x07
	public static int crc8_rohc(List<String> data, byte incrc8,byte Polynomial){
		int i;
		int crc8 = incrc8&0xFF;
	    int length=data.size();
	    for(int j=0;j<length;j++)
	    {
	    	int bytein =Integer.parseInt(data.get(j) ,16 );
	    	crc8 ^= bytein;        // crc ^= *data; data++;
	        for (i = 0; i < 8; ++i)
	        {
	            if ((crc8 & 1)!=0)
	            	crc8 =  (crc8 >> 1) ^ ReverseByte2Int(Polynomial);        // 0xE0 = reverse 0x07
	            else
	            	crc8 =  (crc8 >> 1);
	        }
	    }
	    return crc8&0xFF;}
	 /******************************************************************************
	 * Name:    CRC-8/MAXIM         x8+x5+x4+1
	 * Poly:    0x31
	 * Init:    0x00
	 * Refin:   True
	 * Refout:  True
	 * Xorout:  0x00
	 * Alias:   DOW-CRC,CRC-8/IBUTTON
	 * Use:     Maxim(Dallas)'s some devices,e.g. DS18B20
	 *****************************************************************************/
//	uint8_t crc8 = 0;         // Initial value
	// 0x8C = reverse 0x31
	public static int crc8_maxim(List<String> data, byte incrc8,byte Polynomial){
		int i;
		int crc8 = incrc8&0xFF;
	    int length=data.size();
	    for(int j=0;j<length;j++)
	    {
	    	int bytein =Integer.parseInt(data.get(j) ,16 );
	    	crc8 ^= bytein;        // crc ^= *data; data++;
	        for (i = 0; i < 8; i++)
	        {
	            if ((crc8 & 1)!=0)
	            	crc8 =  (crc8 >> 1) ^ ReverseByte2Int(Polynomial);        // 0x8C = reverse 0x31
	            else
	            	crc8 >>= 1;
	        }
	    }
	    return crc8&0xFF;}
	 /******************************************************************************
	 * Name:    CRC-16/IBM          x16+x15+x2+1
	 * Poly:    0x8005
	 * Init:    0x0000
	 * Refin:   True
	 * Refout:  True
	 * Xorout:  0x0000
	 * Alias:   CRC-16,CRC-16/ARC,CRC-16/LHA
	 *****************************************************************************/
//    uint16_t crc16 = 0;        // Initial value
    // 0xA001 = reverse 0x8005
	public static int crc16_ibm(List<String> data, int crc16,int Polynomial){
		int i;

	    int length=data.size();
	    for(int j=0;j<length;j++)
	    {
	    	int bytein =Integer.parseInt(data.get(j) ,16 );
	    	crc16 ^= bytein;        // crc ^= *data; data++;
	    	crc16 &=VALUE_0XFFFF;
	        for (i = 0; i < 8; ++i)
	        {
	            if ((crc16 & 1)!=0)
	            	crc16 = (crc16 >> 1) ^ ReverseShort(Polynomial);        // 0xA001 = reverse 0x8005
	            else
	            	crc16 = (crc16 >> 1);
	        }
	    }
	    return crc16&VALUE_0XFFFF;}
	 /******************************************************************************
	 * Name:    CRC-16/MAXIM        x16+x15+x2+1
	 * Poly:    0x8005
	 * Init:    0x0000
	 * Refin:   True
	 * Refout:  True
	 * Xorout:  0xFFFF
	 * Note:
	 *****************************************************************************/
//    uint16_t crc16 = 0;        // Initial value
    // 0xA001 = reverse 0x8005
//	return ~crc16;    // crc^0xffff
	public static int crc16_maxim(List<String> data, int crc16,int Polynomial){
		int i;

	    int length=data.size();
	    for(int j=0;j<length;j++)
	    {
	    	int bytein =Integer.parseInt(data.get(j) ,16 );
	    	crc16 ^= bytein;        // crc ^= *data; data++;
	    	crc16 &=VALUE_0XFFFF;
	        for (i = 0; i < 8; ++i)
	        {
	            if ((crc16 & 1)!=0)
	            	crc16 = (crc16 >> 1) ^ ReverseShort(Polynomial);
	            else
	            	crc16 = (crc16 >> 1);
	        }
	    }
	    return crc16&VALUE_0XFFFF;    // crc^0xffff
	    }
	 /******************************************************************************
	 * Name:    CRC-16/USB          x16+x15+x2+1
	 * Poly:    0x8005
	 * Init:    0xFFFF
	 * Refin:   True
	 * Refout:  True
	 * Xorout:  0xFFFF
	 * Note:
	 *****************************************************************************/
//    uint16_t crc16 = 0xffff;        // Initial value
	// 0xA001 = reverse 0x8005
//	return ~crc16;    // crc^0xffff
	public static int crc16_usb(List<String> data, int crc16,int Polynomial){
		int i;

	    int length=data.size();
	    for(int j=0;j<length;j++)
	    {
	    	int bytein =Integer.parseInt(data.get(j) ,16 );
	    	crc16 ^= bytein;        // crc ^= *data; data++;
	    	crc16 &=VALUE_0XFFFF;
	        for (i = 0; i < 8; ++i)
	        {
	            if ((crc16 & 1)!=0)
	            	crc16 = (crc16 >> 1) ^ ReverseShort(Polynomial);        // 0xA001 = reverse 0x8005
	            else
	            	crc16 = (crc16 >> 1);
	        }
	    }
	    return crc16&VALUE_0XFFFF;    // crc^0xffff
	    }
	 /******************************************************************************
	 * Name:    CRC-16/MODBUS       x16+x15+x2+1
	 * Poly:    0x8005
	 * Init:    0xFFFF
	 * Refin:   True
	 * Refout:  True
	 * Xorout:  0x0000
	 * Note:
	 *****************************************************************************/
	public static int crc16_modbus(List<String> data, int crc16,int Polynomial){
		int i;
//	    uint16_t crc = 0xffff;        // Initial value
	    int length=data.size();
	    for(int j=0;j<length;j++)
	    {
	    	int bytein =Integer.parseInt(data.get(j) ,16 );
	    	crc16 ^= bytein;        // crc ^= *data; data++;
	    	crc16 &=VALUE_0XFFFF;
	        for (i = 0; i < 8; ++i)
	        {
	            if ((crc16 & 1)!=0)
	            	crc16 = (crc16 >> 1) ^ ReverseShort(Polynomial);        // 0xA001 = reverse 0x8005
	            else
	            	crc16 = (crc16 >> 1);
	        }
	    }
	    return crc16&VALUE_0XFFFF;}
	 /******************************************************************************
	 * Name:    CRC-16/CCITT        x16+x12+x5+1
	 * Poly:    0x1021
	 * Init:    0x0000
	 * Refin:   True
	 * Refout:  True
	 * Xorout:  0x0000
	 * Alias:   CRC-CCITT,CRC-16/CCITT-TRUE,CRC-16/KERMIT
	 *****************************************************************************/
//    uint16_t crc16 = 0;        // Initial value
    // 0x8408 = reverse 0x1021
//	0x1021
	public static int crc16_ccitt(List<String> data, int crc16,int Polynomial){
		int i;

	    int length=data.size();
	    for(int j=0;j<length;j++)
	    {
	    	int bytein =Integer.parseInt(data.get(j) ,16 );
	    	crc16 ^= bytein;        // crc ^= *data; data++;
	    	crc16 &=VALUE_0XFFFF;
	        for (i = 0; i < 8; ++i)
	        {
	            if ((crc16 & 1)!=0)
	            	crc16 = (crc16 >> 1) ^ ReverseShort(Polynomial);        // 0x8408 = reverse 0x1021
	            else
	            	crc16 = (crc16 >> 1);
	        }
	    }
	    return crc16&VALUE_0XFFFF;}
	 /******************************************************************************
	 * Name:    CRC-16/CCITT-FALSE   x16+x12+x5+1
	 * Poly:    0x1021
	 * Init:    0xFFFF
	 * Refin:   False
	 * Refout:  False
	 * Xorout:  0x0000
	 * Note:
	 *****************************************************************************/
//    uint16_t crc16 = 0xffff;        //Initial value
	public static int crc16_ccitt_false(List<String> data, int crc16,int Polynomial){
		int i;

	    int length=data.size();
	    for(int j=0;j<length;j++)
	    {
	    	int bytein =Integer.parseInt(data.get(j) ,16 );
	    	crc16 ^= bytein<<8;        // // crc ^= (uint6_t)(*data)<<8; data++;
	    	crc16 &=VALUE_0XFFFF;	    	
	        for (i = 0; i < 8; ++i)
	        {
	            if (( crc16 & 0x8000 )!=0)
	            	crc16 = (crc16 << 1) ^ Polynomial;
	            else
	            	crc16 <<= 1;
	        }
	    }
	    return crc16&VALUE_0XFFFF;}
	 /******************************************************************************
	 * Name:    CRC-16/X25          x16+x12+x5+1
	 * Poly:    0x1021
	 * Init:    0xFFFF
	 * Refin:   True
	 * Refout:  True
	 * Xorout:  0XFFFF
	 * Note:
	 *****************************************************************************/
//    uint16_t crc16 = 0xffff;        // Initial value
    // 0x8408 = reverse 0x1021
//	return ~crc16;                // crc^Xorout
	public static int crc16_x25(List<String> data, int crc16,int Polynomial){
		int i;

	    int length=data.size();
	    for(int j=0;j<length;j++)
	    {
	    	int bytein =Integer.parseInt(data.get(j) ,16 );
	    	crc16 ^= bytein;        // crc ^= *data; data++;
	    	crc16 &=VALUE_0XFFFF;
	        for (i = 0; i < 8; ++i)
	        {
	            if ((crc16 & 1)!=0)
	            	crc16 = (crc16 >> 1) ^ ReverseShort(Polynomial);        // 0x8408 = reverse 0x1021
	            else
	            	crc16 = (crc16 >> 1);
	        }
	    }
	    return crc16&VALUE_0XFFFF;                // crc^Xorout
	    }
	 /******************************************************************************
	 * Name:    CRC-16/XMODEM       x16+x12+x5+1
	 * Poly:    0x1021
	 * Init:    0x0000
	 * Refin:   False
	 * Refout:  False
	 * Xorout:  0x0000
	 * Alias:   CRC-16/ZMODEM,CRC-16/ACORN
	 *****************************************************************************/
//    uint16_t crc16 = 0;            // Initial value
//	0x1021
	public static int crc16_xmodem(List<String> data, int crc16,int Polynomial){
		int i;

	    int length=data.size();
	    for(int j=0;j<length;j++)
	    {
	    	int bytein =Integer.parseInt(data.get(j) ,16 );
	    	crc16 ^= bytein<<8;        // // crc ^= (uint6_t)(*data)<<8; data++;
	    	crc16 &=VALUE_0XFFFF;	
	        for (i = 0; i < 8; ++i)
	        {
	            if (( crc16 & 0x8000 )!=0)
	            	crc16 = (crc16 << 1) ^ Polynomial;
	            else
	            	crc16 <<= 1;
	        }
	    }
	    return crc16&VALUE_0XFFFF;}
	 /******************************************************************************
	 * Name:    CRC-16/DNP          x16+x13+x12+x11+x10+x8+x6+x5+x2+1
	 * Poly:    0x3D65
	 * Init:    0x0000
	 * Refin:   True
	 * Refout:  True
	 * Xorout:  0xFFFF
	 * Use:     M-Bus,ect.
	 *****************************************************************************/
//    uint16_t crc16 = 0;            // Initial value
	// 0xA6BC = reverse 0x3D65
//	 return ~crc16;                // crc^Xorout
	public static int crc16_dnp(List<String> data, int crc16,int Polynomial){
		int i;

	    int length=data.size();
	    for(int j=0;j<length;j++)
	    {
	    	int bytein =Integer.parseInt(data.get(j) ,16 );
	    	crc16 ^= bytein;        // crc ^= *data; data++;
	    	crc16 &=VALUE_0XFFFF;
	        for (i = 0; i < 8; ++i)
	        {
	            if ((crc16 & 1)!=0)
	            	crc16 = (crc16 >> 1) ^ ReverseShort(Polynomial);        // 0xA6BC = reverse 0x3D65
	            else
	            	crc16 = (crc16 >> 1);
	        }
	    }
	    return crc16&VALUE_0XFFFF;                // crc^Xorout
	    }
	 /******************************************************************************
	 * Name:    CRC-32  x32+x26+x23+x22+x16+x12+x11+x10+x8+x7+x5+x4+x2+x+1
	 * Poly:    0x4C11DB7
	 * Init:    0xFFFFFFF
	 * Refin:   True
	 * Refout:  True
	 * Xorout:  0xFFFFFFF
	 * Alias:   CRC_32/ADCCP
	 * Use:     WinRAR,ect.
	 *****************************************************************************/
//	uint32_t crc32 = 0xffffffff;        // Initial value
// 0xEDB88320= reverse 0x04C11DB7
//    return ~crc32;}
	public static long crc32(List<String> data, long crc32,long Polynomial){
		int i;
	    
	    int length=data.size();
	    for(int j=0;j<length;j++)
	    {
	    	int bytein =Integer.parseInt(data.get(j) ,16 );
	    	crc32 ^=bytein ;               // crc ^= *data; data++;
	    	crc32=crc32&VALUE_0XFFFFFFFF;
	        for (i = 0; i < 8; ++i)
	        {
	            if ((crc32 & 1)!=0)
	            	crc32 = (crc32 >> 1) ^ ReverseLong(Polynomial);// 0xEDB88320= reverse 0x04C11DB7
	            else
	            	crc32 = (crc32 >> 1);
	        }
	    }
	    return crc32&VALUE_0XFFFFFFFF;}
	 /******************************************************************************
	 * Name:    CRC-32/MPEG-2  x32+x26+x23+x22+x16+x12+x11+x10+x8+x7+x5+x4+x2+x+1
	 * Poly:    0x4C11DB7
	 * Init:    0xFFFFFFF
	 * Refin:   False
	 * Refout:  False
	 * Xorout:  0x0000000
	 * Note:
	 *****************************************************************************/
//    uint32_t crc32 = 0xffffffff;  // Initial value
//	  0x04C11DB7;
	public static long crc32_mpeg_2(List<String> data, long crc32,long Polynomial){
		int i;

	    int length=data.size();
	    for(int j=0;j<length;j++)
	    {
	    	long bytein =Long.parseLong(data.get(j) ,16 );	    	
	    	crc32 ^=(bytein<<24) ;               // crc ^=(uint32_t)(*data)<<24; data++;
	    	crc32=crc32&VALUE_0XFFFFFFFF;
	        for (i = 0; i < 8; ++i)
	        {
	            if (( crc32 & VALUE_0X80000000 )!=0)
	            	crc32 = (crc32 << 1) ^ Polynomial;
	            else
	            	crc32 <<= 1;
	        }
	    }
	    return crc32&VALUE_0XFFFFFFFF;}

	public static long crc32_user(List<String> data, long crc32,long Polynomial,boolean isinrev){
		int i;

	    int length=data.size();
	    for(int j=0;j<length;j++)
	    {
	    	long bytein =Long.parseLong(data.get(j) ,16 );	  ////	bytein = ReverseLongByte(bytein);
	    	if(isinrev)	    	
	    		bytein = ReverseLongByte(bytein);
	    	crc32 ^=bytein ;       	
  
	    	crc32=crc32&VALUE_0XFFFFFFFF;
	        for (i = 0; i < 8; ++i)
	        {	
		            if ((crc32 & 1)!=0)
		            	crc32 = (crc32 >> 1) ^ ReverseLong(Polynomial);// 0xEDB88320= reverse 0x04C11DB7
		            else
		            	crc32 = (crc32 >> 1);	        	
	        }
	    }

	    return crc32&VALUE_0XFFFFFFFF;}

//@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
// 表的java语言的生成方法
//				Long value;
//				Long CRCMASK=Long.parseLong("edb88320",16);  // 非 0xedb88320 ,不然后变为-，并后面也计算错误的
//				int i,j;
//				for(i=0;i<256;i++)
//				{
//					value=(long) i;
//					for(j=0;j<8;j++)
//					{
//						if(value%2==1)
//						{
//							value>>=1;
//							value ^= CRCMASK;
//						}
//						else
//						{
//							value>>=1;
//						}
//					}
//					if(i%32==0)
//						System.out.print("\n");
//					String  outv=Long.toHexString(value);
//					outv="000000000"+outv;
//					outv=outv.substring(outv.length()-8);
//					System.out.print("0x"+outv+", ");
//					if((i+1)%4==0)
//						System.out.print("\n");
//					//-------------------
//				}
	//@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
	//static const unsigned int crc32table[256] ={
//			0x00000000, 0x06233697, 0x05c45641, 0x03e760d6,
//			0x020a97ed, 0x0429a17a, 0x07cec1ac, 0x01edf73b,
//			0x04152fda, 0x0236194d, 0x01d1799b, 0x07f24f0c,
//			0x061fb837, 0x003c8ea0, 0x03dbee76, 0x05f8d8e1,
//			0x01a864db, 0x078b524c, 0x046c329a, 0x024f040d,
//			0x03a2f336, 0x0581c5a1, 0x0666a577, 0x004593e0,
//			0x05bd4b01, 0x039e7d96, 0x00791d40, 0x065a2bd7,
//			0x07b7dcec, 0x0194ea7b, 0x02738aad, 0x0450bc3a,
	//
//			0x0350c9b6, 0x0573ff21, 0x06949ff7, 0x00b7a960,
//			0x015a5e5b, 0x077968cc, 0x049e081a, 0x02bd3e8d,
//			0x0745e66c, 0x0166d0fb, 0x0281b02d, 0x04a286ba,
//			0x054f7181, 0x036c4716, 0x008b27c0, 0x06a81157,
//			0x02f8ad6d, 0x04db9bfa, 0x073cfb2c, 0x011fcdbb,
//			0x00f23a80, 0x06d10c17, 0x05366cc1, 0x03155a56,
//			0x06ed82b7, 0x00ceb420, 0x0329d4f6, 0x050ae261,
//			0x04e7155a, 0x02c423cd, 0x0123431b, 0x0700758c,
	//
//			0x06a1936c, 0x0082a5fb, 0x0365c52d, 0x0546f3ba,
//			0x04ab0481, 0x02883216, 0x016f52c0, 0x074c6457,
//			0x02b4bcb6, 0x04978a21, 0x0770eaf7, 0x0153dc60,
//			0x00be2b5b, 0x069d1dcc, 0x057a7d1a, 0x03594b8d,
//			0x0709f7b7, 0x012ac120, 0x02cda1f6, 0x04ee9761,
//			0x0503605a, 0x032056cd, 0x00c7361b, 0x06e4008c,
//			0x031cd86d, 0x053feefa, 0x06d88e2c, 0x00fbb8bb,
//			0x01164f80, 0x07357917, 0x04d219c1, 0x02f12f56,
	//
//			0x05f15ada, 0x03d26c4d, 0x00350c9b, 0x06163a0c,
//			0x07fbcd37, 0x01d8fba0, 0x023f9b76, 0x041cade1,
//			0x01e47500, 0x07c74397, 0x04202341, 0x020315d6,
//			0x03eee2ed, 0x05cdd47a, 0x062ab4ac, 0x0009823b,
//			0x04593e01, 0x027a0896, 0x019d6840, 0x07be5ed7,
//			0x0653a9ec, 0x00709f7b, 0x0397ffad, 0x05b4c93a,
//			0x004c11db, 0x066f274c, 0x0588479a, 0x03ab710d,
//			0x02468636, 0x0465b0a1, 0x0782d077, 0x01a1e6e0,
	//
//			0x04c11db7, 0x02e22b20, 0x01054bf6, 0x07267d61,
//			0x06cb8a5a, 0x00e8bccd, 0x030fdc1b, 0x052cea8c,
//			0x00d4326d, 0x06f704fa, 0x0510642c, 0x033352bb,
//			0x02dea580, 0x04fd9317, 0x071af3c1, 0x0139c556,
//			0x0569796c, 0x034a4ffb, 0x00ad2f2d, 0x068e19ba,
//			0x0763ee81, 0x0140d816, 0x02a7b8c0, 0x04848e57,
//			0x017c56b6, 0x075f6021, 0x04b800f7, 0x029b3660,
//			0x0376c15b, 0x0555f7cc, 0x06b2971a, 0x0091a18d,
	//
//			0x0791d401, 0x01b2e296, 0x02558240, 0x0476b4d7,
//			0x059b43ec, 0x03b8757b, 0x005f15ad, 0x067c233a,
//			0x0384fbdb, 0x05a7cd4c, 0x0640ad9a, 0x00639b0d,
//			0x018e6c36, 0x07ad5aa1, 0x044a3a77, 0x02690ce0,
//			0x0639b0da, 0x001a864d, 0x03fde69b, 0x05ded00c,
//			0x04332737, 0x021011a0, 0x01f77176, 0x07d447e1,
//			0x022c9f00, 0x040fa997, 0x07e8c941, 0x01cbffd6,
//			0x002608ed, 0x06053e7a, 0x05e25eac, 0x03c1683b,
	//
//			0x02608edb, 0x0443b84c, 0x07a4d89a, 0x0187ee0d,
//			0x006a1936, 0x06492fa1, 0x05ae4f77, 0x038d79e0,
//			0x0675a101, 0x00569796, 0x03b1f740, 0x0592c1d7,
//			0x047f36ec, 0x025c007b, 0x01bb60ad, 0x0798563a,
//			0x03c8ea00, 0x05ebdc97, 0x060cbc41, 0x002f8ad6,
//			0x01c27ded, 0x07e14b7a, 0x04062bac, 0x02251d3b,
//			0x07ddc5da, 0x01fef34d, 0x0219939b, 0x043aa50c,
//			0x05d75237, 0x03f464a0, 0x00130476, 0x063032e1,
	//
//			0x0130476d, 0x071371fa, 0x04f4112c, 0x02d727bb,
//			0x033ad080, 0x0519e617, 0x06fe86c1, 0x00ddb056,
//			0x052568b7, 0x03065e20, 0x00e13ef6, 0x06c20861,
//			0x072fff5a, 0x010cc9cd, 0x02eba91b, 0x04c89f8c,
//			0x009823b6, 0x06bb1521, 0x055c75f7, 0x037f4360,
//			0x0292b45b, 0x04b182cc, 0x0756e21a, 0x0175d48d,
//			0x048d0c6c, 0x02ae3afb, 0x01495a2d, 0x076a6cba,
//			0x06879b81, 0x00a4ad16, 0x0343cdc0, 0x0560fb57,
	//};
	//unsigned int hash;
	//unsigned int newhash;
	//unsigned int hash_CRC32(unsigned char *data,unsigned int length)
	//{
//		unsigned int i;
//		hash=0xFFFFFFFF;
//		for(i=0;i<length;i++)
//		{
//			hash=(hash>>8)^crc32table[ (hash&0xFF)^data[i] ];
//		}
//		return hash;
	//}
	//unsigned int hash_CRC32_png(unsigned char *data,unsigned int length)
	//{
//		unsigned int i;
//		hash=0xFFFFFFFF;
//		for(i=0;i<length;i++)
//		{
//			hash=(hash>>8)^crc32table[ (hash&0xFF)^data[i] ];
//		}
//		return ~hash;
	//}
	
	
	
	
	

}

