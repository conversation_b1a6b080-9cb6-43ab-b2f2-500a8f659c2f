package com.chipon32.hex.core;

import java.awt.Button;
import java.util.ArrayList;
import java.util.List;

import com.chipon32.hex.core.util.ByteConvertor;

/**
 * 仅针对Hex的Flash 和 EEdata 存在该对象，特性信息不予容纳
 * <AUTHOR>
 *
 */
public class SectorElementVaule implements Cloneable
{	
	private int numVaule;		// 扇标号                               	基本信息		0-63
	private long address; 		// 扇的映射地址			1K 2K 3K 4K
	private int slen;			// 扇的内容数量	        1K 
	
//	private boolean isread;		// 操作性   				 来自用户意图
//	private boolean iswrite;	// 操作性
	
	private boolean iscanread;	// 操作性限制  			来自芯片
	private boolean iscanwrite;	// 操作性限制
	
//	private Button  isReadButton;
	private Button  isCanReadButton;	// 视图控制显示的按钮
//	private Button  isWrtieButton;
	private Button  isCanWriteButton;
	//==========================================================================
	private List<AddrVauleData> content;
	
	
	//==========================================================================
	// 带目标指令的构建
	public SectorElementVaule(int numVaule, long address, int len, String vaule) {
		this.numVaule = numVaule;
		this.address = address;
		this.slen = len;
		content=new ArrayList<AddrVauleData>();
		for(int i=0;i<slen;i++)
		{
			AddrVauleData buf=new AddrVauleData(vaule);
			buf.setAddress(address+i);
			content.add(buf);
		}
		//#######################
		iscanread=true;
//		isread=true;
		iscanwrite=true;
//		iswrite=true;
	}
	//==========================================================================
//	public Button getIsReadButton()
//	{
//		return isReadButton;
//	}
	public Button getIsCanReadButton()
	{
		return isCanReadButton;
	}	
//	public Button getIsWriteButton()
//	{
//		return isWrtieButton;
//	}	
	public Button getIsCanWtiteButton()
	{
		return isCanWriteButton;
	}
	
	//==========================================================================
	public int getNum() {
		return numVaule;
	}
	public void setNum(int num) {
		this.numVaule = num;
	}
	public int getLen() {
		return slen;
	}
	public void setLen(int len) {
		this.slen = len;
	}	
	public long getAddress() {
		return address;
	}
	public void setAddress(int address) {
		this.address = address;
	}
	public String getAddressHexValue()
	{
		return ByteConvertor.toEight(Long.toString(address, 16));
	}	
	//--------------------------------
	public List<AddrVauleData> getContent() {
		return content;
	}
	public void setContent(List<AddrVauleData> lst) {
		this.content = lst;
	}
	public void setAddrVauleData(int num, AddrVauleData v)
	{
		content.get(num).setAddress(v.getAddress());
		content.get(num).setVaule(v.getVaule());
	}
	//--------------------------------
//	public boolean getIsRead() {
//		return isread;
//	}
//	public void setIsRead(boolean truefalse) {
//		this.isread = truefalse;
//	}
//	public boolean getIsWrite() {
//		return iswrite;
//	}
//	public void setIsWrite(boolean truefalse) {
//		this.iswrite = truefalse;
//	}
	public boolean getIsCanRead() {
		return iscanread;
	}
	public void setIsCanRead(boolean truefalse) {
		this.iscanread = truefalse;
	}
	public boolean getIsCanWrite() {
		return iscanwrite;
	}
	public void setIsCanWrite(boolean truefalse) {
		this.iscanwrite = truefalse;
	}
	//==========================================================================
	public String Get_SigCode_Empty(int chiprange){
		// long型8字节宽，但不能识别高位为1的字符串转换，因此定义long型
		// 但按int长度有效，校验和的128bit，拆分为4个int的long型变量
		long CheckSumH=0,CheckSumHH=0,CheckSumL=0,CheckSumLL=0;	
		
		long NextCheckSumH,NextCheckSumHH,NextCheckSumL,NextCheckSumLL;	 //

		long FlashVauleH,FlashVauleHH,FlashVauleL,FlashVauleLL;			
		
		long BufWordH,BufWordHH,BufWordL,BufWordLL;	 //
		//+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
			int lencout = slen / 16 ; // 16字节的运算
			for(int i=0;i<lencout;i++)
			{
				//-----------------------------
				FlashVauleLL =Long.parseLong(("FF"+""),16);	
				FlashVauleLL+=Long.parseLong(("FF"+"00"),16);	
				FlashVauleLL+=Long.parseLong(("FF"+"0000"),16);	
				FlashVauleLL+=Long.parseLong(("FF"+"000000"),16);	
				
				FlashVauleL =Long.parseLong(("FF"+""),16);	
				FlashVauleL+=Long.parseLong(("FF"+"00"),16);	
				FlashVauleL+=Long.parseLong(("FF"+"0000"),16);	
				FlashVauleL+=Long.parseLong(("FF"+"000000"),16);	
				
				FlashVauleH =Long.parseLong(("FF"+""),16);	
				FlashVauleH+=Long.parseLong(("FF"+"00"),16);	
				FlashVauleH+=Long.parseLong(("FF"+"0000"),16);	
				FlashVauleH+=Long.parseLong(("FF"+"000000"),16);	
				
				FlashVauleHH =Long.parseLong(("FF"+""),16);	
				FlashVauleHH+=Long.parseLong(("FF"+"00"),16);	
				FlashVauleHH+=Long.parseLong(("FF"+"0000"),16);	
				FlashVauleHH+=Long.parseLong(("FF"+"000000"),16);	
				//-----------------------------
				BufWordLL=CheckSumLL/2;			
				if((CheckSumL & 0x0001)>0)
					BufWordLL |= 0x80000000l; // 显式long型，否则int转long会是-80000000
				
				BufWordL=CheckSumL/2;			
				if((CheckSumH&0x0001)>0 && chiprange !=4)   // 11K 区分
					BufWordL |=0x80000000l; // 显式long型，否则int转long会是-80000000
				
				BufWordH=CheckSumH/2;			
				if((CheckSumHH&0x0001)>0)
					BufWordH |=0x80000000l; // 显式long型，否则int转long会是-80000000
				
				BufWordHH=CheckSumHH/2;		 // 显式long型，否则int转long会是-80000000				
				//-----------------------------	//BufWordHH 最高位注定为0，直接异或，即保留flash的bit127的运算	
				NextCheckSumHH	=FlashVauleHH	^	BufWordHH;
				NextCheckSumH	=FlashVauleH	^	BufWordH;
				NextCheckSumL	=FlashVauleL	^	BufWordL;
				NextCheckSumLL	=FlashVauleLL	^	BufWordLL;
				//@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
				//----------------------------- // 9K 9K1  sum 的  0 2 27 29位的异或结果  127  11K 异或结果63
				int buf=0,buf2=0;			
				//+++++++++++++++++++++++++++++++++++++++
				if((CheckSumLL	&(1<<0))!=0)  // 0   LL[0-31]  L[63-32] H[95-64] HH[128-96]
					buf2++;
				if((CheckSumLL	&(1<<2))!=0)  // 2
					buf2++;
				if((CheckSumLL	&(1<<27))!=0)  // 27
					buf2++;
				if((CheckSumLL	&(1<<29))!=0)  // 29
					buf2++;			
				//+++++++++++++++++++++++++++++++++++++++
				if(chiprange==4) {
					
					if((CheckSumH	&(1<<0))!=0)  // 0   LL[0-31]  L[63-32] H[95-64] HH[128-96]
						buf++;
					if((CheckSumH	&(1<<2))!=0)  // 2
						buf++;
					if((CheckSumH	&(1<<27))!=0)  // 27
						buf++;
					if((CheckSumH	&(1<<29))!=0)  // 29
						buf++;		
					
					if(  (buf2 %2)	==1)		{
						NextCheckSumL ^=0x80000000l; //
					}
					buf2= buf;					
				}
				//+++++++++++++++++++++++++++++++++++++++
				if(buf2%2==1)			{
					NextCheckSumHH ^=0x80000000l; //
				}				
				//@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
				CheckSumHH	=(NextCheckSumHH	&0xFFFFFFFF); 
				CheckSumH	=(NextCheckSumH		&0xFFFFFFFF);
				CheckSumL	=(NextCheckSumL		&0xFFFFFFFF);
				CheckSumLL	=(NextCheckSumLL	&0xFFFFFFFF);
			}			
			//+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
		// 获取数据的内容结果		
		return 	ByteConvertor.toEight(Long.toHexString(CheckSumHH))
				+	ByteConvertor.toEight(Long.toHexString(CheckSumH))
				+	ByteConvertor.toEight(Long.toHexString(CheckSumL))
				+	ByteConvertor.toEight(Long.toHexString(CheckSumLL));
	}
	
	public String Get_SigCode(int chiprange){
		// long型8字节宽，但不能识别高位为1的字符串转换，因此定义long型
		// 但按int长度有效，校验和的128bit，拆分为4个int的long型变量
		long CheckSumH=0,CheckSumHH=0,CheckSumL=0,CheckSumLL=0;	
		
		long NextCheckSumH,NextCheckSumHH,NextCheckSumL,NextCheckSumLL;	 //

		long FlashVauleH,FlashVauleHH,FlashVauleL,FlashVauleLL;			
		
		long BufWordH,BufWordHH,BufWordL,BufWordLL;	 //
		//+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
			int lencout = slen / 16 ; // 16字节的运算
			for(int i=0;i<lencout;i++)
			{
				//-----------------------------
				FlashVauleLL =Long.parseLong((content.get(i*16+0).getVaule()+""),16);	
				FlashVauleLL+=Long.parseLong((content.get(i*16+1).getVaule()+"00"),16);	
				FlashVauleLL+=Long.parseLong((content.get(i*16+2).getVaule()+"0000"),16);	
				FlashVauleLL+=Long.parseLong((content.get(i*16+3).getVaule()+"000000"),16);	
				
				FlashVauleL =Long.parseLong((content.get(i*16+4).getVaule()+""),16);	
				FlashVauleL+=Long.parseLong((content.get(i*16+5).getVaule()+"00"),16);	
				FlashVauleL+=Long.parseLong((content.get(i*16+6).getVaule()+"0000"),16);	
				FlashVauleL+=Long.parseLong((content.get(i*16+7).getVaule()+"000000"),16);	
				
				FlashVauleH =Long.parseLong((content.get(i*16+8).getVaule()+""),16);	
				FlashVauleH+=Long.parseLong((content.get(i*16+9).getVaule()+"00"),16);	
				FlashVauleH+=Long.parseLong((content.get(i*16+10).getVaule()+"0000"),16);	
				FlashVauleH+=Long.parseLong((content.get(i*16+11).getVaule()+"000000"),16);	
				
				FlashVauleHH =Long.parseLong((content.get(i*16+12).getVaule()+""),16);	
				FlashVauleHH+=Long.parseLong((content.get(i*16+13).getVaule()+"00"),16);	
				FlashVauleHH+=Long.parseLong((content.get(i*16+14).getVaule()+"0000"),16);	
				FlashVauleHH+=Long.parseLong((content.get(i*16+15).getVaule()+"000000"),16);	
				//-----------------------------
				BufWordLL=CheckSumLL/2;			
				if((CheckSumL & 0x0001)>0)
					BufWordLL |= 0x80000000l; // 显式long型，否则int转long会是-80000000
				
				BufWordL=CheckSumL/2;			
				if((CheckSumH&0x0001)>0 && chiprange !=4)   // 11K 区分
					BufWordL |=0x80000000l; // 显式long型，否则int转long会是-80000000
				
				BufWordH=CheckSumH/2;			
				if((CheckSumHH&0x0001)>0)
					BufWordH |=0x80000000l; // 显式long型，否则int转long会是-80000000
				
				BufWordHH=CheckSumHH/2;		 // 显式long型，否则int转long会是-80000000				
				//-----------------------------	//BufWordHH 最高位注定为0，直接异或，即保留flash的bit127的运算	
				NextCheckSumHH	=FlashVauleHH	^	BufWordHH;
				NextCheckSumH	=FlashVauleH	^	BufWordH;
				NextCheckSumL	=FlashVauleL	^	BufWordL;
				NextCheckSumLL	=FlashVauleLL	^	BufWordLL;
				//@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
				//----------------------------- // 9K 9K1  sum 的  0 2 27 29位的异或结果  127  11K 异或结果63
				int buf=0,buf2=0;			
				//+++++++++++++++++++++++++++++++++++++++
				if((CheckSumLL	&(1<<0))!=0)  // 0   LL[0-31]  L[63-32] H[95-64] HH[128-96]
					buf2++;
				if((CheckSumLL	&(1<<2))!=0)  // 2
					buf2++;
				if((CheckSumLL	&(1<<27))!=0)  // 27
					buf2++;
				if((CheckSumLL	&(1<<29))!=0)  // 29
					buf2++;			
				//+++++++++++++++++++++++++++++++++++++++
				if(chiprange==4) {
					
					if((CheckSumH	&(1<<0))!=0)  // 0   LL[0-31]  L[63-32] H[95-64] HH[128-96]
						buf++;
					if((CheckSumH	&(1<<2))!=0)  // 2
						buf++;
					if((CheckSumH	&(1<<27))!=0)  // 27
						buf++;
					if((CheckSumH	&(1<<29))!=0)  // 29
						buf++;		
					
					if(  (buf2 %2)	==1)		{
						NextCheckSumL ^=0x80000000l; //
					}
					buf2= buf;					
				}
				//+++++++++++++++++++++++++++++++++++++++
				if(buf2%2==1)			{
					NextCheckSumHH ^=0x80000000l; //
				}				
				//@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
				CheckSumHH	=(NextCheckSumHH	&0xFFFFFFFF); 
				CheckSumH	=(NextCheckSumH		&0xFFFFFFFF);
				CheckSumL	=(NextCheckSumL		&0xFFFFFFFF);
				CheckSumLL	=(NextCheckSumLL	&0xFFFFFFFF);
			}			
			//+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
		// 获取数据的内容结果		
		return 	ByteConvertor.toEight(Long.toHexString(CheckSumHH))
				+	ByteConvertor.toEight(Long.toHexString(CheckSumH))
				+	ByteConvertor.toEight(Long.toHexString(CheckSumL))
				+	ByteConvertor.toEight(Long.toHexString(CheckSumLL));
	}
	//==========================================================================
	// 类似一个对象一个物理地址，构建对象时直接拿物理地址作为结果的存贮空间，如果空间相同，
	// 才调用equals方法，不然不能重复的每建立一个对象都要判断equals方法，效率很差
	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + numVaule;
		result = (int) (prime * result + address);
		return result;
	}
	// 针对的对象判断相等的比较
	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		// 主要比较就是地址 指令要相同就表示相同
		SectorElementVaule other = (SectorElementVaule) obj;
		if (address != other.address)
			return false;
		if (numVaule != other.numVaule)
			return false;		
		if (slen != other.slen)
			return false;			
				
		return true;
	}
}
