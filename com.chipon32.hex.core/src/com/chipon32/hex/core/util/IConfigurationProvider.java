package com.chipon32.hex.core.util;

import java.util.List;
import java.util.Map;

import com.chipon32.hex.core.ConfigMessage;
import com.chipon32.hex.core.model.DeviceModel;


/**
 * <AUTHOR> 配置信息提供器接口
 *
 */
public interface IConfigurationProvider {
	// 必须声明字符串，否则方法失效，但传参数时 采用直接字符串传递 
	// 获取方法直接使用节点字符串，这里声明采用excel工具的公式自动生成
	// =========注意：这里所有数值配置均按照16进制配置，即获取字符串，按16进制解析到内int
	// =========注意：这里所有大小均按照字节计算有效值
	// =========版本信息
	String  Message4 = "seriescode";   //32  // 系列 KF8 或 KF32 ，16进制
	String  Message5 = "chiprange";   //01  // 芯片类型编码，16进制
	String  Message6 = "debugflag";   //01  // 芯片调试类型编码 ，16进制
	String  Message7 = "kernel";   //00  // 内核版本，确定调用库资源，16进制
	String  Message8 = "version";   //01  // 命令版本号 区别 编程版本，16进制
	String  Message9 = "cpunumber";   //01  // cpu number，16进制
	
	// =========编程数据区 RAM信息 十六进制
	String  Message10 = "ramstartaddr";   //10000000  // 
	String  Message11 = "ramsize";   //00000000  // 
	String  Message12 = "ramoffset";   //00000000  // 
	// =========编程数据区 ROM信息 十六进制
	String  Message14 = "romstartaddr";   //1FFF0000  // 
	String  Message15 = "romsize";   //00000000  // 
	String  Message16 = "romoffset";   //00000000  // 
	// =========编程程序区Flash信息
	String  Message18 = "flashstartaddr";   //00000000  // 
	String  Message19 = "flashsize";   //00000800  // 
	String  Message20 = "flashoffset";   //00000000  // 
	// =========
	// =========编程User 信息 十六进制
	String  Message23 = "userstartaddr";   //0C001800  // 
	String  Message24 = "usersize";   //00000800  // 
	String  Message25 = "useroffset";   //00000000  // 
	// =========编程EEprom信息
	String  Message27 = "eepromstartaddr";   //30000000  // 
	String  Message28 = "eepromsize";   //00000010  // 
	String  Message29 = "eepromoffset";   //00000000  // 
	// =========芯片加密信息
	String  Message31 = "debugprogramflagstartaddr";   //0C001430  // 
	String  Message32 = "debugprogramflaglen";   //4  // 
	// =========
	String  Message34 = "protectstartaddr";   //0C001000  // 
	String  Message35 = "protectlen";   //4  // 
	// =========
	// =========编程配置字信息
	String  Message38 = "configsetstartaddr";   //0C001500  // 
	String  Message39 = "configsetlen";   //10  // 
	String  Message40 = "defaultconfigvaule";   //12,34,56,78,9A,BC,DE,F0,ED,CB,A9,87,76,54,32,21  // 
	// =========芯片码识别信息
	String  Message42 = "devicesoftidaddr";   //0C000C24  // 
	String  Message43 = "devicesoftidaddrlen";   //4  // 
	String  Message44 = "devicesoftid";   //12345678  // 
	// =========
	String  Message46 = "devicehardidaddr";   //0C000C28  // 
	String  Message47 = "devicehardidaddrlen";   //4  // 
	String  Message48 = "devicehardid";   //12345678  // 
	// =========
	String  Message50 = "devicebootidaddr";   //0C000C2C  // 
	String  Message51 = "devicebootidaddrlen";   //4  // 
	String  Message52 = "devicebootid";   //12345678  // 
	// =========
	String  Message54 = "deviceicodeidaddr";   //0C000C2C  // 
	String  Message55 = "deviceicodeidaddrlen";   //4  // 
	String  Message56 = "deviceicodeid";   //12345678  // 
	// =========芯片校准值
	String  Message58 = "devicecalstartaddr";   //0C000C00  // 
	String  Message59 = "devicecallenr";   //00000020  // 
	// =========芯片唯一值
	String  Message61 = "deviceonlyidrtaddr";   //0C000C44  // 
	String  Message62 = "deviceonlyidlen";   //00000010  // 
	// =========IP校准值1，仅内部使用
	String  Message64 = "ip1calstartaddr";   //0C000000  // 
	String  Message65 = "ip1callenr";   //00000400  // 
	// =========IP校准值2，仅内部使用
	String  Message67 = "ip2calstartaddr";   //0C800000  // 
	String  Message68 = "ip2callenr";   //00000040  // 
	// =========
	// =========编程参数
	String  Message82 = "memoryelementsize";   //00000400  // 
	String  Message83 = "icspwritesize";   //00000200  // 
	String  Message84 = "ispwritesize";   //00000100  // 
	String  Message85 = "swdwritesize";   //00000400  // 
	// =========
	// =========ISP信息集
	// =========
	// =========SWD信息集
	String  Message90 = "bootloaderfile";   //kf32dlxxloader.bin  // swd编程引导程序代码文件
	String  Message91 = "bootloaderstartRamAddr";   //  // bootloader写RAM起始地址
	
	String  Message92 = "bootloaderInitFunAddr";   //  // 配置芯片时钟等特性
	String  Message921 = "bootloaderRunFunAddr";
	String  Message922 = "bootloadIAPEnter";
	
	String  Message98 = "bootloaderParameterRamAddr";   //  // 参数RAM起始地址
	String  Message99 = "bootloaderDataRamAddr";   //  // 数据RAM起始地址
	
	String  Message100 = "swdcontrolstateReg";   //E000EDF0  // 调试控制及状态寄存器地址
	String  Message101 = "swdwatchcontrolReg";   //E000EDFC  // 调试监控控制寄存器地址
	String  Message102 = "swdeventstateReg";   //E000ED30  // 调试状态寄存器
	String  Message103 = "appresetReg";   //402000B8  // 应用复位控制寄存器地址
	// =========
	// =========ICSP编程命令集
	String  Message106 = "ICSP_LC";   //
	String  Message107 = "ICSP_LDFPM";   //
	String  Message108 = "ICSP_LDFDM";   //
	String  Message109 = "ICSP_RDFPM";   //
	String  Message110 = "ICSP_RDFDM";   //
	String  Message111 = "ICSP_IA";   //
	String  Message112 = "ICSP_BPIT";   //
	String  Message114 = "ICSP_BPET";   //
	String  Message115 = "ICSP_BEPM";   //
	String  Message116 = "ICSP_BEDM";   //
	String  Message117 = "ICSP_REPM";   //
	String  Message118 = "ICSP_RADD";   //
	String  Message119 = "ICSP_WADD";   //
	String  Message120 = "ICSP_UUSN";  //
	String  Message121 = "ICSP_LDSFPM";   //
	String  Message1211 = "ICSP_RUNTEST";   //
	String  Message1212 = "ICSP_CONDEN";   //
	String  Message1213 = "ICSP_SFR";   //
	
	String  Message1214 = "issupprotR16TR31";   //  // 
	String  Message1215 = "issupprotDSPACC";   //  // 
	String  Message1215_2 = "issupprotFPU";   //  // 
	// =========详细配置字的配置信息
	String  Message123 = "configsetconfig";   //  // 
	String  Message124 = "name";   //配置功能1  // 配置功能名称
	String  Message125 = "addr";   //0C001500  // 起始的配置地址 
	String  Message126 = "bytelen";   //1  // 该配置占用的字节数 ，应该最多2个字节，支持3个字节设计
	String  Message127 = "description";   //这是功能配置字1  // 功能指示文字描述
	String  Message128 = "element";   //A1,A2,A3,A4  // 直观文字功能描述选项
	String  Message129 = "elementvaule";   //00,08,10,18  // 和选择对应的后台数组,值应该按照字节长度的整体对应bit上的数值
	String  Message130 = "elementSel";   //2  // 默认的功能选择
	String  Message131 = "elementSelvaule";   //A3  // 默认的功能选项描述
	String  Message132 = "vaulemask";   //E7  // 自身位掩码，用于对应位清空，字节16进制配置，字节间隔使用","分割
	String  Message133 = "configsetconfig";   //  // 
	
	String  Message134 = "configsetconfig";   //  // 
	String  Message135 = "name";   //配置功能2  // 配置功能名称
	String  Message136 = "addr";   //0C001500  // 起始的配置地址 
	String  Message137 = "bytelen";   //2  // 该配置占用的字节数 ，应该最多2个字节，支持3个字节设计
	String  Message138 = "description";   //这是功能配置字2  // 功能指示文字描述
	String  Message139 = "element";   //B1,B2,B3,B4  // 直观文字功能描述选项
	String  Message140 = "elementvaule";   //0000,0080,0100,0180  // 和选择对应的后台数组,值应该按照字节长度的整体对应bit上的数值
	String  Message141 = "elementSel";   //1  // 默认的功能选择
	String  Message142 = "elementSelvaule";   //B2  // 默认的功能选项描述
	String  Message143 = "vaulemask";   //FE,7F  // 自身位掩码，用于对应位清空，字节16进制配置，字节间隔使用","分割
	String  Message144 = "configsetconfig";   //  // 
	
	String  Message145 = "configsetconfig";   //  // 
	String  Message146 = "name";   //配置功能3  // 配置功能名称
	String  Message147 = "addr";   //0C001501  // 起始的配置地址 
	String  Message148 = "bytelen";   //3  // 该配置占用的字节数 ，应该最多2个字节，支持3个字节设计
	String  Message149 = "description";   //这是功能配置字3  // 功能指示文字描述
	String  Message150 = "element";   //C1,C2,C3,C4  // 直观文字功能描述选项
	String  Message151 = "elementvaule";   //000000,0007E0,01F800,01FFE0  // 和选择对应的后台数组,值应该按照字节长度的整体对应bit上的数值
	String  Message152 = "elementSel";   //0  // 默认的功能选择
	String  Message153 = "elementSelvaule";   //C1  // 默认的功能选项描述
	String  Message154 = "vaulemask";   //E0,3F,1F  // 自身位掩码，用于对应位清空，字节16进制配置，字节间隔使用","分割
	String  Message155 = "configsetconfig";   //  // 
	
	String  Message156 = "configsetconfig";   //  // 
	String  Message157 = "name";   //配置功能4  // 配置功能名称
	String  Message158 = "addr";   //0C001501  // 起始的配置地址 
	String  Message159 = "bytelen";   //1  // 该配置占用的字节数 ，应该最多2个字节，支持3个字节设计
	String  Message160 = "description";   //这是功能配置字4  // 功能指示文字描述
	String  Message161 = "element";   //D1,D2,D3,D4  // 直观文字功能描述选项
	String  Message162 = "elementvaule";   //00,04,08,0C  // 和选择对应的后台数组,值应该按照字节长度的整体对应bit上的数值
	String  Message163 = "elementSel";   //3  // 默认的功能选择
	String  Message164 = "elementSelvaule";   //D4  // 默认的功能选项描述
	String  Message165 = "vaulemask";   //F3  // 自身位掩码，用于对应位清空，字节16进制配置，字节间隔使用","分割
	String  Message166 = "configsetconfig";   //  // 

	// =========芯片电压操作特性
	String  Message169 = "classhighpowerlevel";   //21  // 21 3.3V  32 5.0V
	
	String  Message170 = "firewareneedconfig1"; // 
	String  Message171 = "firewareneedconfig2"; // 
	String  Message172 = "firewareneedconfig3"; // 
	String  Message173 = "firewareneedconfig4"; // 	
	//========================================
	String  Message200 = "ifsuperfactory";   //21  // 21 3.3V  32 5.0V
	
	String  Message201 = "superfactoryVaule"; // 
	String  Message202 = "superAddress"; // 
	String  Message203 = "superLength"; // 
	String  Message204 = "superCodeValue"; // 
	//========================================
	public List<ConfigMessage> getConfigMessage();	

	// 所有芯片结果的传入
	public void setConfigurationMap(Map<String, List<?>> configurationMap);
	// 获取芯片系列
	public String getChipSerise();
	// 芯片的编程系列
	public String getChipRange();
	// 芯片的调试系列
	public String getChipDebugRange();
	// 内核
	public String getKernel();
	// 命令版本号
	public String getVersion() ;
	// 当前芯片的名字
	public String getChipName();
	// 是否存在R16~R31
	public String getIsSupportR16();
	// 是否存在支持ACC的DSP
	public String getIsSupportACC();
	// 是否存在支持ACC的FPU
	public String getIsSupportFPU();
	
	public String getCPUNumber();
//====================================RAM	
	public List<DeviceModel> getRams();
	//============================ROM
	public List<DeviceModel> getRoms();
	//============================Flash
	public List<DeviceModel> getFlashs();
	//============================UserFlash
	public List<DeviceModel> getUserFlashs();
	//============================EEPROM
	public List<DeviceModel> getEEproms();
	//============================芯片加密
	public String getDebugProgramFlagsize();
	public String getDebugProgramFlagStartAddr();
	
	public String getProtectsize() ;
	public String getProtectStartAddr();
	//============================芯片配置字
	public String getConfigsize() ;
	public String getConfigStartAddr();
	public String getConfigDefaultVaule() ;
	//=============================== 获取芯片的识别码
	public String getDeviceSoftIDAddr() ;
	public String getDeviceSoftIDLen() ;
	public String getDeviceSoftIDVaule();
	
	public String getDeviceHardIDAddr() ;
	public String getDeviceHardIDLen();
	public String getDeviceHardIDVaule() ;
	
	public String getDeviceBootIDAddr() ;
	public String getDeviceBootIDLen();
	public String getDeviceBootIDVaule() ;
	
	public String getDeviceIcodeIDAddr();
	public String getDeviceIcodeIDLen() ;
	public String getDeviceIcodeIDVaule();
	//=============================== 芯片校准值
	public String getCalVauleAddr();
	public String getCalVauleLen() ;
	//===============================IP模块校准值
	public String getFlashCal1VauleAddr();
	public String getFlashCal1VauleLen();
	
	public String getFlashCal2VauleAddr();
	public String getFlashCal2VauleLen();
	//=============================== 芯片唯一ID
	public String getOnlyVauleAddr();
	public String getOnlyVauleLen() ;
	//============================ 编程参数
	public String getElementSize();
	public String getIcspWriteSize();
	public String getIspWriteSize();
	public String getSwdWriteSize();
	
	//============================ 芯片ISP命令
	
	
	//============================ 芯片SWD命令
	
	public String getbootloaderfile();
	public String getbootloaderstartRamAddr();
	
	public String getbootloaderInitFunAddr();
	public String getbootloaderRunFunAddr();
	public String getbootloaderConfigIAPEnterAddr();
	
	public String getbootloaderParameterRamAddr();

	
	public String getswdcontrolstateReg();
	public String getswdwatchcontrolReg();
	public String getswdeventstateReg();
	public String getappresetReg();
	//============================ 芯片ICSP命令
	public String getCommand1();
	public String getCommand2();
	public String getCommand3();
	public String getCommand4();
	public String getCommand5();
	public String getCommand6();
	public String getCommand7();
	public String getCommand8();
	public String getCommand9();
	public String getCommand10();
	public String getCommand11();
	public String getCommand12();
	public String getCommand13();
	public String getCommand14();
	public String getCommand15();
	public String getCommand16();
	public String getCommand17();
	public String getCommand18();
	public String getCommand19();
	public String getCommand20();
	//============================ 
	public String getClassPowerLevel();
	public String getChipWihtHardNeedSet1();
	public String getChipWihtHardNeedSet2();
	public String getChipWihtHardNeedSet3();
	public String getChipWihtHardNeedSet4();
	//=========================
	public String getifsuperfactory();
	/** tab superfactoryValue value  */
	public String getsuperfactoryNeedTrue();
	public String getsuperfactoryAddress();
	public String getsuperfactoryLength();
	/** tab superCodeValue value */
	public String getsuperfactoryValue();
	//=========================
	
}
