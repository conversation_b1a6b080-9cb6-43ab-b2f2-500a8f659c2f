package com.chipon32.hex.core;

public class DownLoad_ElementVaule{
		private int numVaule;
		private String type;
		private long address; 
		private long length;
		private boolean isWrite;
		
		public int getNumVaule() {
			return numVaule;
		}

		public void setNumVaule(int numVaule) {
			this.numVaule = numVaule;
		}

		public String getType() {
			return type;
		}

		public void setType(String type) {
			this.type = type;
		}

		public long getAddress() {
			return address;
		}

		public void setAddress(long address) {
			this.address = address;
		}

		public long getLength() {
			return length;
		}

		public void setLength(long length) {
			this.length = length;
		}

		public boolean isIswrite() {
			return isWrite;
		}

		public void setIswrite(boolean iswrite) {
			this.isWrite = iswrite;
		}

		public DownLoad_ElementVaule(int num,String type, long addr, long len, boolean enable){
			this.numVaule = num;
			this.type = type;
			this.address = addr;
			this.length =len;
			this.isWrite=enable;
		}	
		
	}
