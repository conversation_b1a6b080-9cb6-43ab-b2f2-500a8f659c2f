package com.chipon32.hex.core;

import java.util.ArrayList;
import java.util.List;

import com.chipon32.hex.core.util.ByteConvertor;


// 分段式内容
public class GroupMemory {
	//~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
	// 整个芯片资源映射
	private Memory memory;
	// 行的段结果
	private List<GroupAddrVauleData> groupFlashDataList;
	private List<GroupAddrVauleData> groupUserDataList;
	private List<GroupAddrVauleData> groupEEpromDataList;
	//~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
	//  从 memory中获取到对象 的构建
	public GroupMemory(Memory memory)
	{
		this.memory = memory;
		// 初始化即是分类
		init();
	}
	// 分类
	private void init() 
	{
		boolean isFlashShowPart=false;
		int flashCount = memory.getFlashSize();  // 内容多少
		int groupFlashCount = flashCount/16;      		// 按行占用行数
		//###
		int showstart	=memory.getEditShowstart();
		int showstop	=memory.getEditShowstop();
		
		if(showstart!=-1 && showstop!=-1 )
		{
			isFlashShowPart=true;						
			showstart = showstart & 0xFFFFFFE0;
			showstop = (showstop + 0x20) & 0xFFFFFFE0;			
			if(showstart> flashCount)		{
				showstart=flashCount-32;
				showstop=flashCount-1;
			}
			if(showstop>= flashCount)		{				
				showstop=flashCount-1;
			}
			groupFlashCount=((showstop-showstart)+1)/16+1;
			if(showstart>=32) groupFlashCount +=1;
		}	
		
		int UserCount=memory.getDataSize();
		int groupUserCount=UserCount/16;
		
		int EEpromCount=memory.getEEpromSize();
		int groupEEpromCount=EEpromCount/16;
		//-------------------------------------------------
		// 构建 行内容 容器，多1行，首行为 内容标签
		groupFlashDataList  = new ArrayList<GroupAddrVauleData>(groupFlashCount+1);
		groupUserDataList   = new ArrayList<GroupAddrVauleData>(groupUserCount+1);
		groupEEpromDataList = new ArrayList<GroupAddrVauleData>(groupEEpromCount+1);
		
		//标识table中的“程序区”和“数据区”中的Label用的GroupRomData对象
		GroupAddrVauleData flashlabel = new GroupAddrVauleData();
		flashlabel.setAddress(-1);
		groupFlashDataList.add(flashlabel);
		if(showstart!=-1 && showstop!=-1  && showstart>=32)
		{
			GroupAddrVauleData flashlabelfirst = new GroupAddrVauleData();
			flashlabelfirst.setAddress(-4);
			groupFlashDataList.add(flashlabelfirst);
		}
		
		GroupAddrVauleData userlabel = new GroupAddrVauleData();
		userlabel.setAddress(-2);
		groupUserDataList.add(userlabel);
		
		GroupAddrVauleData eepromlabel=new GroupAddrVauleData();
		eepromlabel.setAddress(-3);
		groupEEpromDataList.add(eepromlabel);
		
		//  程序区按照行 追加到内容中
		if(isFlashShowPart==true)
		{
			for(int i = 0; i < ((showstop-showstart)+1)/16; i++)
			{
				//###########################################
				GroupAddrVauleData groupFlashData = new GroupAddrVauleData();	
				groupFlashData.setAddress(showstart+i*16+ memory.getFlashStartAddr());			
				
				for(int j = 0; j < 16; j++)
				{
					groupFlashData.addRomData(memory.getFlashAddrVaule(showstart + i * 16 + j));
				}
				groupFlashDataList.add(groupFlashData);				
			}
			
			if(showstart!=-1 && showstop!=-1 )
			{
				GroupAddrVauleData flashlabelstop = new GroupAddrVauleData();
				flashlabelstop.setAddress(-4);
				groupFlashDataList.add(flashlabelstop);
			}
		}
		else
		{
			for(int i = 0; i < groupFlashCount; i++)
			{
				//###########################################
				GroupAddrVauleData groupFlashData = new GroupAddrVauleData();	
				groupFlashData.setAddress(0+i*16);			
				
				for(int j = 0; j < 16; j++)
				{
					groupFlashData.addRomData(memory.getFlashAddrVaule((int) (i * 16 + j+ memory.getFlashStartAddr())));
				}
				groupFlashDataList.add(groupFlashData);
				
			}
		}
		//  用户区(信息区)按照行 追加到内容中
		for(int i = 0; i < groupUserCount; i++)
		{
			GroupAddrVauleData groupFlashData = new GroupAddrVauleData();	
			int dataStratAddr = memory.getDataStartAddr();
			String dataStratAddrHex = ByteConvertor.toEight(Integer.toHexString(dataStratAddr));
			if(!dataStratAddrHex.substring(0, 4).equals("0C00")) {//11KA02
				groupFlashData.setAddress(i*16	+ memory.getDataStartAddr());
			}else {
				groupFlashData.setAddress(i*16	+ memory.getDataStartAddr() & 0x0000FFFF);		//起始地址 
			}
			    
			for(int j = 0; j < 16; j++)
			{
				groupFlashData.addRomData(memory.getDataAddrVaule(i * 16 + j));
			}
			groupUserDataList.add(groupFlashData);
		}
		//  EE 数据区按照行 追加到内容中
		for(int i = 0; i < groupEEpromCount; i++)
		{
			GroupAddrVauleData groupEEpromData = new GroupAddrVauleData();	
			groupEEpromData.setAddress(i*16);			
			
			for(int j = 0; j < 16; j++)
			{
				groupEEpromData.addRomData(memory.getEEpromAddrVaule(i * 16 + j));
			}
			groupEEpromDataList.add(groupEEpromData);
			
		}		
	}
	//~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
	//  获取程序段
	public List<GroupAddrVauleData> getGroupFlashDataList() 
	{
		return groupFlashDataList;
	}
	// 获取用户段
	public List<GroupAddrVauleData> getGroupUserDataList() {
		return groupUserDataList;
	}
	// 获取数据段
	public List<GroupAddrVauleData> getGroupEEpromDataList() {
		return groupEEpromDataList;
	}

	//~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
	// 对外提供整个内存	
	public Memory getMemory() {
		return memory;
	}
	//~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
}
