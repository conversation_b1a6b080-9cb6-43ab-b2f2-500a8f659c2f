package com.chipon32.hex.core;


import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.List;

import com.chipon32.hex.core.util.ByteConvertor;
import com.chipon32.hex.core.util.IConfigurationProvider;

/**
 * <AUTHOR>         解析HEX规则:hex文件中为地址和值的隐射，把所有的隐射都解析出来，然后memory再根据配置文件把隐射与FLASH
 *         、EE对应起来
 * 
 */
public class HexFileParser {
	public  int MaxFileFlashAddr;
	// 传入的文件名类
	private String fileName;
	private List<String> fileNames;
	// 芯片hex的结果类
	private Memory memory;
	private DownLoad_ElementVaule[] diffdowncontrol;
	// 文件读入结果类
	private BufferedReader bfreader;
	// 芯片信息提供类
	private IConfigurationProvider configurationProvider;
	// 对外提供结果的芯片hex内容
	public Memory getMemory() {
		return memory;
	}
//##########################################################################################
//##########################################################################################
//##########################################################################################
	// 通过文件名，芯片信息构建类
	public HexFileParser(File f,String name,
			IConfigurationProvider configurationProvider) {
		
		diffdowncontrol=null;
		this.configurationProvider = configurationProvider;		
		this.fileName = name;
		initFileReader(f);
	}
	// 基于输入流（文件的结果），芯片提供者构建类
	public HexFileParser(InputStream inputStream,
			IConfigurationProvider configurationProvider) {
		
		diffdowncontrol=null;
		this.configurationProvider = configurationProvider;
		initStreamReader(inputStream);
		
		
	}
	// 基于输入流（文件的结果），芯片提供者构建类
	public HexFileParser(InputStream inputStream, IConfigurationProvider configurationProvider, DownLoad_ElementVaule[] userdown) {
		diffdowncontrol=userdown;
		this.configurationProvider = configurationProvider;
		initStreamReader(inputStream);
	}
	//多文件的解析器
	public HexFileParser(List<String> file_List,
			IConfigurationProvider configurationProvider) {
		diffdowncontrol=null;
		this.configurationProvider = configurationProvider;		
		this.fileNames = file_List;
		
	}
//##########################################################################################
//##########################################################################################
//##########################################################################################
	// 初始化读入流为参数
	private void initStreamReader(InputStream inputStream) {
		bfreader = new BufferedReader(new InputStreamReader(inputStream));
		this.fileName ="?.hex";  // 将作为hex
	}
	// 基于文件名参数，信息提供者构建初始化的程序hex
	private void initFileReader(File file) {
		// 空文件时，基于型号内容构建空的memory
		if (fileName == null || "".equals(fileName)) 
		{
			memory = new Memory(configurationProvider,diffdowncontrol);			
			return;
		}
		if (!file.exists()) {
			return;
		}
		// 文件有效，文件转入到文件读入流
		FileReader in;
		try {
			in = new FileReader(file);
			bfreader = new BufferedReader(in);
		} catch (FileNotFoundException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}
	//##########################################################################################
	//##########################################################################################
	//##########################################################################################
	// HEX的有效解析函数
	public boolean parse() 	
	{
		try {				
			// 芯片提供者提供的 空hex对象
			memory = new Memory(configurationProvider,null);
			// 内存变量，默认配置字
			String chipname =configurationProvider.getChipName();			
			// 芯片型号名称
			memory.setChipName(chipname);
			//+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
			// 解析bin文件添加顺序地址
			boolean isTryhexFormat=true;
			if (fileName == null || "".equals(fileName)) 
			{
				
			}
			else
			{
				//+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++			
				if(fileName.endsWith("bin")|| fileName.endsWith("Bin")|| fileName.endsWith("BIN"))
				{
					isTryhexFormat=false;
					int comm_add_start=0;
					//------------------------------------
					if(fileName.contains("@0x"))
					{
						String aaa= fileName.substring(fileName.indexOf("@0x") +3);
						int data = GetStringHexVauleFrom(aaa);
						comm_add_start=data;
					}
					//------------------------------------
					byte[] buf = new byte[1];
					bfreader.close();
					InputStream inStream = (new FileInputStream(fileName)); 
					
					while(inStream.read(buf)>0)
					{
						
						AddrVauleData addrDataBuf = new AddrVauleData( ByteConvertor.toHex(buf[0])	);
						addrDataBuf.setAddress(comm_add_start++);				
						memory.setMemoryVauleIn(addrDataBuf);					
					}	
					MaxFileFlashAddr=comm_add_start;
					inStream.close();				
				}
				//+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
				else if(fileName.endsWith("s19")|| fileName.endsWith("S19"))
				{
					isTryhexFormat=false;
					
					boolean is_file_no_end=true;
					String s ;
					
					while ((s = bfreader.readLine()) != null) 
					{
						// 空行跳过
						s=s.trim();  // 滤除空格和制表符
						if(s.length() == 0){
							continue;
						}
						// S0     S1 S2 S3    S5      S7 S8 S9
							if(is_file_no_end)
							{
								int line_data_type=Integer.parseInt(s.substring(1,2),16);	// 行内容								
								int line_data_len=Integer.parseInt(s.substring(2,4),16); 	// 数据长度								
								int line_data_addr_i=0;	// 地址	根据行内容确定//										
								//---------------------
								switch(line_data_type)
								{
								//---------------------
								case 0:// 文件信息  版本信息的不处理
								{

								}break;
								//---------------------地址宽度下内容
								case 1:
								case 2:
								case 3:
								{
									line_data_addr_i=Integer.parseInt(s.substring(4,4+(line_data_type+1)*2),16);	// 地址		
									int Release_addr=0;
									line_data_len -= (line_data_type+1+1);
									for(int i=0;i<line_data_len;i++)
									{
										Release_addr=line_data_addr_i+i;
										AddrVauleData addrDataBuf = new AddrVauleData( s.substring(4+(line_data_type+1)*2+i*2,6+(line_data_type+1)*2+i*2)	);
										addrDataBuf.setAddress(Release_addr);
									
										memory.setMemoryVauleIn(addrDataBuf);						
									}	
									MaxFileFlashAddr=Release_addr;
								}break;
								//---------------------
								case 5:
								case 6:
								{
									continue; // 忽略非必须的验证前面处理的数量
								}
								//---------------------
								case 7:
								case 8:
								case 9:// 文件结束
								{
									is_file_no_end=false;
									break;
								}	
								//------------------------
								}
							}	// not_file_end			
					} // read_line_in						
					bfreader.close();// 关闭文件输入流
					
				}
			}
			//+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
			  // 逐行解析 hex文件
			if(isTryhexFormat==true)
			{			
					int comm_add_start=0;
					boolean is_file_no_end=true;
					String s ;
					while ((s = bfreader.readLine()) != null) 
					{
						// 空行跳过
						s=s.trim();  // 滤除空格和制表符
						if(s.length() == 0){
							continue;
						}
					//case 0:		//表示数据记录
					//case 1:	 	//表示文件中最后一个记录
					//case 2: 		//描述扩展段地址的记录（高20bit结果）   2 和 4 类型与机器特性相关  类段内数据地址长度 16 或 65536,kf32另外定义  为如4，但顺序反
					//case 4:  		//偏移地址重新计算，类型不同，起始地址要处理----------------
		            //case 3: 		//描述开始段地址的记录  机器差异下的 8086型号下 CS IP 
					//case 5: 		// 描述开始线性地址的记录 即全新的开始地址，如4结果代表高16位，这里代表全32位 起始基准
						if(is_file_no_end)
						{
							int line_data_len=Integer.parseInt(s.substring(1,3),16); 		// 数据长度
							int line_data_addr_i=Integer.parseInt(s.substring(3,7),16);	// 偏移地址
							int line_data_type=Integer.parseInt(s.substring(7,9),16);		// 行内容
							//---------------------
							switch(line_data_type)
							{
								//---------------------
								case 0:// 有效代码段
								{
									int Release_addr=0;
									for(int i=0;i<line_data_len;i++)
									{
										Release_addr=comm_add_start+line_data_addr_i+i;
										AddrVauleData addrDataBuf = new AddrVauleData( s.substring(9+i*2,11+i*2)	);
										addrDataBuf.setAddress(Release_addr);
									
										memory.setMemoryVauleIn(addrDataBuf);						
									}	
									MaxFileFlashAddr=Release_addr;
								}break;
								case 1:// 文件结束
								{
									is_file_no_end=false;
									break;
								}					
								case 2:// 段基地址的第4~19 ，kf32特殊的偏移量为反序 如 高16bit为 1234，hex顺序为4321
								{
									/*  标准格式
									int bufL=Integer.parseInt(s.substring(9,11));
									int bufH=Integer.parseInt(s.substring(11,13));					
									comm_add_start=(bufH*256+bufL)<<4;
									*/
									//  KF32适用
									String addstr=s.substring(12,13)+s.substring(11,12)+s.substring(10,11)+s.substring(9,10);
									comm_add_start=(Integer.parseInt(addstr,16)<<16);
									
								}break;
								case 3:// 体系中不解析						 //  非我们使用
								{
									break;
								}
								case 4:// 16位代表20bit编码的下的高16位  		//  非我们使用
								{
									int bufH=Integer.parseInt(s.substring(9,11),16);
									int bufL=Integer.parseInt(s.substring(11,13),16);		
									comm_add_start=((bufH*256+bufL)<<16);
									break;
								}
								case 5:// 完整的32位的新起始地址	
								{
									int buf1=Integer.parseInt(s.substring(9,11),16);
									int buf2=Integer.parseInt(s.substring(11,13),16);	
									int buf3=Integer.parseInt(s.substring(13,15),16);
									int buf4=Integer.parseInt(s.substring(15,17),16);	
									
									comm_add_start=buf1;
									comm_add_start=comm_add_start*256+buf2;
									comm_add_start=comm_add_start*256+buf3;
									comm_add_start=comm_add_start*256+buf4;
								}break;
								//------------------------
							}
						}	// not_file_end			
					} // read_line_in						
					bfreader.close();// 关闭文件输入流
			}// read hex file

			return true;
		} catch (Exception e) {
			e.printStackTrace();
			return false;
		}
	}	
	//-----------------------------------------------------------------------------------
	//now design  only for ide
	public boolean parsediff() {
		try {				
			// 芯片提供者提供的 空hex对象
			memory = new Memory(configurationProvider, diffdowncontrol);
			// 内存变量，默认配置字
			String chipname = configurationProvider.getChipName();			
			// 芯片型号名称
			memory.setChipName(chipname);
			//##########################################################################################	
			//+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
			// 解析bin文件添加顺序地址
			boolean isTryhexFormat = true;
			if (fileName == null || "".equals(fileName)){				
			}
			else {
				//+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++			
				if(fileName.endsWith("bin")|| fileName.endsWith("Bin")|| fileName.endsWith("BIN"))
				{
					isTryhexFormat=false;
					int comm_add_start=0;
					//------------------------------------
					if(fileName.contains("@0x"))					{
						String aaa= fileName.substring(fileName.indexOf("@0x") +3);
						int data = GetStringHexVauleFrom(aaa);
						comm_add_start=data;
					}
					//------------------------------------
					byte[] buf = new byte[1];
					bfreader.close();
					InputStream inStream = (new FileInputStream(fileName)); 
					
					while(inStream.read(buf)>0)					{				 		
						AddrVauleData addrDataBuf = new AddrVauleData( ByteConvertor.toHex(buf[0])	);
						addrDataBuf.setAddress(comm_add_start++);				
						memory.setMemoryVauleIn(addrDataBuf);					
					}	
					MaxFileFlashAddr=comm_add_start;
					inStream.close();				
				}
				//+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
				else if(fileName.endsWith("s19")|| fileName.endsWith("S19"))
				{
					isTryhexFormat=false;
					
					boolean is_file_no_end=true;
					String s ;
					
					while ((s = bfreader.readLine()) != null) 
					{
						// 空行跳过
						s=s.trim();  // 滤除空格和制表符
						if(s.length() == 0){
							continue;
						}
						// S0     S1 S2 S3    S5      S7 S8 S9
							if(is_file_no_end)
							{
								int line_data_type=Integer.parseInt(s.substring(1,2),16);	// 行内容								
								int line_data_len=Integer.parseInt(s.substring(2,4),16); 	// 数据长度								
								int line_data_addr_i=0;	// 地址	根据行内容确定//										
								//---------------------
								switch(line_data_type)
								{
								//---------------------
								case 0:// 文件信息  版本信息的不处理
								{

								}break;
								//---------------------地址宽度下内容
								case 1:
								case 2:
								case 3:
								{
									line_data_addr_i=Integer.parseInt(s.substring(4,4+(line_data_type+1)*2),16);	// 地址		
									int Release_addr=0;
									line_data_len -= (line_data_type+1+1);
									for(int i=0;i<line_data_len;i++)
									{
										Release_addr=line_data_addr_i+i;
										AddrVauleData addrDataBuf = new AddrVauleData( s.substring(4+(line_data_type+1)*2+i*2,6+(line_data_type+1)*2+i*2)	);
										addrDataBuf.setAddress(Release_addr);
									
										memory.setMemoryVauleIn(addrDataBuf);						
									}	
									MaxFileFlashAddr=Release_addr;
								}break;
								//---------------------
								case 5:
								case 6:
								{
									continue; // 忽略非必须的验证前面处理的数量
								}
								//---------------------
								case 7:
								case 8:
								case 9:// 文件结束
								{
									is_file_no_end=false;
									break;
								}	
								//------------------------
								}
							}	// not_file_end			
					} // read_line_in						
					bfreader.close();// 关闭文件输入流
					
				}
			}
			//+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
			// 逐行解析 hex文件
			if(isTryhexFormat==true)
			{			
				int comm_add_start=0;
				boolean is_file_no_end=true;
				String s ;
				while ((s = bfreader.readLine()) != null) 
				{
					// 空行跳过
					s = s.trim();  // 滤除空格和制表符
					if(s.length() == 0){
						continue;
					}
					//case 0:		//表示数据记录
					//case 1:	 	//表示文件中最后一个记录
					//case 2: 		//描述扩展段地址的记录（高20bit结果）   2 和 4 类型与机器特性相关  类段内数据地址长度 16 或 65536,kf32另外定义  为如4，但顺序反
					//case 4:  		//偏移地址重新计算，类型不同，起始地址要处理----------------
		            //case 3: 		//描述开始段地址的记录  机器差异下的 8086型号下 CS IP 
					//case 5: 		// 描述开始线性地址的记录 即全新的开始地址，如4结果代表高16位，这里代表全32位 起始基准
					if(is_file_no_end)
					{
						int line_data_len=Integer.parseInt(s.substring(1,3),16); 		// 数据长度
						int line_data_addr_i=Integer.parseInt(s.substring(3,7),16);	// 偏移地址
						int line_data_type=Integer.parseInt(s.substring(7,9),16);		// 行内容
						//---------------------
						switch(line_data_type)
						{
							//---------------------
							case 0:// 有效代码段
							{
								int Release_addr=0;
								for(int i=0;i<line_data_len;i++)
								{
									Release_addr=comm_add_start+line_data_addr_i+i;
									AddrVauleData addrDataBuf = new AddrVauleData( s.substring(9+i*2,11+i*2)	);
									addrDataBuf.setAddress(Release_addr);
								
									memory.setMemoryVauleIn(addrDataBuf);						
								}	
								MaxFileFlashAddr=Release_addr;
							}break;
							case 1:// 文件结束
							{
								is_file_no_end=false;
								break;
							}					
							case 2:// 段基地址的第4~19 ，kf32特殊的偏移量为反序 如 高16bit为 1234，hex顺序为4321
							{
								/*  标准格式
								int bufL=Integer.parseInt(s.substring(9,11));
								int bufH=Integer.parseInt(s.substring(11,13));					
								comm_add_start=(bufH*256+bufL)<<4;
								*/
								//  KF32适用
								String addstr=s.substring(12,13)+s.substring(11,12)+s.substring(10,11)+s.substring(9,10);
								comm_add_start=(Integer.parseInt(addstr,16)<<16);
								
							}break;
							case 3:// 体系中不解析						 //  非我们使用
							{
								break;
							}
							case 4:// 16位代表20bit编码的下的高16位  		//  非我们使用
							{
								int bufH=Integer.parseInt(s.substring(9,11),16);
								int bufL=Integer.parseInt(s.substring(11,13),16);		
								comm_add_start=((bufH*256+bufL)<<16);
								break;
							}
							case 5:// 完整的32位的新起始地址	
							{
								int buf1=Integer.parseInt(s.substring(9,11),16);
								int buf2=Integer.parseInt(s.substring(11,13),16);	
								int buf3=Integer.parseInt(s.substring(13,15),16);
								int buf4=Integer.parseInt(s.substring(15,17),16);	
								
								comm_add_start=buf1;
								comm_add_start=comm_add_start*256+buf2;
								comm_add_start=comm_add_start*256+buf3;
								comm_add_start=comm_add_start*256+buf4;
							}break;
							//------------------------
						}
					}	// not_file_end			
				} // read_line_in						
				bfreader.close();// 关闭文件输入流
			}// read hex file

			return true;
		} catch (FileNotFoundException e) {
			//e.printStackTrace();
			return false;
		} catch (Exception e) {
			//e.printStackTrace();
			return false;
		}
	}
	

	public boolean parses() {
		try {				
			// 芯片提供者提供的 空hex对象
			memory = new Memory(configurationProvider,null);
			// 内存变量，默认配置字
			String chipname =configurationProvider.getChipName();			
			// 芯片型号名称
			memory.setChipName(chipname);
			for (int fi=0; fi<fileNames.size();fi++ )
			{
				fileName=fileNames.get(fi);
				File file = new File(fileName);
				
				if (!file.exists()) {				
				}
				
				FileReader in;
				try {
					in = new FileReader(file);
					bfreader = new BufferedReader(in);
				} catch (FileNotFoundException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
			//+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
				// 解析bin文件添加顺序地址
				boolean isTryhexFormat=true;
				if (fileName == null || "".equals(fileName)) 
				{
					
				}else{
					//+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++			
					if(fileName.endsWith("bin")|| fileName.endsWith("Bin")|| fileName.endsWith("BIN")){
						isTryhexFormat=false;
						int comm_add_start=0; 
						//------------------------------------
						if(fileName.contains("@0x"))
						{
							String aaa= fileName.substring(fileName.indexOf("@0x") +3);
							int data = GetStringHexVauleFrom(aaa);
							comm_add_start=data;
						}
						//------------------------------------
						
						byte[] buf = new byte[1];
						bfreader.close();
						InputStream inStream = (new FileInputStream(fileName)); 
						
						while(inStream.read(buf)>0)
						{
							
							AddrVauleData addrDataBuf = new AddrVauleData( ByteConvertor.toHex(buf[0])	);
							addrDataBuf.setAddress(comm_add_start++);				
							memory.setMemoryVauleIn(addrDataBuf);					
						}	
						MaxFileFlashAddr=comm_add_start;
						inStream.close();				
					}
				//+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
					else if(fileName.endsWith("s19")|| fileName.endsWith("S19"))
					{
						isTryhexFormat=false;
						
						boolean is_file_no_end=true;
						String s ;
						
						while ((s = bfreader.readLine()) != null) 
						{
							// 空行跳过
							s=s.trim();  // 滤除空格和制表符
							if(s.length() == 0){
								continue;
							}
							// S0     S1 S2 S3    S5      S7 S8 S9
							if(is_file_no_end)
							{
								int line_data_type=Integer.parseInt(s.substring(1,2),16);	// 行内容								
								int line_data_len=Integer.parseInt(s.substring(2,4),16); 	// 数据长度								
								int line_data_addr_i=0;	// 地址	根据行内容确定//										
								//---------------------
								switch(line_data_type)
								{
									//---------------------
									case 0:// 文件信息  版本信息的不处理
									{
			
									}break;
									//---------------------地址宽度下内容
									case 1:
									case 2:
									case 3:
									{
										line_data_addr_i=Integer.parseInt(s.substring(4,4+(line_data_type+1)*2),16);	// 地址		
										int Release_addr=0;
										line_data_len -= (line_data_type+1+1);
										for(int i=0;i<line_data_len;i++)
										{
											Release_addr=line_data_addr_i+i;
											AddrVauleData addrDataBuf = new AddrVauleData( s.substring(4+(line_data_type+1)*2+i*2,6+(line_data_type+1)*2+i*2)	);
											addrDataBuf.setAddress(Release_addr);
										
											memory.setMemoryVauleIn(addrDataBuf);						
										}	
										MaxFileFlashAddr=Release_addr;
									}break;
									//---------------------
									case 5:
									case 6:
									{
										continue; // 忽略非必须的验证前面处理的数量
									}
									//---------------------
									case 7:
									case 8:
									case 9:// 文件结束
									{
										is_file_no_end=false;
										break;
									}	
									//------------------------
								}
							}	// not_file_end			
						} // read_line_in						
						bfreader.close();// 关闭文件输入流
						
					}
				}
			//+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
				// 逐行解析 hex文件
				if(isTryhexFormat==true)
				{			
					int comm_add_start=0;
					boolean is_file_no_end=true;
					String s ;
					while ((s = bfreader.readLine()) != null) 
					{
						// 空行跳过
						s=s.trim();  // 滤除空格和制表符
						if(s.length() == 0){
							continue;
						}
						//case 0:		//表示数据记录
						//case 1:	 	//表示文件中最后一个记录
						//case 2: 		//描述扩展段地址的记录（高20bit结果）   2 和 4 类型与机器特性相关  类段内数据地址长度 16 或 65536,kf32另外定义  为如4，但顺序反
						//case 4:  		//偏移地址重新计算，类型不同，起始地址要处理----------------
			            //case 3: 		//描述开始段地址的记录  机器差异下的 8086型号下 CS IP 
						//case 5: 		// 描述开始线性地址的记录 即全新的开始地址，如4结果代表高16位，这里代表全32位 起始基准
						if(is_file_no_end)
						{
							int line_data_len=Integer.parseInt(s.substring(1,3),16); 		// 数据长度
							int line_data_addr_i=Integer.parseInt(s.substring(3,7),16);	// 偏移地址
							int line_data_type=Integer.parseInt(s.substring(7,9),16);		// 行内容
							//---------------------
							switch(line_data_type)
							{
								//---------------------
								case 0:// 有效代码段
								{
									int Release_addr=0;
									for(int i=0;i<line_data_len;i++)
									{
										Release_addr=comm_add_start+line_data_addr_i+i;
										AddrVauleData addrDataBuf = new AddrVauleData( s.substring(9+i*2,11+i*2)	);
										addrDataBuf.setAddress(Release_addr);
									
										memory.setMemoryVauleIn(addrDataBuf);						
									}	
									MaxFileFlashAddr=Release_addr;
								}break;
								case 1:// 文件结束
								{
									is_file_no_end=false;
									break;
								}					
								case 2:// 段基地址的第4~19 ，kf32特殊的偏移量为反序 如 高16bit为 1234，hex顺序为4321
								{
									/*  标准格式
									int bufL=Integer.parseInt(s.substring(9,11));
									int bufH=Integer.parseInt(s.substring(11,13));					
									comm_add_start=(bufH*256+bufL)<<4;
									*/
									//  KF32适用
									String addstr=s.substring(12,13)+s.substring(11,12)+s.substring(10,11)+s.substring(9,10);
									comm_add_start=(Integer.parseInt(addstr,16)<<16);
									
								}break;
								case 3:// 体系中不解析						 //  非我们使用
								{
									break;
								}
								case 4:// 16位代表20bit编码的下的高16位  		//  非我们使用
								{
									int bufH=Integer.parseInt(s.substring(9,11),16);
									int bufL=Integer.parseInt(s.substring(11,13),16);		
									comm_add_start=((bufH*256+bufL)<<16);
									break;
								}
								case 5:// 完整的32位的新起始地址	
								{
									int buf1=Integer.parseInt(s.substring(9,11),16);
									int buf2=Integer.parseInt(s.substring(11,13),16);	
									int buf3=Integer.parseInt(s.substring(13,15),16);
									int buf4=Integer.parseInt(s.substring(15,17),16);	
									
									comm_add_start=buf1;
									comm_add_start=comm_add_start*256+buf2;
									comm_add_start=comm_add_start*256+buf3;
									comm_add_start=comm_add_start*256+buf4;
								}break;
								//------------------------
							}
						}	// not_file_end			
					} // read_line_in						
					bfreader.close();// 关闭文件输入流
				}// read hex file		
			}// 遍历文件
			return true;
		} catch (FileNotFoundException e) {
			//e.printStackTrace();
			return false;
		} catch (Exception e) {
			//e.printStackTrace();
			return false;
		}
	}
	
	//
	int GetStringHexVauleFrom(String input)
	{
		int a=0;
		int size=input.length();
		for(int i=0;i<size;i++)
		{
			if(  input.charAt(i) >='0' && input.charAt(i) <= '9')
			{
				a=a*16+ input.charAt(i)-'0';
			}
			else 	if(  input.charAt(i) >='a' && input.charAt(i) <= 'f')
			{
				a=a*16+ input.charAt(i)-'a'+10;
			}
			else 	if(  input.charAt(i) >='A' && input.charAt(i) <= 'F')
			{
				a=a*16+ input.charAt(i)-'A'+10;
			}
			else
				return a;
		}		
		return a;		
	}
}
