package com.chipon32.hex.core;


import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.chipon32.hex.core.util.ByteConvertor;
import com.chipon32.hex.core.util.IConfigurationProvider;

/**
 * 芯片信息
 * <AUTHOR>
 *
 */
public class Memory {	
	//###################################################################### 芯片锟斤拷息
	private String  chipName;
	//++222222222222222222222222222222222222222222222222222222222	 serve  down attribute 
	private DownLoad_ElementVaule[] diffdowncontrol;  // org set , size : 4 Element
	
	public boolean is_different_download;					// order switch var

	public boolean isAutoRangeDown=false;                     // order 1
	public Map<String,String>  autoDown_ele_Size= new HashMap<String, String>();
	public long  noAuto_Start1=-1;public long  noAuto_Stop1=-1;             // order 2
	public long  noAuto_Start2=-1;public long  noAuto_Stop2=-1;             // order 3
	public long  noAuto_Start3=-1;public long  noAuto_Stop3=-1;             // order 4
	public long  noAuto_Start4=-1;public long  noAuto_Stop4=-1;             // order 5
	public DownLoad_ElementVaule[] getDiffdowncontro() {
		return diffdowncontrol;
	}
	public void setDiffdowncontro(DownLoad_ElementVaule[] diffdowncontro) {
		this.diffdowncontrol = diffdowncontro;
	}	
//////////////////////////////////////////////////////////////  ide  pro show range var

	public static int editShowstart=-1;
	public static int editShowstop=-1;
//////////////////////////////////////////////////////////////	
	public int getEditShowstart() {
		return editShowstart;
	}
	
	public static void setEditShowstart(int vvvv) {
		editShowstart = vvvv;
	}
	public int getEditShowstop() {
		return editShowstop;
	}
	
	public static void setEditShowstop(int vvvv) {
		editShowstop = vvvv;
	}
//////////////////////////////////////////////////////////////
	public String getChipName() {
		return chipName;
	}
	public void setChipName(String chipName) {
		this.chipName = chipName;
	}	
	// 信息获取者
	private IConfigurationProvider configurationProvider;
	//配置字节的配置详情
	public List<ConfigMessage> configs = null;
//###################################################################### 扇区元素对象
	/**
		// 线性统一对象下： falsh  userflash  eeprom config debugprogramflag protect  rom ram  其中 配置字节 保护 调试内容简单，直接使用AddrVauleData定义
	 */
	private SectorElementVaule[] flashElement=null;
	private SectorElementVaule[] dataElement=null;
	private SectorElementVaule[] eepromElement=null;
	private SectorElementVaule[] romElement=null;
	private SectorElementVaule[] ramElement=null;
	private List<AddrVauleData> addrVauleDatas=new ArrayList<>();

	public List<AddrVauleData> getAddrVauleDatas() {
		return addrVauleDatas;
	}

	private int ElementOneLen;
	
	private AddrVauleData[] configDatas=null;	
	private AddrVauleData[] debugprogramflagDatas=null;	
	private AddrVauleData[] protectflagDatas=null;	
	
	private AddrVauleData[] Kernel_11K_ISMU_Key=null; // 16
	private AddrVauleData[] Kernel_11K_ISMU_Key_DownEnable=null; // 16
	

	public AddrVauleData[] getKernel_11K_ISMU_Key() {
		return Kernel_11K_ISMU_Key;
	}
	public void setKernel_11K_ISMU_Key(AddrVauleData[] kernel_11k_ISMU_Key) {
		Kernel_11K_ISMU_Key = kernel_11k_ISMU_Key;
	}
	public AddrVauleData[] getKernel_11K_ISMU_Key_DownEnable() {
		return Kernel_11K_ISMU_Key_DownEnable;
	}
	public void setKernel_11K_ISMU_Key_DownEnable(
			AddrVauleData[] kernel_11k_ISMU_Key_DownEnable) {
		Kernel_11K_ISMU_Key_DownEnable = kernel_11k_ISMU_Key_DownEnable;
	}

	private AddrVauleData[] otherDatas=null;  // 实现特殊功能的小构件方法，如校准值编辑界面
	
	private List<String>  pidMessage=null;  // pid自增在配置区的非AddrVauleData对象内容的传递
	
	private int 	flashSize;
	private long 	flashStartAddr;	
	
	private int 	dataSize;
	private int 	dataStartAddr;		
	
	private int 	eepromSize;
	private int 	eepromStartAddr;

	private int 	configSize;
	private int 	configStartAddr;	
	
	private int 	debugprogramflagSize;
	private int 	debugprogramflagStartAddr;
	
	private int 	protectSize;
	private int 	protectStartAddr;

	private int 	romSize;
	private int 	romStartAddr;

	private int 	ramSize;
	private long 	ramStartAddr;
//######################################################################	锟斤拷锟斤拷锟斤拷息锟斤拷锟斤拷
	public List<String> getPidMessage() {
		return pidMessage;
	}
	public void setPidMessage(List<String> pidMessage) {
		this.pidMessage = pidMessage;
	}
//######################################################################  标准空间内容和大小
	// 线性统一数量下管理：顺序 falsh  userflash  eeprom config debugprogramflag protect  rom ram
	
	//&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&
	//小众需要的特殊类和方法，如用于少量的校准信息的对象构建
	public  AddrVauleData[] getOtherDates()
	{
		return otherDatas;
	}

	public  AddrVauleData getOtherDates(int addr)
	{
		return otherDatas[addr];
	}
	
	public void setotherDatas(AddrVauleData[] oDatas) 
	{
		otherDatas=oDatas;
	}	
	
	//  n 个  地址 和  值对应关系的  对象    对应：otherDatas       构造函数
	public Memory(IConfigurationProvider configurationProvider,int startaddr, int len)
	{		
		// 配置信息用的临时对象构建，借用0开始的Flash映射
		this.configurationProvider = configurationProvider;
		otherDatas = new AddrVauleData[len];    //len个地址及其对应的值
		for(int i=0;i<len;i++)
		{
			AddrVauleData tmp = new AddrVauleData("FF"); 			
			tmp.setAddress(startaddr+i);			
			otherDatas[i] = tmp;
		}
	}
	//&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&
	//&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&
	/**
	 *  基于地址和 值的 最小单位传入到该对象下，值传递到对应的模块单元
	 * @param avd
	 */
	//@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
	public boolean  setMemoryFlashReadWriteOptionAll(boolean canopt)
	{
		for(int i=0;i<flashElement.length; i++)
		{
			flashElement[i].setIsCanRead(canopt);
			flashElement[i].setIsCanWrite(canopt);
		}
		return true;		
	}
	
	public boolean 	setMemoryFlashReadWriteOption(int from,int end,boolean canopt)
	{
		for(int i=from;i<end && i<flashElement.length ; i++)	
		{
			flashElement[i].setIsCanRead(canopt);
			flashElement[i].setIsCanWrite(canopt);
		}		
		return true;					
	}
	
	
	public void setMemoryVauleIn(AddrVauleData avd)
	{
		AddrVauleData addrVauleData =  new AddrVauleData(avd.getVaule());
		long mark = 0XFFFFFFFFL;
		addrVauleData.setAddress(avd.getAddress() & mark);
		addrVauleDatas.add(addrVauleData);
		//##########################################################################################			
		long addrget=avd.getAddress();
		if(addrget>=flashStartAddr	&& 	addrget<flashStartAddr+flashSize)
		{
			long buf1=addrget-flashStartAddr;
			int buf2=(int) (buf1/ElementOneLen);
			int buf3=(int) (buf1%ElementOneLen);
			flashElement[buf2].setAddrVauleData(buf3, avd);		
			//<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
			if(isAutoRangeDown) {
				{  flashElement[buf2].setIsCanWrite(true); }  // find for auto range
				autoDown_ele_Size.put(Integer.toString(buf2), "exsit");
			}
			
			else if( addrget >= noAuto_Start1 &&  addrget <= noAuto_Stop1) 
				{  flashElement[buf2].setIsCanWrite(true); }  // verbalization
			else if( addrget >= noAuto_Start2 &&  addrget <= noAuto_Stop2) 
				{  flashElement[buf2].setIsCanWrite(true); }  // verbalization
			else if( addrget >= noAuto_Start3 &&  addrget <= noAuto_Stop3) 
				{  flashElement[buf2].setIsCanWrite(true); }  // verbalization
			else if( addrget >= noAuto_Start4 &&  addrget <= noAuto_Stop4) 
				{  flashElement[buf2].setIsCanWrite(true); }  // verbalization
			//<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
		}
		else if(addrget>=dataStartAddr	&& 	addrget<dataStartAddr+dataSize)
		{
			int buf1=(int) (addrget-dataStartAddr);
			int buf2=buf1/ElementOneLen;
			int buf3=buf1%ElementOneLen;
			dataElement[buf2].setAddrVauleData(buf3, avd);		
		}
		//+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
		else if(addrget>=eepromStartAddr	&& 	addrget<eepromStartAddr+eepromSize)
		{
			int buf1=(int) (addrget-eepromStartAddr);
			int buf2=buf1/ElementOneLen;
			int buf3=buf1%ElementOneLen;
			eepromElement[buf2].setAddrVauleData(buf3, avd);	
		}
		//============================================================
		else if(addrget>=configStartAddr	&& 	addrget<configStartAddr+configSize)
		{
			configDatas[(int) (addrget-configStartAddr)]=avd;
		}
		//+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
		else if(addrget>=debugprogramflagStartAddr	&& 	addrget<debugprogramflagStartAddr+debugprogramflagSize)
		{
			debugprogramflagDatas[(int) (addrget-debugprogramflagStartAddr)]=avd;
		}
		else if(addrget>=debugprogramflagStartAddr+debugprogramflagSize	&& 	addrget<debugprogramflagStartAddr+debugprogramflagSize+16*16)
		{
			Kernel_11K_ISMU_Key[(int) (addrget-(debugprogramflagStartAddr+debugprogramflagSize))]=avd;
		}
		else if(addrget>=debugprogramflagStartAddr+debugprogramflagSize+16*16	&& 	addrget<debugprogramflagStartAddr+debugprogramflagSize+16*16+16)
		{
			Kernel_11K_ISMU_Key_DownEnable[(int) (addrget-(debugprogramflagStartAddr+debugprogramflagSize+16*16))]=avd;
		}
		else if(addrget>=protectStartAddr	&& 	addrget<protectStartAddr+protectSize)
		{
			protectflagDatas[(int) (addrget-protectStartAddr)]=avd;
		}		
	}
	//#############################################################################################
	//#############################################################################################
	//#############################################################################################
	//#############################################################################################
	/**
	 * 芯片程序或文件程序的构建方法
	 * @param cProvider
	 * @param diffdowncontrolinput
	 */
	public Memory(IConfigurationProvider cProvider,DownLoad_ElementVaule[] diffdowncontrolinput)
	{		
		// 信息提供者
		configurationProvider = cProvider;		
		// 芯片各种对象地址、大小	
		flashSize 				=Integer.parseInt( configurationProvider.getFlashs().get(0).getSize(),16);
		dataSize  				=Integer.parseInt( configurationProvider.getUserFlashs().get(0).getSize(),16);		
		eepromSize 				=Integer.parseInt( configurationProvider.getEEproms().get(0).getSize(),16);
		configSize 				=Integer.parseInt( configurationProvider.getConfigsize(),16);
		debugprogramflagSize 	=Integer.parseInt( configurationProvider.getDebugProgramFlagsize(),16);
		protectSize 			=Integer.parseInt( configurationProvider.getProtectsize(),16);
		romSize					=Integer.parseInt( configurationProvider.getRoms().get(0).getSize(),16);
		ramSize					=Integer.parseInt( configurationProvider.getRams().get(0).getSize(),16);
		
		dataStartAddr 				=Integer.parseInt( configurationProvider.getUserFlashs().get(0).getStartAddr(),16);
		flashStartAddr 				=Long.parseLong( configurationProvider.getFlashs().get(0).getStartAddr(),16);
		eepromStartAddr 			=Integer.parseInt( configurationProvider.getEEproms().get(0).getStartAddr(),16);
		configStartAddr 			=Integer.parseInt( configurationProvider.getConfigStartAddr(),16);
		debugprogramflagStartAddr 	=Integer.parseInt( configurationProvider.getDebugProgramFlagStartAddr(),16);
		protectStartAddr 			=Integer.parseInt( configurationProvider.getProtectStartAddr(),16);		
		romStartAddr				=Integer.parseInt( configurationProvider.getRoms().get(0).getStartAddr(),16);
		ramStartAddr				=Long.parseLong( configurationProvider.getRams().get(0).getStartAddr(),16);	
		// 对象构建
		ElementOneLen=Integer.parseInt( configurationProvider.getElementSize(),16);	
		// ------------------------------------------------------------------------ 配置字对象	
		configs=configurationProvider.getConfigMessage();
		// ------------------------------------------------------------------------ 扇区对象
		int cyclecountbuf;
		cyclecountbuf	=flashSize/ElementOneLen + (flashSize%ElementOneLen>0	?	1	:	0	);
		flashElement	=new SectorElementVaule[cyclecountbuf];
		
		cyclecountbuf	=dataSize/ElementOneLen	+ (dataSize%ElementOneLen>0	?	1	:	0	);
		dataElement		=new SectorElementVaule[cyclecountbuf];
		
		cyclecountbuf	=eepromSize/ElementOneLen	+	(eepromSize%ElementOneLen>0	?	1	:	0	);
		eepromElement	=new SectorElementVaule[cyclecountbuf];
		
	//	cyclecountbuf=romSize/SectorOneLen	+	(romSize%SectorOneLen>0	?	1	:	0	);
	//	romElement=new SectorElementVaule[cyclecountbuf];
		
	//	cyclecountbuf=ramSize/SectorOneLen	+	(ramSize%SectorOneLen>0	?	1	:	0	);
	//	ramElement=new SectorElementVaule[cyclecountbuf];
		
		configDatas					=	new AddrVauleData[configSize];	
		debugprogramflagDatas		=	new AddrVauleData[debugprogramflagSize];	
		protectflagDatas			=	new AddrVauleData[protectSize];	
			
		Kernel_11K_ISMU_Key			=		new AddrVauleData[20*16];	
		Kernel_11K_ISMU_Key_DownEnable =	new AddrVauleData[20]; 
		// ------------------------------------------------------------------------ 功能对象
		// ---初始值实现
		init();
		diffdowncontrol=diffdowncontrolinput;
		init_flash_enable();
		// ------------------------------------------------------------------------
		
	}
	/**
	 * 
	 */
	public void init_flash_enable()  
	{
		is_different_download=false;
		isAutoRangeDown=false;                          // order 1
		noAuto_Start1=-1;noAuto_Stop1=-1;             // order 2
		noAuto_Start2=-1;noAuto_Stop2=-1;             // order 3
		noAuto_Start3=-1;noAuto_Stop3=-1;             // order 4
		noAuto_Start4=-1;noAuto_Stop4=-1;             // order 5
		if(autoDown_ele_Size!=null)
			autoDown_ele_Size.clear();
//####################################################################################		
		if(diffdowncontrol!=null){
			
			if( diffdowncontrol[0].isIswrite())
			{	isAutoRangeDown=true;		
				is_different_download=true;
			}
			else{
				for(int i=0;i<diffdowncontrol.length;i++)		{
					if(diffdowncontrol[i].isIswrite())			{
						switch(i)								{
							case 0: is_different_download=true; break;
							case 1: {
								noAuto_Start1= diffdowncontrol[i].getAddress();
								noAuto_Stop1 = diffdowncontrol[i].getAddress()+diffdowncontrol[i].getLength()-1;
								is_different_download=true;
								}break;
							case 2: {
								noAuto_Start2= diffdowncontrol[i].getAddress();
								noAuto_Stop2 = diffdowncontrol[i].getAddress()+diffdowncontrol[i].getLength()-1;
								is_different_download=true;
								}break;
							case 3: {
								noAuto_Start3= diffdowncontrol[i].getAddress();
								noAuto_Stop3 = diffdowncontrol[i].getAddress()+diffdowncontrol[i].getLength()-1;
								is_different_download=true;
								}break;
							case 4: {
								noAuto_Start4= diffdowncontrol[i].getAddress();
								noAuto_Stop4 = diffdowncontrol[i].getAddress()+diffdowncontrol[i].getLength()-1;
								is_different_download=true;
								}break;
							default: {									
								is_different_download=true;
								}break;						
						}						
					}// enable
				}// for 1-3
			}// range config
		}// with set	
		if(is_different_download ){
			
			for(int i = 0; i < flashElement.length; i++)
			{
				long addrget=flashStartAddr+i*ElementOneLen;
				int buf1=(int) (addrget-flashStartAddr);
				int buf2=buf1/ElementOneLen;
	
				//<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
//				if(isAutoRangeDown) 
//				{ 
//					flashElement[buf2].setIsWrite(false); flashElement[buf2].setIsCanWrite(false); 
//					flashElement[buf2].setIsRead(false); flashElement[buf2].setIsCanRead(false); 
//				}
//				else if( addrget >= noAuto_Start1 &&  addrget <= noAuto_Stop1) 
//				{ 
//					flashElement[buf2].setIsWrite(false); flashElement[buf2].setIsCanWrite(false); 
//					flashElement[buf2].setIsRead(false); flashElement[buf2].setIsCanRead(false); 
//			    }
//				else if( addrget >= noAuto_Start2 &&  addrget <= noAuto_Stop2) 
//				{ 
//					flashElement[buf2].setIsWrite(false); flashElement[buf2].setIsCanWrite(false); 
//					flashElement[buf2].setIsRead(false); flashElement[buf2].setIsCanRead(false); 
//				}
//				else if( addrget >= noAuto_Start3 &&  addrget <= noAuto_Stop3) 
//				{ 
//					flashElement[buf2].setIsWrite(false); flashElement[buf2].setIsCanWrite(false); 
//					flashElement[buf2].setIsRead(false); flashElement[buf2].setIsCanRead(false); 
//				}
//				else
				{
					flashElement[buf2].setIsCanWrite(false); 	
					flashElement[buf2].setIsCanRead(false); 
				}				
			}
			for(int i = 0; i < flashElement.length; i++)
			{
				long addrget=flashStartAddr+i*ElementOneLen;
				int buf1=(int) (addrget-flashStartAddr);
				int buf2=buf1/ElementOneLen;
				//int buf3=buf1%ElementOneLen;				
				//<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
				if(isAutoRangeDown) { 
					//flashElement[buf2].setIsWrite(true); flashElement[buf2].setIsCanWrite(true); 
				}
				
				else if( addrget >= noAuto_Start1 &&  addrget <= noAuto_Stop1) 
					{  flashElement[buf2].setIsCanWrite(true);	flashElement[buf2].setIsCanRead(true); }
				else if( addrget >= noAuto_Start2 &&  addrget <= noAuto_Stop2) 
					{  flashElement[buf2].setIsCanWrite(true);	flashElement[buf2].setIsCanRead(true); }
				else if( addrget >= noAuto_Start3 &&  addrget <= noAuto_Stop3) 
					{  flashElement[buf2].setIsCanWrite(true);	flashElement[buf2].setIsCanRead(true); }	
				else if( addrget >= noAuto_Start4 &&  addrget <= noAuto_Stop4) 
					{  flashElement[buf2].setIsCanWrite(true); 	flashElement[buf2].setIsCanRead(true);}	
			}
			
		}else{
			for(int i = 0; i < flashElement.length; i++)
			{
				long addrget=flashStartAddr+i*ElementOneLen;
				int buf1=(int) (addrget-flashStartAddr);
				int buf2=buf1/ElementOneLen;	
				{
					flashElement[buf2].setIsCanWrite(true); 	flashElement[buf2].setIsCanRead(true); 
				}				
			}
		}
	}	
	//----------------------------------------------------------------------------------------------
	private void init()  
	{
		//##############################################################################
		// 程序的默认结果填充
		int sizeControl;

		for(int i = 0; i < flashElement.length; i++)
		{
			long addr=flashStartAddr+i*ElementOneLen;
			if(addr<flashStartAddr+flashSize-ElementOneLen)
				sizeControl=ElementOneLen;
			else 
				sizeControl=(int) (flashStartAddr+flashSize-addr);
			flashElement[i]=new SectorElementVaule(i, addr, sizeControl, "FF");
		}
		// 用户程序区
		for(int i = 0; i < dataElement.length; i++)
		{
			int addr=dataStartAddr+i*ElementOneLen;
			if(addr<dataStartAddr+dataSize-ElementOneLen)
				sizeControl=ElementOneLen;
			else 
				sizeControl=dataStartAddr+dataSize-addr;
			dataElement[i]=new SectorElementVaule(i, addr, sizeControl,	"FF");
		}
		
		// 用户EE区
		for(int i = 0; i < eepromElement.length; i++)
		{
			int addr=eepromStartAddr+i*ElementOneLen;
			if(addr<eepromStartAddr+eepromSize-ElementOneLen)
				sizeControl=ElementOneLen;
			else 
				sizeControl=eepromStartAddr+eepromSize-addr;
			eepromElement[i]=new SectorElementVaule(i, addr, sizeControl, "FF");
		}
		// Rom
		
		// Ram
		
		//##############################################################################
		// 配置区,这里的初始化来自芯片配置
		String  defaultvstr=this.configurationProvider.getConfigDefaultVaule();
		String  []vstr = defaultvstr.split(",");	
		
		if(vstr.length!=configSize)
		{
			// 配置错误的提示
			System.out.print("芯片型号的配置字节默认值配置错误");
		}
		
		for(int i = 0; i < configSize; i++)
		{
			AddrVauleData tmp = new AddrVauleData(vstr[i]);   	
			tmp.setAddress(configStartAddr+i);	
			
			configDatas[i] = tmp;
		}
		
		synchronizationConfigFromConfigElement();		
		//##############################################################################
		for(int i = 0; i < 20; i++)		{
			
			AddrVauleData tmp1 = new AddrVauleData("00");
			tmp1.setAddress( 0x0C001804 + 16*16 +i );				
			Kernel_11K_ISMU_Key_DownEnable[i] = tmp1;
			
			for(int j = 0; j < 16; j++) {
				AddrVauleData tmp2 = new AddrVauleData("00");
				tmp2.setAddress( 0x0C001804 + i*16+j);				
				Kernel_11K_ISMU_Key[i*16+j] = tmp2;
			}	
		}
		//##############################################################################
		// 优先调试编程功能选择，保护中一环
		String vauleString ="FFFFFFFF"; // ODMOD 12345678  Not Set FFFFFFFF NO_ISP 21436587
		for(int i = 0; i < debugprogramflagSize; i++)
		{
			AddrVauleData tmp = new AddrVauleData(vauleString.substring(2*i,2*i+2));
			tmp.setAddress(debugprogramflagStartAddr+i);	
			
			debugprogramflagDatas[i] = tmp;
		}

		// ISP功能呢存在下保护信息 
		vauleString ="5A5AA5A5";  //  No_ISP下的不加密
		for(int i = 0; i < protectSize; i++)
		{
			AddrVauleData tmp = new AddrVauleData(vauleString.substring(2*i,2*i+2)); 		
			tmp.setAddress(protectStartAddr+i);			
			
			protectflagDatas[i] = tmp;
		}	
	}	
	//#############################################################################################
	//#############################################################################################
	//#############################################################################################
	//#############################################################################################
	// 从hex中获取配置的信息
	public boolean  synchronizationConfigFromHex()
	{
		try{
			for(ConfigMessage cms:configs)
			{
				// 每个配置信息的遍历下
				int bytes=cms.getBytes();
				int addrstart=(int)cms.getConfigaddr(); //  32位机其中地址  0x7FFF FFFF   				
				long setVaule=0;
				
				for(int i=0; i<bytes;i++)
				{
					AddrVauleData avd=configDatas[addrstart+i-configStartAddr];
					int buf=Integer.parseInt(avd.getVaule(),16);
					int bufmsk=cms.getMask_High()[i];
					buf=buf&~bufmsk;
					// 匹配结果值
					setVaule=setVaule*256+buf;					
				}	
				// 这个信息的hex值对应的匹配功能匹配
				boolean isfind=false;
				for(int j=0;j<cms.getConfigvaule().length;j++)
				{
					// 匹配
					if(cms.getConfigvaule()[j]==setVaule)
					{
						//
						isfind=true;
						cms.setDefaultselid(j);
						cms.setSelelementtext(cms.getElement()[j]);	
						break;
					}
				}
				// 未匹配到时进行赋值为默认值选择值
				if(isfind==false)
				{
					// 配置的默认值
					List<ConfigMessage> sourceconfigs=configurationProvider.getConfigMessage();
					for(int t=0;t<sourceconfigs.size();t++)
					{
						if(cms.getName().equalsIgnoreCase(sourceconfigs.get(t).getName()))
						{
							cms.setDefaultselid(sourceconfigs.get(t).getDefaultselid());
							cms.setSelelementtext(sourceconfigs.get(t).getSelelementtext());
							break;
						}
					}					
				}
			}	
			return true;
	}catch (Exception e) {
		// TODO: handle exception
		return false;
	}
	}
	// 配置字节的配置对象赋值到内存的配置字节元素对象中
	public boolean  synchronizationConfigFromConfigElement()
	{
		try{
				for(ConfigMessage cms:configs)
				{
					int bytes=cms.getBytes();
					int addrstart=(int)cms.getConfigaddr(); //  32位机  0x7FFF FFFF   
					long v=cms.getConfigvaule()[cms.getDefaultselid()]; 
					String bufset=Long.toHexString(v);
					while(bufset.length()!=bytes*2)
					{
						bufset="0"+bufset;
					}
					// 逐字节的掩码下的内容传递
					for(int i=0; i<bytes;i++)
					{
						AddrVauleData avd=configDatas[addrstart+i-configStartAddr];
						int buf=Integer.parseInt(avd.getVaule(),16);
						int bufmsk=cms.getMask_High()[i];
						buf=buf&bufmsk;
						
						String abufstr=bufset.substring(0,2);
						bufset=bufset.substring(2);
						buf|=Integer.parseInt(abufstr,16);
						
						avd.setVaule(		ByteConvertor.toHex((byte)(	buf&0xFF)	)			);
						configDatas[addrstart+i-configStartAddr]=avd;
					}			
				}	
				return true;
		}catch (Exception e) {
			// TODO: handle exception
			return false;
		}
		
	}
	//~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
	// 对外提供信息提供者
	public IConfigurationProvider getConfigurationProvider() 
	{
		return configurationProvider;
	}
	// 信息提供者传入的变量
	public void setConfigurationProvider(IConfigurationProvider configurationProvider) 
	{
		this.configurationProvider = configurationProvider;
	}

	//---------------------------------------------  对外提供区域的大小信息

	//########################################################################### 
	public int getFlashSize() {
		return flashSize;
	}
	public void setFlashSize(int Size) {
		this.flashSize = Size;
	}
	public long getFlashStartAddr() {
		return flashStartAddr;
	}
	public void setFlashStartAddr(long StartAddr) {
		this.flashStartAddr = StartAddr;
	}
	
	public int getDataSize() {
		return dataSize;
	}
	public void setDataSize(int Size) {
		this.dataSize = Size;
	}
	public int getDataStartAddr() {
		return dataStartAddr;
	}
	public void setDataStartAddr(int StartAddr) {
		this.dataStartAddr = StartAddr;
	}
	
	public int getEEpromSize() {
		return eepromSize;
	}
	public void setEEpromSize(int Size) {
		this.eepromSize = Size;
	}
	public int getEEpromStartAddr() {
		return eepromStartAddr;
	}
	public void setEEpromStartAddr(int StartAddr) {
		this.eepromStartAddr = StartAddr;
	}
	//########################################################################### 
	public int getConfigSize() {
		return configSize;
	}
	public void setConfigSize(int Size) {
		this.configSize = Size;
	}
	public int getConfigStartAddr() {
		return configStartAddr;
	}
	public void setConfigStartAddr(int StartAddr) {
		this.configStartAddr = StartAddr;
	}
	public AddrVauleData getConfigAddrVaule(int addr)
	{
		return configDatas[addr];
	}
	//########################################################################### ROM 大小 起始地址  RAM 大小 起始地址
	public int getRomSize() {
		return romSize;
	}
	public void setRomSize(int romSize) {
		this.romSize = romSize;
	}
	public int getRomStartAddr() {
		return romStartAddr;
	}
	public void setRomStartAddr(int romStartAddr) {
		this.romStartAddr = romStartAddr;
	}
	public long getRamSize() {
		return ramSize;
	}
	public void setRamSize(int ramSize) {
		this.ramSize = ramSize;
	}
	public long getRamStartAddr() {
		return ramStartAddr;
	}
	public void setRamStartAddr(int ramStartAddr) {
		this.ramStartAddr = ramStartAddr;
	}
	//###################################################  扇元素对象
	public SectorElementVaule[] getFlashElement() {
		return flashElement;
	}
	public void setFlashElement(SectorElementVaule[] Element) {
		this.flashElement = Element;
	}
	
	public SectorElementVaule[] getDataElement() {
		return dataElement;
	}
	public void setDataElement(SectorElementVaule[] Element) {
		this.dataElement = Element;
	}
	
	public SectorElementVaule[] getEEpromElement() {
		return eepromElement;
	}
	public void setEEpromElement(SectorElementVaule[] Element) {
		this.eepromElement = Element;
	}
	//-----------------------------------------------
	public AddrVauleData getFlashAddrVaule(int addrget)
	{
		if(	addrget<flashSize)
		{
			int buf2=addrget/ElementOneLen;
			int buf3=addrget%ElementOneLen;	
			return flashElement[buf2].getContent().get(buf3);
		}
		return null;
	}
	public AddrVauleData getDataAddrVaule(int addrget)
	{
		if(addrget<dataSize)
		{
			int buf2=addrget/ElementOneLen;
			int buf3=addrget%ElementOneLen;	
			return dataElement[buf2].getContent().get(buf3);
		}
		return null;
	}
	public AddrVauleData getEEpromAddrVaule(int addrget)
	{
		if(addrget<dataSize)
		{			
			int buf2=addrget/ElementOneLen;
			int buf3=addrget%ElementOneLen;	
			return eepromElement[buf2].getContent().get(buf3);
		}
		return null;
	}
	//================
	public void  setFlashAddrVaule(int addrget,AddrVauleData avd)
	{
		if(	addrget<flashSize)
		{
			int buf2=addrget/ElementOneLen;
			int buf3=addrget%ElementOneLen;	
			flashElement[buf2].setAddrVauleData(buf3, avd);	
		}
		
	}
	public void setDataAddrVaule(int addrget,AddrVauleData avd)
	{
		if(addrget<dataSize)
		{
			int buf2=addrget/ElementOneLen;
			int buf3=addrget%ElementOneLen;	
			dataElement[buf2].setAddrVauleData(buf3, avd);
		}	
	}
	public void setEEpromAddrVaule(int addrget,AddrVauleData avd)
	{
		if(addrget<dataSize)
		{			
			int buf2=addrget/ElementOneLen;
			int buf3=addrget%ElementOneLen;	
			eepromElement[buf2].setAddrVauleData(buf3, avd);
		}		
	}
	//###################################################
	public SectorElementVaule[] getRomElement() {
		return romElement;
	}
	public void setRomElement(SectorElementVaule[] Element) {
		this.romElement = Element;
	}
	public SectorElementVaule[] getRamElement() {
		return ramElement;
	}
	public void setRamElement(SectorElementVaule[] Element) {
		this.ramElement = Element;
	}
	//###################################################
	
	public AddrVauleData[] getConfigDatas()
	{
		return configDatas;	
	}
	public AddrVauleData[] getDebugprogramflagDatas()
	{
		return debugprogramflagDatas;	
	}
	public AddrVauleData[] getProtectflagDatas()
	{
		return protectflagDatas;	
	}
	//###################################################


	/**
	 * 计算memory的校验和（当前仅计算程序区和数据区的，配置区的不参与计算)
	 * @return
	 */
	public String getHexFlashSumVaule_Empty(){
		// long型8字节宽，但不能识别高位为1的字符串转换，因此定义long型
		// 但按int长度有效，校验和的128bit，拆分为4个int的long型变量
		int chiprange=Integer.parseInt(this.getConfigurationProvider().getChipRange(),16);
		long CheckSumH=0,CheckSumHH=0,CheckSumL=0,CheckSumLL=0;	
		
		long NextCheckSumH,NextCheckSumHH,NextCheckSumL,NextCheckSumLL;	 //

		long FlashVauleH,FlashVauleHH,FlashVauleL,FlashVauleLL;			
		
		long BufWordH,BufWordHH,BufWordL,BufWordLL;	 //

		for(SectorElementVaule elm:flashElement)
		{
			int lencout = elm.getLen() / 16 ; // 16字节的运算
			for(int i=0;i<lencout;i++)
			{
				//-----------------------------
				FlashVauleLL =Long.parseLong(("FF"+""),16);	
				FlashVauleLL+=Long.parseLong(("FF"+"00"),16);	
				FlashVauleLL+=Long.parseLong(("FF"+"0000"),16);	
				FlashVauleLL+=Long.parseLong(("FF"+"000000"),16);	
				
				FlashVauleL =Long.parseLong(("FF"+""),16);	
				FlashVauleL+=Long.parseLong(("FF"+"00"),16);	
				FlashVauleL+=Long.parseLong(("FF"+"0000"),16);	
				FlashVauleL+=Long.parseLong(("FF"+"000000"),16);	
				
				FlashVauleH =Long.parseLong(("FF"+""),16);	
				FlashVauleH+=Long.parseLong(("FF"+"00"),16);	
				FlashVauleH+=Long.parseLong(("FF"+"0000"),16);	
				FlashVauleH+=Long.parseLong(("FF"+"000000"),16);	
				
				FlashVauleHH =Long.parseLong(("FF"+""),16);	
				FlashVauleHH+=Long.parseLong(("FF"+"00"),16);	
				FlashVauleHH+=Long.parseLong(("FF"+"0000"),16);	
				FlashVauleHH+=Long.parseLong(("FF"+"000000"),16);	
				//-----------------------------
				BufWordLL=CheckSumLL/2;			
				if((CheckSumL & 0x0001)>0)
					BufWordLL |= 0x80000000l; // 显式long型，否则int转long会是-80000000
				
				BufWordL=CheckSumL/2;			
				if((CheckSumH&0x0001)>0 && chiprange !=4)   // 11K 区分
					BufWordL |=0x80000000l; // 显式long型，否则int转long会是-80000000
				
				BufWordH=CheckSumH/2;			
				if((CheckSumHH&0x0001)>0)
					BufWordH |=0x80000000l; // 显式long型，否则int转long会是-80000000
				
				BufWordHH=CheckSumHH/2;		 // 显式long型，否则int转long会是-80000000				
				//-----------------------------	//BufWordHH 最高位注定为0，直接异或，即保留flash的bit127的运算	
				NextCheckSumHH	=FlashVauleHH	^	BufWordHH;
				NextCheckSumH	=FlashVauleH	^	BufWordH;
				NextCheckSumL	=FlashVauleL	^	BufWordL;
				NextCheckSumLL	=FlashVauleLL	^	BufWordLL;
				//@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
				//----------------------------- // 9K 9K1  sum 的  0 2 27 29位的异或结果  127  11K 异或结果63
				int buf=0,buf2=0;			
				//+++++++++++++++++++++++++++++++++++++++
				if((CheckSumLL	&(1<<0))!=0)  // 0   LL[0-31]  L[63-32] H[95-64] HH[128-96]
					buf2++;
				if((CheckSumLL	&(1<<2))!=0)  // 2
					buf2++;
				if((CheckSumLL	&(1<<27))!=0)  // 27
					buf2++;
				if((CheckSumLL	&(1<<29))!=0)  // 29
					buf2++;			
				//+++++++++++++++++++++++++++++++++++++++
				if(chiprange==4) {
					
					if((CheckSumH	&(1<<0))!=0)  // 0   LL[0-31]  L[63-32] H[95-64] HH[128-96]
						buf++;
					if((CheckSumH	&(1<<2))!=0)  // 2
						buf++;
					if((CheckSumH	&(1<<27))!=0)  // 27
						buf++;
					if((CheckSumH	&(1<<29))!=0)  // 29
						buf++;		
					
					if(  (buf2 %2)	==1)		{
						NextCheckSumL ^=0x80000000l; //
					}
					buf2= buf;					
				}
				//+++++++++++++++++++++++++++++++++++++++
				if(buf2%2==1)			{
					NextCheckSumHH ^=0x80000000l; //
				}				
				//@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
				CheckSumHH	=(NextCheckSumHH	&0xFFFFFFFF); 
				CheckSumH	=(NextCheckSumH		&0xFFFFFFFF);
				CheckSumL	=(NextCheckSumL		&0xFFFFFFFF);
				CheckSumLL	=(NextCheckSumLL	&0xFFFFFFFF);
				//-------------监控 
//				if(elm.getAddress()==0xFC00 && i==62)
//				{
//					System.out.print("");
//				}
			}			
		}		
		// 获取数据的内容结果		
		return 	ByteConvertor.toEight(Long.toHexString(CheckSumHH))
				+	ByteConvertor.toEight(Long.toHexString(CheckSumH))
				+	ByteConvertor.toEight(Long.toHexString(CheckSumL))
				+	ByteConvertor.toEight(Long.toHexString(CheckSumLL));
	}
	
	/**
	 * 计算flash的sig校验值
	 * @return
	 */
	public String getHexFlashSumVaule(){
		// long型8字节宽，但不能识别高位为1的字符串转换，因此定义long型
		// 但按int长度有效，校验和的128bit，拆分为4个int的long型变量
		int chiprange=Integer.parseInt(this.getConfigurationProvider().getChipRange(),16);
		long CheckSumH=0,CheckSumHH=0,CheckSumL=0,CheckSumLL=0;	
		
		long NextCheckSumH,NextCheckSumHH,NextCheckSumL,NextCheckSumLL;	 //

		long FlashVauleH,FlashVauleHH,FlashVauleL,FlashVauleLL;			
		
		long BufWordH,BufWordHH,BufWordL,BufWordLL;	 //

		for(SectorElementVaule elm:flashElement)
		{
			int lencout = elm.getLen() / 16 ; // 16字节的运算
			for(int i=0;i<lencout;i++)
			{
				//-----------------------------
				FlashVauleLL =Long.parseLong((elm.getContent().get(i*16+0).getVaule()+""),16);	
				FlashVauleLL+=Long.parseLong((elm.getContent().get(i*16+1).getVaule()+"00"),16);	
				FlashVauleLL+=Long.parseLong((elm.getContent().get(i*16+2).getVaule()+"0000"),16);	
				FlashVauleLL+=Long.parseLong((elm.getContent().get(i*16+3).getVaule()+"000000"),16);	
				
				FlashVauleL =Long.parseLong((elm.getContent().get(i*16+4).getVaule()+""),16);	
				FlashVauleL+=Long.parseLong((elm.getContent().get(i*16+5).getVaule()+"00"),16);	
				FlashVauleL+=Long.parseLong((elm.getContent().get(i*16+6).getVaule()+"0000"),16);	
				FlashVauleL+=Long.parseLong((elm.getContent().get(i*16+7).getVaule()+"000000"),16);	
				
				FlashVauleH =Long.parseLong((elm.getContent().get(i*16+8).getVaule()+""),16);	
				FlashVauleH+=Long.parseLong((elm.getContent().get(i*16+9).getVaule()+"00"),16);	
				FlashVauleH+=Long.parseLong((elm.getContent().get(i*16+10).getVaule()+"0000"),16);	
				FlashVauleH+=Long.parseLong((elm.getContent().get(i*16+11).getVaule()+"000000"),16);	
				
				FlashVauleHH =Long.parseLong((elm.getContent().get(i*16+12).getVaule()+""),16);	
				FlashVauleHH+=Long.parseLong((elm.getContent().get(i*16+13).getVaule()+"00"),16);	
				FlashVauleHH+=Long.parseLong((elm.getContent().get(i*16+14).getVaule()+"0000"),16);	
				FlashVauleHH+=Long.parseLong((elm.getContent().get(i*16+15).getVaule()+"000000"),16);	
				//-----------------------------
				BufWordLL=CheckSumLL/2;			
				if((CheckSumL & 0x0001)>0)
					BufWordLL |= 0x80000000l; // 显式long型，否则int转long会是-80000000
				
				BufWordL=CheckSumL/2;			
				if((CheckSumH&0x0001)>0 && chiprange !=4)   // 11K 区分
					BufWordL |=0x80000000l; // 显式long型，否则int转long会是-80000000
				
				BufWordH=CheckSumH/2;			
				if((CheckSumHH&0x0001)>0)
					BufWordH |=0x80000000l; // 显式long型，否则int转long会是-80000000
				
				BufWordHH=CheckSumHH/2;		 // 显式long型，否则int转long会是-80000000				
				//-----------------------------	//BufWordHH 最高位注定为0，直接异或，即保留flash的bit127的运算	
				NextCheckSumHH	=FlashVauleHH	^	BufWordHH;
				NextCheckSumH	=FlashVauleH	^	BufWordH;
				NextCheckSumL	=FlashVauleL	^	BufWordL;
				NextCheckSumLL	=FlashVauleLL	^	BufWordLL;
				//@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
				//----------------------------- // 9K 9K1  sum 的  0 2 27 29位的异或结果  127  11K 异或结果63
				int buf=0,buf2=0;			
				//+++++++++++++++++++++++++++++++++++++++
				if((CheckSumLL	&(1<<0))!=0)  // 0   LL[0-31]  L[63-32] H[95-64] HH[128-96]
					buf2++;
				if((CheckSumLL	&(1<<2))!=0)  // 2
					buf2++;
				if((CheckSumLL	&(1<<27))!=0)  // 27
					buf2++;
				if((CheckSumLL	&(1<<29))!=0)  // 29
					buf2++;			
				//+++++++++++++++++++++++++++++++++++++++
				if(chiprange==4) {					
					if((CheckSumH	&(1<<0))!=0)  // 0   LL[0-31]  L[63-32] H[95-64] HH[128-96]
						buf++;
					if((CheckSumH	&(1<<2))!=0)  // 2
						buf++;
					if((CheckSumH	&(1<<27))!=0)  // 27
						buf++;
					if((CheckSumH	&(1<<29))!=0)  // 29
						buf++;							
					if(  (buf2 %2)	==1)		{
						NextCheckSumL ^=0x80000000l; //
					}
					buf2= buf;					
				}
				//+++++++++++++++++++++++++++++++++++++++
				if(buf2%2==1)			{
					NextCheckSumHH ^=0x80000000l; //
				}				
				//@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
				CheckSumHH	=(NextCheckSumHH	&0xFFFFFFFF); 
				CheckSumH	=(NextCheckSumH		&0xFFFFFFFF);
				CheckSumL	=(NextCheckSumL		&0xFFFFFFFF);
				CheckSumLL	=(NextCheckSumLL	&0xFFFFFFFF);
				//-------------监控 
//				if(elm.getAddress()==0x100 && i==62)
//				{
//					System.out.print("");
//				}
//				if( i==0 && (elm.getAddress() ==  (511*1024) || elm.getAddress() ==  (512*1024)  || elm.getAddress() ==  (1023*1024) || elm.getAddress() ==  (1024*1024) || elm.getAddress() ==  (1535*1024) || elm.getAddress() ==  (1536*1024))){
//				System.out.print("all sig temp 0x "+ ByteConvertor.toEight(Long.toHexString(CheckSumHH))
//						+" "+	ByteConvertor.toEight(Long.toHexString(CheckSumH))
//						+" "+	ByteConvertor.toEight(Long.toHexString(CheckSumL))
//						+" "+	ByteConvertor.toEight(Long.toHexString(CheckSumLL))+ "\r\n");
//				}
				//				if(i>17)
//					break; // 1K 内test
			}	
//			if(elm.getAddress()> 2048)
//				break; // 1K 内test
//			if(elm.getAddress() == (1024*1024 -2048) )
//			System.out.print("all sig temp 0x "+ ByteConvertor.toEight(Long.toHexString(CheckSumHH))
//					+" "+	ByteConvertor.toEight(Long.toHexString(CheckSumH))
//					+" "+	ByteConvertor.toEight(Long.toHexString(CheckSumL))
//					+" "+	ByteConvertor.toEight(Long.toHexString(CheckSumLL))+ "\r\n");
		}		
		// 获取数据的内容结果		
		return 	ByteConvertor.toEight(Long.toHexString(CheckSumHH))
				+	ByteConvertor.toEight(Long.toHexString(CheckSumH))
				+	ByteConvertor.toEight(Long.toHexString(CheckSumL))
				+	ByteConvertor.toEight(Long.toHexString(CheckSumLL));
	}
	
	public String getHexFlashSumVaule(long startaddr,long stopaddress){
		// long型8字节宽，但不能识别高位为1的字符串转换，因此定义long型
		// 但按int长度有效，校验和的128bit，拆分为4个int的long型变量
		int chiprange=Integer.parseInt(this.getConfigurationProvider().getChipRange(),16);
		long CheckSumH=0,CheckSumHH=0,CheckSumL=0,CheckSumLL=0;	
		
		long NextCheckSumH,NextCheckSumHH,NextCheckSumL,NextCheckSumLL;	 //

		long FlashVauleH,FlashVauleHH,FlashVauleL,FlashVauleLL;			
		
		long BufWordH,BufWordHH,BufWordL,BufWordLL;	 //

		for(SectorElementVaule elm:flashElement)
		{
			int lencout = elm.getLen() / 16 ; // 16字节的运算
			long nowstartaddress= elm.getAddress();
			long nowstopaddress= elm.getAddress()+elm.getLen();
			if(nowstopaddress >stopaddress ||  nowstopaddress<= startaddr)	{   // noin range  noin
				continue;
			}			
			for(int i=0;i<lencout;i++)
			{				
				nowstopaddress  =nowstartaddress +16;
				if(nowstopaddress >stopaddress ||  nowstopaddress<= startaddr)    // not all , app by sector aligned ,but function can less
					continue;  // aligned 16bytes is a minimal block with sig code 
				//-----------------------------
				FlashVauleLL =Long.parseLong((elm.getContent().get(i*16+0).getVaule()+""),16);	
				FlashVauleLL+=Long.parseLong((elm.getContent().get(i*16+1).getVaule()+"00"),16);	
				FlashVauleLL+=Long.parseLong((elm.getContent().get(i*16+2).getVaule()+"0000"),16);	
				FlashVauleLL+=Long.parseLong((elm.getContent().get(i*16+3).getVaule()+"000000"),16);	
				
				FlashVauleL =Long.parseLong((elm.getContent().get(i*16+4).getVaule()+""),16);	
				FlashVauleL+=Long.parseLong((elm.getContent().get(i*16+5).getVaule()+"00"),16);	
				FlashVauleL+=Long.parseLong((elm.getContent().get(i*16+6).getVaule()+"0000"),16);	
				FlashVauleL+=Long.parseLong((elm.getContent().get(i*16+7).getVaule()+"000000"),16);	
				
				FlashVauleH =Long.parseLong((elm.getContent().get(i*16+8).getVaule()+""),16);	
				FlashVauleH+=Long.parseLong((elm.getContent().get(i*16+9).getVaule()+"00"),16);	
				FlashVauleH+=Long.parseLong((elm.getContent().get(i*16+10).getVaule()+"0000"),16);	
				FlashVauleH+=Long.parseLong((elm.getContent().get(i*16+11).getVaule()+"000000"),16);	
				
				FlashVauleHH =Long.parseLong((elm.getContent().get(i*16+12).getVaule()+""),16);	
				FlashVauleHH+=Long.parseLong((elm.getContent().get(i*16+13).getVaule()+"00"),16);	
				FlashVauleHH+=Long.parseLong((elm.getContent().get(i*16+14).getVaule()+"0000"),16);	
				FlashVauleHH+=Long.parseLong((elm.getContent().get(i*16+15).getVaule()+"000000"),16);	
				//-----------------------------
				BufWordLL=CheckSumLL/2;			
				if((CheckSumL & 0x0001)>0)
					BufWordLL |= 0x80000000l; // 显式long型，否则int转long会是-80000000
				
				BufWordL=CheckSumL/2;			
				if((CheckSumH&0x0001)>0 && chiprange !=4)   // 11K 区分
					BufWordL |=0x80000000l; // 显式long型，否则int转long会是-80000000
				
				BufWordH=CheckSumH/2;			
				if((CheckSumHH&0x0001)>0)
					BufWordH |=0x80000000l; // 显式long型，否则int转long会是-80000000
				
				BufWordHH=CheckSumHH/2;		 // 显式long型，否则int转long会是-80000000				
				//-----------------------------	//BufWordHH 最高位注定为0，直接异或，即保留flash的bit127的运算	
				NextCheckSumHH	=FlashVauleHH	^	BufWordHH;
				NextCheckSumH	=FlashVauleH	^	BufWordH;
				NextCheckSumL	=FlashVauleL	^	BufWordL;
				NextCheckSumLL	=FlashVauleLL	^	BufWordLL;
				//@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
				//----------------------------- // 9K 9K1  sum 的  0 2 27 29位的异或结果  127  11K 异或结果63
				int buf=0,buf2=0;			
				//+++++++++++++++++++++++++++++++++++++++
				if((CheckSumLL	&(1<<0))!=0)  // 0   LL[0-31]  L[63-32] H[95-64] HH[128-96]
					buf2++;
				if((CheckSumLL	&(1<<2))!=0)  // 2
					buf2++;
				if((CheckSumLL	&(1<<27))!=0)  // 27
					buf2++;
				if((CheckSumLL	&(1<<29))!=0)  // 29
					buf2++;			
				//+++++++++++++++++++++++++++++++++++++++
				if(chiprange==4) {
					
					if((CheckSumH	&(1<<0))!=0)  // 0   LL[0-31]  L[63-32] H[95-64] HH[128-96]
						buf++;
					if((CheckSumH	&(1<<2))!=0)  // 2
						buf++;
					if((CheckSumH	&(1<<27))!=0)  // 27
						buf++;
					if((CheckSumH	&(1<<29))!=0)  // 29
						buf++;		
					
					if(  (buf2 %2)	==1)		{
						NextCheckSumL ^=0x80000000l; //
					}
					buf2= buf;					
				}
				//+++++++++++++++++++++++++++++++++++++++
				if(buf2%2==1)			{
					NextCheckSumHH ^=0x80000000l; //
				}
				//-----------------------------
				CheckSumHH	=(NextCheckSumHH	&0xFFFFFFFF); 
				CheckSumH	=(NextCheckSumH		&0xFFFFFFFF);
				CheckSumL	=(NextCheckSumL		&0xFFFFFFFF);
				CheckSumLL	=(NextCheckSumLL	&0xFFFFFFFF);
				//@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
				nowstartaddress += 16;
				//@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@监控 
//				if(elm.getAddress()==0xFC00 && i==62)
//				{
//					System.out.print("");
//				}
			}			
		}		
		// 获取数据的内容结果		
		return 	ByteConvertor.toEight(Long.toHexString(CheckSumHH))
				+	ByteConvertor.toEight(Long.toHexString(CheckSumH))
				+	ByteConvertor.toEight(Long.toHexString(CheckSumL))
				+	ByteConvertor.toEight(Long.toHexString(CheckSumLL));
	}
	
	public String getHexUserSumVaule(){
		// long型8字节宽，但不能识别高位为1的字符串转换，因此定义long型
		// 但按int长度有效，校验和的128bit，拆分为4个int的long型变量
		int chiprange=Integer.parseInt(this.getConfigurationProvider().getChipRange(),16);
		long CheckSumH=0,CheckSumHH=0,CheckSumL=0,CheckSumLL=0;	
		
		long NextCheckSumH,NextCheckSumHH,NextCheckSumL,NextCheckSumLL;	

		long FlashVauleH,FlashVauleHH,FlashVauleL,FlashVauleLL;			
		
		long BufWordH,BufWordHH,BufWordL,BufWordLL;			

		
		for(SectorElementVaule elm:dataElement)
		{
				int lencout=elm.getLen() / 16 ; // 16字节的运算
				for(int i=0;i<lencout;i++)
				{
					//-----------------------------
					FlashVauleLL =Long.parseLong((elm.getContent().get(i*16+0).getVaule()+""),16);	
					FlashVauleLL+=Long.parseLong((elm.getContent().get(i*16+1).getVaule()+"00"),16);	
					FlashVauleLL+=Long.parseLong((elm.getContent().get(i*16+2).getVaule()+"0000"),16);	
					FlashVauleLL+=Long.parseLong((elm.getContent().get(i*16+3).getVaule()+"000000"),16);	
					
					FlashVauleL =Long.parseLong((elm.getContent().get(i*16+4).getVaule()+""),16);	
					FlashVauleL+=Long.parseLong((elm.getContent().get(i*16+5).getVaule()+"00"),16);	
					FlashVauleL+=Long.parseLong((elm.getContent().get(i*16+6).getVaule()+"0000"),16);	
					FlashVauleL+=Long.parseLong((elm.getContent().get(i*16+7).getVaule()+"000000"),16);	
					
					FlashVauleH =Long.parseLong((elm.getContent().get(i*16+8).getVaule()+""),16);	
					FlashVauleH+=Long.parseLong((elm.getContent().get(i*16+9).getVaule()+"00"),16);	
					FlashVauleH+=Long.parseLong((elm.getContent().get(i*16+10).getVaule()+"0000"),16);	
					FlashVauleH+=Long.parseLong((elm.getContent().get(i*16+11).getVaule()+"000000"),16);	
					
					FlashVauleHH =Long.parseLong((elm.getContent().get(i*16+12).getVaule()+""),16);	
					FlashVauleHH+=Long.parseLong((elm.getContent().get(i*16+13).getVaule()+"00"),16);	
					FlashVauleHH+=Long.parseLong((elm.getContent().get(i*16+14).getVaule()+"0000"),16);	
					FlashVauleHH+=Long.parseLong((elm.getContent().get(i*16+15).getVaule()+"000000"),16);	
					//-----------------------------
					BufWordLL=CheckSumLL/2;			
					if((CheckSumL&0x0001)>0)
						BufWordLL|=0x80000000l; // 显式long型，否则int转long会是-80000000
					
					BufWordL=CheckSumL/2;			
					if((CheckSumH&0x0001)>0 && chiprange !=4)   // 11K 区分
						BufWordL |=0x80000000l; // 显式long型，否则int转long会是-80000000
					
					BufWordH=CheckSumH/2;			
					if((CheckSumHH&0x0001)>0)
						BufWordH |=0x80000000l; // 显式long型，否则int转long会是-80000000
					
					BufWordHH=CheckSumHH/2;		 // 显式long型，否则int转long会是-80000000				
					//-----------------------------	//BufWordHH 最高位注定为0，直接异或，即保留flash的bit127的运算	
					NextCheckSumHH	=FlashVauleHH	^	BufWordHH;
					NextCheckSumH	=FlashVauleH	^	BufWordH;
					NextCheckSumL	=FlashVauleL	^	BufWordL;
					NextCheckSumLL	=FlashVauleLL	^	BufWordLL;
					//@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
					//----------------------------- // 9K 9K1  sum 的  0 2 27 29位的异或结果  127  11K 异或结果63
					int buf=0,buf2=0;			
					//+++++++++++++++++++++++++++++++++++++++
					if((CheckSumLL	&(1<<0))!=0)  // 0   LL[0-31]  L[63-32] H[95-64] HH[128-96]
						buf2++;
					if((CheckSumLL	&(1<<2))!=0)  // 2
						buf2++;
					if((CheckSumLL	&(1<<27))!=0)  // 27
						buf2++;
					if((CheckSumLL	&(1<<29))!=0)  // 29
						buf2++;			
					//+++++++++++++++++++++++++++++++++++++++
					if(chiprange==4) {
						
						if((CheckSumH	&(1<<0))!=0)  // 0   LL[0-31]  L[63-32] H[95-64] HH[128-96]
							buf++;
						if((CheckSumH	&(1<<2))!=0)  // 2
							buf++;
						if((CheckSumH	&(1<<27))!=0)  // 27
							buf++;
						if((CheckSumH	&(1<<29))!=0)  // 29
							buf++;		
						
						if(  (buf2 %2)	==1)		{
							NextCheckSumL ^=0x80000000l; //
						}
						buf2= buf;					
					}
					//+++++++++++++++++++++++++++++++++++++++
					if(buf2%2==1)			{
						NextCheckSumHH ^=0x80000000l; //
					}
					//-----------------------------
					CheckSumHH	=(NextCheckSumHH	&0xFFFFFFFF); 
					CheckSumH	=(NextCheckSumH		&0xFFFFFFFF);
					CheckSumL	=(NextCheckSumL		&0xFFFFFFFF);
					CheckSumLL	=(NextCheckSumLL	&0xFFFFFFFF);
				}			
		}		
		// 获取数据的内容结果		
		return 		ByteConvertor.toEight(Long.toHexString(CheckSumHH))
				+	ByteConvertor.toEight(Long.toHexString(CheckSumH))
				+	ByteConvertor.toEight(Long.toHexString(CheckSumL))
				+	ByteConvertor.toEight(Long.toHexString(CheckSumLL));
	}
	
	public String getHexEEpromSumVaule(){
		// long型8字节宽，但不能识别高位为1的字符串转换，因此定义long型
		// 但按int长度有效，校验和的128bit，拆分为4个int的long型变量
		int chiprange=Integer.parseInt(this.getConfigurationProvider().getChipRange(),16);
		long CheckSumH=0,CheckSumHH=0,CheckSumL=0,CheckSumLL=0;	
		
		long NextCheckSumH,NextCheckSumHH,NextCheckSumL,NextCheckSumLL;	

		long FlashVauleH,FlashVauleHH,FlashVauleL,FlashVauleLL;			
		
		long BufWordH,BufWordHH,BufWordL,BufWordLL;			

		
		for(SectorElementVaule elm:eepromElement)
		{
			int lencout=elm.getLen() / 16 ; // 16字节的运算
			for(int i=0;i<lencout;i++)
			{
				//-----------------------------
				FlashVauleLL =Long.parseLong((elm.getContent().get(i*16+0).getVaule()+""),16);	
				FlashVauleLL+=Long.parseLong((elm.getContent().get(i*16+1).getVaule()+"00"),16);	
				FlashVauleLL+=Long.parseLong((elm.getContent().get(i*16+2).getVaule()+"0000"),16);	
				FlashVauleLL+=Long.parseLong((elm.getContent().get(i*16+3).getVaule()+"000000"),16);	
				
				FlashVauleL =Long.parseLong((elm.getContent().get(i*16+4).getVaule()+""),16);	
				FlashVauleL+=Long.parseLong((elm.getContent().get(i*16+5).getVaule()+"00"),16);	
				FlashVauleL+=Long.parseLong((elm.getContent().get(i*16+6).getVaule()+"0000"),16);	
				FlashVauleL+=Long.parseLong((elm.getContent().get(i*16+7).getVaule()+"000000"),16);	
				
				FlashVauleH =Long.parseLong((elm.getContent().get(i*16+8).getVaule()+""),16);	
				FlashVauleH+=Long.parseLong((elm.getContent().get(i*16+9).getVaule()+"00"),16);	
				FlashVauleH+=Long.parseLong((elm.getContent().get(i*16+10).getVaule()+"0000"),16);	
				FlashVauleH+=Long.parseLong((elm.getContent().get(i*16+11).getVaule()+"000000"),16);	
				
				FlashVauleHH =Long.parseLong((elm.getContent().get(i*16+12).getVaule()+""),16);	
				FlashVauleHH+=Long.parseLong((elm.getContent().get(i*16+13).getVaule()+"00"),16);	
				FlashVauleHH+=Long.parseLong((elm.getContent().get(i*16+14).getVaule()+"0000"),16);	
				FlashVauleHH+=Long.parseLong((elm.getContent().get(i*16+15).getVaule()+"000000"),16);	
				//-----------------------------
				BufWordLL=CheckSumLL/2;			
				if((CheckSumL&0x0001)>0)
					BufWordLL|=0x80000000l; // 显式long型，否则int转long会是-80000000
				
				BufWordL=CheckSumL/2;			
				if((CheckSumH&0x0001)>0 && chiprange !=4)   // 11K 区分
					BufWordL |=0x80000000l; // 显式long型，否则int转long会是-80000000
				
				BufWordH=CheckSumH/2;			
				if((CheckSumHH&0x0001)>0)
					BufWordH |=0x80000000l; // 显式long型，否则int转long会是-80000000
				
				BufWordHH=CheckSumHH/2;		 // 显式long型，否则int转long会是-80000000				
				//-----------------------------	//BufWordHH 最高位注定为0，直接异或，即保留flash的bit127的运算	
				NextCheckSumHH	=FlashVauleHH	^	BufWordHH;
				NextCheckSumH	=FlashVauleH	^	BufWordH;
				NextCheckSumL	=FlashVauleL	^	BufWordL;
				NextCheckSumLL	=FlashVauleLL	^	BufWordLL;
				//@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
				//----------------------------- // 9K 9K1  sum 的  0 2 27 29位的异或结果  127  11K 异或结果63
				int buf=0,buf2=0;			
				//+++++++++++++++++++++++++++++++++++++++
				if((CheckSumLL	&(1<<0))!=0)  // 0   LL[0-31]  L[63-32] H[95-64] HH[128-96]
					buf2++;
				if((CheckSumLL	&(1<<2))!=0)  // 2
					buf2++;
				if((CheckSumLL	&(1<<27))!=0)  // 27
					buf2++;
				if((CheckSumLL	&(1<<29))!=0)  // 29
					buf2++;			
				//+++++++++++++++++++++++++++++++++++++++
				if(chiprange==4) {
					
					if((CheckSumH	&(1<<0))!=0)  // 0   LL[0-31]  L[63-32] H[95-64] HH[128-96]
						buf++;
					if((CheckSumH	&(1<<2))!=0)  // 2
						buf++;
					if((CheckSumH	&(1<<27))!=0)  // 27
						buf++;
					if((CheckSumH	&(1<<29))!=0)  // 29
						buf++;		
					
					if(  (buf2 %2)	==1)		{
						NextCheckSumL ^=0x80000000l; //
					}
					buf2= buf;					
				}
				//+++++++++++++++++++++++++++++++++++++++
				if(buf2%2==1)			{
					NextCheckSumHH ^=0x80000000l; //
				}
				//-----------------------------
				CheckSumHH	=(NextCheckSumHH	&0xFFFFFFFF); 
				CheckSumH	=(NextCheckSumH		&0xFFFFFFFF);
				CheckSumL	=(NextCheckSumL		&0xFFFFFFFF);
				CheckSumLL	=(NextCheckSumLL	&0xFFFFFFFF);
			}			
		}		
		// 获取数据的内容结果		
		return 		ByteConvertor.toEight(Long.toHexString(CheckSumHH))
				+	ByteConvertor.toEight(Long.toHexString(CheckSumH))
				+	ByteConvertor.toEight(Long.toHexString(CheckSumL))
				+	ByteConvertor.toEight(Long.toHexString(CheckSumLL));
	}

}
