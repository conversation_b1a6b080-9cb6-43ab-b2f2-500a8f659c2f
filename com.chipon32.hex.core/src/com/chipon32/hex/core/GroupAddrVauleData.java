package com.chipon32.hex.core;

import java.util.ArrayList;
import java.util.List;

import com.chipon32.hex.core.util.ByteConvertor;
// 行化的地址指令集合
public class GroupAddrVauleData 
{
	//###################### 起始地址
	private long address;
	
	public long getAddress() {
		return address;
	}

	public void setAddress(long address) {
		this.address = address;
	}

	public String getAddressHexValue()
	{ 
		String columCount = "";
		columCount = ByteConvertor.toEight(Long.toString(address, 16));		
		return columCount;
	} 
	//################### 当前行的结果列表,16个字节对象
	private List<AddrVauleData> addrCommDataList;
	
	public List<AddrVauleData> getAddrVauleDatasDataList() {
		return addrCommDataList;
	}

	public void setRomDataList(List<AddrVauleData> addrcommDataList) {
		this.addrCommDataList = addrcommDataList;
	}
	//#################默认空的构建 16个 指令地址的集合
	public GroupAddrVauleData()
	{
		addrCommDataList = new ArrayList<AddrVauleData>(16); // 只是数值识别，内容未定义申请空间的
	}
	//#################当个地址下指令的追加到当前结果
	public void addRomData(AddrVauleData romData)
	{
		addrCommDataList.add(romData);
	}

	// 设定对应索引元素下的结果
	public void setCommand(int index, String command)
	{
		AddrVauleData addrCommData = addrCommDataList.get(index);
		addrCommData.setVaule(command);
	}
	//  HEX 行内容的 ascii结果显示内容
	public String getAscii()
	{
		StringBuffer buffer = new StringBuffer();
		if(addrCommDataList.size()==0 || addrCommDataList.get(0)==null)
			return "????????????????";
		for(AddrVauleData romData : addrCommDataList)
		{
			byte byteLow = romData.getByteVaule();
			
			byte[] array = new byte[] { byteLow};
			
			for(byte b:array){
				int i = byteToint(b);
				if(i < '!' || i > '~'){
					buffer.append(".");
				}else{
					buffer.append((char) i);
				}
			}
		}
		return buffer.toString();
	}
	
	// 类型扩展
	public int byteToint(byte b){
		int i;
		if(b < 0){
			i = 256 + b;
		}else{
			i = b;
		}
		return i;
	}
}
