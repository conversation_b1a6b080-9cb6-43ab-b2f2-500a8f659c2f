package com.chipon32.hex.core;

import com.chipon32.hex.core.util.ByteConvertor;



public class AddrVauleData implements Cloneable
{
	private long address; // 地址	//  
	private String Vaule;  // ָ指令的8位形式
	//==========================================================================
	// 带目标指令的构建
	public AddrVauleData(String vaule) {
		while(vaule.length()<2)		
			vaule="0"+vaule;		
		this.Vaule = vaule.substring(vaule.length()-2).toUpperCase();
	}
	//==========================================================================
	public long getAddress() {
		return address;
	}
	public void setAddress(long address) {
		this.address = address;
	}
	
	public String getAddressHexValue()
	{
		return toEight(Long.toString(address, 16));
	}	
	//--------------------------------
	public byte getByteVaule()
	{
		return ByteConvertor.hexToByte(Vaule);
	}
	public String getVaule() {
		return Vaule;
	}
	public void setVaule(String vaule) {	
		while(vaule.length()<2)		
			vaule="0"+vaule;		
		this.Vaule = vaule.substring(vaule.length()-2).toUpperCase();
	}
	//==========================================================================

	public String toEight(String input)
	{
		String tmp = input.toUpperCase();		
		while(tmp.length()<8)
		{
			tmp="0"+tmp;
		}
		return tmp.substring(tmp.length()-8);
	}
	//==========================================================================


	//==========================================================================
	// 类似一个对象一个物理地址，构建对象时直接拿物理地址作为结果的存贮空间，如果空间相同，
	// 才调用equals方法，不然不能重复的每建立一个对象都要判断equals方法，效率很差
	@Override
	public int hashCode() {
		final int prime = 31;
		long result = 1;
		result = prime * result + address;
		result = prime * result + ((Vaule == null) ? 0 : Vaule.hashCode());
		return (int) result;
	}
	// 针对的对象判断相等的比较
	@Override
	public boolean equals(Object obj) {
		
		if (this == obj)
			return true;
		
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		// 主要比较就是地址 指令要相同就表示相同
		AddrVauleData other = (AddrVauleData) obj;
		if (address != other.address)
			return false;
		
		if (Vaule == null) {
			if (other.Vaule != null)
				return false;
		} else if (!Vaule.equals(other.Vaule))
			return false;
				
		return true;
	}
}
