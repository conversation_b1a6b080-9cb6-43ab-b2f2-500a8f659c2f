<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <artifactId>com.chipon32.hex.core</artifactId>
  <build>
    <sourceDirectory>src</sourceDirectory>
    <plugins>
      <plugin>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>3.8.1</version>
        <configuration>
          <release>11</release>
        </configuration>
      </plugin>
    </plugins>
  </build>
  <packaging>eclipse-plugin</packaging>
  <parent>
  	<groupId>com.chipon32</groupId>
  	<artifactId>com.chipon32.parent</artifactId>
  	<version>${revision}</version>
  	<relativePath>../</relativePath>
  </parent>
</project>