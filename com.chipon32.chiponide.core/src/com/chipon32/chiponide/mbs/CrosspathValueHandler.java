package com.chipon32.chiponide.mbs;

import org.eclipse.cdt.managedbuilder.core.BuildException;
import org.eclipse.cdt.managedbuilder.core.IBuildObject;
import org.eclipse.cdt.managedbuilder.core.IHoldsOptions;
import org.eclipse.cdt.managedbuilder.core.IManagedOptionValueHandler;
import org.eclipse.cdt.managedbuilder.core.IOption;
import org.eclipse.core.runtime.Path;
import org.eclipse.core.runtime.Platform;
import org.eclipse.jface.dialogs.MessageDialog;

import com.chipon32.chiponide.core.paths.win32.SystemPathsWin32;

public class CrosspathValueHandler implements IManagedOptionValueHandler {

    @Override
    public boolean handleValue(IBuildObject configuration, IHoldsOptions holder,
            IOption option, String extraArgument, int event) {

        String currCrosspath = (String) option.getValue();
//      System.out.println(currCrosspath);
        if(!currCrosspath.isEmpty() && !currCrosspath.endsWith("ChipONCC32")) { //$NON-NLS-1$
            MessageDialog.openWarning(null, Messages.CrosspathValueHandler_1, Messages.CrosspathValueHandler_2);
            try {
                if(Platform.getOS().equals(Platform.OS_WIN32) && SystemPathsWin32.fWinKF32Path!=null) {
                    option.setValue(SystemPathsWin32.fWinKF32Path.toOSString());
                }
            } catch (BuildException e) {
                e.printStackTrace();
            }
            
        }else if(currCrosspath.isEmpty()) {
            try {
                if(Platform.getOS().equals(Platform.OS_WIN32) && SystemPathsWin32.fWinKF32Path!=null) {
                    option.setValue(SystemPathsWin32.fWinKF32Path.toOSString());
                }
            } catch (BuildException e) {
                e.printStackTrace();
            }
            
        }else {
//            SystemPathsWin32.fWinKF32Path = new Path(currCrosspath); //下一次重新新建项目使用这个工具链路径信息
        }
        
        return false;
    }

    @Override
    public boolean isDefaultValue(IBuildObject configuration,
            IHoldsOptions holder, IOption option, String extraArgument) {
     // TODO Auto-generated method stub
        
        return false;
    }

    @Override
    public boolean isEnumValueAppropriate(IBuildObject configuration,
            IHoldsOptions holder, IOption option, String extraArgument,
            String enumValue) {
        // TODO Auto-generated method stub
        return false;
    }

}
