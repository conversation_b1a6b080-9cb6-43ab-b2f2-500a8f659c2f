package com.chipon32.chiponide.mbs;

import java.util.ArrayList;
import java.util.List;


public class MapSizeCollect {
    boolean    isParent;
    
    String fileName;
    
    String SegmentName;
    String   SegmentAddress;
    String   SegmentSize;
    
    boolean    SegmentUse;
    
    private List<MapSizeCollect> fChildren = new ArrayList<MapSizeCollect>();

    public boolean isParent() {
        return isParent;
    }

    public void setParent(boolean isParent) {
        this.isParent = isParent;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getSegmentName() {
        return SegmentName;
    }

    public void setSegmentName(String segmentName) {
        SegmentName = segmentName;
    }

    public String getSegmentSize() {
        if(SegmentSize.startsWith("0x"))
        {
            String buf=SegmentSize.substring(2);
            while(buf.length()<4)
                buf="0"+buf;
            return buf;
        }
        return SegmentSize;
    }

    public void setSegmentSize(String segmentSize) {
        SegmentSize = segmentSize;
    }
    
    public String getSegmentAddress() {
        return SegmentAddress.trim();
    }

    public void setSegmentAddress(String segmentAddress) {
        SegmentAddress = segmentAddress;
    }

    public boolean getSegmentUse() {
        return SegmentUse;
    }

    public void setSegmentUse(boolean segmentUse) {
        SegmentUse = segmentUse;
    }

    public List<MapSizeCollect> getfChildren() {
        return fChildren;
    }

    public void setfChildren(List<MapSizeCollect> fChildren) {
        this.fChildren = fChildren;
    } 
   
    public void addfChildren(MapSizeCollect fChildren) {
        if(this.fChildren==null)
        this.fChildren = new ArrayList<MapSizeCollect>();
        this.fChildren.add(fChildren);
    }
    

}
