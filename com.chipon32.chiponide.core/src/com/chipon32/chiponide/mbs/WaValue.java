package com.chipon32.chiponide.mbs;

import org.eclipse.cdt.managedbuilder.core.BuildException;
import org.eclipse.cdt.managedbuilder.core.IConfiguration;
import org.eclipse.cdt.managedbuilder.core.IManagedBuildInfo;
import org.eclipse.cdt.managedbuilder.core.IManagedProject;
import org.eclipse.cdt.managedbuilder.core.IOption;
import org.eclipse.cdt.managedbuilder.core.IOptionCommandGenerator;
import org.eclipse.cdt.managedbuilder.core.ManagedBuildManager;
import org.eclipse.cdt.managedbuilder.internal.macros.BuildfileMacroSubstitutor;
import org.eclipse.cdt.managedbuilder.internal.macros.ExplicitFileMacroCollector;
import org.eclipse.cdt.utils.cdtvariables.IVariableSubstitutor;
import org.eclipse.core.resources.IProject;

import com.chipon32.chiponide.core.properties.ChipOnProjectProperties;
import com.chipon32.chiponide.core.properties.ProjectPropertyManager;
import com.chipon32.hex.core.util.IConfigurationProvider;
import com.chipon32.util.provider.ConfigurationFactory;




@SuppressWarnings("restriction")
public class WaValue implements IOptionCommandGenerator {
    
    public WaValue() {
            }        
    /**
     * 根据实际选择的编译器版本选择编译指令；
     */
    @SuppressWarnings({"unused"})
    @Override
    public String generateCommand(IOption option,
            IVariableSubstitutor macroSubstitutor) {

        if(macroSubstitutor instanceof ExplicitFileMacroCollector){//
            ExplicitFileMacroCollector macroCollector = 
                    (ExplicitFileMacroCollector)macroSubstitutor;

        }else if(macroSubstitutor instanceof BuildfileMacroSubstitutor){    //构建文件宏构造设计
            BuildfileMacroSubstitutor buildfileMacroSubstitutor = 
                    (BuildfileMacroSubstitutor)macroSubstitutor;
            
            // 从传递的服务对象获取配置，从而可以从配置中获取到项目
            IConfiguration configuration = buildfileMacroSubstitutor.getConfiguration();            
            IProject project = (IProject) configuration.getOwner(); 
//          IProject project = (IProject) configuration.getManagedProject().getOwner();  //获取project的第二种方法

            // 项目就可以获取项目的扩展属性了，如型号，编译器版本等信息
            ProjectPropertyManager projectPropertyManage =     ProjectPropertyManager.getPropertyManager(project);
            ChipOnProjectProperties projectProperties = projectPropertyManage.getActiveProperties();
            
            // 属性信息的差异化应用，从而返回不同的字符串结果
            String compilerType = projectProperties.getCompilerType();
            
            String chipName     = projectProperties.getChipName();
            IConfigurationProvider provider = ConfigurationFactory.getProvider(chipName);

            int  Kernel = Integer.parseInt(provider.getKernel(),16);    
            int  chiprange = Integer.parseInt(provider.getChipRange(),16);
            
            int  issupportGP = Integer.parseInt(provider.getIsSupportR16(),16);  
            int  issupportDSP = Integer.parseInt(provider.getIsSupportACC(),16);  
            int  issupportFPU = Integer.parseInt(provider.getIsSupportFPU(),16);  
            String Arch_String="kf32";
            if(issupportDSP>0){
                Arch_String+="d";
                if(issupportFPU>0)
                    Arch_String+="f"; // means GD GDF
            }
            else if(issupportFPU>0)
                Arch_String+="f";   // means GF
            else if(issupportGP>0)
                Arch_String+="g";   // means G
            else
                Arch_String+="r";
            //获取项目类型，汇编和C项目的命令不一样
            IManagedBuildInfo bi = ManagedBuildManager.getBuildInfo(project);
            if (bi != null) 
            {           
                IManagedProject buildcfg = bi.getManagedProject();
                String type="";                
                try{
                	type= buildcfg.getProjectType().getId();               
                }catch (Exception e) {
                    // 为了兼容，为了让c项目选项默认并在上面的  类型 asm变为s。
                    type="com.chipon32.chiponide.core.projectType.s";
                }
                try {
                    if(option!=null && option.getBasicValueType() == IOption.BOOLEAN && option.getBooleanValue()==false )
                    {
                        return ""; 
                    }
                } catch (BuildException e) {
                    // TODO 自动生成的 catch 块
                    e.printStackTrace();
                }
                // 如果是汇编项目
                if (type.equals("com.chipon32.chiponide.core.projectType.s"))                 {                       
                        return "--kf32-arch="+Arch_String;           // -I\"${GPUTILS_EXPAND_INCLUDE_PATH}\"                      
                }else{  
                       return "-Wa,"+"--kf32-arch="+Arch_String+",-I\"${GPUTILS_EXPAND_INCLUDE_PATH}\"";     
                }                
            }

        } 
        // 默认情况下的结果返回
        return "";  //"-Wa,-I\"${GPUTILS_EXPAND_INCLUDE_PATH}\"";
    }

}
