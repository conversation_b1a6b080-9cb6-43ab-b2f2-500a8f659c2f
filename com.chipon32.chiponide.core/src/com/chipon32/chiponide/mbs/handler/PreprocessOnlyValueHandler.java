package com.chipon32.chiponide.mbs.handler;

import org.eclipse.cdt.managedbuilder.core.IBuildObject;
import org.eclipse.cdt.managedbuilder.core.IHoldsOptions;
import org.eclipse.cdt.managedbuilder.core.IManagedOptionValueHandler;
import org.eclipse.cdt.managedbuilder.core.IOption;
import org.eclipse.cdt.managedbuilder.internal.core.ToolChain;

public class PreprocessOnlyValueHandler implements IManagedOptionValueHandler {

    @Override
    public boolean handleValue(IBuildObject configuration, IHoldsOptions holder,
            IOption option, String extraArgument, int event) {
        if (event == IManagedOptionValueHandler.EVENT_APPLY
                && holder instanceof ToolChain) {
            IOption cppTargetOption = ((Tool<PERSON>hain) holder)
                    .getToolsBySuperClassId(
                            "com.chipon32.chiponide.tool.cpp.compiler.lang")[0]
                                    .getOptionBySuperClassId(
                                            "com.chipon.tool.c.compiler.clang.base.option.preproconly");
            if (cppTargetOption != null) {
                cppTargetOption.setValue(option.getValue());
            }

            IOption cTargetOption = ((ToolChain) holder).getToolsBySuperClassId(
                    "com.chipon32.chiponide.tool.c.compiler.lang")[0]
                            .getOptionBySuperClassId(
                                    "com.chipon.tool.c.compiler.clang.base.option.preproconly");

            if (cTargetOption != null) {
                cTargetOption.setValue(option.getValue());
            }
        }
        return false;
    }

    @Override
    public boolean isDefaultValue(IBuildObject configuration,
            IHoldsOptions holder, IOption option, String extraArgument) {
        return false;
    }

    @Override
    public boolean isEnumValueAppropriate(IBuildObject configuration,
            IHoldsOptions holder, IOption option, String extraArgument,
            String enumValue) {
        return false;
    }

}
