package com.chipon32.chiponide.mbs.handler;

import java.util.List;

import org.eclipse.cdt.managedbuilder.core.IBuildObject;
import org.eclipse.cdt.managedbuilder.core.IHoldsOptions;
import org.eclipse.cdt.managedbuilder.core.IManagedOptionValueHandler;
import org.eclipse.cdt.managedbuilder.core.IOption;
import org.eclipse.cdt.managedbuilder.internal.core.ToolChain;

public class UndefineMacroValueHandler implements IManagedOptionValueHandler {

    @Override
    public boolean handleValue(IBuildObject configuration, IHoldsOptions holder,
            IOption option, String extraArgument, int event) {
        boolean isSetValue = false;
        if (holder instanceof ToolChain) {
            switch (event) {
            case IManagedOptionValueHandler.EVENT_APPLY:
                isSetValue = true;
                break;
            case IManagedOptionValueHandler.EVENT_OPEN:
                if (option.getValue() instanceof List
                        && ((List) option.getValue()).size() > 0) {
                    isSetValue = true;
                }
                break;
            default:
                break;
            }
        }

        if (isSetValue) {
            IOption cppOption = ((Tool<PERSON>hain) holder).getToolsBySuperClassId(
                    "com.chipon32.chiponide.tool.cpp.compiler.lang")[0]
                            .getOptionBySuperClassId(
                                    "com.chipon.tool.c.compiler.clang.base.option.undefmac");
            if (cppOption != null
                    && !cppOption.getValue().equals(option.getValue())) {
                cppOption.setValue(option.getValue());
            }

            IOption cOption = ((ToolChain) holder).getToolsBySuperClassId(
                    "com.chipon32.chiponide.tool.c.compiler.lang")[0]
                            .getOptionBySuperClassId(
                                    "com.chipon.tool.c.compiler.clang.base.option.undefmac");

            if (cOption != null
                    && !cOption.getValue().equals(option.getValue())) {
                cOption.setValue(option.getValue());
            }
        }

        return false;
    }

    @Override
    public boolean isDefaultValue(IBuildObject configuration,
            IHoldsOptions holder, IOption option, String extraArgument) {
        return false;
    }

    @Override
    public boolean isEnumValueAppropriate(IBuildObject configuration,
            IHoldsOptions holder, IOption option, String extraArgument,
            String enumValue) {
        return false;
    }

}
