package com.chipon32.patch.model;

import java.util.ArrayList;
import java.util.List;
/**
 * 
 * @ClassName PatchModel
 *
 */
public class PatchModel {
	/**
	 * 
	 * @ClassName PluginModel
	 *
	 */
	public class PluginModel {

		private String name;
		private String version;

		public String getName() {
			return name;
		}

		public void setName(String name) {
			this.name = name;
		}

		public String getVersion() {
			return version;
		}

		public void setVersion(String version) {
			this.version = version;
		}

	}

	private String version;
	private String description;
	private String date;

	private List<PluginModel> pluginModelList = new ArrayList<PluginModel>();

	public String getVersion() {
		return version;
	}

	public void setVersion(String version) {
		this.version = version;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public String getDate() {
		return date;
	}

	public void setDate(String date) {
		this.date = date;
	}

	public List<PluginModel> getPluginModelList() {
		return pluginModelList;
	}

}
