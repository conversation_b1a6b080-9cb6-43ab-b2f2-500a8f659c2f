package com.chipon32.patch.dialog;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.CharArrayWriter;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.FilenameFilter;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.io.OutputStreamWriter;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.InvocationTargetException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;
import java.util.zip.ZipInputStream;

import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.Element;
import org.dom4j.io.OutputFormat;
import org.dom4j.io.SAXReader;
import org.dom4j.io.XMLWriter;
import org.eclipse.core.runtime.IProgressMonitor;
import org.eclipse.core.runtime.Platform;
import org.eclipse.jface.dialogs.MessageDialog;
import org.eclipse.jface.dialogs.ProgressMonitorDialog;
import org.eclipse.jface.dialogs.TrayDialog;
import org.eclipse.jface.operation.IRunnableWithProgress;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.events.SelectionListener;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.FileDialog;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.Shell;
import org.eclipse.swt.widgets.Table;
import org.eclipse.swt.widgets.TableColumn;
import org.eclipse.swt.widgets.TableItem;
import org.eclipse.ui.PlatformUI;

import com.chipon32.patch.Messages;
import com.chipon32.patch.model.PatchModel;
import com.chipon32.patch.model.PatchModel.PluginModel;
import com.chipon32.patch.util.FileUtil;

/**
 * 
 * @ClassName PatchManagementDialog
 *
 */
public class PatchManagementDialog extends TrayDialog {

	private List<PatchModel> patchModelList;
	private Table historyTable;
	private Table contentTable;
	private String installPath;

	public PatchManagementDialog(Shell shell) {
		super(shell);
		installPath = Platform.getInstallLocation().getURL().getPath();
		if (installPath.startsWith("/")) {
			installPath = installPath.substring(1, installPath.length());
		}
		refreshPatchModelList();
	}

	private void refreshPatchModelList() {
		patchModelList = new ArrayList<PatchModel>();

		String patchMainPath = installPath + "/patch.xml";
		if (!new File(patchMainPath).exists()) {
			return;
		}
		SAXReader reader = new SAXReader();
		Document document = null;
		try {
			document = reader.read(new File(patchMainPath));
		} catch (DocumentException e) {
			e.printStackTrace();
		}
		if (document == null) {
			return;
		}
		Element rootElement = document.getRootElement(); 
		for (Object obj : rootElement.elements("patch")) {
			if (obj instanceof Element) {
				PatchModel patchModel = new PatchModel();
				Element oldPatchEle = (Element) obj;
				patchModel.setDate(oldPatchEle.attributeValue("date"));
				patchModel.setDescription(oldPatchEle.attributeValue("des"));
				patchModel.setVersion(oldPatchEle.attributeValue("version"));
				for (Object item : oldPatchEle.elements("plugin")) {
					if (item instanceof Element) {
						Element pluginEle = (Element) item;
						PluginModel pluginModel = patchModel.new PluginModel();
						pluginModel.setName(pluginEle.attributeValue("name"));
						pluginModel.setVersion(pluginEle.attributeValue("version"));
						patchModel.getPluginModelList().add(pluginModel);
					}
				}
				patchModelList.add(patchModel);
			}
		}
	}

	@Override
	protected Control createDialogArea(Composite parent) {
		parent.getShell().setText(Messages.PATCH_MANAGEMENT);
		Composite composite = (Composite) super.createDialogArea(parent);
		Label patchHistoryLabel = new Label(composite, SWT.NONE);
		patchHistoryLabel.setText(Messages.PATCH_HISTORY);

		historyTable = new Table(composite, SWT.BORDER | SWT.V_SCROLL | SWT.FULL_SELECTION);
		historyTable.setHeaderVisible(true);
		historyTable.setLinesVisible(true);
		historyTable.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, true, 1, 15));
		new TableColumn(historyTable, SWT.CENTER);

		historyTable.addSelectionListener(new SelectionListener() {

			@Override
			public void widgetSelected(SelectionEvent e) {
				Object data = historyTable.getSelection()[0].getData();
				if (data instanceof PatchModel) {
					contentTable.removeAll();
					PatchModel patchModel = (PatchModel) data;
					for (PluginModel pluginModel : patchModel.getPluginModelList()) {
						TableItem item = new TableItem(contentTable, SWT.NONE);
						item.setText(1, pluginModel.getName());
						item.setText(2, pluginModel.getVersion());
					}
				}
			}

			@Override
			public void widgetDefaultSelected(SelectionEvent e) {

			}
		});

		TableColumn dataCol = new TableColumn(historyTable, SWT.CENTER);
		dataCol.setText(Messages.UPDATE_DATE);
		dataCol.setWidth(300);

		TableColumn versionCol = new TableColumn(historyTable, SWT.CENTER);
		versionCol.setText(Messages.UPDATE_VERSION);
		versionCol.setWidth(200);

		TableColumn desCol = new TableColumn(historyTable, SWT.CENTER);
		desCol.setText(Messages.UPDATE_DESCRIPTION);
		desCol.setWidth(300);

		refreshHistoryTable();

		Label patchContentLabel = new Label(composite, SWT.NONE);
		patchContentLabel.setText(Messages.PATCH_CONTENT);

		contentTable = new Table(composite, SWT.BORDER | SWT.V_SCROLL | SWT.FULL_SELECTION);
		contentTable.setHeaderVisible(true);
		contentTable.setLinesVisible(true);
		contentTable.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, true, 1, 15));
		new TableColumn(contentTable, SWT.CENTER);

		TableColumn pluginNameCol = new TableColumn(contentTable, SWT.CENTER);
		pluginNameCol.setText(Messages.PLUGIN_NAME);
		pluginNameCol.setWidth(400);

		TableColumn pluginVersionCol = new TableColumn(contentTable, SWT.CENTER);
		pluginVersionCol.setText(Messages.PLUGIN_VERSION);
		pluginVersionCol.setWidth(400);
		return composite;
	}

	private void refreshHistoryTable() {
		if (historyTable != null && !historyTable.isDisposed()) {
			historyTable.removeAll();
		}
		if (contentTable != null && !contentTable.isDisposed()) {
			contentTable.removeAll();
		}
		for (PatchModel patchModel : patchModelList) {
			TableItem item = new TableItem(historyTable, SWT.NONE);
			item.setText(1, patchModel.getDate());
			item.setText(2, patchModel.getVersion());
			item.setText(3, patchModel.getDescription());
			item.setData(patchModel);
		}
	}

	@Override
	protected void createButtonsForButtonBar(Composite parent) {
		createButton(parent, 0, Messages.INSTALL, true);
		createButton(parent, 1, Messages.UNINSTALL, false);
		createButton(parent, 2, Messages.CLOSE, false);
	}

	@Override
	protected void buttonPressed(int buttonId) {
		switch (buttonId) {
		case 0:
			FileDialog dialog = new FileDialog(Display.getDefault().getActiveShell());
			String[] filter = { "*.patch" };
			dialog.setText(Messages.SELECT_PATCH_PATH);
			dialog.setFilterExtensions(filter);
			String path = dialog.open();
			if (path != null && path.length() > 0) {
				installPatch(path);
			}

			break;
		case 1:
			if (historyTable.getSelection().length == 0) {
				return;
			}

			Object obj = historyTable.getSelection()[0].getData();
			if (!(obj instanceof PatchModel)) {
				return;
			}
			PatchModel patchModel = (PatchModel) obj;
			IRunnableWithProgress runnable = new IRunnableWithProgress() {

				@Override
				public void run(IProgressMonitor monitor) throws InvocationTargetException, InterruptedException {
					monitor.beginTask(Messages.UNINSTALL_START + "... ", IProgressMonitor.UNKNOWN);
					monitor.setTaskName(Messages.UNINSTALL_IN_PROGRESS + "...");
					uninstall(patchModel);
				}
			};
			try {
				new ProgressMonitorDialog(getShell()).run(true, true, runnable);
			} catch (InvocationTargetException | InterruptedException e) {
				e.printStackTrace();
			}
			break;
		case 2:
			cancelPressed();
			break;
		default:
			break;
		}
	}

	private void uninstall(PatchModel patchModel) {
		String pluginFolderPath = installPath + "/plugins";
		String patchMainPath = installPath + "/patch.xml";
		Document document = null;
		SAXReader reader = new SAXReader();
		try {
			document = reader.read(new File(patchMainPath));

			String budleInfoFilePath = installPath
					+ "configuration/org.eclipse.equinox.simpleconfigurator/bundles.info";
			System.gc();
			Map<String, String> rollbackVersionMap = getRollbackVersion(patchModelList, patchModel);
			for (PluginModel pluginModel : patchModel.getPluginModelList()) {

				File file = new File(pluginFolderPath + File.separator + pluginModel.getName() + "_"
						+ pluginModel.getVersion() + ".jar");
				if (file.isFile() && file.exists()) {
					boolean flag = file.delete();
					if (!flag) {
						file.deleteOnExit();
					}
				}

				String pluginName = pluginModel.getName();
				String oldVersion = rollbackVersionMap.get(pluginName);
				if (oldVersion == null) {
					continue;
				}

				FileUtil.replacFileContent(budleInfoFilePath, pluginName + "_" + pluginModel.getVersion(),
						pluginName + "_" + oldVersion);
				FileUtil.replacFileContent(budleInfoFilePath, pluginName + "," + pluginModel.getVersion(),
						pluginName + "," + oldVersion);

			}
			removeNodeByAttributeValue(document, patchMainPath, "patch", "version", patchModel.getVersion());

			Display.getDefault().asyncExec(() -> {
				refreshPatchModelList();
				refreshHistoryTable();
				okPressed();
				if (MessageDialog.openQuestion(getShell(), Messages.WARNING, Messages.UNINSTALL_COMPLETE)) {
					PlatformUI.getWorkbench().restart();
				}
			});

		} catch (DocumentException e) {
			e.printStackTrace();
			errorWarning(Messages.MAIN_PATCH_FILE_PARSING_FAILED);
		}
	}

	private void installPatch(String path) {
		IRunnableWithProgress runnable = new IRunnableWithProgress() {

			@Override
			public void run(IProgressMonitor monitor) throws InvocationTargetException, InterruptedException {
				monitor.beginTask(Messages.UPDATE_START + "... ", IProgressMonitor.UNKNOWN);
				monitor.setTaskName(Messages.UPDATE_IN_PROGRESS + "...");

				SimpleDateFormat simpleFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
				String currentDate = simpleFormat.format(System.currentTimeMillis());
				String installPathLocal = Platform.getInstallLocation().getURL().getPath();
				if (installPathLocal.startsWith("/")) {
					installPathLocal = installPathLocal.substring(1, installPathLocal.length());
				}

				String budleInfoFilePath = installPathLocal
						+ "/configuration/org.eclipse.equinox.simpleconfigurator/bundles.info";

				File budleInfoFolder = new File(budleInfoFilePath);
				if (!budleInfoFolder.exists()) {
					errorWarning(Messages.CONFIGURATION_FILE_NOT_EXIST);
					return;
				}

				File patchFile = new File(path);
				if (!patchFile.exists()) {
					errorWarning(Messages.PATCH_FILE_NOT_EXIST);
					return;
				}

				File zipTempFile = new File(patchFile.getParent() + File.separator + "Temp");
				FileUtil.deleteFile(zipTempFile);
				zipTempFile.mkdirs();

				String patchXMLPath = "";
				List<String> pluginPathList = new ArrayList<String>();

				FileInputStream fis = null;
				ZipFile zipFile = null;
				try {
					zipFile = new ZipFile(patchFile);
					fis = new FileInputStream(path);
					ZipInputStream zInputStream = new ZipInputStream(fis);

					ZipEntry entry;

					while ((entry = zInputStream.getNextEntry()) != null) {
						File file = new File(zipTempFile.getAbsolutePath() + File.separator + entry.getName());
						if (entry.getName().equals("patch.xml")) {
							patchXMLPath = zipTempFile.getAbsolutePath() + File.separator + entry.getName();
							file.getParentFile().mkdirs();
							FileOutputStream fOutput = new FileOutputStream(file);
							OutputStreamWriter writer = new OutputStreamWriter(fOutput, "UTF-8");
							BufferedWriter bufferedWriter = new BufferedWriter(writer);
							InputStream in = zipFile.getInputStream(entry);
							if (in == null) {
								continue;
							}
							InputStreamReader reader = new InputStreamReader(in, "UTF-8");
							BufferedReader buffReader = new BufferedReader(reader);
							String line;
							while ((line = buffReader.readLine()) != null) {
								bufferedWriter.write(line);
								bufferedWriter.newLine();
							}
							fOutput.flush();
							writer.flush();
							bufferedWriter.flush();
							bufferedWriter.close();
							writer.close();
							fOutput.close();
							buffReader.close();
							reader.close();
							in.close();
						} else {
							pluginPathList.add(zipTempFile.getAbsolutePath() + File.separator + entry.getName());
							if (entry.isDirectory()) {
								file.mkdirs();
							} else {
								FileOutputStream fos = new FileOutputStream(
										zipTempFile.getAbsolutePath() + File.separator + entry.getName());
								if (entry.getName().equals("patch.xml")) {
									patchXMLPath = zipTempFile.getAbsolutePath() + File.separator + entry.getName();
								} else {
									pluginPathList
											.add(zipTempFile.getAbsolutePath() + File.separator + entry.getName());
								}
								byte[] buffer = new byte[1024];
								int len = -1;
								while ((len = zInputStream.read(buffer)) != -1) {
									fos.write(buffer, 0, len);
								}
								fos.close();
								zInputStream.closeEntry();
							}

						}
					}
					zInputStream.close();
				} catch (IOException e) {
					e.printStackTrace();
					errorWarning(Messages.UNZIP_PATCH_FILE_FAILED + e.getMessage());
					return;
				} finally {
					try {
						if (fis != null) {
							fis.close();
						}
						if (zipFile != null) {
							zipFile.close();
						}

					} catch (IOException e) {
						e.printStackTrace();
					}
				}
				if (patchXMLPath == null || patchXMLPath.length() == 0) {
					openWarning(Messages.NOT_A_VALID_PATCH_FILE);
					FileUtil.deleteFile(zipTempFile);
					return;
				}

				String patchMainPath = installPathLocal + "patch.xml";
				SAXReader reader = new SAXReader();
				Document document = null;
				try {
					document = reader.read(new File(patchMainPath));
				} catch (DocumentException e1) {
					e1.printStackTrace();
					errorWarning(Messages.FAILED_TO_PARSE_MAIN_PATCH);
					return;
				}

				if (document == null) {
					errorWarning(Messages.FAILED_TO_PARSE_MAIN_PATCH);
					return;
				}

				Element mainRoot = document.getRootElement(); 
				List<String> versionList = new ArrayList<String>();

				for (Object obj : mainRoot.elements("patch")) {
					if (obj instanceof Element) {
						Element oldPatchEle = (Element) obj;
						String oldVersion = oldPatchEle.attributeValue("version");
						versionList.add(oldVersion);
					}
				}
				try {
					document = reader.read(new File(patchXMLPath));

				} catch (DocumentException e) {
					e.printStackTrace();
					errorWarning(Messages.FAILED_TO_PARSE_PATCH);
					return;
				}
				if (document == null) {
					errorWarning(Messages.FAILED_TO_PARSE_PATCH);
					return;
				}
				PatchModel patchModel = new PatchModel();
				Element patchRoot = document.getRootElement();
				for (Object obj : patchRoot.elements("patch")) {
					if (obj instanceof Element) {
						Element newPatchEle = (Element) obj;

						String version = newPatchEle.attributeValue("version");

						if (versionList.contains(version)) {
							openWarning(Messages.DUPLICATION_PATCH_VERSION);
							return;
						}

						patchModel.setVersion(version);
						String desc = newPatchEle.attributeValue("des");
						patchModel.setDescription(desc);
						patchModel.setDate(currentDate);
						patchModel.getPluginModelList().clear();
						for (Object plugin : newPatchEle.elements("plugin")) {
							if (plugin instanceof Element) {
								PluginModel pluginModel = patchModel.new PluginModel();
								pluginModel.setName(((Element) plugin).attributeValue("name"));
								pluginModel.setVersion(((Element) plugin).attributeValue("version"));
								patchModel.getPluginModelList().add(pluginModel);
							}
						}
					}
				}
				try {
					document = reader.read(new File(patchMainPath));

				} catch (DocumentException e) {
					e.printStackTrace();
					errorWarning(Messages.FAILED_TO_PARSE_MAIN_PATCH);
					return;
				}
				mainRoot = document.getRootElement();

				Element patchEle = mainRoot.addElement("patch");
				patchEle.addAttribute("version", patchModel.getVersion());
				patchEle.addAttribute("des", patchModel.getDescription());
				patchEle.addAttribute("date", patchModel.getDate());

				for (PluginModel pluginModel : patchModel.getPluginModelList()) {
					Element pluginEle = patchEle.addElement("plugin");
					pluginEle.addAttribute("name", pluginModel.getName());
					pluginEle.addAttribute("version", pluginModel.getVersion());
				}
				OutputStream os = null;
				XMLWriter writer = null;
				try {
					os = new FileOutputStream(patchMainPath);
					writer = new XMLWriter(os, OutputFormat.createPrettyPrint());
					writer.write(document);
				} catch (FileNotFoundException e1) {
					e1.printStackTrace();
				} catch (UnsupportedEncodingException e) {
					e.printStackTrace();
				} catch (IOException e) {
					e.printStackTrace();
				} finally {
					try {
						if (writer != null) {
							writer.close();
						}
						if (os != null) {
							os.close();
						}
					} catch (IOException e1) {
						e1.printStackTrace();
					}
				}

				String pluginFolderPath = installPathLocal + "plugins";
				for (String pluginPath : pluginPathList) {
					FileUtil.copyTo(pluginPath, pluginFolderPath, true);
				}


				replacePluginVersion(patchModel.getPluginModelList());

				FileUtil.deleteFile(zipTempFile);

				Display.getDefault().asyncExec(() -> {
					refreshPatchModelList();
					refreshHistoryTable();
					okPressed();
					successWarning();
				});
			}
		};
		try {
			new ProgressMonitorDialog(getShell()).run(true, true, runnable);
		} catch (InvocationTargetException | InterruptedException e) {
			e.printStackTrace();
		}
	}

	private Map<String, String> getRollbackVersion(List<PatchModel> installedPatchList, PatchModel uninstallPatch) {
		List<String> uninstallPluginNames = new ArrayList<>();
		for (PluginModel pluginModel : uninstallPatch.getPluginModelList()) {
			uninstallPluginNames.add(pluginModel.getName());
		}
		File pluginFolder = new File(installPath + "plugins");
		File[] files = pluginFolder.listFiles(new FilenameFilter() {

			@Override
			public boolean accept(File dir, String name) {
				if (name.contains("_")) {
					String pluginName = name.substring(0, name.lastIndexOf("_"));
					if (uninstallPluginNames.contains(pluginName)) {
						return true;
					}
				}
				return false;
			}
		});

		Map<String, String> currentPluginMap = getCurrentPluginVersion(uninstallPatch.getPluginModelList());
		Map<String, String> oldVersionMap = new HashMap<>();
		for (PluginModel pluginModel : uninstallPatch.getPluginModelList()) {
			String currentVersion = currentPluginMap.get(pluginModel.getName()); 
			if (currentVersion == null || !currentVersion.equals(pluginModel.getVersion())) {
				continue;
			}

			List<File> pluginFileList = new ArrayList<>(); 
			for (File file : files) {
				if (file.getName().startsWith(pluginModel.getName())) {
					pluginFileList.add(file);
				}
			}
			Collections.sort(pluginFileList, new Comparator<File>() {

				@Override
				public int compare(File firstFile, File secondFile) {
					long firstModified = firstFile.lastModified();
					long secondModified = secondFile.lastModified();
					if (firstModified < secondModified) {
						return -1;
					} else if (firstModified > secondModified) {
						return 1;
					} else {
						return 0;
					}
				}
			});

			for (int i = pluginFileList.size() - 1; i > -1; i--) {
				String name = pluginFileList.get(i).getName();
				if (name.equals(pluginModel.getName() + "_" + pluginModel.getVersion() + ".jar")) {
					String fileName = pluginFileList.get(i - 1).getName();
					String oldVersion = fileName.substring(fileName.lastIndexOf("_") + 1, fileName.lastIndexOf("."));
					oldVersionMap.put(pluginModel.getName(), oldVersion);
					break;
				}
			}

		}
		return oldVersionMap;
	}

	private Map<String, String> getCurrentPluginVersion(List<PluginModel> uninstallingPlugins) {

		Map<String, String> map = new HashMap<>();
		String installPathLocal = Platform.getInstallLocation().getURL().getPath();
		if (installPathLocal.startsWith("/")) {
			installPathLocal = installPathLocal.substring(1, installPathLocal.length());
		}
		String budleInfoFilePath = installPathLocal
				+ "/configuration/org.eclipse.equinox.simpleconfigurator/bundles.info";
		File file = new File(budleInfoFilePath);
		FileReader in = null;
		BufferedReader bufIn;
		try {
			in = new FileReader(file);
			bufIn = new BufferedReader(in);
			String line = null;
			while ((line = bufIn.readLine()) != null) {
				for (PluginModel pluginModel : uninstallingPlugins) {
					String[] infos = line.split(",");
					if (infos[0].equals(pluginModel.getName())) {
						String pluginName = pluginModel.getName();
						map.put(pluginName, infos[1]);
						break;
					}
				}
			}
			bufIn.close();
		} catch (IOException e) {
			e.printStackTrace();
		} finally {
			if (in != null) {
				try {
					in.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
		return map;
	}

	private boolean replacePluginVersion(List<PluginModel> installingPlugins) {

		String installPathLocal = Platform.getInstallLocation().getURL().getPath();
		if (installPathLocal.startsWith("/")) {
			installPathLocal = installPathLocal.substring(1, installPathLocal.length());
		}
		String budleInfoFilePath = installPathLocal
				+ "/configuration/org.eclipse.equinox.simpleconfigurator/bundles.info";
		File file = new File(budleInfoFilePath);
		FileReader in = null;
		BufferedReader bufIn;
		CharArrayWriter tempStream = null;
		FileWriter out = null;
		try {
			in = new FileReader(file);
			bufIn = new BufferedReader(in);
			tempStream = new CharArrayWriter();
			String line = null;
			while ((line = bufIn.readLine()) != null) {
				String[] infos = line.split(",");
				String pluginName = null, newVersion = null;
				for (PluginModel pluginModel : installingPlugins) {
					if (infos[0].equals(pluginModel.getName())) {
						pluginName = pluginModel.getName();
						newVersion = pluginModel.getVersion();
						break;
					}
				}
				if (pluginName != null && line.contains(",")) {
					for (int i = 0; i < infos.length; i++) {
						if (i == 1) {
							tempStream.write(newVersion);
						} else if (i == 2) {
							tempStream.write(infos[2].substring(0, infos[2].lastIndexOf("_")));
							tempStream.write("_");
							tempStream.write(newVersion);
							tempStream.write(".jar");
						} else {
							tempStream.write(infos[i]);
						}
						if (i < infos.length - 1) {
							tempStream.write(",");
						}
					}
				} else {
					tempStream.write(line);
				}
				tempStream.append(System.getProperty("line.separator"));
			}
			bufIn.close();
			out = new FileWriter(file);
			tempStream.writeTo(out);
			tempStream.flush();
			return true;
		} catch (IOException e) {
			e.printStackTrace();
			return false;
		} finally {
			if (tempStream != null) {
				tempStream.close();
			}
			try {
				if (out != null) {
					out.close();
				}
				if (in != null) {
					in.close();
				}
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
	}

	private void removeNodeByAttributeValue(Document document, String file, String nodeName, String attributeName,
			String attributeVal) {
		List<?> nodes = document.getRootElement().elements(nodeName);
		Iterator<?> it = nodes.iterator();
		while (it.hasNext()) {
			Object obj = it.next();
			if (obj instanceof Element) {
				Element childEle = (Element) obj;
				String val = childEle.attributeValue(attributeName);
				if (val.equals(attributeVal)) {
					childEle.getParent().remove(childEle);

					OutputFormat format = OutputFormat.createPrettyPrint();
					XMLWriter xmlWriter;
					try {
						xmlWriter = new XMLWriter(new FileOutputStream(file), format);
						xmlWriter.write(document);
						xmlWriter.close();
					} catch (IOException e) {
						e.printStackTrace();
					}
					break;
				}
			}
		}
	}

	private void successWarning() {
		if (MessageDialog.openQuestion(getShell(), Messages.UPDATE_SUCCESS, Messages.UPDATE_SUCCESS_INFO)) {
			PlatformUI.getWorkbench().restart();
		}
	}

	private void errorWarning(String msg) {
		Display.getDefault().asyncExec(() -> {
			MessageDialog.openError(getShell(), Messages.UPDATE_FAILED, msg);
		});
	}

	private void openWarning(String msg) {
		Display.getDefault().asyncExec(() -> {
			MessageDialog.openWarning(getShell(), Messages.WARNING, msg);
		});
	}
}
