Manifest-Version: 1.0
Bundle-ManifestVersion: 2
Bundle-Name: com.chipon32.os.common
Bundle-SymbolicName: com.chipon32.os.common;singleton:=true
Automatic-Module-Name: com.chipon32.os.common
Bundle-Version: 1.0.0.qualifier
Bundle-Activator: com.chipon32.os.common.TadCommonActivator
Bundle-Vendor: CHIPON
Bundle-RequiredExecutionEnvironment: JavaSE-17
Bundle-ActivationPolicy: lazy
Require-Bundle: org.eclipse.ui,
 org.eclipse.core.runtime,
 org.eclipse.core.resources,
 org.eclipse.ui.console,
 org.eclipse.cdt.dsf.gdb,
 org.eclipse.cdt.dsf,
 org.eclipse.cdt.dsf.ui,
 org.eclipse.debug.core,
 org.eclipse.debug.ui
Export-Package: com.chipon32.os.common,
 com.chipon32.os.common.benchmark,
 com.chipon32.os.common.controller,
 com.chipon32.os.common.handlers,
 com.chipon32.os.common.logger,
 com.chipon32.os.common.messages,
 com.chipon32.os.common.model,
 com.chipon32.os.common.model.console,
 com.chipon32.os.common.model.readers,
 com.chipon32.os.common.model.state,
 com.chipon32.os.common.model.view.generic,
 com.chipon32.os.common.model.view.queues,
 com.chipon32.os.common.model.view.tasks,
 com.chipon32.os.common.preferences,
 com.chipon32.os.common.rtos,
 com.chipon32.os.common.view,
 com.chipon32.os.common.view.action,
 com.chipon32.os.common.view.column,
 com.chipon32.os.common.view.items,
 com.chipon32.os.common.view.providers
