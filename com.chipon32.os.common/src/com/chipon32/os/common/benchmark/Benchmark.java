package com.chipon32.os.common.benchmark;

import java.util.HashMap;
import java.util.Map;

import com.chipon32.os.common.logger.Logger;
import com.chipon32.os.common.messages.Messages;

public class Benchmark {
	private static Map<String, Long> timeMap = new HashMap<>();

	public static void start(Logger logger, String id) {
		timeMap.put(id, System.currentTimeMillis());
		logger.info(String.format(Messages.Info_BenchmarkStart, id));
	}

	public static void stopAndLog(Logger logger, String id) {
		if (timeMap.containsKey(id)) {
			long measuredTime = System.currentTimeMillis() - (Long) timeMap.get(id);
			logger.info(String.format(measuredTime < 2L ? Messages.Info_BenchmarkEndCache : Messages.Info_BenchmarkEnd, id, measuredTime));
			timeMap.remove(id);
		}

	}
}
