package com.chipon32.os.common.rtos;

import java.util.HashMap;
import java.util.Map;

public class Rtos {
	protected RtosAvailability availability;
	protected double version = 0.0F;
	protected Map<IRtosConfig, Boolean> config = new HashMap<>();
	protected Map<String, String> structs = new HashMap<>();

	public Rtos() {
		this.init();
	}

	public void init() {
		this.availability = RtosAvailability.UNKNOWN;
	}

	public boolean isMacroEnabled(IRtosConfig macro) {
		return this.config.get(macro);
	}

	public IRtosConfig getFirstMissingMacro(IRtosConfig... macros) {
		for (IRtosConfig macro : macros) {
			if (!this.isMacroEnabled(macro)) {
				return macro;
			}
		}

		return null;
	}

	public void enableMacro(IRtosConfig macro, boolean enable) {
	}

	public RtosAvailability getAvailability() {
		return this.availability;
	}

	public void setAvailability(RtosAvailability availability) {
		this.availability = availability;
	}

	public double getVersion() {
		return this.version;
	}

	public void initStructs(double version) {
		this.structs.clear();
		this.version = version;
	}

	public String getStruct(String key) {
		return this.structs.get(key);
	}
}
