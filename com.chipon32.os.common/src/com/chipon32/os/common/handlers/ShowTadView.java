package com.chipon32.os.common.handlers;

import org.eclipse.core.commands.AbstractHandler;
import org.eclipse.core.commands.ExecutionEvent;
import org.eclipse.core.commands.ExecutionException;
import org.eclipse.ui.IWorkbenchPage;
import org.eclipse.ui.IWorkbenchWindow;
import org.eclipse.ui.PartInitException;
import org.eclipse.ui.handlers.HandlerUtil;

import com.chipon32.os.common.Reporter;
import com.chipon32.os.common.messages.Messages;

public class ShowTadView extends AbstractHandler {
	public Object execute(ExecutionEvent event) throws ExecutionException {
		IWorkbenchWindow window = HandlerUtil.getActiveWorkbenchWindowChecked(event);
		String tadViewId = event.getCommand().getId();
		if (tadViewId != null) {
			try {
				IWorkbenchPage activePage = window.getActivePage();
				if (activePage != null) {
					activePage.showView(tadViewId);
				}
			} catch (PartInitException var5) {
				Reporter.log(String.format(Messages.Exception_CouldNotInit, tadViewId));
			}
		}

		return null;
	}
}
