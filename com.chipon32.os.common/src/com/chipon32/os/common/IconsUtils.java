package com.chipon32.os.common;

import java.util.Optional;

import org.eclipse.core.runtime.IPath;
import org.eclipse.core.runtime.Path;
import org.eclipse.jface.resource.ImageDescriptor;
import org.eclipse.jface.resource.ImageRegistry;
import org.eclipse.jface.resource.ResourceLocator;

public class IconsUtils {

	public static final String PLUGIN_ID_CDT_UI = "org.eclipse.cdt.ui";
	public static final String PLUGIN_ID_CDT_DEBUG_UI = "org.eclipse.cdt.debug.ui";
	public static final String PLUGIN_ID_CDT_MAKE_UI = "org.eclipse.cdt.make.ui";
	public static final String PLUGIN_ID_CDT_MBUILDER_UI = "org.eclipse.cdt.managedbuilder.ui";
	public static final String PLUGIN_ID_DEBUG_UI = "org.eclipse.debug.ui";
	public static final String PLUGIN_ID_ECLIPSE_COMPARE = "org.eclipse.compare";
	public static final String PLUGIN_ID_ECLIPSE_EQUINOX_P2_UI = "org.eclipse.equinox.p2.ui";
	public static final String PLUGIN_ID_ECLIPSE_LOG = "org.eclipse.ui.views.log";
	public static final String PLUGIN_ID_ECLIPSE_UI = "org.eclipse.ui";
	public static final String PLUGIN_ID_ECLIPSE_UI_VIEWS = "org.eclipse.ui.views";
	public static final String PLUGIN_ID_ECLIPSE_UI_VIEWS_LOG = "org.eclipse.ui.views.log";
	public static final String PLUGIN_ID_EGIT_UI = "org.eclipse.egit.ui";
	public static final String PLUGIN_ID_HELP_UI = "org.eclipse.help.ui";
	public static final String PLUGIN_ID_JFACE = "org.eclipse.jface";
	public static final String PLUGIN_ID_UI_IDE = "org.eclipse.ui.ide";
	public static final String PLUGIN_ID_TEAM_EGIT_UI = "org.eclipse.egit.ui";
	public static final String PLUGIN_ID_WST_XML_UI = "org.eclipse.wst.xml.ui";
	public static final String PLUGIN_ID_XSD_EDIT = "org.eclipse.xsd.edit";
	protected static final String IMAGE_PREFIX = "icons/";

	public static void registerImage(ImageRegistry registry, String key, String pluginID, String filePath) {
		try {
			IPath imgPath = new Path("icons/", filePath);
			Optional<ImageDescriptor> desc = ResourceLocator.imageDescriptorFromBundle(pluginID, imgPath.toString());
			registry.put(key, desc.get());
		} catch (Exception e) {
			System.out.println(e);
		}
	}
}
