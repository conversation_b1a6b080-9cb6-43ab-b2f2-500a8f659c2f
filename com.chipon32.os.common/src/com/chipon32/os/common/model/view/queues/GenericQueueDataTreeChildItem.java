package com.chipon32.os.common.model.view.queues;

import com.chipon32.os.common.view.items.TadItem;

public abstract class GenericQueueDataTreeChildItem extends TadItem {
	protected long address;
	protected long data;
	protected int size;
	protected TadItem parent;

	public GenericQueueDataTreeChildItem(TadItem parent, long address, long data, int size) {
		super(1);
		this.parent = parent;
		this.address = address;
		this.data = data;
		this.size = size;
	}

	public TadItem getParent() {
		return this.parent;
	}
}
