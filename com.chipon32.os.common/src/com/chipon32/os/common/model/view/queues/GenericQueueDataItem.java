package com.chipon32.os.common.model.view.queues;

import java.util.LinkedList;
import java.util.List;
import java.util.Optional;

import com.chipon32.os.common.view.items.TadItem;

public abstract class GenericQueueDataItem extends TadItem {
	protected GenericQueueData queueData;
	protected List<GenericQueueDataTreeChildItem> treeChildren;

	public GenericQueueDataItem(GenericQueueData queueData) {
		super(queueData.getId());
		this.queueData = queueData;
		this.treeChildren = new LinkedList<>();
		if (queueData.hasData()) {
			int i = 0;

			for (Long data : queueData.getData()) {
				Optional.ofNullable(this.createChildItem(this, queueData.getAddress() + (long) (i++ * queueData.getSize()), data, queueData.getSize()))
						.ifPresent((item) -> this.treeChildren.add(item));
			}
		}

	}

	public List<? extends TadItem> getTreeChildren() {
		return this.treeChildren;
	}

	public GenericQueueData getQueueData() {
		return this.queueData;
	}

	public abstract GenericQueueDataTreeChildItem createChildItem(TadItem var1, long var2, long var4, int var6);
}
