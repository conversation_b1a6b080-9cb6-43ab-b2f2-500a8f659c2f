package com.chipon32.os.common.model.view.generic;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.chipon32.os.common.messages.Messages;

public class TadValue {
	private Long value;
	private final String context;

	public TadValue(String text) {
		Matcher matcher = Pattern.compile("^[^\\s]+").matcher(text);
		if (matcher.find()) {
			try {
				this.value = Long.decode(matcher.group(0));
			} catch (NumberFormatException ignored) {
			}
		}

		matcher = Pattern.compile("<([^>]+)>").matcher(text);
		this.context = matcher.find() ? matcher.group(1) : Messages.Error_Unknown;
	}

	public Long getValue() {
		return this.value;
	}

	public String getContext() {
		return this.context;
	}
}
