package com.chipon32.os.common.model.view.generic;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.chipon32.os.common.controller.TadFactoryData;
import com.chipon32.os.common.controller.TadFactoryDataStatus;

public class TadDataCache {
	private final Map<String, TadFactoryData> dataCache = new HashMap<>();

	public boolean isViewDataReady(String view) {
		if (!this.dataCache.containsKey(view)) {
			return false;
		} else {
			TadFactoryData data = this.dataCache.get(view);
			return data != null && (data.getStatus() == TadFactoryDataStatus.WARNING || data.getStatus() == TadFactoryDataStatus.INFORMATION || data.isValid());
		}
	}

	public void setViewData(String view, TadFactoryData data) {
		if (view != null && data != null) {
			this.dataCache.put(view, data);
		}

	}

	public TadFactoryData getViewData(String view) {
		return this.isViewDataReady(view) ? this.dataCache.get(view) : null;
	}

	public List<? extends TadObject> getViewTadObjects(String view) {
		TadFactoryData data = this.getViewData(view);
		return data != null ? data.getTadObjects() : null;
	}

	public void clear() {
		this.dataCache.clear();
	}
}
