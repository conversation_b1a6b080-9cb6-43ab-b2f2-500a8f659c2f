package com.chipon32.os.common.model.view.tasks;

import java.util.List;
import java.util.ListIterator;

public class GenericTaskStack {
	protected long baseAddress;
	protected long endAddress;
	protected long highWaterMarkAddress;
	protected boolean stackOverflow;

	public GenericTaskStack(long baseAddress, long endAddress, long highWaterMarkAddress, boolean stackOverflow) {
		this.baseAddress = baseAddress;
		this.endAddress = endAddress;
		this.highWaterMarkAddress = highWaterMarkAddress;
		this.stackOverflow = stackOverflow;
	}

	public long getBaseAddress() {
		return this.baseAddress;
	}

	public long getEndAddress() {
		return this.endAddress;
	}

	public long getHighWaterMarkAddress() {
		return this.highWaterMarkAddress;
	}

	public long getSize() {
		return this.endAddress - this.baseAddress;
	}

	public long getUsage() {
		return this.endAddress - this.highWaterMarkAddress;
	}

	public boolean isOverflown() {
		return this.stackOverflow;
	}

	public static long getStackHighWaterMarkBytes(List<Integer> memory, int stackFillByte) {
		ListIterator<Integer> iter = memory.listIterator();

		long freeBytesCount;
		for (freeBytesCount = 0L; iter.hasNext() && iter.next() == stackFillByte; ++freeBytesCount) {
		}

		return freeBytesCount;
	}

	public static boolean isStackOverflown(List<Integer> memory, int stackFillByte) {
		int BYTES_TO_CHECK = 16;

		for (int i = 0; i < 16; ++i) {
			if (memory.get(i) != stackFillByte) {
				return true;
			}
		}

		return false;
	}
}
