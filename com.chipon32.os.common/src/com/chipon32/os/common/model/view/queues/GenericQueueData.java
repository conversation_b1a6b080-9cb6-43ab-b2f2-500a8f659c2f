package com.chipon32.os.common.model.view.queues;

import java.math.BigInteger;
import java.util.List;

import com.chipon32.os.common.model.Auxiliary;

public class GenericQueueData {
	public static final int BYTES_TO_READ = 4;
	private final int id;
	private final long address;
	private final List<Long> listData;
	private final int size;
	private final BigInteger rawData;

	public GenericQueueData(int id, long address, int size, List<Long> data) {
		this.id = id;
		this.address = address;
		this.size = size;
		this.listData = data;
		BigInteger _rawData = new BigInteger("0");

		for (int i = 0; i < data.size(); ++i) {
			String itemStrVal = Long.toString((Long) data.get(i));
			_rawData = _rawData.shiftLeft(size * 8).or(new BigInteger(itemStrVal));
		}

		this.rawData = _rawData;
	}

	public int getId() {
		return this.id;
	}

	public long getAddress() {
		return this.address;
	}

	public int getSize() {
		return this.size;
	}

	public boolean hasData() {
		return this.listData != null && !this.listData.isEmpty();
	}

	public List<Long> getData() {
		return this.listData;
	}

	public String toString(GenericQueueDataType type) {
		StringBuilder builder = new StringBuilder();
		switch (type) {
			case BINARY:
				builder.append(this.rawData.toString(2));
				break;
			case DECIMAL:
				builder.append(String.format("%d", this.rawData));
				break;
			case HEXADECIMAL:
			default:
				String formatStr = "0x%0" + 2 * this.listData.size() * this.size + "x";
				builder.append(String.format(formatStr, this.rawData));
				break;
			case ASCII:
				for (long number : this.listData) {
					builder.append(Auxiliary.toASCII(number, this.getSize()));
				}
		}

		return builder.toString();
	}
}
