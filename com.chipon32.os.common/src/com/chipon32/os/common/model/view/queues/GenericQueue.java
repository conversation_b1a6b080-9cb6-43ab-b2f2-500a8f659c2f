package com.chipon32.os.common.model.view.queues;

import java.util.List;

import com.chipon32.os.common.model.view.generic.TadObject;

public class GenericQueue extends TadObject {
	protected String name;
	protected long address;
	protected long head;
	protected long tail;
	protected long writeTo;
	protected long readFrom;
	protected long currentLength;
	protected long maxLength;
	protected int dataSize;
	protected int dataItemSize;
	protected List<GenericQueueData> data;

	public GenericQueue(long handleAddress, String name, long maxLength, long currentLength, int dataItemSize, long headAddress, long tailAddress, long readFromAddress,
			long writeToAddress) {
		this.address = handleAddress;
		this.name = name;
		this.maxLength = maxLength;
		this.currentLength = currentLength;
		this.dataItemSize = dataItemSize;
		this.head = headAddress;
		this.tail = tailAddress;
		this.readFrom = readFromAddress;
		this.writeTo = writeToAddress;
		this.dataSize = (int) (currentLength > 0L && dataItemSize > 0 ? currentLength * (long) dataItemSize : 0L);
	}

	public String getName() {
		return this.name;
	}

	public long getAddress() {
		return this.address;
	}

	public long getHead() {
		return this.head;
	}

	public long getTail() {
		return this.tail;
	}

	public long getWriteTo() {
		return this.writeTo;
	}

	public long getReadFrom() {
		return this.readFrom;
	}

	public long getCurrentLength() {
		return this.currentLength;
	}

	public long getMaxLength() {
		return this.maxLength;
	}

	public int getDataItemSize() {
		return this.dataItemSize;
	}

	public int getDataSize() {
		return this.dataSize;
	}

	public List<GenericQueueData> getData() {
		return this.data;
	}

	public void setData(List<GenericQueueData> data) {
		this.data = data;
	}
}
