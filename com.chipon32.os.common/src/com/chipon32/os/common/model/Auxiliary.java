package com.chipon32.os.common.model;

import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.graphics.RGB;
import org.eclipse.swt.widgets.Display;

public class Auxiliary {
	public static final Color LIGHT_GREEN_COLOUR = new Color(Display.getDefault(), new RGB(120.0F, 1.0F, 0.9F));
	public static final Color GREEN_COLOUR = new Color(Display.getDefault(), new RGB(130.0F, 1.0F, 0.7F));
	public static final Color RED_COLOUR = new Color(Display.getDefault(), new RGB(360.0F, 1.0F, 1.0F));
	public static final Color WHITE_COLOUR = new Color(Display.getDefault(), new RGB(255, 255, 255));
	public static final Color BLACK_COLOUR = new Color(Display.getDefault(), new RGB(0, 0, 0));

	public static String capitalize(String word) {
		return word != null && word.length() > 1 ? word.substring(0, 1).toUpperCase() + word.substring(1).toLowerCase() : null;
	}

	public static String yesNo(boolean value) {
		return value ? "Yes" : "No";
	}

	public static double getPercentage(Long part, Long size) {
		return part != null && size != null && size > 0L ? (double) part / (double) size * (double) 100.0F : (double) 0.0F;
	}

	public static String toASCII(Long number, int actualSize) {
		StringBuilder builder = new StringBuilder();

		for (int i = 0; i < actualSize; ++i) {
			builder.append(toPrintableASCII((char) ((int) (number >> 8 * i & 255L))));
		}

		return builder.toString();
	}

	private static String toPrintableASCII(char c) {
		return isPrintable(c) ? "" + c : ".";
	}

	public static boolean isPrintable(char ch) {
		return ch >= ' ' && ch < 127;
	}

	public static Color getContrastColor(Color colour) {
		float LUMINANCE_THRESHOLD = 0.5F;
		double luminance = 1.0F - (0.299 * colour.getRed() + 0.587 * colour.getGreen() + 0.114 * colour.getBlue()) / 255.0F;
		return luminance < (double) LUMINANCE_THRESHOLD ? BLACK_COLOUR : WHITE_COLOUR;
	}
}
