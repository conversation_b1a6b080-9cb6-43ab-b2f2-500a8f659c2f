package com.chipon32.os.common.model.readers;

import java.util.LinkedList;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.stream.Stream;

import org.eclipse.cdt.dsf.concurrent.DataRequestMonitor;
import org.eclipse.cdt.dsf.datamodel.DMContexts;
import org.eclipse.cdt.dsf.debug.service.IRunControl;
import org.eclipse.cdt.dsf.debug.service.IStack;
import org.eclipse.cdt.dsf.debug.service.command.ICommandControlService;
import org.eclipse.cdt.dsf.gdb.internal.GdbPlugin;
import org.eclipse.cdt.dsf.mi.service.MIVariableManager;
import org.eclipse.cdt.dsf.mi.service.command.commands.MIDataEvaluateExpression;
import org.eclipse.cdt.dsf.service.DsfServicesTracker;
import org.eclipse.cdt.dsf.service.DsfSession;

import com.chipon32.os.common.messages.Messages;
import com.chipon32.os.common.model.TadModel;

public class VariableReader {

	private int readTimeout = 3000;
	private final TadModel tadModel;
	private IRunControl.IExecutionDMContext context;
	private ICommandControlService.ICommandControlDMContext commandControlContext;
	private MIVariableManager variableManager;
	private DsfServicesTracker tracker;

	public VariableReader(TadModel tadModel) {
		this.tadModel = tadModel;
	}

	public String getStructSize(String structName) {
		return this.readVariable("sizeof(struct " + structName + ")");
	}

	public String getSize(String name) {
		return this.readVariable("sizeof(" + name + ")");
	}

	public String getOffset(String structName, String memberName) {
		return this.readVariable("&(((struct " + structName + " *) 0)->" + memberName + ")");
	}

	public String getAddressAtAddress(long address, String structName, String memberName) {
		return address < 0L ? null : this.readVariable("&(((struct " + structName + " *) 0x" + Long.toHexString(address) + ")->" + memberName + ")");
	}

	public String getValueAtAddress(long address, String structName, String memberName, boolean rawData) {
		return address < 0L ? null : this.readVariable("(((struct " + structName + " *) 0x" + Long.toHexString(address) + ")->" + memberName + ")", rawData);
	}

	public String getAddressOfVariable(String name) {
		return this.readVariable("&(" + name + ")");
	}

	public String getVariable(String name) {
		LinkedList<String> vars = new LinkedList<>();
		String original = name;
		int i = -1;

		while ((i = name.lastIndexOf("->")) > 0) {
			name = name.substring(0, i);
			vars.add(name);
		}

		for (String v : vars) {
			try {
				if (Long.decode(this.readVariable(v)) == 0L) {
					return "0x0";
				}
			} catch (NumberFormatException e) {
				this.tadModel.getLogger().exception(e, String.format(Messages.Exception_WhileReadingVariable, v));
				return null;
			}
		}

		return this.readVariable(original);
	}

	public void init() {
		try {
			this.tracker = new DsfServicesTracker(GdbPlugin.getBundleContext(), this.getContext().getSessionId());
			this.commandControlContext = DMContexts.getAncestorOfType(this.getContext(), ICommandControlService.ICommandControlDMContext.class);
			this.variableManager = new MIVariableManager(DsfSession.getSession(this.getContext().getSessionId()), this.tracker);
		} catch (Exception e) {
			this.tadModel.getLogger().exception(e, Messages.Exception_WhileInit);
		}

	}

	private String readVariable(final String expression, final boolean rawData) {
		final StringBuffer buffer = new StringBuffer();
		final CountDownLatch gate = new CountDownLatch(1);
		IStack stackService = this.tracker.getService(IStack.class);
		if (stackService != null) {
			this.variableManager.queueCommand(new MIDataEvaluateExpression<>(this.commandControlContext, expression), new DataRequestMonitor<>(stackService.getExecutor(), null) {
				public void handleSuccess() {
					String data = this.getData().getValue();
					buffer.append(rawData ? data : data.replaceFirst(" .*", ""));
					gate.countDown();
				}

				protected void handleError() {
					VariableReader.this.tadModel.getLogger().error(String.format(Messages.Error_WhileReadingVariable, expression));
					Stream.of(this.getStatus().toString().split("\n")).forEach((msg) -> VariableReader.this.tadModel.getLogger().error(msg));
					buffer.append("null");
					gate.countDown();
				}
			});
		} else {
			buffer.append("null");
			gate.countDown();
		}

		try {
			gate.await(this.readTimeout, TimeUnit.MILLISECONDS);
		} catch (InterruptedException ignored) {
		}

		return buffer.toString().equals("null") ? null : buffer.toString();
	}

	private String readVariable(String expression) {
		return this.readVariable(expression, false);
	}

	private IRunControl.IExecutionDMContext getContext() {
		return this.context;
	}

	public void setContext(IRunControl.IExecutionDMContext context) {
		this.context = context;
	}

	public void setReadTimeout(int readTimeout) {
		this.readTimeout = readTimeout;
	}
}
