package com.chipon32.os.common.model.readers;

import java.util.ArrayList;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

import org.eclipse.cdt.dsf.concurrent.DataRequestMonitor;
import org.eclipse.cdt.dsf.debug.service.IRunControl;
import org.eclipse.cdt.dsf.debug.service.IStack;
import org.eclipse.cdt.dsf.debug.service.command.ICommandControl;
import org.eclipse.cdt.dsf.internal.ui.DsfUIPlugin;
import org.eclipse.cdt.dsf.mi.service.command.commands.MIDataReadMemoryBytes;
import org.eclipse.cdt.dsf.mi.service.command.output.MIDataReadMemoryBytesInfo;
import org.eclipse.cdt.dsf.service.DsfServicesTracker;
import org.eclipse.debug.core.DebugException;
import org.eclipse.debug.core.model.MemoryByte;

import com.chipon32.os.common.messages.Messages;
import com.chipon32.os.common.model.TadModel;

public class MemoryReader {
	private final TadModel tadModel;
	private IRunControl.IExecutionDMContext context;
	private int readTimeout = 5000;

	public MemoryReader(TadModel tadModel) {
		this.tadModel = tadModel;
	}

	private static long getAlignedAddress(long address) {
		return address & -4L;
	}

	private static long getAlignedSize(long size) {
		long alignedSize = size;
		if ((size & 3L) > 0L) {
			alignedSize = (size & -4L) + 4L;
		}

		return alignedSize;
	}

	public synchronized List<Integer> getMemoryBlock(long address, long length) throws DebugException {
		if (address >= 0L && length >= 0L) {
			long addressAligned = getAlignedAddress(address);
			long lengthAligned = getAlignedSize(length + Math.abs(address - addressAligned));
			MemoryByte[] bytes = this.readBytes(addressAligned, lengthAligned);
			if (bytes != null && bytes.length > 0) {
				List<Integer> memory = new LinkedList<>();
				long startAddr = Math.max(address, addressAligned);
				long startIndex = Math.abs(startAddr - Math.min(address, addressAligned));

				for (long i = startIndex; i < startIndex + length; ++i) {
					memory.add(this.convertToUnsigned(bytes[(int) i].getValue()));
				}

				return memory;
			} else {
				this.tadModel.getLogger().error(String.format(Messages.Error_CouldNotGetMemoryBlock, address, length));
				return null;
			}
		} else {
			return null;
		}
	}

	public synchronized List<Long> getMemoryBlock(long address, long length, int bytes) throws DebugException {
		if (address >= 0L && length >= 0L && bytes >= 0) {
			List<Integer> block = this.getMemoryBlock(address, length);
			if (block == null) {
				return null;
			} else {
				List<Long> list = new LinkedList<>();
				if (bytes > 0) {
					int numBlocks = (block.size() + bytes - 1) / bytes;

					for (int i = 0; i < numBlocks; ++i) {
						int addr = i * bytes;
						long item = 0L;

						for (int j = 0; j < bytes && (long) (addr + j) < length; ++j) {
							item |= (long) block.get(addr + j) << j * 8;
						}

						list.add(item);
					}
				}

				return list;
			}
		} else {
			return null;
		}
	}

	public synchronized List<Integer> readMemoryUntilZero(long address) throws DebugException {
		if (address < 0L) {
			return new LinkedList<>();
		} else {
			LinkedList<Integer> memory = new LinkedList<>();
			int blockLength = 32;
			boolean go = true;

			for (int counter = 0; go; ++counter) {
				MemoryByte[] bytes = this.readBytes(address + ((long) counter * blockLength), blockLength);
				if (bytes == null) {
					break;
				}

				for (MemoryByte b : bytes) {
					int value = this.convertToUnsigned(b.getValue());
					if (value == 0) {
						go = false;
						break;
					}

					memory.add(value);
				}
			}

			return memory;
		}
	}

	private MemoryByte[] readBytes(long address, long length) {
		if (address >= 0 && length >= 0) {
			DsfServicesTracker tracker = new DsfServicesTracker(DsfUIPlugin.getBundleContext(), this.getContext().getSessionId());

			try {
				IStack stack = tracker.getService(IStack.class);
				MIDataReadMemoryBytes mi = new MIDataReadMemoryBytes(this.getContext(), "0x" + Long.toString(address, 16), 0L, (int) length);
				final CountDownLatch gate = new CountDownLatch(1);
				final List<MemoryByte[]> list = Collections.synchronizedList(new ArrayList<>());
				DataRequestMonitor<MIDataReadMemoryBytesInfo> rm = new DataRequestMonitor<>(stack.getExecutor(), null) {
					public void handleSuccess() {
						list.add(this.getData().getMIMemoryBlock());
						gate.countDown();
					}

					protected void handleError() {
						super.handleError();
					}
				};
				ICommandControl cc = tracker.getService(ICommandControl.class);
				cc.queueCommand(mi, rm);

				try {
					gate.await(this.readTimeout, TimeUnit.MILLISECONDS);
				} catch (InterruptedException ignored) {
				}

				if (!list.isEmpty()) {
					return list.get(0);
				}

				this.tadModel.getLogger().error(Messages.Error_ReadBytesTimout);
			} finally {
				tracker.dispose();
			}

			return null;
		} else {
			return null;
		}
	}

	private int convertToUnsigned(int i) {
		return i < 0 ? i & 255 : i;
	}

	private IRunControl.IExecutionDMContext getContext() {
		return this.context;
	}

	public void setContext(IRunControl.IExecutionDMContext context) {
		this.context = context;
	}

	public void setReadTimeout(int readTimeout) {
		this.readTimeout = readTimeout;
	}
}
