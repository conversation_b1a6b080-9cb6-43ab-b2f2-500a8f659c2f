package com.chipon32.os.common.model;

import java.text.SimpleDateFormat;
import java.util.Calendar;

public enum Time {
	MINUTES("HH:mm"),
	SECONDS("HH:mm:ss"),
	MILLISECONDS("HH:mm:ss.SS"),
	SAVE_FORMAT("yyMMdd_HHmmss");

	private final String format;

	Time(String format) {
		this.format = format;
	}

	public String toString() {
		return (new SimpleDateFormat(this.format)).format(Calendar.getInstance().getTime());
	}
}
