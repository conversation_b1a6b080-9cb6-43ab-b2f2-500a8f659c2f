package com.chipon32.os.common.model.state;

import org.eclipse.cdt.dsf.debug.service.IRunControl;

import com.chipon32.os.common.messages.Messages;
import com.chipon32.os.common.model.TadModel;

public class TadState {

	private TadStates tadState;
	private final TadModel tadModel;

	public TadState(TadModel tadModel) {
		this.tadState = TadStates.NONE;
		this.tadModel = tadModel;
	}

	public void shiftState(DMEvent event, IRunControl.StateChangeReason reason) {
		String oldTadState = tadState.toString();
		switch (tadState) {
			case DEBUG_STARTED:
				tadState = TadStates.DEBUG_INIT_0;
				break;
			case DEBUG_INIT_0:
				tadState = TadStates.DEBUG_INIT_1;
				break;
			case DEBUG_INIT_1:
				tadState = TadStates.DEBUG_SUSPENDED;
				break;
			case DEBUG_SUSPENDED:
				if (event == DMEvent.RESUMED) {
					tadState = TadStates.DEBUG_RESUMED;
				}
				break;
			case DEBUG_RESUMED:
				if (event == DMEvent.SUSPENDED) {
					tadState = TadStates.READY;
				}
				break;
			case READY:
				tadState = TadStates.DEBUG_RESUMED;
				break;
			default:
				tadState = TadStates.DEBUG_INIT_0;
		}

		tadModel.getLogger().info(Messages.State_Change + oldTadState + " -> " + tadState.toString() + " (" + event.toString() + ", " + reason.toString() + ")");
	}

	public boolean isReady() {
		return tadState == TadStates.READY;
	}

	public boolean isDebugRunning() {
		return tadState != TadStates.NONE;
	}

	public void debugStarted(boolean started) {
		tadState = started ? TadStates.DEBUG_STARTED : TadStates.NONE;
	}
}
