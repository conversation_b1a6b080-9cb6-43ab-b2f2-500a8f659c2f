package com.chipon32.os.common.model.console;

import org.eclipse.jface.resource.ImageDescriptor;
import org.eclipse.ui.console.ConsolePlugin;
import org.eclipse.ui.console.IConsole;
import org.eclipse.ui.console.IConsoleFactory;
import org.eclipse.ui.console.IConsoleManager;
import org.eclipse.ui.console.MessageConsole;

public abstract class TadConsoleFactory implements IConsoleFactory {
	protected MessageConsole tadConsole;

	protected abstract String getConsoleName();

	protected abstract ImageDescriptor getConsoleImage();

	public void openConsole() {
		IConsoleManager consoleManager = ConsolePlugin.getDefault().getConsoleManager();
		boolean tadConsoleExists = false;

		for (IConsole console : consoleManager.getConsoles()) {
			if (console.getName().equals(this.getConsoleName())) {
				tadConsoleExists = true;
				break;
			}
		}

		if (!tadConsoleExists) {
			consoleManager.addConsoles(new IConsole[] { this.getConsole() });
		}

		consoleManager.showConsoleView(this.getConsole());
	}

	protected MessageConsole createConsole() {
		return new MessageConsole(this.getConsoleName(), this.getConsoleImage());
	}

	public MessageConsole getConsole() {
		if (this.tadConsole == null) {
			this.tadConsole = this.createConsole();
		}

		return this.tadConsole;
	}
}
