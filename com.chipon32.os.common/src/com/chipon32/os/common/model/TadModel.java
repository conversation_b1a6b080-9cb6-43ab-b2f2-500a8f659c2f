package com.chipon32.os.common.model;

import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import org.eclipse.cdt.dsf.debug.service.IRunControl;
import org.eclipse.cdt.dsf.service.DsfServiceEventHandler;
import org.eclipse.cdt.dsf.service.DsfSession;
import org.eclipse.debug.core.ILaunch;
import org.eclipse.debug.core.ILaunchConfiguration;
import org.eclipse.jface.resource.ImageDescriptor;
import org.eclipse.swt.graphics.Image;
import org.eclipse.ui.console.ConsolePlugin;
import org.eclipse.ui.console.IConsole;
import org.eclipse.ui.console.MessageConsole;

import com.chipon32.os.common.Reporter;
import com.chipon32.os.common.TadCommonActivator;
import com.chipon32.os.common.benchmark.Benchmark;
import com.chipon32.os.common.controller.TadFactory;
import com.chipon32.os.common.controller.TadFactoryData;
import com.chipon32.os.common.controller.TadFactoryDataStatus;
import com.chipon32.os.common.logger.Logger;
import com.chipon32.os.common.messages.Messages;
import com.chipon32.os.common.model.readers.MemoryReader;
import com.chipon32.os.common.model.readers.VariableReader;
import com.chipon32.os.common.model.state.DMEvent;
import com.chipon32.os.common.model.state.TadState;
import com.chipon32.os.common.model.view.generic.TadDataCache;
import com.chipon32.os.common.rtos.Rtos;
import com.chipon32.os.common.rtos.RtosAvailability;

public abstract class TadModel implements DsfSession.SessionStartedListener, DsfSession.SessionEndedListener {
	public static final Image WARNING_BIG_IMG = TadCommonActivator.getDefault().getImage("warning_bigger");
	public static final Image WARNING_IMG = TadCommonActivator.getDefault().getImage("warning");
	public static final Image ERROR_IMG = TadCommonActivator.getDefault().getImage("error");
	public static final Image INFORMATION_IMG = TadCommonActivator.getDefault().getImage("information");
	public static final ImageDescriptor SAVE_IMG_DESC = TadCommonActivator.getDefault().getImageDescriptor("save");
	protected static final DataRequest POISON_DATA_REQUEST = new DataRequest(0) {
		public void execute() {
		}
	};
	protected BlockingQueue<DataRequest> requestQueue = new LinkedBlockingQueue<>();
	protected List<ITadModelListener> listeners = new LinkedList<>();
	protected Map<String, TadFactory> factories;
	protected TadDataCache dataCache = new TadDataCache();
	protected TadState tadState = new TadState(this);
	protected Thread workerThread;
	protected AtomicInteger ticket = new AtomicInteger(1);
	protected Logger tadLogger = null;
	protected MessageConsole tadConsole = null;
	protected final VariableReader variableReader = new VariableReader(this);
	protected final MemoryReader memoryReader = new MemoryReader(this);
	protected Map<ILaunchConfiguration, DsfSession> startedDebugLaunchConfigs = new HashMap<>();

	public TadModel() {
		DsfSession.addSessionStartedListener(this);
		DsfSession.addSessionEndedListener(this);
	}

	public synchronized void requestViewData(final IDataListener listener, final String viewName) {
		DataRequest req = new DataRequest(this.ticket.get()) {
			public void execute() {
				if (getRTOS().getAvailability() == RtosAvailability.UNKNOWN) {
					RtosAvailability availability = isRTOSAvailable();
					if (availability == RtosAvailability.AVAILABLE) {
						String id = "Determine RTOS version";
						Benchmark.start(getLogger(), id);
						getRTOS().initStructs(getRTOSVersion());
						Benchmark.stopAndLog(getLogger(), id);
					}

					getRTOS().setAvailability(availability);
				}

				if (getRTOS().getAvailability() != RtosAvailability.AVAILABLE) {
					listener.dataEmpty(new TadFactoryData(TadModel.this, null, TadFactoryDataStatus.INFORMATION, String.format(Messages.Info_RTOSNotUsed, viewName)));
				} else if (!isTadReady()) {
					listener.dataCorrupted();
					getLogger().error(String.format(Messages.Error_DataRequestFailed, viewName));
				} else {
					if (factories.containsKey(viewName)) {
						Benchmark.start(getLogger(), viewName);
						TadFactory factory = factories.get(viewName);
						cachePreRequisiteData(viewName);
						TadFactoryData data = dataCache.getViewData(viewName);
						if (data == null) {
							data = factory.getData();
						}

						Benchmark.stopAndLog(getLogger(), viewName);
						if (this.isTicketValid(ticket.get())) {
							if (data.isValid()) {
								listener.dataReady(data);
							} else {
								if (!data.hasMessage()) {
									data.setStatus(TadFactoryDataStatus.INFORMATION);
									data.setMessage(String.format(Messages.Info_ViewDataEmpty, viewName));
								}

								listener.dataEmpty(data);
							}

							dataCache.setViewData(viewName, data);
						} else {
							listener.dataCorrupted();
						}
					} else {
						getLogger().error(String.format(Messages.Error_FactoryNotFound, viewName));
						listener.dataCorrupted();
					}

				}
			}
		};

		try {
			this.requestQueue.put(req);
		} catch (InterruptedException e) {
			listener.dataCorrupted();
			this.getLogger().exception(e);
		}

	}

	protected void cachePreRequisiteData(String viewName) {
	}

	protected abstract RtosAvailability isRTOSAvailable();

	protected abstract double getRTOSVersion();

	public boolean isTadReady() {
		return this.tadState.isReady();
	}

	public TadDataCache getDataCache() {
		return this.dataCache;
	}

	public MessageConsole getTadConsole() {
		return this.tadConsole;
	}

	public Logger getLogger() {
		return this.tadLogger;
	}

	public abstract Rtos getRTOS();

	public void addListener(ITadModelListener listener) {
		this.listeners.add(listener);
	}

	public void removeListener(ITadModelListener listener) {
		this.listeners.remove(listener);
	}

	private void startWorkerThread() {
		this.workerThread = new Thread(() -> {
			while (true) {
				if (tadState.isDebugRunning()) {
					try {
						DataRequest request = requestQueue.take();
						if (request != TadModel.POISON_DATA_REQUEST && tadState.isDebugRunning()) {
							if (request.isTicketValid(ticket.get())) {
								request.execute();
							}
							continue;
						}
					} catch (InterruptedException ignored) {
					}
				}

				return;
			}
		});
		this.workerThread.start();
	}

	private void stopWorkerThread() {
		try {
			this.requestQueue.put(POISON_DATA_REQUEST);
		} catch (InterruptedException e) {
			this.getLogger().exception(e, Messages.Exception_CouldNotStopWorkerThread);
		}

	}

	private void increaseTicket() {
		this.ticket.incrementAndGet();
	}

	protected abstract void initRTOS(DsfSession var1);

	public void sessionStarted(DsfSession session) {
		ensureNonNullLaunchConfig(session);
		if (this.getLogger().isLoggingEnabled()) {
			ConsolePlugin.getDefault().getConsoleManager().addConsoles(new IConsole[] { this.getTadConsole() });
		} else {
			ConsolePlugin.getDefault().getConsoleManager().removeConsoles(new IConsole[] { this.getTadConsole() });
			this.createLoggingItems();
		}

		ILaunchConfiguration launchConfig = getLaunchConfigForSession(session);
		if (launchConfig != null) {
			if (this.startedDebugLaunchConfigs.containsKey(launchConfig)) {
				return;
			}

			this.startedDebugLaunchConfigs.put(launchConfig, session);
		}

		session.addServiceEventListener(this, null);
		this.increaseTicket();
		this.dataCache.clear();
		this.configureFromPreferences();
		this.tadState.debugStarted(true);
		this.initRTOS(session);
		this.requestQueue.clear();
		this.startWorkerThread();

		for (ITadModelListener l : this.listeners) {
			l.debuggerSessionCreated();
		}

		this.getLogger().info(String.format(Messages.Info_DSFStarted, session.getId()));
	}

	public void sessionEnded(DsfSession session) {
		if (this.startedDebugLaunchConfigs.containsValue(session)) {
			Optional.ofNullable(getLaunchConfigForSession(session)).ifPresent((launchConfig) -> this.startedDebugLaunchConfigs.remove(launchConfig));
			this.getLogger().info(String.format(Messages.Info_DSFEnded, session.getId()));
			session.removeServiceEventListener(this);
			this.increaseTicket();
			this.dataCache.clear();
			this.setContextInFactories(null);
			this.tadState.debugStarted(false);
			this.stopWorkerThread();

			for (ITadModelListener l : this.listeners) {
				l.debuggerTerminated();
			}

			this.getLogger().saveLogFile();
			this.getLogger().clear();
			if (!this.getLogger().isLoggingEnabled()) {
				ConsolePlugin.getDefault().getConsoleManager().removeConsoles(new IConsole[] { this.getTadConsole() });
				this.createLoggingItems();
			}

		}
	}

	@DsfServiceEventHandler
	public void handleEvent(IRunControl.ISuspendedDMEvent event) {
		this.increaseTicket();
		this.dataCache.clear();
		this.setContextInFactories(event.getDMContext());
		this.tadState.shiftState(DMEvent.SUSPENDED, event.getReason());
		if (this.isTadReady()) {
			this.variableReader.init();

			for (ITadModelListener l : this.listeners) {
				l.debuggerSuspended();
			}
		}

	}

	@DsfServiceEventHandler
	public void handleEvent(IRunControl.IResumedDMEvent event) {
		this.increaseTicket();
		this.dataCache.clear();
		this.tadState.shiftState(DMEvent.RESUMED, event.getReason());

		for (ITadModelListener l : this.listeners) {
			l.debuggerResumed();
		}

	}

	private void setContextInFactories(IRunControl.IExecutionDMContext context) {
		for (TadFactory factory : this.factories.values()) {
			factory.setContext(context);
		}

	}

	protected static ILaunchConfiguration getLaunchConfigForSession(DsfSession session) {
		return Optional.ofNullable(session)
				.map((dsfSession) -> (ILaunch) dsfSession.getModelAdapter(ILaunch.class))
				.map(ILaunch::getLaunchConfiguration)
				.orElse(null);
	}

	protected static void ensureNonNullLaunchConfig(DsfSession session) {
		int NUM_RETRIES = 10;
		int MS_TO_SLEEP = 100;
		CountDownLatch waitForNonNullLaunch = new CountDownLatch(1);
		new Thread(() -> {
			for (int numTry = 0; numTry < NUM_RETRIES && getLaunchConfigForSession(session) == null; ++numTry) {
				try {
					Thread.sleep(MS_TO_SLEEP);
				} catch (InterruptedException ignored) {
				}
			}

			waitForNonNullLaunch.countDown();
		}).start();

		try {
			waitForNonNullLaunch.await(1000L, TimeUnit.MILLISECONDS);
		} catch (InterruptedException e) {
			Reporter.log(e);
		}
	}

	protected abstract void createLoggingItems();

	protected abstract void configureFromPreferences();
}
