package com.chipon32.os.common;

import org.eclipse.jface.resource.ImageRegistry;
import org.osgi.framework.BundleContext;

public class TadCommonActivator extends AbstractCommonActivator {
	public static final String PLUGIN_ID = "com.chipon32.os.common";
	public static final String IMG_INFORMATION = "information";
	public static final String IMG_WARNING = "warning";
	public static final String IMG_WARNING_BIGGER = "warning_bigger";
	public static final String IMG_ERROR = "error";
	public static final String IMG_SAVE = "save";
	public static final String IMG_PAUSE = "pause";
	private static TadCommonActivator plugin;

	public void start(BundleContext context) throws Exception {
		super.start(context);
		plugin = this;
	}

	public void post_start(BundleContext context) throws Exception {
	}

	public void stop(BundleContext context) throws Exception {
		plugin = null;
		super.stop(context);
	}

	public static TadCommonActivator getDefault() {
		return plugin;
	}

	protected void initializeImageRegistry(ImageRegistry registry) {
		IconsUtils.registerImage(registry, "information", "org.eclipse.ui.cheatsheets", "obj16/information.png");
		IconsUtils.registerImage(registry, "warning", "org.eclipse.ui.ide", "full/obj16/warning.png");
		IconsUtils.registerImage(registry, "warning_bigger", "org.eclipse.jface", "full/message_warning.png");
		IconsUtils.registerImage(registry, "error", "org.eclipse.ui.cheatsheets", "obj16/error.png");
		IconsUtils.registerImage(registry, "save", "org.eclipse.ui", "full/etool16/save_edit.png");
		IconsUtils.registerImage(registry, "pause", "org.eclipse.debug.ui", "full/elcl16/suspend_co.png");
	}
}
