package com.chipon32.os.common.preferences;

import java.util.Objects;
import java.util.stream.Stream;

import org.eclipse.jface.preference.BooleanFieldEditor;
import org.eclipse.jface.preference.DirectoryFieldEditor;
import org.eclipse.jface.preference.FieldEditorPreferencePage;
import org.eclipse.jface.preference.RadioGroupFieldEditor;
import org.eclipse.jface.util.PropertyChangeEvent;
import org.eclipse.swt.events.KeyAdapter;
import org.eclipse.swt.events.KeyEvent;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.widgets.Group;
import org.eclipse.ui.IWorkbench;
import org.eclipse.ui.IWorkbenchPreferencePage;

import com.chipon32.os.common.TadCommonActivator;
import com.chipon32.os.common.messages.Messages;

public class TadPreferences extends FieldEditorPreferencePage implements IWorkbenchPreferencePage {
	protected Group loggingGroup;
	protected BooleanFieldEditor consoleEnablement;
	protected RadioGroupFieldEditor radioFieldLogLocation;
	protected DirectoryFieldEditor browseFieldLogLocation;

	public TadPreferences() {
		super(0);
	}

	public TadPreferences(int style) {
		super(style);
	}

	public void init(IWorkbench arg0) {
		this.setPreferenceStore(TadCommonActivator.getDefault().getPreferenceStore());
		this.setTitle(Messages.PreferencesPage_Title);
		this.setDescription(Messages.PreferencesPage_Description);
	}

	protected void initialize() {
		super.initialize();
		boolean logGroupEnabled = this.consoleEnablement == null || this.getPreferenceStore().getBoolean(this.consoleEnablement.getPreferenceName());
		if (logGroupEnabled) {
			if (this.radioFieldLogLocation != null) {
				this.propertyChange(new PropertyChangeEvent(this.radioFieldLogLocation, "field_editor_value", null,
						this.getPreferenceStore().getString(this.radioFieldLogLocation.getPreferenceName())));
			}
		} else {
			Stream.of(this.radioFieldLogLocation, this.browseFieldLogLocation).filter(Objects::nonNull).forEach((property) -> property.setEnabled(false, this.loggingGroup));
		}

	}

	protected void createFieldEditors() {
	}

	protected void createLoggingFieldEditors() {
		this.loggingGroup = new Group(this.getFieldEditorParent(), 32);
		GridData layoutData = new GridData(4, 4, true, false);
		layoutData.horizontalSpan = 2;
		this.loggingGroup.setLayoutData(layoutData);
		this.loggingGroup.setText(Messages.Log_GroupLabel);
		this.consoleEnablement = new BooleanFieldEditor(this.getPreferencesPrefix() + "rtosLoggingEnablement", IPreferencesConstants.L_RTOS_LOGGING_ENABLEMENT, this.loggingGroup) {
			protected void valueChanged(boolean oldValue, boolean newValue) {
				super.valueChanged(oldValue, newValue);
				TadPreferences.this.radioFieldLogLocation.setEnabled(newValue, TadPreferences.this.loggingGroup);
				TadPreferences.this.propertyChange(new PropertyChangeEvent(TadPreferences.this.radioFieldLogLocation, "field_editor_value", null,
						TadPreferences.this.radioFieldLogLocation.getSelectionValue()));
			}
		};
		this.radioFieldLogLocation = new RadioGroupFieldEditor(this.getPreferencesPrefix() + "rtosLoggingLocation", IPreferencesConstants.L_RTOS_LOGGING_LOCATION,
				IPreferencesConstants.LV_RTOS_LOGGING_LOCATION.length, IPreferencesConstants.LV_RTOS_LOGGING_LOCATION, this.loggingGroup);
		this.browseFieldLogLocation = new DirectoryFieldEditor(this.getPreferencesPrefix() + "rtosLoggingLocationCustomPath",
				IPreferencesConstants.L_RTOS_LOGGING_LOCATION_CUSTOM_PATH, this.loggingGroup) {
			private boolean isCustomLoggingLocation() {
				return "rtosLoggingCustom".equals(TadPreferences.this.radioFieldLogLocation.getSelectionValue());
			}

			public boolean isValid() {
				return !TadPreferences.this.consoleEnablement.getBooleanValue() || !this.isCustomLoggingLocation() || super.isValid();
			}

			protected void refreshValidState() {
				if (TadPreferences.this.consoleEnablement.getBooleanValue() && this.isCustomLoggingLocation()) {
					super.refreshValidState();
					TadPreferences.this.setValid(this.isValid() && TadPreferences.this.isValid());
				} else {
					this.clearErrorMessage();
					TadPreferences.this.setValid(true);
				}

			}
		};
		this.browseFieldLogLocation.setEmptyStringAllowed(false);
		this.browseFieldLogLocation.getTextControl(this.loggingGroup).addKeyListener(new KeyAdapter() {
			public void keyReleased(KeyEvent e) {
				TadPreferences.this.browseFieldLogLocation.getTextControl(TadPreferences.this.loggingGroup).notifyListeners(16, null);
			}
		});
		this.addField(this.consoleEnablement);
		this.addField(this.radioFieldLogLocation);
		this.addField(this.browseFieldLogLocation);
	}

	public void propertyChange(PropertyChangeEvent event) {
		if (this.radioFieldLogLocation != null && this.radioFieldLogLocation.equals(event.getSource()) && "field_editor_value".equals(event.getProperty())) {
			this.browseFieldLogLocation.setEnabled(this.consoleEnablement.getBooleanValue() && "rtosLoggingCustom".equals(this.radioFieldLogLocation.getSelectionValue()),
					this.loggingGroup);
			this.browseFieldLogLocation.getTextControl(this.loggingGroup).notifyListeners(16, null);
		}

		super.propertyChange(event);
	}

	protected String getPreferencesPrefix() {
		return "";
	}

	protected void performDefaults() {
		super.performDefaults();
		if (this.radioFieldLogLocation != null) {
			this.propertyChange(new PropertyChangeEvent(this.radioFieldLogLocation, "field_editor_value", null, "rtosLoggingWorkspace"));
		}

	}
}
