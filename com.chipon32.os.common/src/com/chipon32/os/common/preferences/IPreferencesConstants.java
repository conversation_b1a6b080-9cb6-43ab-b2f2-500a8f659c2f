package com.chipon32.os.common.preferences;

import com.chipon32.os.common.messages.Messages;

public interface IPreferencesConstants {
	String P_RTOS_LOGGING_ENABLEMENT = "rtosLoggingEnablement";
	String P_RTOS_LOGGING_LOCATION = "rtosLoggingLocation";
	String P_RTOS_LOGGING_LOCATION_CUSTOM_PATH = "rtosLoggingLocationCustomPath";
	String L_RTOS_LOGGING_ENABLEMENT = Messages.Log_EnablementDescription;
	String L_RTOS_LOGGING_LOCATION = Messages.Log_LocationDescription;
	String L_RTOS_LOGGING_OMIT = Messages.Log_DontSave;
	String L_RTOS_LOGGING_WORKSPACE = Messages.Log_Workspace;
	String L_RTOS_LOGGING_CUSTOM = Messages.Log_Custom;
	String L_RTOS_LOGGING_LOCATION_CUSTOM_PATH = Messages.Log_CustomPath;
	boolean V_RTOS_LOGGING_ENABLEMENT_DEFAULT = true;
	String V_RTOS_LOGGING_OMIT = "rtosLoggingOmit";
	String V_RTOS_LOGGING_WORKSPACE = "rtosLoggingWorkspace";
	String V_RTOS_LOGGING_CUSTOM = "rtosLoggingCustom";
	String[][] LV_RTOS_LOGGING_LOCATION = new String[][] { { L_RTOS_LOGGING_OMIT, "rtosLoggingOmit" }, { L_RTOS_LOGGING_WORKSPACE, "rtosLoggingWorkspace" },
			{ L_RTOS_LOGGING_CUSTOM, "rtosLoggingCustom" } };
}
