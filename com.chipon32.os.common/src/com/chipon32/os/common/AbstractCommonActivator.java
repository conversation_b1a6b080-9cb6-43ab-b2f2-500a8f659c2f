package com.chipon32.os.common;

import java.io.File;
import java.util.MissingResourceException;
import java.util.Optional;
import java.util.ResourceBundle;

import org.eclipse.core.runtime.IPath;
import org.eclipse.core.runtime.Path;
import org.eclipse.core.runtime.Platform;
import org.eclipse.core.runtime.Status;
import org.eclipse.jface.resource.ImageDescriptor;
import org.eclipse.jface.resource.ResourceLocator;
import org.eclipse.swt.graphics.Image;
import org.eclipse.swt.widgets.Display;
import org.eclipse.ui.plugin.AbstractUIPlugin;
import org.osgi.framework.Bundle;
import org.osgi.framework.BundleContext;

public abstract class AbstractCommonActivator extends AbstractUIPlugin {
	private static final String EMPTY_STRING = "";
	private static final String P_RES_HOME_ENV = "utils.home_env";
	private static final String DATA_PATH = "Data";
	public static final int T_CONFIRM = 0;
	public static final int T_ERROR = 2;
	public static final int T_INFO = 3;
	public static final int T_WARN = 4;
	protected static final String IMAGE_PREFIX = "icons/";
	protected static final String IMAGE_SUFFIX = "gif";
	protected String pluginID = null;
	protected boolean dataPathsInitialized = false;
	protected String localPluginDataPath = null;
	protected String externalPluginDataPath = null;
	protected Bundle bundle = null;

	protected ImageDescriptor declareImage(String img) {
		IPath imgPath = new Path("icons/", img);
		String ext = imgPath.getFileExtension();
		if (ext == null || ext.isEmpty()) {
			imgPath = imgPath.addFileExtension("gif");
		}

		String key = this.getKey(img);
		Optional<ImageDescriptor> optional = ResourceLocator.imageDescriptorFromBundle(this.pluginID, imgPath.toString());

		try {
			if (this.getImageRegistry().getDescriptor(key) == null) {
				this.getImageRegistry().put(key, (ImageDescriptor) optional.get());
			}
		} catch (Exception var7) {
		}

		return (ImageDescriptor) optional.get();
	}

	private String findDataDirectory(IPath base) {
		String dataDir = null;
		if (base != null) {
			File homeDir = base.toFile();
			if (homeDir.isDirectory()) {
				dataDir = homeDir.getAbsolutePath().concat(File.separator);
			}
		}

		return dataDir;
	}

	private String getKey(String img) {
		return img.toLowerCase();
	}

	public Image getImage(String img) {
		String key = this.getKey(img);
		Image image = this.getImageRegistry().get(key);

		if (image == null || image.isDisposed()) {
			ImageDescriptor id = this.getImageRegistry().getDescriptor(key);
			if (id == null) {
				image = new Image(Display.getDefault(), 16, 16);
			} else {
				image = id.createImage();
			}
		}

		return image;
	}

	public ImageDescriptor getImageDescriptor(String img) {
		String key = this.getKey(img);
		return this.getImageRegistry().getDescriptor(key);
	}

	public String getPluginID() {
		return this.pluginID;
	}

	private ResourceBundle getResourceBundle() {
		return Platform.getResourceBundle(this.getBundle());
	}

	public String getResourceString(String key) {
		ResourceBundle bundle = this.getResourceBundle();

		try {
			return bundle != null ? bundle.getString(key) : key;
		} catch (MissingResourceException var4) {
			return key;
		}
	}

	public void log(String msg, Exception e) {
		this.getLog().log(new Status(1, this.getPluginID(), 0, msg, e));
	}

	public void log(Exception e) {
		this.log("Exception logged", e);
	}

	public abstract void post_start(BundleContext var1) throws Exception;

	public void reportException(Exception e) {
		Reporter.reportException(this.getPluginID(), e);
	}

	public void start(BundleContext context) throws Exception {
		super.start(context);
		this.pluginID = this.getBundle().getSymbolicName();
		this.post_start(context);
	}

	public void stop(BundleContext context) throws Exception {
		super.stop(context);
	}

}
