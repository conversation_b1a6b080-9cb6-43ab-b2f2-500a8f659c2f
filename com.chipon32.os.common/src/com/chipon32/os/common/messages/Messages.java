package com.chipon32.os.common.messages;

import org.eclipse.osgi.util.NLS;

public class Messages extends NLS {
	private static final String BUNDLE_NAME = "com.chipon32.os.common.messages.messages";
	public static String PreferencesPage_Title;
	public static String PreferencesPage_Description;
	public static String Info_BenchmarkStart;
	public static String Info_BenchmarkEnd;
	public static String Info_BenchmarkEndCache;
	public static String Info_ViewPaused;
	public static String Info_DSFStarted;
	public static String Info_DSFEnded;
	public static String Info_FetchingData;
	public static String Info_MacroDisabled;
	public static String Info_RTOSNotUsed;
	public static String Info_ViewDataEmpty;
	public static String Warning_Dependency;
	public static String Warning_DependencyInvalidValue;
	public static String Error_Unknown;
	public static String Error_LogDirNotCreated;
	public static String Error_WhileReadingVariable;
	public static String Error_ReadBytesTimout;
	public static String Error_CouldNotGetMemoryBlock;
	public static String Error_ReadSafely;
	public static String Error_SavePathInvalid;
	public static String Error_ShowMessage;
	public static String Error_DataRequestFailed;
	public static String Error_FactoryNotFound;
	public static String Error_CouldNotGetFactoryData;
	public static String Error_TaskStackDefault;
	public static String Exception_CouldNotAccess;
	public static String Exception_CouldNotInit;
	public static String Exception_ElementsNotComparable;
	public static String Exception_WhileSavingLog;
	public static String Exception_WhileSavingView;
	public static String Exception_WhileInit;
	public static String Exception_WhileReadingVariable;
	public static String Exception_CouldNotStopWorkerThread;
	public static String Exception_WhileDataLoad;
	public static String Exception_WhileGetTextColumn;
	public static String Log_FileExtension;
	public static String Log_Saved;
	public static String Log_SaveName;
	public static String Log_EnablementDescription;
	public static String Log_LocationDescription;
	public static String Log_DontSave;
	public static String Log_Workspace;
	public static String Log_Custom;
	public static String Log_CustomPath;
	public static String Log_GroupLabel;
	public static String State_Change;
	public static String Label_PauseView;
	public static String Label_PauseView_ToolTip;
	public static String Label_CopyLine;
	public static String Label_SaveToFile;
	public static String StackOverflow;

	static {
		NLS.initializeMessages("com.chipon32.os.common.messages.messages", Messages.class);
	}

	private Messages() {
	}
}
