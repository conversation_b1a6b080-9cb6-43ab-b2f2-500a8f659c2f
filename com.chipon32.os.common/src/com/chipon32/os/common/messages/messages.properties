PreferencesPage_Title=RTOS TAD preferences
PreferencesPage_Description=Please select a specific RTOS page for configuring various preferences
Info_BenchmarkStart=Loading data for \"%s\" has started.
Info_BenchmarkEnd=Loading data for \"%s\" took %d ms.
Info_BenchmarkEndCache=Loaded data for \"%s\" from cache.
Info_ViewPaused=View \"%s\" has been %s.
Info_DSFStarted=DSF session ID %s has started.
Info_DSFEnded=DSF session ID %s has ended.
Info_FetchingData=Fetching the debugger data...
Info_MacroDisabled=RTOS macro \"%s\" is disabled.
Info_RTOSNotUsed=RTOS has not been detected.
Info_ViewDataEmpty=%s view data is empty.
Warning_Dependency=Enable \"%s\" macro to see \"%s\".
Warning_DependencyInvalidValue=Enable \"%s\" macro to see a valid value.
Error_Unknown=Unknown
Error_LogDirNotCreated=TAD logs directory \"%s\" could not be created.
Error_WhileReadingVariable=Could not read variable expression: \"%s\".
Error_ReadBytesTimout=Read bytes method timed out!
Error_CouldNotGetMemoryBlock=Could not get memory block! Invalid address \"0x%08x\" and/or \"0x%x\" length of a block!
Error_ReadSafely=Reading exception during: \"%s(%s)\"
Error_SavePathInvalid=Save path is invalid.
Error_ShowMessage=Cannot show message when view's data loaded successfully!
Error_DataRequestFailed=\"%s\" data request for failed, because TAD is not ready yet.
Error_FactoryNotFound=\"%s\" factory not found.
Error_CouldNotGetFactoryData=Could not load data for \"%s\" view! For more information check TAD log.
Error_TaskStackDefault=Stack memory block was not read properly, stack has default values.
Exception_CouldNotAccess=\"%s\" could not have been accessed.
Exception_CouldNotInit=\"%s\" could not be initialized.
Exception_ElementsNotComparable=Elements are not mutually comparable!
Exception_WhileSavingLog=while trying to save TAD log.
Exception_WhileSavingView=while saving \"%s\" into a file.
Exception_WhileInit=while trying to initialize.
Exception_WhileReadingVariable=while reading variable: \"%s\".
Exception_CouldNotStopWorkerThread=Worker thread could not be stopped!
Exception_WhileDataLoad=while \"%s\" data load.
Exception_WhileGetTextColumn=While getting text for column \"%s"\.
Log_EnablementDescription=Create logging console and log messages from TAD plugin
Log_FileExtension=.log
Log_Saved=TAD log was saved in \"%s\"
Log_SaveName=TAD_log_
Log_LocationDescription=Location used for saving logs
Log_DontSave=Do not save logs
Log_Workspace=Save logs in workspace area
Log_Custom=Save logs in specified folder
Log_CustomPath=Path
Log_GroupLabel=Logging configuration
State_Change=TAD state changed: 
Label_PauseView=Pause %s view
Label_PauseView_ToolTip=Pause %s view, so it's not refreshed every time debugger is stopped.
Label_CopyLine=Copy selected line
Label_SaveToFile=Save %s view to file
StackOverflow=Stack overflow!
