package com.chipon32.os.common;

import com.chipon32.os.common.controller.TadFactory;
import com.chipon32.os.common.messages.Messages;

public class ReadingException extends Exception {

	private String args;

	public ReadingException() {
		super("Target Reading Exception");
	}

	public ReadingException(String args) {
		this();
		this.args = args;
	}

	public String getSummary() {
		int lineNumber = 0;
		String methodName = Messages.Error_Unknown;
		String className = Messages.Error_Unknown;
		StackTraceElement[] stackTrace = this.getStackTrace();

		for (int i = 0; i < stackTrace.length; ++i) {
			if (stackTrace[i].getClassName().equals(TadFactory.class.getName()) && i + 1 < stackTrace.length) {
				methodName = stackTrace[i].getMethodName();
				className = stackTrace[i + 1].getClassName();
				lineNumber = stackTrace[i + 1].getLineNumber();
				break;
			}
		}

		return String.format("in class \"%s\" at %d. line when calling method \"%s(%s)\"", className, lineNumber, methodName, this.args != null ? this.args : "");
	}

	public String getArgs() {
		return this.args;
	}
}
