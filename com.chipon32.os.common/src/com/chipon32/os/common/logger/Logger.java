package com.chipon32.os.common.logger;

import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStreamWriter;
import java.io.Writer;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;

import org.eclipse.core.resources.ResourcesPlugin;
import org.eclipse.jface.preference.IPreferenceStore;
import org.eclipse.ui.console.MessageConsole;
import org.eclipse.ui.console.MessageConsoleStream;

import com.chipon32.os.common.messages.Messages;
import com.chipon32.os.common.model.Time;

public abstract class Logger {
	private List<Log> logs;
	private static final int STORED_LOG_COUNT = 3;
	private final MessageConsoleStream tadConsoleStream;

	public Logger(MessageConsole messageConsole) {
		this.tadConsoleStream = messageConsole.newMessageStream();
	}

	private synchronized void log(Log log) {
		if (this.logs == null) {
			this.logs = new LinkedList<>();
		}

		if (this.isLoggingEnabled()) {
			this.logs.add(log);
			this.tadConsoleStream.println(log.toString());
		}

	}

	public synchronized void info(String text) {
		this.log(new Log(LogType.INFO, text));
	}

	public synchronized void debug(String text, Object... args) {
		this.log(new Log(LogType.DEBUG, String.format(text, args)));
	}

	public synchronized void error(String text) {
		this.log(new Log(LogType.ERROR, text));
	}

	public synchronized void exception(Exception e, String message) {
		this.log(new Log(e, message));
	}

	public synchronized void exception(Exception e) {
		this.log(new Log(LogType.EXCEPTION, e.getClass().getSimpleName()));
	}

	public void clear() {
		if (this.logs != null) {
			this.logs.clear();
		}

	}

	public Log[] getLogs() {
		return !this.logs.isEmpty() ? this.logs.toArray(new Log[0]) : new Log[0];
	}

	protected abstract String getDefaultDirName();

	protected abstract String getDirName();

	protected String getDirName(IPreferenceStore prefStore) {
		String loggingLocation = prefStore.getString(this.getPreferencesPrefix() + "rtosLoggingLocation");
		if ("rtosLoggingWorkspace".equals(loggingLocation)) {
			return this.getDefaultDirName();
		} else {
			return "rtosLoggingCustom".equals(loggingLocation) ? prefStore.getString(this.getPreferencesPrefix() + "rtosLoggingLocationCustomPath") : this.getDefaultDirName();
		}
	}

	public void saveLogFile() {
		if (this.isLoggingEnabled()) {
			if (this.mustSaveLogs()) {
				String tadLogsDirStr = this.getDirName();
				File tadLogsDir = new File(tadLogsDirStr);
				if (!tadLogsDir.isAbsolute()) {
					File workspace = ResourcesPlugin.getWorkspace().getRoot().getLocation().toFile();
					if (workspace != null && workspace.exists()) {
						tadLogsDir = new File(workspace, tadLogsDirStr);
					} else {
						tadLogsDir = null;
					}
				}

				if (tadLogsDir != null) {
					try {
						if (!tadLogsDir.exists() && !tadLogsDir.mkdir()) {
							this.error(String.format(Messages.Error_LogDirNotCreated, tadLogsDir.getAbsolutePath()));
							return;
						}
					} catch (SecurityException e) {
						this.exception(e, Messages.Exception_WhileSavingLog);
					}

					this.deleteOldLogFiles(tadLogsDir);
					String var10000 = Messages.Log_SaveName;
					String logName = var10000 + Time.SAVE_FORMAT + Messages.Log_FileExtension;
					File logPath = new File(tadLogsDir, logName);
					try (Writer txt = new OutputStreamWriter(new FileOutputStream(logPath), StandardCharsets.UTF_8)) {
						txt.write("==================================");
						txt.write(System.lineSeparator());

						for (Log msg : this.logs) {
							txt.write(msg.toString());
							txt.write(System.lineSeparator());
						}

						txt.close();
						this.info(String.format(Messages.Log_Saved, logPath));

					} catch (Exception e) {
						this.exception(e, null);
					}
				}
			}
		}
	}

	public void deleteOldLogFiles(File directory) {
		File[] files = null;

		try {
			files = directory.listFiles((dir, name) -> name != null && dir != null && dir.isDirectory() && name.contains(Messages.Log_FileExtension));
		} catch (SecurityException e) {
			this.exception(e, String.format(Messages.Exception_CouldNotAccess, directory));
		}

		if (files != null && files.length > 3) {
			try {
				Arrays.sort(files);
			} catch (ClassCastException e) {
				this.exception(e, Messages.Exception_ElementsNotComparable);
			}

			for (File file : Arrays.copyOfRange(files, 0, files.length - 3)) {
				try {
					file.delete();
				} catch (SecurityException e) {
					this.exception(e, Messages.Exception_CouldNotAccess);
				}
			}
		}

	}

	public abstract boolean isLoggingEnabled();

	protected boolean isLoggingEnabled(IPreferenceStore prefStore) {
		return prefStore.getBoolean(this.getPreferencesPrefix() + "rtosLoggingEnablement");
	}

	protected abstract boolean mustSaveLogs();

	protected boolean mustSaveLogs(IPreferenceStore prefStore) {
		String var10001 = this.getPreferencesPrefix();
		String loggingLocation = prefStore.getString(var10001 + "rtosLoggingLocation");
		return !"rtosLoggingOmit".equals(loggingLocation);
	}

	protected abstract String getPreferencesPrefix();
}
