package com.chipon32.os.common.logger;

import com.chipon32.os.common.model.Time;

public class Log {
	private final String text;
	private final String time;
	private String className;
	private final LogType type;
	private StackTraceElement[] stackTrace;

	public Log(LogType type, String text) {
		this.text = text;
		this.type = type;
		this.time = Time.MILLISECONDS.toString();
		this.stackTrace = Thread.currentThread().getStackTrace();

		for (int i = 0; i < this.stackTrace.length; ++i) {
			if (this.stackTrace[i].getClassName().equals(Logger.class.getName()) && i + 1 < this.stackTrace.length) {
				String fileName = this.stackTrace[i + 1].getFileName();
				if (fileName != null) {
					this.className = fileName.replace(".java", "");
				}
				break;
			}
		}

	}

	public Log(Exception e, String message) {
		this(LogType.EXCEPTION, String.format("%s %s", e.getClass().getSimpleName(), message));
		this.stackTrace = e.getStackTrace();
	}

	public String toString() {
		StringBuilder builder = new StringBuilder();
		builder.append(this.time + " ");
		builder.append(this.type.toString() + ": ");
		if (this.className != null) {
			builder.append("[" + this.className + "] ");
		}

		builder.append(this.text);
		if (this.type == LogType.EXCEPTION) {
			for (StackTraceElement line : this.stackTrace) {
				builder.append(System.lineSeparator() + "\t" + line);
			}
		}

		return builder.toString();
	}

	public LogType getType() {
		return this.type;
	}
}
