package com.chipon32.os.common.controller;

import com.chipon32.os.common.model.IDataListener;
import com.chipon32.os.common.model.ITadModelListener;
import com.chipon32.os.common.model.TadModel;
import com.chipon32.os.common.view.TadView;

public abstract class TadViewController implements ITadModelListener, IDataListener {
	protected TadView view;
	protected Object previousData;
	protected final TadModel tadModel;

	public TadViewController(TadModel tadModel, TadView view) {
		this.tadModel = tadModel;
		this.view = view;
		tadModel.addListener(this);
	}

	public void requestData() {
		tadModel.requestViewData(this, view.getTitle());
	}

	public boolean isDataReady() {
		return tadModel.getDataCache().isViewDataReady(view.getTitle());
	}

	public void dispose() {
		tadModel.removeListener(this);
	}

	public void debuggerSuspended() {
		if (view != null && view.isVisible() && !view.isViewPaused()) {
			view.showProgressBar();
			view.enableAllActions(true);
			requestData();
		}

	}

	public void debuggerTerminated() {
		if (view != null) {
			view.showTadView();
			view.clearData();
			view.enableAllActions(false);
			view.enableAllViewers(false);
		}

	}

	public void debuggerResumed() {
		if (view != null && !view.isViewPaused()) {
			view.enableAllViewers(false);
		}

	}

	public void debuggerSessionCreated() {
		if (view != null) {
			view.clearData();
			view.enableAllViewers(false);
		}

	}

	public void dataCorrupted() {
		if (view != null) {
			view.showTadView();
			view.refreshData(null, false);
		}

	}

	public void dataEmpty(TadFactoryData data) {
		view.showMessage(data.getStatus(), data.getMessage());
		view.enableAllViewers(false);
	}

}
