package com.chipon32.os.common.controller;

import java.util.List;

import com.chipon32.os.common.model.TadModel;
import com.chipon32.os.common.model.view.generic.TadObject;

public class TadFactoryData {
	private final TadModel tadModel;
	private final List<? extends TadObject> tadObjects;
	private TadFactoryDataStatus status;
	private String message;

	public TadFactoryData(TadModel tadModel, List<? extends TadObject> tadObjects, TadFactoryDataStatus status, String message) {
		this.tadModel = tadModel;
		this.tadObjects = tadObjects;
		this.status = status;
		this.message = message;
		if (status == TadFactoryDataStatus.ERROR || status == TadFactoryDataStatus.ERROR_WITH_LOGS) {
			this.tadModel.getLogger().error(message);
		}

	}

	public boolean isValid() {
		return tadObjects != null && !tadObjects.isEmpty() && status != TadFactoryDataStatus.ERROR && status != TadFactoryDataStatus.ERROR_WITH_LOGS;
	}

	public List<? extends TadObject> getTadObjects() {
		return tadObjects;
	}

	public TadFactoryDataStatus getStatus() {
		return status;
	}

	public void setStatus(TadFactoryDataStatus status) {
		this.status = status;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	public boolean hasMessage() {
		return message != null && !message.isEmpty();
	}
}
