package com.chipon32.os.common.view.items;

import java.text.DecimalFormat;
import java.util.List;

import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.graphics.Image;
import org.eclipse.swt.widgets.Display;

import com.chipon32.os.common.messages.Separator;
import com.chipon32.os.common.view.column.ITadColumnId;

public abstract class TadItem {
	protected final int index;

	public TadItem(int index) {
		this.index = index;
	}

	public int getIndex() {
		return this.index;
	}

	public TadItem getParent() {
		return null;
	}

	public boolean isParent() {
		return this.getParent() == null;
	}

	public List<? extends TadItem> getChildItems() {
		return null;
	}

	public boolean hasChildItems() {
		return this.getChildItems() != null && !this.getChildItems().isEmpty();
	}

	public List<? extends TadItem> getTreeChildren() {
		return null;
	}

	public boolean hasTreeChildren() {
		return this.getTreeChildren() != null && !this.getTreeChildren().isEmpty();
	}

	public abstract String getText(ITadColumnId var1);

	public Image getImage(ITadColumnId column) {
		return null;
	}

	public Long getNumValue(ITadColumnId column) {
		return null;
	}

	public abstract String toString(Separator var1);

	protected String toString(ITadColumnId[] columns, Separator separator) {
		StringBuilder line = new StringBuilder();

		for (ITadColumnId column : columns) {
			String value = this.getText(column);
			if (value != null) {
				line.append(value);
			}

			line.append(separator.toString());
		}

		return line.toString();
	}

	public boolean isValid() {
		return true;
	}

	public Color getTextColour() {
		return Display.getDefault().getSystemColor(SWT.COLOR_BLACK);
	}

	public static String formatBytes(long bytes) {
		float number = (float) bytes;

		for (String unit : new String[] { "B", "kB", "MB" }) {
			if (!(number >= 1024.0F)) {
				String var10000 = (new DecimalFormat("###0.##")).format(number);
				return var10000 + " " + unit;
			}

			number = (float) ((double) number / (double) 1024.0F);
		}

		return String.format("%d B", bytes);
	}
}
