package com.chipon32.os.common.view.providers;

import org.eclipse.jface.viewers.CellEditor;
import org.eclipse.jface.viewers.EditingSupport;
import org.eclipse.jface.viewers.TextCellEditor;
import org.eclipse.jface.viewers.TreeViewer;

import com.chipon32.os.common.view.column.TadColumn;
import com.chipon32.os.common.view.items.TadItem;

public class TadEditingSupport extends EditingSupport {
	private TextCellEditor cellEditor;
	private TadColumn column;

	public TadEditingSupport(TreeViewer viewer, TextCellEditor cellEditor, TadColumn column) {
		super(viewer);
		this.column = column;
		this.cellEditor = cellEditor;
	}

	protected CellEditor getCellEditor(Object element) {
		return this.cellEditor;
	}

	protected boolean canEdit(Object element) {
		if (element instanceof TadItem) {
			return ((TadItem) element).getText(this.column.getId()) != null && this.column.isCopyable();
		} else {
			return true;
		}
	}

	protected Object getValue(Object element) {
		return element instanceof TadItem ? ((TadItem) element).getText(this.column.getId()) : null;
	}

	protected void setValue(Object element, Object value) {
	}
}
