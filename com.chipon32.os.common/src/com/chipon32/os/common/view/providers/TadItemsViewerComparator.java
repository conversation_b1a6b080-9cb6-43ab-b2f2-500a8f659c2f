package com.chipon32.os.common.view.providers;

import org.eclipse.jface.viewers.TreeViewer;
import org.eclipse.jface.viewers.Viewer;
import org.eclipse.jface.viewers.ViewerComparator;
import org.eclipse.swt.widgets.Tree;

import com.chipon32.os.common.view.column.TadColumn;
import com.chipon32.os.common.view.items.TadItem;

public class TadItemsViewerComparator extends ViewerComparator {
	private TadColumn column;

	public TadItemsViewerComparator(TadColumn column) {
		this.column = column;
	}

	public int compare(Viewer viewer, Object e1, Object e2) {
		if (viewer instanceof TreeViewer && e1 instanceof TadItem && e2 instanceof TadItem) {
			Tree tree = ((TreeViewer) viewer).getTree();
			if (tree.getSortColumn() != null) {
				int direction = tree.getSortDirection() == 1024 ? -1 : 1;
				Long num1 = ((TadItem) e1).getNumValue(this.column.getId());
				Long num2 = ((TadItem) e2).getNumValue(this.column.getId());
				if (num1 != null && num2 != null) {
					return (int) (num1 - num2) * direction;
				}

				String text1 = ((TadItem) e1).getText(this.column.getId());
				String text2 = ((TadItem) e2).getText(this.column.getId());
				if (text1 == null && text2 == null) {
					return 0;
				}

				if (text1 != null && text2 != null) {
					try {
						return (Integer.parseInt(text1) - Integer.parseInt(text2)) * direction;
					} catch (NumberFormatException var11) {
						if (this.removeNum(text1).equalsIgnoreCase(this.removeNum(text2))) {
							return (this.extractNum(text1) - this.extractNum(text2)) * direction;
						}

						return text1.compareToIgnoreCase(text2) * direction;
					}
				}

				return (text1 == null ? -1 : 1) * direction;
			}
		}

		return super.compare(viewer, e1, e2);
	}

	private String removeNum(String text) {
		return text.replaceAll("\\d", "");
	}

	private int extractNum(String text) {
		String num = text.replaceAll("\\D", "");
		if (!num.isEmpty()) {
			try {
				return Integer.parseInt(num);
			} catch (NumberFormatException var4) {
				return 0;
			}
		} else {
			return -1;
		}
	}
}
