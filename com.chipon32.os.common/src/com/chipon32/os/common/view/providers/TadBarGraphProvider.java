package com.chipon32.os.common.view.providers;

import org.eclipse.jface.viewers.StyledCellLabelProvider;
import org.eclipse.jface.viewers.TreeViewerColumn;
import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.graphics.GC;
import org.eclipse.swt.graphics.RGBA;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Event;

import com.chipon32.os.common.model.Auxiliary;
import com.chipon32.os.common.model.TadModel;
import com.chipon32.os.common.rtos.IRtosConfig;
import com.chipon32.os.common.view.column.ITadColumnId;

public abstract class TadBarGraphProvider extends StyledCellLabelProvider {
	protected IRtosConfig dependency;
	protected ITadColumnId columnId;
	protected String columnName;
	protected boolean showWarning = false;

	protected void paintBarGraph(Event event, double percentage, String text, boolean showWarning) {
		this.showWarning = showWarning;
		GC gc = event.gc;
		Color oldColor = gc.getBackground();
		Color whiteColor = Display.getDefault().getSystemColor(SWT.COLOR_WHITE);
		int startX = 0;
		if (showWarning) {
			gc.drawImage(TadModel.WARNING_IMG, event.x, event.y);
			startX = TadModel.WARNING_IMG.getBounds().width;
		}

		if (text != null && percentage >= (double) 0.0F && percentage <= (double) 100.0F) {
			int fullHeight = event.height - 1;
			int fullWidth = ((TreeViewerColumn) this.getColumn()).getColumn().getWidth() - 1;
			int width = (int) ((double) fullWidth * (percentage / (double) 100.0F));
			float hue = (float) (percentage > (double) 90.0F ? 0
					: (percentage > (double) 75.0F ? 30 : (percentage > (double) 50.0F ? 55 : (percentage > (double) 30.0F ? 90 : 125))));
			gc.setBackground(new Color(Display.getDefault(), new RGBA(hue, 1.0F, 1.0F, 120.0F)));
			gc.fillRectangle(startX + event.x, event.y, width, fullHeight);
			gc.setBackground(whiteColor);
			gc.fillRectangle(width + event.x, event.y, fullWidth - width, fullHeight);
			gc.setForeground(Auxiliary.getContrastColor(gc.getBackground()));
			gc.setFont(Display.getDefault().getSystemFont());
			gc.drawString(text, event.x + fullWidth / 2 - gc.textExtent(text).x / 2, event.y, true);
			gc.setBackground(oldColor);
		}

	}

	public abstract String getToolTipText(Object var1);

	public void setDependency(IRtosConfig dependency) {
		this.dependency = dependency;
	}

	public void setColumnId(ITadColumnId columnId) {
		this.columnId = columnId;
	}

	public void setColumnName(String columnName) {
		this.columnName = columnName;
	}
}
