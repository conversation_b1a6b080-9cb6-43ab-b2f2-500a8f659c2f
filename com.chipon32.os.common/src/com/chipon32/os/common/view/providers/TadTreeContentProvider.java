package com.chipon32.os.common.view.providers;

import java.util.List;

import org.eclipse.jface.viewers.ITreeContentProvider;

import com.chipon32.os.common.view.TadViewViewerType;
import com.chipon32.os.common.view.items.TadItem;

public class Tad<PERSON><PERSON><PERSON>ontentProvider implements ITreeContentProvider {
	private final TadViewViewerType type;

	public TadTreeContentProvider(TadViewViewerType type) {
		this.type = type;
	}

	public Object[] getElements(Object inputElement) {
		if (inputElement != null) {
			if (this.type.equals(TadViewViewerType.PARENT) && inputElement instanceof List) {
				return ((List<?>) inputElement).toArray();
			}

			if (this.type.equals(TadViewViewerType.CHILD) && inputElement instanceof TadItem) {
				List<? extends TadItem> childItemsList = ((TadItem) inputElement).getChildItems();
				if (childItemsList != null) {
					return childItemsList.toArray();
				}
			}
		}

		return new Object[0];
	}

	public Object[] getChildren(Object parentElement) {
		return parentElement instanceof TadItem ? ((TadItem) parentElement).getTreeChildren().toArray() : null;
	}

	public Object getParent(Object element) {
		return null;
	}

	public boolean hasChildren(Object element) {
		return element instanceof TadItem && ((TadItem) element).hasTreeChildren();
	}
}
