package com.chipon32.os.common.view.providers;

import org.eclipse.jface.viewers.ColumnLabelProvider;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.graphics.Image;
import org.eclipse.swt.widgets.Display;

import com.chipon32.os.common.messages.Messages;
import com.chipon32.os.common.model.TadModel;
import com.chipon32.os.common.rtos.IRtosConfig;
import com.chipon32.os.common.rtos.Rtos;
import com.chipon32.os.common.view.column.TadColumn;
import com.chipon32.os.common.view.items.TadItem;

public class TadItemColumnLabelProvider extends ColumnLabelProvider {
	protected TadColumn column;
	protected IRtosConfig dependency;
	protected Rtos rtos;

	public TadItemColumnLabelProvider(TadColumn column) {
		this.column = column;
		this.dependency = column.getDependency();
	}

	public Image getImage(Object element) {
		if (element instanceof TadItem item) {
			if (item.isParent() && this.dependency != null) {
				if (this.dependency.getInfo() != null && !this.rtos.isMacroEnabled(this.dependency)) {
					return TadModel.INFORMATION_IMG;
				}

				if (item.getText(this.column.getId()) == null) {
					return TadModel.WARNING_IMG;
				}
			}

			return item.getImage(this.column.getId());
		} else {
			return null;
		}
	}

	public String getToolTipText(Object element) {
		if (element instanceof TadItem item) {
			if (item.isParent() && this.dependency != null) {
				if (this.dependency.getInfo() != null && !this.rtos.isMacroEnabled(this.dependency)) {
					return this.dependency.getInfo();
				}

				if (item.getText(this.column.getId()) == null) {
					return String.format(Messages.Warning_Dependency, this.dependency.toString(), this.column.getName());
				}
			}
		}

		return null;
	}

	public String getText(Object element) {
		return element instanceof TadItem ? ((TadItem) element).getText(this.column.getId()) : null;
	}

	public Color getForeground(Object element) {
		if (element instanceof TadItem item) {
			if (item.getTextColour() != null) {
				return item.getTextColour();
			}

			if (!item.isValid()) {
				return Display.getDefault().getSystemColor(3);
			}
		}

		return null;
	}
}
