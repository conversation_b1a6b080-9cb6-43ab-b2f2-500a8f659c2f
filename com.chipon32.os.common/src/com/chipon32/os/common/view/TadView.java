package com.chipon32.os.common.view;

import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import org.eclipse.core.runtime.IProgressMonitor;
import org.eclipse.core.runtime.IStatus;
import org.eclipse.core.runtime.Status;
import org.eclipse.core.runtime.jobs.Job;
import org.eclipse.jface.action.Action;
import org.eclipse.jface.action.IToolBarManager;
import org.eclipse.jface.action.MenuManager;
import org.eclipse.jface.action.Separator;
import org.eclipse.jface.layout.TreeColumnLayout;
import org.eclipse.jface.viewers.ColumnViewerToolTipSupport;
import org.eclipse.jface.viewers.ColumnWeightData;
import org.eclipse.jface.viewers.IStructuredSelection;
import org.eclipse.jface.viewers.StyledCellLabelProvider;
import org.eclipse.jface.viewers.TextCellEditor;
import org.eclipse.jface.viewers.TreeViewer;
import org.eclipse.jface.viewers.TreeViewerColumn;
import org.eclipse.jface.viewers.Viewer;
import org.eclipse.swt.custom.CLabel;
import org.eclipse.swt.custom.SashForm;
import org.eclipse.swt.custom.StackLayout;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.events.SelectionListener;
import org.eclipse.swt.layout.FillLayout;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.ProgressBar;
import org.eclipse.swt.widgets.Shell;
import org.eclipse.swt.widgets.Text;
import org.eclipse.swt.widgets.Tree;
import org.eclipse.swt.widgets.TreeColumn;
import org.eclipse.swt.widgets.TreeItem;
import org.eclipse.ui.IPartListener2;
import org.eclipse.ui.IWorkbenchPartReference;
import org.eclipse.ui.part.ViewPart;
import org.eclipse.ui.progress.UIJob;

import com.chipon32.os.common.controller.TadFactoryDataStatus;
import com.chipon32.os.common.controller.TadViewController;
import com.chipon32.os.common.logger.Log;
import com.chipon32.os.common.logger.LogType;
import com.chipon32.os.common.messages.Messages;
import com.chipon32.os.common.model.Auxiliary;
import com.chipon32.os.common.model.TadModel;
import com.chipon32.os.common.view.action.PauseViewAction;
import com.chipon32.os.common.view.action.SaveToClipboard;
import com.chipon32.os.common.view.action.SaveToFileAction;
import com.chipon32.os.common.view.column.TadColumn;
import com.chipon32.os.common.view.items.TadItem;
import com.chipon32.os.common.view.providers.TadBarGraphProvider;
import com.chipon32.os.common.view.providers.TadEditingSupport;
import com.chipon32.os.common.view.providers.TadItemColumnLabelProvider;
import com.chipon32.os.common.view.providers.TadItemsViewerComparator;
import com.chipon32.os.common.view.providers.TadTreeContentProvider;

public abstract class TadView extends ViewPart {
	protected final TadModel tadModel;
	private final TadViewType viewType;
	private final Map<TadViewViewerType, TreeViewer> viewers;
	protected List<TadColumn> parentColumns;
	protected List<TadColumn> childColumns;
	private TadDoubleViewWeight doubleViewWeight;
	private IPartListener2 partListener;
	private boolean visible;
	private Composite parentComposite;
	private Composite progressViewComposite;
	private Composite messageViewComposite;
	private CLabel messageLabel;
	private Label messageDescLabel;
	private Text exceptionText;
	private Composite tadViewComposite;
	private StackLayout layout;
	private TadViewController viewController;
	private int previousSortColumn;
	private TadItem previouslySelectedItem;
	private Action saveToCSV;
	private Action pauseView;

	protected abstract TadView getTadView();

	public TadView(TadModel tadModel, TadViewType type) {
		this.tadModel = tadModel;
		viewType = type;
		doubleViewWeight = TadDoubleViewWeight.SAME;
		viewers = new LinkedHashMap<>();
		parentColumns = new LinkedList<>();
		childColumns = new LinkedList<>();
		previousSortColumn = 0;
		previouslySelectedItem = null;
	}

	public TadView(TadModel tadModel, TadViewType type, TadDoubleViewWeight weight) {
		this(tadModel, type);
		doubleViewWeight = weight;
	}

	public TadViewController getController() {
		if (viewController == null) {
			viewController = createController();
		}

		return viewController;
	}

	protected abstract TadViewController createController();

	public Map<TadViewViewerType, TreeViewer> getTreeViewers() {
		return viewers;
	}

	public void createPartControl(Composite parent) {
		partListener = new IPartListener2() {
			public void partVisible(IWorkbenchPartReference partRef) {
				if (TadView.this.equals(partRef.getPart(false))) {
					setVisible(true);
					if (tadModel.isTadReady() && !isViewPaused()) {
						if (!getController().isDataReady()) {
							showProgressBar();
						}

						getController().requestData();
					}

				}

			}

			public void partHidden(IWorkbenchPartReference partRef) {
				if (TadView.this.equals(partRef.getPart(false))) {
					setVisible(false);
				}

			}

		};
		getSite().getPage().addPartListener(partListener);
		parent.addDisposeListener(arg0 -> {
			viewers.clear();
			getSite().getPage().removePartListener(partListener);
			getController().dispose();
		});
		getController();
		parentComposite = new Composite(parent, 0);
		layout = new StackLayout();
		parentComposite.setLayout(layout);
		tadViewComposite = new Composite(parentComposite, 0);
		tadViewComposite.setLayout(new FillLayout());
		progressViewComposite = new Composite(parentComposite, 0);
		GridLayout gridLayout = new GridLayout();
		gridLayout.numColumns = 1;
		progressViewComposite.setLayout(gridLayout);
		Label label = new Label(progressViewComposite, 0);
		label.setText(Messages.Info_FetchingData);
		ProgressBar pb = new ProgressBar(progressViewComposite, 258);
		pb.setLayoutData(new GridData(768));
		messageViewComposite = new Composite(parentComposite, 0);
		gridLayout = new GridLayout();
		gridLayout.numColumns = 1;
		messageViewComposite.setLayout(gridLayout);
		messageLabel = new CLabel(messageViewComposite, 16777216);
		messageLabel.setLayoutData(new GridData(1808));
		messageDescLabel = new Label(messageViewComposite, 16777280);
		messageDescLabel.setLayoutData(new GridData(1808));
		exceptionText = new Text(messageViewComposite, 584);
		exceptionText.setLayoutData(new GridData(1808));
		switch (viewType) {
			case SINGLE:
				TreeViewer viewer = createTreeViewer(new Composite(tadViewComposite, 0), parentColumns);
				viewer.setContentProvider(new TadTreeContentProvider(TadViewViewerType.PARENT));
				viewers.put(TadViewViewerType.PARENT, viewer);
				break;
			case DOUBLE:
				SashForm sashForm = new SashForm(tadViewComposite, 512);
				sashForm.setBackground(sashForm.getDisplay().getSystemColor(15));
				TreeViewer parentViewer = createTreeViewer(new Composite(sashForm, 0), parentColumns);
				final TreeViewer childViewer = createTreeViewer(new Composite(sashForm, 0), childColumns);
				sashForm.setWeights(doubleViewWeight.getWeight());
				parentViewer.addSelectionChangedListener(event -> {
					if (event.getSelection() instanceof IStructuredSelection selection) {
						if (selection.getFirstElement() instanceof TadItem selectedItem) {
							TadItem activeItem = selectedItem.isParent() ? selectedItem : selectedItem.getParent();
							if (selectedItem != previouslySelectedItem) {
								previouslySelectedItem = selectedItem;
								enableViewer(childViewer, activeItem.hasChildItems());
								childViewer.setInput(activeItem);
							}
						}
					}

				});
				parentViewer.setContentProvider(new TadTreeContentProvider(TadViewViewerType.PARENT));
				childViewer.setContentProvider(new TadTreeContentProvider(TadViewViewerType.CHILD));
				viewers.put(TadViewViewerType.PARENT, parentViewer);
				viewers.put(TadViewViewerType.CHILD, childViewer);
		}

		IToolBarManager toolbar = getViewSite().getActionBars().getToolBarManager();
		final Shell parentShell = getViewSite().getShell();
		pauseView = new PauseViewAction(tadModel, this);
		pauseView.setEnabled(false);
		pauseView.setChecked(false);
		toolbar.add(pauseView);
		toolbar.add(new Separator());
		saveToCSV = new SaveToFileAction(tadModel, parentShell, getTitle(), viewers);
		saveToCSV.setEnabled(false);
		toolbar.add(saveToCSV);

		for (final TreeViewer viewer : viewers.values()) {
			MenuManager menuMgr = new MenuManager();
			menuMgr.setRemoveAllWhenShown(true);
			menuMgr.addMenuListener(manager -> manager.add(new SaveToClipboard(tadModel, parentShell, viewer)));
			viewer.getControl().setMenu(menuMgr.createContextMenu(viewer.getTree()));
		}

		showTadView();
	}

	private TreeViewer createTreeViewer(Composite composite, List<TadColumn> columns) {
		TreeColumnLayout layout = new TreeColumnLayout();
		composite.setLayout(layout);
		final TreeViewer viewer = new TreeViewer(composite, 67584);
		final Tree tree = viewer.getTree();
		tree.setHeaderVisible(true);
		tree.setLinesVisible(true);
		TextCellEditor cellEditor = new TextCellEditor(tree, 8);

		for (final TadColumn column : columns) {
			TreeViewerColumn treeViewerColumn = new TreeViewerColumn(viewer, 0);
			TreeColumn treeColumn = treeViewerColumn.getColumn();
			layout.setColumnData(treeColumn, new ColumnWeightData(column.getWeight()));
			treeColumn.setText(column.getName());
			treeColumn.setToolTipText(column.getTooltip());
			treeViewerColumn.setEditingSupport(new TadEditingSupport(viewer, cellEditor, column));
			treeColumn.addSelectionListener(new SelectionListener() {
				public void widgetSelected(SelectionEvent e) {
					TreeColumn selectedColumn = (TreeColumn) e.getSource();
					if (tree.getSortColumn() == selectedColumn) {
						tree.setSortDirection(tree.getSortDirection() == 128 ? 1024 : 128);
					} else {
						tree.setSortColumn(selectedColumn);
						tree.setSortDirection(1024);
					}

					viewer.setComparator(new TadItemsViewerComparator(column));
				}

				public void widgetDefaultSelected(SelectionEvent e) {
				}
			});
			StyledCellLabelProvider styledCellLabelProvider = column.getStyledCell();
			if (styledCellLabelProvider != null) {
				if (styledCellLabelProvider instanceof TadBarGraphProvider barGraphProvider) {
					barGraphProvider.setDependency(column.getDependency());
					barGraphProvider.setColumnId(column.getId());
					barGraphProvider.setColumnName(column.getName());
				}

				treeViewerColumn.setLabelProvider(styledCellLabelProvider);
			} else {
				treeViewerColumn.setLabelProvider(createColumnLabelProvider(column));
			}
		}

		ColumnViewerToolTipSupport.enableFor(viewer);
		viewer.getControl().setEnabled(false);
		return viewer;
	}

	protected abstract TadItemColumnLabelProvider createColumnLabelProvider(TadColumn var1);

	private void showPage(final Composite composite) {
		Job job = new UIJob("Show page") {
			public IStatus runInUIThread(IProgressMonitor monitor) {
				if (parentComposite != null && !parentComposite.isDisposed() && layout != null && composite != null) {
					layout.topControl = composite;
					parentComposite.layout();
				}

				return Status.OK_STATUS;
			}
		};
		job.setSystem(true);
		job.setUser(false);
		job.schedule();
	}

	public void showMessage(final TadFactoryDataStatus dataStatus, final String message) {
		Job job = new UIJob("") {
			public IStatus runInUIThread(IProgressMonitor monitor) {
				if (parentComposite != null && !parentComposite.isDisposed() && layout != null && messageViewComposite != null && !messageViewComposite.isDisposed()
						&& messageLabel != null && !messageLabel.isDisposed() && messageDescLabel != null && !messageDescLabel.isDisposed() && exceptionText != null
						&& !exceptionText.isDisposed() && message != null) {
					messageLabel.setText(Auxiliary.capitalize(dataStatus.toString()));
					messageDescLabel.setText(message);
					exceptionText.setVisible(false);
					switch (dataStatus) {
						case SUCCESS:
							tadModel.getLogger().error(Messages.Error_ShowMessage);
							return Status.CANCEL_STATUS;
						case ERROR:
						case ERROR_WITH_LOGS:
							messageLabel.setImage(TadModel.ERROR_IMG);
							if (dataStatus == TadFactoryDataStatus.ERROR_WITH_LOGS) {
								exceptionText.setVisible(true);
								Log[] logs = tadModel.getLogger().getLogs();

								for (int i = logs.length - 1; i > 0; --i) {
									if (logs[i].getType() == LogType.EXCEPTION) {
										exceptionText.setText(logs[i].toString());
										break;
									}
								}
							}
							break;
						case WARNING:
							messageLabel.setImage(TadModel.WARNING_BIG_IMG);
							break;
						case INFORMATION:
							messageLabel.setImage(TadModel.INFORMATION_IMG);
					}

					layout.topControl = messageViewComposite;
					parentComposite.layout();
				}

				return Status.OK_STATUS;
			}
		};
		job.setSystem(true);
		job.setUser(false);
		job.schedule();
		saveToCSV.setEnabled(false);
		pauseView.setEnabled(false);
		pauseView.setChecked(false);
	}

	public void showProgressBar() {
		showPage(progressViewComposite);
	}

	public void showTadView() {
		showPage(tadViewComposite);
	}

	public boolean isVisible() {
		return visible;
	}

	private void setVisible(boolean isVisible) {
		visible = isVisible;
	}

	public boolean isViewPaused() {
		return pauseView.isChecked();
	}

	public void enableAllActions(boolean enable) {
		saveToCSV.setEnabled(enable);
		pauseView.setEnabled(enable);
		if (!enable) {
			pauseView.setChecked(false);
		}

	}

	public void refreshData(final Object data, final boolean enabled) {
		for (final TreeViewer viewer : viewers.values()) {
			UIJob job = new UIJob("Refresh " + getTitle()) {
				public IStatus runInUIThread(IProgressMonitor monitor) {
					if (viewer.getControl().isDisposed()) {
						return Status.CANCEL_STATUS;
					} else {
						viewer.setInput(data);
						viewer.getControl().setEnabled(enabled);
						return Status.OK_STATUS;
					}
				}
			};
			job.setSystem(true);
			job.setUser(false);
			job.schedule();
		}

		enableAllActions(enabled);
		if (!enabled) {
			pauseView.setChecked(false);
		}

	}

	public void clearData() {
		for (final Viewer viewer : viewers.values()) {
			UIJob job = new UIJob("Clear " + getTitle()) {
				public IStatus runInUIThread(IProgressMonitor monitor) {
					if (viewer.getControl().isDisposed()) {
						return Status.CANCEL_STATUS;
					} else {
						viewer.setInput(null);
						return Status.OK_STATUS;
					}
				}
			};
			job.setSystem(true);
			job.setUser(false);
			job.schedule();
		}

	}

	public void enableAllViewers(final boolean enable) {
		final TreeViewer viewer = viewers.get(TadViewViewerType.PARENT);
		final TreeViewer childViewer = viewers.get(TadViewViewerType.CHILD);
		if (viewer != null && !viewer.getControl().isDisposed() && childViewer != null && !childViewer.getControl().isDisposed()) {
			UIJob job = new UIJob((enable ? "Enable " : "Disable ") + getTitle()) {
				public IStatus runInUIThread(IProgressMonitor monitor) {
					if (viewer.getControl().isDisposed()) {
						return Status.CANCEL_STATUS;
					} else {
						enableViewer(viewer, enable);
						Tree tree = viewer.getTree();
						if (childViewer.getControl().isDisposed()) {
							return Status.CANCEL_STATUS;
						}

						enableViewer(childViewer, enable);
						if (enable) {
							for (TreeItem treeItem : tree.getItems()) {
								TadItem item = (TadItem) treeItem.getData();
								if (item.isValid()) {
									tree.setSelection(treeItem);
									childViewer.setInput(item);
									enableViewer(childViewer, item.hasChildItems());
									break;
								}
							}
						}

						return Status.OK_STATUS;
					}
				}
			};
			job.setSystem(true);
			job.setUser(false);
			job.schedule();
		}
	}

	public void enableViewer(TreeViewer viewer, boolean enable) {
		if (viewer != null) {
			Tree tree = viewer.getTree();
			if (!tree.isDisposed()) {
				if (enable) {
					if (tree.getColumnCount() > previousSortColumn) {
						tree.setSortColumn(tree.getColumn(previousSortColumn));
					} else {
						tree.setSortColumn(null);
					}

					tree.setSortDirection(128);
				} else {
					if (tree.getSortColumn() != null) {
						int index = 0;

						for (TreeColumn column : tree.getColumns()) {
							if (column.getText().equals(tree.getSortColumn().getText())) {
								previousSortColumn = index;
								break;
							}

							++index;
						}
					}

					tree.setSortColumn(null);
					tree.setSortDirection(0);
				}

				viewer.getControl().setEnabled(enable);
			}
		}
	}

	public void setFocus() {
	}
}
