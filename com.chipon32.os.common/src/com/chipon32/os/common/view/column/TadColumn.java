package com.chipon32.os.common.view.column;

import org.eclipse.jface.viewers.StyledCellLabelProvider;

import com.chipon32.os.common.rtos.IRtosConfig;

public class TadColumn {
	private ITadColumnId id;
	private String name;
	private String tooltip;
	private IRtosConfig dependency;
	private TadColumnWeight weight;
	private StyledCellLabelProvider styledCell;
	private TadColumnCopyable copyable;

	public TadColumn(ITadColumnId id, String name, String tooltip, TadColumnWeight weight, TadColumnCopyable copyable, StyledCellLabelProvider styledCell, IRtosConfig dependency) {
		this.id = id;
		this.name = name;
		this.tooltip = tooltip;
		this.weight = weight;
		this.copyable = copyable;
		this.styledCell = styledCell;
		this.dependency = dependency;
	}

	public TadColumn(ITadColumnId id, String name, String tooltip, TadColumnWeight weight, TadColumnCopyable copyable) {
		this(id, name, tooltip, weight, copyable, (StyledCellLabelProvider) null, (IRtosConfig) null);
	}

	public ITadColumnId getId() {
		return this.id;
	}

	public String getName() {
		return this.name;
	}

	public String getTooltip() {
		return this.tooltip;
	}

	public int getWeight() {
		switch (this.weight) {
			case NARROW:
				return 2;
			case SMALL:
				return 5;
			case NORMAL:
			default:
				return 10;
			case WIDE:
				return 15;
		}
	}

	public StyledCellLabelProvider getStyledCell() {
		return this.styledCell;
	}

	public IRtosConfig getDependency() {
		return this.dependency;
	}

	public boolean isCopyable() {
		return this.copyable == TadColumnCopyable.COPYABLE;
	}
}
