package com.chipon32.os.common.view.action;

import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.io.Writer;
import java.nio.charset.StandardCharsets;
import java.nio.file.FileSystems;
import java.util.Map;
import java.util.regex.Matcher;

import javax.swing.*;

import org.eclipse.jface.action.Action;
import org.eclipse.jface.resource.ImageDescriptor;
import org.eclipse.jface.viewers.TreeViewer;
import org.eclipse.swt.widgets.FileDialog;
import org.eclipse.swt.widgets.Shell;
import org.eclipse.swt.widgets.Tree;
import org.eclipse.swt.widgets.TreeColumn;
import org.eclipse.swt.widgets.TreeItem;

import com.chipon32.os.common.messages.Messages;
import com.chipon32.os.common.messages.Separator;
import com.chipon32.os.common.model.TadModel;
import com.chipon32.os.common.model.Time;
import com.chipon32.os.common.view.TadViewViewerType;
import com.chipon32.os.common.view.items.TadItem;

public class SaveToFileAction extends Action {

	private final String CSV_SEPARATOR;
	private final String EOL;
	private final String[] FILTER_NAMES;
	private final String[] FILTER_EXTENSIONS;
	private final TadModel tadModel;
	private final String viewName;
	private final FileDialog dialog;
	private final Map<TadViewViewerType, TreeViewer> viewers;

	public SaveToFileAction(TadModel tadModel, Shell parentShell, String viewName, Map<TadViewViewerType, TreeViewer> viewers) {
		this.CSV_SEPARATOR = "sep=" + Separator.SEMICOLON;
		this.EOL = System.lineSeparator();
		this.FILTER_NAMES = new String[] { "CSV", "Plain text" };
		this.FILTER_EXTENSIONS = new String[] { "*.csv", "*.txt" };
		this.tadModel = tadModel;
		this.viewName = viewName;
		this.viewers = viewers;
		this.dialog = new FileDialog(parentShell, 8192);
		this.dialog.setText(String.format(Messages.Label_SaveToFile, viewName));
		this.dialog.setFilterPath((new JFileChooser()).getFileSystemView().getDefaultDirectory().toString());
		this.dialog.setFilterNames(this.FILTER_NAMES);
		this.dialog.setFilterExtensions(this.FILTER_EXTENSIONS);
	}

	public void run() {
		this.dialog.setFileName(viewName.replace(" ", "_") + "_" + Time.SAVE_FORMAT);
		String path = this.dialog.open();
		if (path != null) {
			Separator separator;
			if (this.dialog.getFilterIndex() == 0) {
				path = addExtensionToFile(path, ".csv");
				separator = Separator.SEMICOLON;
			} else {
				path = addExtensionToFile(path, ".txt");
				separator = Separator.TAB;
			}

			try (Writer csv = new OutputStreamWriter(new FileOutputStream(path), StandardCharsets.UTF_8)) {
				if (separator == Separator.SEMICOLON) {
					csv.append(this.CSV_SEPARATOR);
					csv.append(this.EOL);
				}

				for (TreeViewer viewer : this.viewers.values()) {
					Tree tree = viewer.getTree();
					TreeItem selectedItem = null;
					if (viewer.getTree().getSelection().length > 0) {
						selectedItem = viewer.getTree().getSelection()[0];
					}

					for (TreeColumn column : tree.getColumns()) {
						csv.append(column.getText());
						csv.append(separator.toString());
					}

					csv.append(this.EOL);

					for (TreeItem treeItem : tree.getItems()) {
						if (treeItem.getData() instanceof TadItem item) {
							csv.append(item.toString(separator));
							if (treeItem.equals(selectedItem)) {
								csv.append("*");
							}

							if (item.hasTreeChildren()) {
								for (TadItem treeChild : item.getTreeChildren()) {
									csv.append(this.EOL);
									csv.append(separator.toString());
									csv.append(treeChild.toString(separator));
								}
							}

							csv.append(this.EOL);
						}
					}

					csv.append(this.EOL);
				}
			} catch (IOException e) {
				this.tadModel.getLogger().exception(e, String.format(Messages.Exception_WhileSavingView, this.viewName));
			}
		} else {
			this.tadModel.getLogger().error(Messages.Error_SavePathInvalid);
		}

	}

	public static String addExtensionToFile(String filename, String extension) {
		if (filename != null) {
			String[] splittedPath = filename.split(Matcher.quoteReplacement(FileSystems.getDefault().getSeparator()));
			String[] splittedName = splittedPath[splittedPath.length - 1].split("\\.");
			if (splittedName.length == 1) {
				filename = filename.concat(extension);
			}
		}

		return filename;
	}

	public String getText() {
		return String.format(Messages.Label_SaveToFile, this.viewName);
	}

	public String getToolTipText() {
		return String.format(Messages.Label_SaveToFile, this.viewName);
	}

	public ImageDescriptor getImageDescriptor() {
		return TadModel.SAVE_IMG_DESC;
	}
}
