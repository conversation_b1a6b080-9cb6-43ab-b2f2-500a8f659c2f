package com.chipon32.os.common.view.action;

import org.eclipse.jface.action.Action;
import org.eclipse.jface.resource.ImageDescriptor;
import org.eclipse.jface.viewers.TreeViewer;
import org.eclipse.swt.dnd.Clipboard;
import org.eclipse.swt.dnd.TextTransfer;
import org.eclipse.swt.dnd.Transfer;
import org.eclipse.swt.widgets.Shell;
import org.eclipse.swt.widgets.TreeItem;
import org.eclipse.ui.PlatformUI;

import com.chipon32.os.common.messages.Messages;
import com.chipon32.os.common.messages.Separator;
import com.chipon32.os.common.model.TadModel;
import com.chipon32.os.common.view.items.TadItem;

public class SaveToClipboard extends Action {
	private TadModel tadModel;
	private Shell parentShell;
	private TreeViewer viewer;

	public SaveToClipboard(TadModel tadModel, Shell parentShell, TreeViewer viewer) {
		this.tadModel = tadModel;
		this.parentShell = parentShell;
		this.viewer = viewer;
	}

	public void run() {
		Clipboard clipboard = new Clipboard(this.parentShell.getDisplay());
		TreeItem[] selected = this.viewer.getTree().getSelection();
		if (selected.length > 0 && selected[0].getData() instanceof TadItem) {
			String tadItemStr = ((TadItem) selected[0].getData()).toString(Separator.TAB);
			if (tadItemStr != null && !tadItemStr.isEmpty()) {
				try {
					clipboard.setContents(new Object[] { tadItemStr }, new Transfer[] { TextTransfer.getInstance() });
				} catch (IllegalArgumentException e) {
					this.tadModel.getLogger().exception(e);
				}
			}
		}

		clipboard.dispose();
	}

	public String getText() {
		return Messages.Label_CopyLine;
	}

	public String getToolTipText() {
		return Messages.Label_CopyLine;
	}

	public ImageDescriptor getImageDescriptor() {
		return PlatformUI.getWorkbench().getSharedImages().getImageDescriptor("IMG_TOOL_COPY");
	}
}
