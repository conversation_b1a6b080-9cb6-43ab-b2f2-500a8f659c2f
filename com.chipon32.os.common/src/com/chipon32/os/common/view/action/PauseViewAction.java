package com.chipon32.os.common.view.action;

import org.eclipse.jface.action.Action;
import org.eclipse.jface.resource.ImageDescriptor;
import org.eclipse.jface.viewers.TreeViewer;

import com.chipon32.os.common.TadCommonActivator;
import com.chipon32.os.common.messages.Messages;
import com.chipon32.os.common.model.TadModel;
import com.chipon32.os.common.view.TadView;

public class PauseViewAction extends Action {
	private TadModel tadModel;
	private TadView view;

	public PauseViewAction(TadModel tadModel, TadView view) {
		this.tadModel = tadModel;
		this.view = view;
	}

	public void run() {
		if (this.isEnabled()) {
			for (TreeViewer viewer : this.view.getTreeViewers().values()) {
				this.view.enableViewer(viewer, !this.isChecked());
			}

			this.tadModel.getLogger().info(String.format(Messages.Info_ViewPaused, this.view.getTitle(), this.isChecked() ? "paused" : "unpaused"));
			if (!this.isChecked() && !this.view.getController().isDataReady()) {
				this.view.showProgressBar();
				this.view.getController().requestData();
			}
		}

	}

	public String getText() {
		return String.format(Messages.Label_PauseView, this.view.getTitle());
	}

	public String getToolTipText() {
		return String.format(Messages.Label_PauseView_ToolTip, this.view.getTitle());
	}

	public ImageDescriptor getImageDescriptor() {
		return TadCommonActivator.getDefault().getImageDescriptor("pause");
	}
}
