package com.chipon32.os.common;

import java.util.Optional;

import org.eclipse.cdt.dsf.datamodel.DMContexts;
import org.eclipse.cdt.dsf.datamodel.IDMContext;
import org.eclipse.cdt.dsf.debug.service.IRunControl;
import org.eclipse.cdt.dsf.service.DsfSession;
import org.eclipse.debug.ui.DebugUITools;

public class DebugHelper {
	public static DsfSession getSession(IDMContext dmCtx) {
		return dmCtx != null ? DsfSession.getSession(dmCtx.getSessionId())
				: Optional.ofNullable(getCurrentContext())
						.map((ctx) -> DsfSession.getSession(ctx.getSessionId()))
						.orElse(null);
	}

	public static DsfSession getCurrentSession() {
		return getSession(null);
	}

	public static IDMContext getCurrentContext() {
		return Optional.ofNullable(DebugUITools.getDebugContext())
				.map((ctx) -> ctx.getAdapter(IDMContext.class))
				.orElse(null);
	}

	public static IRunControl.IContainerDMContext getCurrentExecutionContext() {
		return Optional.ofNullable(getCurrentContext())
				.map((ctx) -> ctx.getAdapter(IDMContext.class))
				.map((ctx) -> DMContexts.getAncestorOfType(ctx, IRunControl.IContainerDMContext.class))
				.orElse(null);
	}
}
