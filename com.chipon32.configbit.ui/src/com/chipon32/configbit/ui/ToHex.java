package com.chipon32.configbit.ui;
/**
 * 进制转换类
 * */
public class ToHex {
	//二进制转化成十六进制
	public static String binaryToHex(String binaryValue) {
        String[] num = {"0000", "0001","0010", "0011", "0100", "0101", "0110",
            "0111", "1000", "1001", "1010", "1011", "1100", "1101",
            "1110", "1111"};
 
        while (true) {
            if (binaryValue.length() % 4 != 0) {
                binaryValue = "0" + binaryValue;
            }
            else break;
        }
		 
        String hexValue = "";
        for (int i = 0; i < binaryValue.length(); i += 4) {
            String temp = binaryValue.substring(i, i + 4);
            for (int j = 0; j < num.length; j++) {
                if (temp.equals(num[j])) {
                    switch (j) {
                        case 0:
                            hexValue += '0';
                            break;
                        case 1:
                            hexValue += '1';
                            break;
                        case 2:
                            hexValue += '2';
                            break;
                        case 3:
                            hexValue += '3';
                            break;
                        case 4:
                            hexValue += '4';
                            break;
                        case 5:
                            hexValue += '5';
                            break;
                        case 6:
                            hexValue += '6';
                            break;
                        case 7:
                            hexValue += '7';
                            break;
                        case 8:
                            hexValue += '8';
                            break;
                        case 9:
                            hexValue += '9';
                            break;
                        case 10:
                            hexValue += 'a';
                            break;
                        case 11:
                            hexValue += 'b';
                            break;
                        case 12:
                            hexValue += 'c';
                            break;
                        case 13:
                            hexValue += 'd';
                            break;
                        case 14:
                            hexValue += 'e';
                            break;
                        case 15:
                            hexValue += 'f';
                            break;
                    }
 
                }
            }
        }
        return hexValue;
    }
	
	//十六进制转化成十进制
	public static int sixToTen(String hexString){
		int decimalValue = 0;
		int ch = 0;
		hexString = hexString.toUpperCase();
		for (int i = 0; i < hexString.length(); i++) {
			char hexChar = hexString.charAt(i);
			
			if(hexChar >= 'A' && hexChar <= 'F')
				ch = 10 + hexChar - 'A';
			else //hexChar is '0','1','2',……, or '9'
				ch = hexChar - '0';
			decimalValue = decimalValue * 16 + ch;
		}
		
		
		return decimalValue;
	}
	//十进制转化成十六进制
	public static String tenToSix(String hexString){
		String hex = "";
		int decimal = Integer.parseInt(hexString);
		while (decimal !=0){
			char ch ;
			int hexValue = decimal % 16;
			if(hexValue <=9 && hexValue >=0)
				ch = (char)(hexValue + '0');
			else 
				ch = (char)(hexValue - 10 + 'A');
			
			hex = ch + hex ;
			decimal = decimal /16 ;
		}
		
		return hex;
	}
}
