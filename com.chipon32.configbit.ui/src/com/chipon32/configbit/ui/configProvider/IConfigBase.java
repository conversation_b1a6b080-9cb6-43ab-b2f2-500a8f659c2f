package com.chipon32.configbit.ui.configProvider;

/**
 * 芯片名，配置字，扩展节点   是否汇编项目，是否C语言项目的字符串码
 * 接口类，但不另实现，都是静态的字符串声明，独立使用
 * <AUTHOR>
 *
 */
public interface IConfigBase {
	//chipName
    public static final String chipName = "chipName";  

    
    //汇编项目类型ID
    public static final String CHIPON_ASM_ID = "com.chipon32.chiponide.core.projectType.s";
    //C项目类型ID
	public static final String CHIPON_C_ID = "com.chipon32.chiponide.core.projectType.c";
    //C++项目类型ID
	public static final String CHIPON_Cplus_ID = "com.chipon32.chiponide.core.projectType.g";
	
	public static final String CHIPON_ARCHIVE_ID = "com.chipon32.chiponide.core.projectType.x";
}
