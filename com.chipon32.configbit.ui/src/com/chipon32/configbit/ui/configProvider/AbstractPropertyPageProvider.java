package com.chipon32.configbit.ui.configProvider;


import org.eclipse.swt.SWT;

import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.widgets.Button;

import org.eclipse.swt.widgets.Composite;

import org.eclipse.swt.widgets.Group;



public  class AbstractPropertyPageProvider implements IConfigProvider
		 {	
	
	// 唯一的该类对象
	private static AbstractPropertyPageProvider APPG;	
	/**
	 * The constructor
	 */
	public AbstractPropertyPageProvider() {
	}
	
	public static IConfigProvider getDefaultIConfigProvider()
	{
		if(APPG==null)
			APPG=new AbstractPropertyPageProvider();
		return APPG;		
	}
	
// 电压选择内容
	protected Group group;//调试电压选择的group
	
	protected Button btn_providerByPower;//外部供电选择按钮
	
	protected Button btn_3point3v;//3.3V的选择按钮	
	protected Button btn_5v;//5V的选择按钮	

	protected String chipPower;
	
	protected Composite	composite;
// 	
//	@Override
//	public void createPartControl(Composite parent) {	
//		 
//		 setconfigbits(parent);
//		 initUI();
//	}


	//配置位选择
	public  void setconfigbits(Composite parent)
	{
		
	}
	//初始化各组件（即下拉框）的值
	public  void initUI()
	{
		
	}

	protected void createDebugPowerSelectionGroup(Composite usercomp){
		Composite container = new Composite(usercomp, SWT.NULL);
		
		group = new Group(container, SWT.NONE);
	    group.setText("目标板电压选择");
	    group.setBounds(5, 5, 480, 52);
	    
	    btn_providerByPower = new Button(group, SWT.RADIO);
	    btn_providerByPower.setBounds(4, 26, 67, 16);
	    btn_providerByPower.setText("外部供电");
	    btn_providerByPower.addSelectionListener(new SelectionAdapter() {
            @Override
            public void widgetSelected(SelectionEvent e) {
              
            }
        });
	    	  
	    
	    btn_3point3v = new Button(group, SWT.RADIO);
	    btn_3point3v.setBounds(119, 26, 47, 16);
	    btn_3point3v.setText("3.3V");
	    btn_3point3v.addSelectionListener(new SelectionAdapter() {

            @Override
            public void widgetSelected(SelectionEvent e) {
               
            }
        });
	    
	    btn_5v = new Button(group, SWT.RADIO);
	    btn_5v.addSelectionListener(new SelectionAdapter() {
	        @Override
	        public void widgetSelected(SelectionEvent e) {
	          
	        }
	    });
	    btn_5v.setBounds(172, 26, 33, 16);
	    btn_5v.setText("5V");
	    
	  
	    /*
	     * 如果电压的配置文件不为空，则根据配置文件的值赋值
	     */
	    if(chipPower != null && !"".equalsIgnoreCase(chipPower)){
	    	switch(chipPower){
	    	case "0":
	    		btn_providerByPower.setSelection(true);
	    		break;	    
	    	case "3.3":
	    		btn_3point3v.setSelection(true);
	    		break;
	    	case "5":
	    		btn_5v.setSelection(true);
	    		break;
	    	default:
	    		btn_3point3v.setSelection(true);
	    	}
	    }else{
	    	btn_3point3v.setSelection(true);
	    }
	}

	@Override
	public String getChipPower() {
		if(btn_providerByPower == null || btn_providerByPower.isDisposed()){
			return chipPower;
		}
		if(btn_providerByPower.getSelection()){ 
			return "0";
		}else if(btn_3point3v.getSelection()){
			return "3.3";
		}else if(btn_5v.getSelection()){
			return "5";
		}
		return "3.3";//默认电压3.3V
	}

	public void setChipPower(String chipPower) {
		this.chipPower = chipPower;
	}
	
	
	
//	//设置组件是否可用
//	@Override
//	public void setInCode(boolean code) {
//		
//	}
//
//
//	@Override
//	public void init() {
//		// TODO Auto-generated method stub
//		
//	}
//
//
//	@Override
//	public void configUiToFile() {
//		// TODO Auto-generated method stub
//		
//	}
//
//
//	@Override
//	public void configUiFromFile() {
//		// TODO Auto-generated method stub
//		
//	}
	
}
