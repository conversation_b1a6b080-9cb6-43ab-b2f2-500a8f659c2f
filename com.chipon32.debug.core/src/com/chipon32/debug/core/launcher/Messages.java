package com.chipon32.debug.core.launcher;

import org.eclipse.osgi.util.NLS;

public class Messages extends NLS {
	private static final String BUNDLE_NAME = "com.chipon32.debug.core.launcher.messages"; //$NON-NLS-1$
	public static String ChiponLaunchConfigurationDelegate_10;
	public static String ChiponLaunchConfigurationDelegate_11;
	public static String ChiponLaunchConfigurationDelegate_12;
	public static String ChiponLaunchConfigurationDelegate_16;
	public static String ChiponLaunchConfigurationDelegate_24;
	public static String ChiponLaunchConfigurationDelegate_26;
	public static String ChiponLaunchConfigurationDelegate_28;
	public static String ChiponLaunchConfigurationDelegate_29;
	public static String ChiponLaunchConfigurationDelegate_3;

	public static String ChiponLaunchConfigurationDelegate_38;
	public static String ChiponLaunchConfigurationDelegate_40;
	public static String ChiponLaunchConfigurationDelegate_41;
	public static String ChiponLaunchConfigurationDelegate_42;
	public static String ChiponLaunchConfigurationDelegate_43;
	public static String ChiponLaunchConfigurationDelegate_6;
	public static String ChiponLaunchConfigurationDelegate_7;
	public static String ChiponLaunchConfigurationDelegate_8;
	public static String ChiponLaunchConfigurationDelegate_9;
	
	public static String ChiponLaunchConfigurationDelegate_1;
	static {
		// initialize resource bundle
		NLS.initializeMessages(BUNDLE_NAME, Messages.class);
	}

	private Messages() {
	}
}
