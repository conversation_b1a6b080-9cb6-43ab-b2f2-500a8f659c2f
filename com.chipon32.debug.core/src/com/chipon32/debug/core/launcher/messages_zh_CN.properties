ChiponLaunchConfigurationDelegate_10=，请使用COM1~COM64或将大于该范围的设备修改为在支持范围...
ChiponLaunchConfigurationDelegate_11=项目:
ChiponLaunchConfigurationDelegate_12=，芯片在线调试，调试器启动中.....

ChiponLaunchConfigurationDelegate_16=调试失败，请检测模式下构建输出文件

ChiponLaunchConfigurationDelegate_24=硬件启动gdb :
ChiponLaunchConfigurationDelegate_26=下载失败，退出调试！
ChiponLaunchConfigurationDelegate_28=项目:
ChiponLaunchConfigurationDelegate_29=:软件仿真调试.....
ChiponLaunchConfigurationDelegate_3=调试目标为空
ChiponLaunchConfigurationDelegate_38=软件启动gdb :
ChiponLaunchConfigurationDelegate_40=芯片配置不存在，写入退出
ChiponLaunchConfigurationDelegate_41=目标文件未生成，请先build项目\!
ChiponLaunchConfigurationDelegate_42=当前调试项目为：
ChiponLaunchConfigurationDelegate_43=设备未连接，请重新检查设备
ChiponLaunchConfigurationDelegate_6=调试请选择Debug模式编译，并Debug模式下启动调试
ChiponLaunchConfigurationDelegate_7=设备状态异常，调试请求退出\!
ChiponLaunchConfigurationDelegate_8=设备状态异常，调试请求退出\!
ChiponLaunchConfigurationDelegate_9=当前调试设备

ChiponLaunchConfigurationDelegate_1=芯片无更多可用断点资源，可删除部分不用断点后再启动硬件调试