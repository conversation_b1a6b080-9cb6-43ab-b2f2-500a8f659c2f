/**
 * 
 */
package com.chipon32.debug.core.launcher;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.Arrays;

import javax.swing.*;

import org.eclipse.cdt.managedbuilder.core.IConfiguration;
import org.eclipse.cdt.managedbuilder.core.IManagedBuildInfo;
import org.eclipse.cdt.managedbuilder.core.ManagedBuildManager;
import org.eclipse.cdt.managedbuilder.macros.BuildMacroException;
import org.eclipse.cdt.managedbuilder.macros.IBuildMacroProvider;
import org.eclipse.core.resources.IFile;
import org.eclipse.core.resources.IProject;
import org.eclipse.core.resources.ResourcesPlugin;
import org.eclipse.core.runtime.CoreException;
import org.eclipse.core.runtime.IPath;
import org.eclipse.core.runtime.IProgressMonitor;
import org.eclipse.core.runtime.IStatus;
import org.eclipse.core.runtime.Path;
import org.eclipse.core.runtime.Platform;
import org.eclipse.core.runtime.Status;
import org.eclipse.debug.core.DebugPlugin;
import org.eclipse.debug.core.ILaunch;
import org.eclipse.debug.core.ILaunchConfiguration;
import org.eclipse.debug.core.model.IBreakpoint;
import org.eclipse.debug.core.model.LaunchConfigurationDelegate;

import com.chipon32.chiponide.core.paths.ChipOnPath;
import com.chipon32.chiponide.core.paths.SystemPathHelper;
import com.chipon32.chiponide.core.properties.ChipOnProjectProperties;
import com.chipon32.chiponide.core.properties.ProjectPropertyManager;
import com.chipon32.chiponide.core.utils.ChipNameManager;
import com.chipon32.chiponide.ui.handlers.DownHexToChip;
import com.chipon32.chiponide.ui.handlers.ResetDriver;
import com.chipon32.chiponide.ui.util.TargetHexFileMonitorUtil;
import com.chipon32.debug.core.DebugCoreActivator;
import com.chipon32.debug.core.model.ChiponDebugInfoModel;
import com.chipon32.debug.core.model.ChiponDebugTarget;
import com.chipon32.debug.core.model.ChiponThread;
import com.chipon32.debug.core.util.ChiponDebugUtil;
import com.chipon32.hex.core.DownLoad_ElementVaule;
import com.chipon32.hex.core.HexFileParser;
import com.chipon32.hex.core.Memory;
import com.chipon32.hex.core.util.IConfigurationProvider;
import com.chipon32.util.communicate.DeviceManager;
import com.chipon32.util.communicate.OperationFactory;
import com.chipon32.util.communicate.WriteMessage;
import com.chipon32.util.provider.ConfigurationFactory;
import com.chipon32.util.ui.UiUtilActivator;

/**
 * 调试入口
 * 
 * <AUTHOR> 
 */
public class ChiponLaunchConfigurationDelegate extends LaunchConfigurationDelegate {

	private Memory memory;

	private IConfigurationProvider configElement;
	private static boolean isUploadSuccess = false;
	private String chipName;

	/**
	 * 当前调试对象
	 */
	private IProject project;

	/*
	 * (non-Javadoc) 调试入口函数
	 * 默认为 Relase或debug，并加 硬件或软件标志 
	 * @see
	 * org.eclipse.debug.core.model.ILaunchConfigurationDelegate#launch(org.
	 * eclipse.debug.core.ILaunchConfiguration, java.lang.String,
	 * org.eclipse.debug.core.ILaunch,
	 * org.eclipse.core.runtime.IProgressMonitor)
	 */
	@Override
	public void launch(ILaunchConfiguration configuration, String mode, ILaunch launch, IProgressMonitor monitor) throws CoreException {
		//对目前已经启动调试的情况处理
		ChiponThread fThread = ChiponDebugUtil.getCurrentChiponThread();
		if(fThread!=null) {
			ChiponDebugTarget target = fThread.getChiponDebugTarget();
			if (target!=null && /*!target.isTerminated()*/ChiponDebugTarget.isDebugging) {
				fThread.terminate();
				target.debuggerTerminated();
			}
		}
		//===============================
		
		UiUtilActivator.isNeedShowConsoleFlag=true;
		String program = configuration.getAttribute(DebugCoreActivator.ATTR_CHIP_PROGRAM, (String) null);// 空间的相对路径
		String programLine = configuration.getAttribute(DebugCoreActivator.ATTR_CHIP_PROGRAM_LINE, (String) null);
		String programMain = configuration.getAttribute(DebugCoreActivator.ATTR_CHIP_PROGRAM_MAIN, (String) null);
		String pcAddress = configuration.getAttribute(DebugCoreActivator.ATTR_CHIP_PC_ADDRESS, (String) null);// 空间的相对路径
		boolean downloadSetting = configuration.getAttribute(DebugCoreActivator.ATTR_CHIP_DOWNLOAD_SETTING, false);
		int autoStepInInterval = configuration.getAttribute(DebugCoreActivator.ATTR_CHIP_AUTO_STEPIN_INTERVAL, 100);
		String instructionSetType = configuration.getAttribute(DebugCoreActivator.ATTR_CHIP_INSTRUCTION_SET_TYPE, "kf32g");
		boolean isMultiCore = configuration.getAttribute(DebugCoreActivator.ATTR_CHIP_SINGLE_CORE_OR_MULTI_CORE, false);
		
		IPath path = new Path(program);
		// 调试目标文件路径
		IFile file = ResourcesPlugin.getWorkspace().getRoot().getFile(path);
		if (!file.exists()) {
			abort(Messages.ChiponLaunchConfigurationDelegate_3, null);//中止调试
		}
		project = file.getProject();
		String chipName = ChipNameManager.getChipName(project);
		//芯片的相关配置信息
		IConfigurationProvider chipxmlmessage = ConfigurationFactory.getProvider(chipName); 
		int chipRangeCode = Integer.parseInt(chipxmlmessage.getChipRange(), 16);
        int issupportGP = Integer.parseInt(chipxmlmessage.getIsSupportR16(),16);  
        int issupportDSP = Integer.parseInt(chipxmlmessage.getIsSupportACC(),16);  
        int issupportFPU = Integer.parseInt(chipxmlmessage.getIsSupportFPU(),16);  
        int cpuNumber = Integer.parseInt(chipxmlmessage.getCPUNumber(),16); 
        if(!isMultiCore) {
        	cpuNumber = 1;
        }
        
        String  ChipVer="kf32"; //$NON-NLS-1$
        if(issupportDSP>0){
        	ChipVer+="d";
            if(issupportFPU>0)
            	ChipVer+="f"; // means GD GDF
        }
        else if(issupportFPU>0)
        	ChipVer+="f";   // means GF
        else if(issupportGP>0)
        	ChipVer+="g";   // means G
        else
        	ChipVer+="r";   //means r
		// --------------------------------------------
		
		 //仅debug模式下存在调试信息才能启用调试器 -> 已优化，debug和release模式都可进行调试
//		String isDebugOrRelease="Debug"; //$NON-NLS-1$
//		try{
//			isDebugOrRelease = ManagedBuildManager.getBuildInfoLegacy(project).getConfigurationName(); //得到处于活动状态Release或Debug？
//		}catch (Exception e) {
//			// TODO: handle exception
//			// 启动的调试界面下会获取不到构建信息，假定Releas，使回到编辑界面启动，避免错误信息
//		}
//		if(!isDebugOrRelease.equalsIgnoreCase("Debug")) //$NON-NLS-1$
//		{			
//			WriteMessage.getDefault().writeErrMessage(Messages.ChiponLaunchConfigurationDelegate_6);
//			return;
//		}
		//@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
		IFile fileelf = null;
		String elfDir="";
		IManagedBuildInfo bi = ManagedBuildManager.getBuildInfo(project);
		if(bi==null){
			WriteMessage.getDefault().writeErrMessage(Messages.ChiponLaunchConfigurationDelegate_16);
			return ;
		}
		IConfiguration buildcfg = bi.getDefaultConfiguration();
		String cfgName = buildcfg.getName();
		IBuildMacroProvider provider = ManagedBuildManager.getBuildMacroProvider();
		try {
//			String buildArtifactName = provider.resolveValue(buildcfg.getArtifactName(),
//			        "", " ", IBuildMacroProvider.CONTEXT_CONFIGURATION, buildcfg);
			String buildArtifactName =provider.getMacro("BuildArtifactFileBaseName", IBuildMacroProvider.CONTEXT_CONFIGURATION, buildcfg, true).getStringValue();	
			buildArtifactName = provider.resolveValue(buildArtifactName,
			        "", " ", IBuildMacroProvider.CONTEXT_CONFIGURATION, buildcfg);	
			
			elfDir = project.getFullPath() + "/" +  cfgName;
			String fileName = buildArtifactName + "." + "elf";//buildcfg.getArtifactExtension();
			IFile tmpFile = project.getFile(cfgName + "/" + fileName);
			if(tmpFile != null && tmpFile.exists())
			{
				fileelf = tmpFile;
				elfDir = tmpFile.getLocation().toOSString().toString();
			}
			else
			{
				WriteMessage.getDefault().writeErrMessage(Messages.ChiponLaunchConfigurationDelegate_16);
				return ;			
			}
		} catch (BuildMacroException e) {
//			e.printStackTrace();
			WriteMessage.getDefault().writeErrMessage(Messages.ChiponLaunchConfigurationDelegate_16);
			return ;
		}	
		
		// -------------------------- 调试文件 elf的获取		
		if(!fileelf.exists()){
			WriteMessage.getDefault().writeErrMessage(Messages.ChiponLaunchConfigurationDelegate_16);
			return ;
		}
		// --------------------------mode
		//@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@		
		// 根据软件或硬件调试模式进行分支
		ChipOnProjectProperties copbuf = ProjectPropertyManager.getPropertyManager(project).getProjectProperties();
		int debugMode = copbuf.getFdebugMode();   //调试方式：0为硬件调试,   1为软件调试
		if (debugMode == 0) { // 硬件调试 条件
			IBreakpoint[] breakpoints = DebugPlugin.getDefault().getBreakpointManager().getBreakpoints(DebugCoreActivator.ID_CHIP_DEBUG_MODEL);
			int breakpointscount = 0;
			for(int i=0; i<breakpoints.length; i++) {
				if(breakpoints[i].isEnabled()) {
					breakpointscount++;
				}
			}
			if(breakpointscount>=5) {
				JOptionPane.showMessageDialog(null, Messages.ChiponLaunchConfigurationDelegate_1);
				return;
			}
			//===================================================================
			
			boolean isUpload = true;
			// 尝试干掉上次可能崩溃没退出的调试进程，这里必须注销进程，否则影响真正的启动，即getRuntime().exec太耗资源
			// 通过复位检测编程器功能优先实现退出......
			// 是否检测过设备
			if(DeviceManager.portName==null || DeviceManager.portName.length()<2)
			{
//				WriteMessage.getDefault().writeErrMessage("未选择有效设备，请刷新或手动指定调试设备");
//				return;
				new ResetDriver().ResetDriverFun();  //选择编程器串口调试设备
			}
			// 健壮设计验证串口编程调试设备是否可用
			try{			
				if(DeviceManager.open()==false)
				{				
					WriteMessage.getDefault().writeMessage(Messages.ChiponLaunchConfigurationDelegate_7);				
					return;
				}
				DeviceManager.close();				
			}catch (Exception e) {					
				isUpload=false;
				WriteMessage.getDefault().writeMessage(Messages.ChiponLaunchConfigurationDelegate_8);	
				return;
			}	
			// 调试gdb的linux模拟环境的最大串口号为0~63的限定
			String com  = DeviceManager.portName;//串口			
			if(com!=null && com.length()>3) {
				if(!CheckPortNum(com,Platform.getOS().equals(Platform.OS_WIN32)?256:64)){
					WriteMessage.getDefault().writeErrMessage(Messages.ChiponLaunchConfigurationDelegate_9+com+Messages.ChiponLaunchConfigurationDelegate_10);
					return;
				}
			}
		
			// 临时调试关闭下载,方法里面未建立属性默认返回值调整
			if (downloadSetting) {
				// 调试模式的下载程序
				UiUtilActivator.isNeedShowConsoleFlag=false;
				isUpload = this.debugUpload();
				UiUtilActivator.isNeedShowConsoleFlag=true;
			}
			
			if(!isUpload) {
				WriteMessage.getDefault().writeErrMessage(Messages.ChiponLaunchConfigurationDelegate_26);
				return;
			}
		}

		// ----------------------------
		String debuggerPath = null;
		String[] commandLine = null;  
		// -----------------------------获取构建信息对象			
		if (bi != null) {
			// 存在构建信息下，获取调试程序类型 C或汇编
			String type = bi.getManagedProject().getProjectType().getId();
			//kf32-gdb.exe路径
			
			if(chipRangeCode == 5) {
				debuggerPath = SystemPathHelper.getSystemPath(ChipOnPath.CHIPONCLANG, project).toOSString();
			} else {
				debuggerPath = SystemPathHelper.getPath(ChipOnPath.CHIPON_COMMON,false, project).toOSString();
			}
			
			String fileSufix=".exe";
			if(Platform.getOS().equals(Platform.OS_LINUX))
				fileSufix="";
			if(debuggerPath.isEmpty() || debuggerPath.equalsIgnoreCase("")) //$NON-NLS-1$
			{
				debuggerPath ="kf32-gdb"+fileSufix; //$NON-NLS-1$
			}
			else 
			{
				debuggerPath+= File.separator + "kf32-gdb"+fileSufix; //$NON-NLS-1$
			}
			if (type != null) {
				debuggerPath="\""+debuggerPath+"\"";  //  \"表示    引号 （"） //$NON-NLS-1$ //$NON-NLS-2$
				elfDir="\""+elfDir+"\""; //$NON-NLS-1$ //$NON-NLS-2$
				//commandLine 中存放两个路径
				
				if(Platform.getOS().equals(Platform.OS_WIN32))
				{
					commandLine = new String[] { //"cmd.exe","/c",
							debuggerPath, // 路径
							elfDir, // 文件路径，elf路径
							// range // 调试型号编码
					};//command=new String []{"cmd.exe","/c",commandLine};
				}
				else if(Platform.getOS().equals(Platform.OS_LINUX))
				{
					commandLine = new String[] { "/bin/sh","-c",
							debuggerPath+ " " + elfDir, // 路径   linux下程序名和参数必须按一个字符串传递
							//elfDir, // 文件路径，elf路径  
							// range // 调试型号编码
					};
				}
				else
				{
					commandLine = new String[] { debuggerPath, // 路径
							elfDir, // 文件路径，elf路径
							// range // 调试型号编码
					};
				}

			}
		}
		
		//--------------------软件或硬件调试打印信息
		if (debugMode == 0) {
			WriteMessage.getDefault().writeBLUEMessage(Messages.ChiponLaunchConfigurationDelegate_11+project.getName()+Messages.ChiponLaunchConfigurationDelegate_12);
			WriteMessage.getDefault().writeMessage("--------------------------------------------------------------"); //$NON-NLS-1$
			System.out.println(Messages.ChiponLaunchConfigurationDelegate_24+debuggerPath+" "+elfDir); //$NON-NLS-2$
		}
		else {
			WriteMessage.getDefault().writeBLUEMessage(Messages.ChiponLaunchConfigurationDelegate_28+project.getName()+Messages.ChiponLaunchConfigurationDelegate_29);
			WriteMessage.getDefault().writeMessage("--------------------------------------------------------------"); //$NON-NLS-1$
			System.out.println(Messages.ChiponLaunchConfigurationDelegate_38+debuggerPath+" "+elfDir); //$NON-NLS-2$
		}
		//=================================================================================
		//==================启动调试器
		String commandLineArrayValue = Arrays.toString(commandLine).replaceAll(",", " ").replaceAll("\\\\", "/");
		Process process = DebugPlugin.exec(commandLine, new File(SystemPathHelper.getPath(ChipOnPath.CHIPON_COMMON,false, project).toOSString()));
		try {
			
			ChiponDebugTarget target = new ChiponDebugTarget(launch, process, file.getProject(), ChipVer, cpuNumber);
			target.setChipRange(chipRangeCode);
			ChiponDebugInfoModel debugInfoModel = new ChiponDebugInfoModel();
			debugInfoModel.setDebugFileName(file.getName());
			debugInfoModel.setDebugFileLine(programLine);
			debugInfoModel.setDebugFileMain(programMain);
			debugInfoModel.setPcAddress(pcAddress);
			debugInfoModel.setDownloadSetting(downloadSetting);
			debugInfoModel.setAutoStepInInterval(autoStepInInterval);
			debugInfoModel.setInstructionSetType(instructionSetType);
			
			target.setChiponDebugInfoModel(debugInfoModel);
			launch.addDebugTarget(target);
			
			UiUtilActivator.isNeedShowConsoleFlag = false;
		} catch (Exception e) {
				e.printStackTrace();
		}
		
	}

	/**
	 * Throws an exception with a new status containing the given message and
	 * optional exception.
	 * 
	 * @param message
	 *            error message
	 * @param e
	 *            underlying exception
	 * @throws CoreException
	 */
	private void abort(String message, Throwable e) throws CoreException {
		throw new CoreException(new Status(IStatus.ERROR, DebugCoreActivator.PLUGIN_ID, 0, message, e));
	}


	/*
	 * 向芯片中下载文件
	 */
	private boolean debugUpload() {
		chipName = ChipNameManager.getChipName(project);
		configElement = ConfigurationFactory.getProvider(chipName);
		// 芯片信息提供者存在与否
		if (configElement == null) {
			WriteMessage.getDefault().writeErrMessage(Messages.ChiponLaunchConfigurationDelegate_40);
			return false;
		}
		// 调试目标文件存在与否
		IFile file = TargetHexFileMonitorUtil.getBuildTargetFileFromProject(project); //hex文件
		if (file == null) {
			WriteMessage.getDefault().writeErrMessage(Messages.ChiponLaunchConfigurationDelegate_41);
			return false;
		}
		
		ChipOnProjectProperties fTargetProps = ProjectPropertyManager.getPropertyManager(project).getProjectProperties();
		DownLoad_ElementVaule[] sector = new DownLoad_ElementVaule[5];
		sector[0]= new DownLoad_ElementVaule(0, fTargetProps.getFdiffdownload0_type(), 	 
				fTargetProps.getFdiffdownload0_address(), 
				fTargetProps.getFdiffdownload0_length(),
				fTargetProps.isFdiffdownload0_enable());
		
		sector[1]= new DownLoad_ElementVaule(1, fTargetProps.getFdiffdownload1_type(), 	 
				fTargetProps.getFdiffdownload1_address(), 
				fTargetProps.getFdiffdownload1_length(),
				fTargetProps.isFdiffdownload1_enable());
		
		sector[2]= new DownLoad_ElementVaule(2, fTargetProps.getFdiffdownload2_type(), 	 
				fTargetProps.getFdiffdownload2_address(), 
				fTargetProps.getFdiffdownload2_length(),
				fTargetProps.isFdiffdownload2_enable());
		
		sector[3]= new DownLoad_ElementVaule(3, fTargetProps.getFdiffdownload3_type(), 	 
				fTargetProps.getFdiffdownload3_address(), 
				fTargetProps.getFdiffdownload3_length(),
				fTargetProps.isFdiffdownload3_enable());
		
		sector[4]= new DownLoad_ElementVaule(4, fTargetProps.getFdiffdownload4_type(), 						
				fTargetProps.getFdiffdownload4_address(), 
				fTargetProps.getFdiffdownload4_length(),
				fTargetProps.isFdiffdownload4_enable());
		// 输入hex，解析为memory
		try {
			// ------------
			InputStream is = file.getContents();
			HexFileParser parser = new HexFileParser(is, configElement, sector);
			parser.parsediff();
			memory = parser.getMemory();
			// ------------
			try {
				is.close();
			} catch (IOException e1) {
				e1.printStackTrace();
			}
			// 将芯片信息提供者传入memory类
			memory.setConfigurationProvider(configElement);
			// 未提前获取设备下的自动关联
			if(DeviceManager.portName==null || DeviceManager.portName=="") //$NON-NLS-1$
				new ResetDriver().ResetDriverFun();
			// 启动编程信息提示
			WriteMessage.getDefault().writeBLUEMessage(Messages.ChiponLaunchConfigurationDelegate_42 + project.getName());

			// 下载程序执行
			OperationFactory.DownAimDebug=true;
			DownHexToChip.Upload(project, memory, false);

		} catch (CoreException e) {
			e.printStackTrace();
			return false;
		}
		
		if (DownHexToChip.readMap.get("OK") != null && DownHexToChip.readMap.get("OK").get(0).equals("OK")) {
			isUploadSuccess = true;
		} else {
			isUploadSuccess = false;
		}

		return isUploadSuccess;
	}
	
	
	// windows mingw can be 256,cyginw and linux can be 64 by send 63
	private boolean CheckPortNum(String str,int maxLimit)
	{
		String input=str;
		while(input!=null && (input.charAt(0)<'0' || input.charAt(0)>'9')  )
		{
			input=input.substring(1);
		}
		if(input==null)return false;
		try{
			if(Integer.parseInt(input)<=maxLimit)
			{
				return true;
			}
		}
		catch(Exception e)
		{
			
		}
		return false;	
	}
}
