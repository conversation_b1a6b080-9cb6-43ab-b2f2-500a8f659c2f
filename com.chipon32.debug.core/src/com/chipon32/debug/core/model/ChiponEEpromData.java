package com.chipon32.debug.core.model;

import org.eclipse.swt.graphics.Color;

/**
 * <AUTHOR>
 * @since 2014-11-27 
 */
public class ChiponEEpromData implements Cloneable{
	
	private Color color;

	private String value;
	
	private String address;
	
	private String rowNum;
	
	
	public ChiponEEpromData(String address,String value) {
		super();
		this.value = value;
		this.address = address;
	}
	
	/**
	 * @return ллхпои
	 */
	public String getRomNum(){
		if(address != null && !"".equals(address.trim())){
			String addr = address.trim();
			int length = addr.length();
			StringBuffer strAddr = new StringBuffer();
			for(int i = 0; i < 4-length; i++){	
				strAddr.append("0");
			}
			if(length==1){
				strAddr.append("0");
			}else if(length >=2){
				strAddr.append(addr.substring(0, length-1)+"0");
			}else{
				return null;
			}
			return strAddr.toString();
		}
		return "";
	}
	
	public String getRowNum() {
		return rowNum;
	}

	public void setRowNum(String rowNum) {
		this.rowNum = rowNum;
	}
	
	
	/**
	 * @return ┴лхпои
	 */
	public String getColumnNum(){
		if(address != null && !"".equals(address.trim())){
			return String.valueOf(address.trim().charAt(address.length()-1));
		}
		return null;
	}
	
//	public String getRigisterAddress(){
//		if(address != null){
//			String addr = address.trim().substring(2);
//			if(addr.length() == 1){
//				return "0"+addr+"H";
//			}else if(addr.length() == 2){
//				return addr+"H";
//			}else if(addr.length() == 3){
//				return addr+"H";
//			}else{
//				return null;
//			}
//		}
//		return null;
//	}
//	
	

	@Override
	protected ChiponEEpromData clone() throws CloneNotSupportedException {
		ChiponEEpromData data= (ChiponEEpromData)super.clone();
		data.setAddress(address);
		data.setValue(value);
		return data;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((address == null) ? 0 : address.hashCode());
		result = prime * result + ((value == null) ? 0 : value.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ChiponEEpromData other = (ChiponEEpromData) obj;
		if (address == null) {
			if (other.address != null)
				return false;
		} else if (!address.equals(other.address))
			return false;
		if (value == null) {
			if (other.value != null)
				return false;
		} else if (!value.equals(other.value))
			return false;
		return true;
	}



	public String getValue() {
//		if(value != null){
//			String val = value.trim().substring(2);
//			if(val.length() == 1){
//				return "0"+val;
//			}else{
//				return val;
//			}
//		}
		return value;
	}
	public String getOldValue() {
		return value;
	}



	public void setValue(String value) {
		this.value = value;
	}



	public String getAddress() {
		return address;
	}



	public void setAddress(String address) {
		this.address = address;
	}

	public Color getColor() {
		return color;
	}

	public void setColor(Color color) {
		this.color = color;
	}

	
	
	
}
