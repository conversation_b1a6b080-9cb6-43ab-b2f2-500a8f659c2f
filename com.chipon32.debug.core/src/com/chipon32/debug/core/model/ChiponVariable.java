package com.chipon32.debug.core.model;

import java.util.ArrayList;
import org.eclipse.debug.core.DebugEvent;
import org.eclipse.debug.core.DebugException;
import org.eclipse.debug.core.model.IStackFrame;
import org.eclipse.debug.core.model.IValue;
import org.eclipse.debug.core.model.IVariable;
import org.eclipse.jface.util.IPropertyChangeListener;

import com.chipon32.debug.core.protocol.ChiponSetVariableCommand;
import com.chipon32.debug.core.protocol.ChiponSetVariableCommandResult;


/**
 * ChiponVariable是对变量名的一个封装
 * <AUTHOR>
 */
public class ChiponVariable extends ChiponDebugElement implements IVariable{
//	private String fName;
//	private ChiponStackFrame fFrame;
	//private String fName;
	
	private boolean hasChanged = false;
	
	
	ArrayList<IPropertyChangeListener> myListeners = PropertyChangeViewPlugin.getInstant().myListeners;
	
	private ChiponVariableTree fVariableTree;     //表达式或者变量视图变量
	//private ChiponVariable[] fVariables = {new ChiponVariable(getChiponDebugTarget(),fVariableTree)};
	
	public ChiponVariable(ChiponDebugTarget target,ChiponVariableTree variable) {
		super(target);
		fVariableTree = variable;
		//将要修改的变量变成线程对象中的变量
		
		
		//这一块代码实际并没有起作用，因为变量是直接打印的，并没有获取其地址值，其地址值一直为null
		try {
			IStackFrame[] stackFrame= target.getCurrentThread().getStackFrames();
			if(stackFrame!=null && stackFrame.length>0){
				for (int i = 0; i < stackFrame.length; i++) {
					//目前实际上只有ChiponStackFrame这一个元素
					if(stackFrame[i]!=null && stackFrame[i].hasVariables()){
						IVariable[] variables= stackFrame[i].getVariables();
						if(variables!=null && variables.length>0){
							for (int j = 0; j < variables.length; j++) {
								if(variables[j] instanceof ChiponVariable){
									ChiponVariable variableChildren=(ChiponVariable)variables[j];
									if((variableChildren.fVariableTree)!=null && 
											(variableChildren.fVariableTree.getAddress() !=null )
											&&(variableChildren.fVariableTree.getAddress()).equals(variable.getAddress())){
										fVariableTree = variableChildren.fVariableTree;
									}
									
								}
							}
						}
					}
				}
			}				
		
//		fFrame = frame;
//		fName = name.getName();
		
		} catch (DebugException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}

	@Override
	public void setValue(String expression) throws DebugException {
		
		fVariableTree.setValue(expression);
		
		fireChangeEvent(DebugEvent.CONTENT);
		
	}

	@Override
	public void setValue(IValue value) throws DebugException {
		fireChangeEvent(DebugEvent.CONTENT);
	}

	/* (non-Javadoc)
	 * @see org.eclipse.debug.core.model.IValueModification#supportsValueModification()
	 * 是否支持修改变量的值
	 */
	@Override
	public boolean supportsValueModification() {
		if (fVariableTree.getValue() != null && !"".equals(fVariableTree.getValue()) && !fVariableTree.hasChidren() ){
			if(fVariableTree.getValue().contains("<"))  // gdb未分析到位的内容的输出
				return false;
			return true;
		}
		return false;
	}

	@Override
	public boolean verifyValue(String expression) throws DebugException {
//		int io=10;
//		String[] ss={"1","9","4"};
		
		//获得变量名
		String checkName = getCheckName();
		
		ChiponSetVariableCommandResult result = (ChiponSetVariableCommandResult) sendCommand(
				new ChiponSetVariableCommand(checkName, expression.trim()));
//		String returnText = result.resultText ;
//		返回：
//		address inpurt err!		地址不对
//		input err!			值不对
//		type input err!		类型不对
//		ok				正常
		if(result.resultText !=null && (result.resultText.trim()).equalsIgnoreCase("success")){
			boolean isValid = true;
			for(String resultout:result.resultout) {
				if(resultout.contains("Invalid number") || resultout.contains("error")) {
					isValid = false;
					break;
				}
			}
			if(isValid) {
				setValue(expression);
				//hasChanged = true;
				return true;
			}
		}
		
		return false;
	}
	

	@Override
	public boolean verifyValue(IValue value) throws DebugException {
		//System.out.println("verifyValue = value");
		return true;
	}

	@Override
	public IValue getValue() throws DebugException {
		//ChiponStackCommandResult result = (ChiponStackCommandResult) sendCommand(new ChiponStackCommand());
		return new ChiponValue(getChiponDebugTarget(), fVariableTree, this);
	}

	@Override
	public String getName() throws DebugException {
		if(fVariableTree.getName() == null){
			return "";
		}
		return fVariableTree.getName();
	}

	@Override
	public String getReferenceTypeName() throws DebugException {
		return fVariableTree.getType();
	}

	@Override
	public boolean hasValueChanged() throws DebugException {
		return hasChanged;
	}
	
	//根据名称修改变量，获取查询的变量名   x.a  x.a[]
	private String getCheckName(){
		StringBuffer checkName = new StringBuffer();
		ChiponVariableTree tree = fVariableTree;
		ChiponVariableTree parent = fVariableTree.getParent();
		
		//为数组时不需要添加父名
		while(parent != null){
			if(tree.getName().contains("[")){
				tree = parent;
				parent = tree.getParent();
			}else{
				checkName.insert(0, parent.getName()+".");
				tree = parent;
				parent = tree.getParent();
			}
		}
		checkName.append(fVariableTree.getName());		
		return checkName.toString();
	}

	//将监听添加到监听列表中
	public void addPropertyChangeListener(IPropertyChangeListener listener){
		if(!myListeners.contains(listener)){
			myListeners.add(listener);
		}
	}
	//将监听从监听列表中删除
	public void removePropertyChangeListener(IPropertyChangeListener listener){
		myListeners.remove(listener);
	}
	
//	//获得有数据的Thread模型数据List  更新内存
//	private List<ChiponRomData> getNewThreadList(List<ChiponRomData> list,ChiponVariableTree fVariableTree) {
//		List<ChiponRomData> newList =new ArrayList<ChiponRomData>();
//		
////		List valueList=getValueList(Integer.parseInt(fVariableTree.getValue()));
//		String newValue=(Integer.toHexString(Integer.parseInt(fVariableTree.getValue()))).toString();
//		if(newValue.length()%2!=0){
//			newValue="0"+newValue;
//		}
//		
//		for(int i=0;i<list.size();i++){
//			if(fVariableTree.getAddress().equals(list.get(i).getAddress())){
//				for(int j=0;j<newValue.length()/2;j++){
//					ChiponRomData oldRomData=list.get(i+j);
//					String address=oldRomData.getAddress();
//					String oldValue="0x"+newValue.substring(newValue.length()-2*j-2,newValue.length()-2*j);
//					ChiponRomData romData=new ChiponRomData(address, oldValue);
//					newList.add(romData);
//				}
//				i=i+(newValue.length()/2)-1;
//			}else{
//				ChiponRomData oldRomData=list.get(i);
//				String address=oldRomData.getAddress();
//				String oldValue=oldRomData.getOldValue();
//				ChiponRomData romData=new ChiponRomData(address, oldValue);
//				newList.add(romData);
//			}
//			
//		}
//		return newList;
//	}
	
	
	//返回对应的ChiponVariableTree的值
	public ChiponVariableTree getfVariableTree() {
		return fVariableTree;
	}
	
	
//	private List getValueList(int value) {
//		List listValue=new ArrayList(); 
//		if(value>256){
//			
//		}
//		int newValue=value/256;
//		int nextValue=value%256;
//		listValue.add(value);
//		while(nextValue>256){
//			
//		}
//		return nextValue;
//	}
}
