ChiponDebugTarget_22=Debug Target Error,Debug terminated.
ChiponDebugTarget_31=Currently Debug Port
ChiponDebugTarget_32=, Support Port Number Must Less Then 64
ChiponDebugTarget_61=Parse File Error,Try again
ChiponDebugTarget_62=Send Command :
ChiponDebugTarget_76=Debug Target Busy,Please Wait Or Force Pause...
ChiponDebugTarget_77=Set Break Fail
ChiponDebugTarget_78=Debug Target Busy,Please Wait Or Force Pause...
ChiponDebugTarget_79=Delete Break Error
ChiponDebugTarget_81=Debug Target Busy,Please Wait Or Force Pause...
ChiponDebugTarget_83=Run To Main But timeout,Check Option gstabs+,identical program ,ISP condition remove,debug Pin Free,Or other possibility
ChiponDebugTarget_84=Other possibility Like Watchdog,More Code Before Main,Or Beforehand Run Error.
ChiponDebugTarget_90=Debug Target Start-up Finish...
ChiponDebugTarget_94=Debug Target Start-up Failed,Please Check Program Or Other Limited Condition
ChiponDebugTarget_95=Set PC Error!


ChiponNullValue_0=Variable Parse Failed,Nonexistent Or Remove By Optimize

ChiponThread_16=Debug Target Pause By Break.
ChiponThread_17=Step Into End.
ChiponThread_25=Debug Target Reset End.
ChiponThread_29=Debug Target terminated.
ChiponThread_30=Step Over...
ChiponThread_39=Step Over End.
ChiponThread_40=Run......
ChiponThread_46=Debug Target Pause By Break.
ChiponThread_52=Launch Target Debug Pause.
ChiponThread_53=Step Finish...
ChiponThread_56=Currently Function Can't Run Over, Replaced by step-i
ChiponThread_62=Step Finish End.
ChiponThread_69=Debug Target Pause
ChiponThread_7=Step Into...


ChiponWatchpointData_0=Read
ChiponWatchpointData_1=Write
ChiponWatchpointData_14=Format Command Matched Fail...
ChiponWatchpointData_2=Read Or Write
ChiponWatchpointData_3=PC Break Point
ChiponWatchpointData_4=Address

ChiponWatchpointView_1=Watch Variable:
ChiponWatchpointView_2=Address_S_0x:
