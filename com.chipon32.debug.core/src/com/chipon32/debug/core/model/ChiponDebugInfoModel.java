package com.chipon32.debug.core.model;

public class ChiponDebugInfoModel {
	
	//debug file name
	private String debugFileName = null;
	
	//debug file line
	private String debugFileLine = null;
	
	//debug file main name
	private String debugFileMain = null;
	
	private boolean downloadSetting = false;
	
	private int autoStepInInterval = 100;
	
    private String  pcAddress = "";   
    
    private String instructionSetType = "";
	
	public String getDebugFileName() {
		return debugFileName;
	}

	public void setDebugFileName(String debugFileName) {
		this.debugFileName = debugFileName;
	}
	
	public String getDebugFileLine() {
		return debugFileLine;
	}

	public void setDebugFileLine(String debugFileLine) {
		this.debugFileLine = debugFileLine;
	}

	public int getAutoStepInInterval() {
		return autoStepInInterval;
	}

	public void setAutoStepInInterval(int autoStepInInterval) {
		this.autoStepInInterval = autoStepInInterval;
	}

	public boolean isDownloadSetting() {
		return downloadSetting;
	}

	public void setDownloadSetting(boolean downloadSetting) {
		this.downloadSetting = downloadSetting;
	}
	
    public String getPcAddress() {
        return pcAddress;
    }

    public void setPcAddress(String pcAddress) {
        this.pcAddress = pcAddress;
    }

	public String getDebugFileMain() {
		return debugFileMain;
	}

	public void setDebugFileMain(String debugFileMain) {
		this.debugFileMain = debugFileMain;
	}
		
	public String getInstructionSetType() {
		return instructionSetType;
	}

	public void setInstructionSetType(String instructionSetType) {
		this.instructionSetType = instructionSetType;
	}

}
