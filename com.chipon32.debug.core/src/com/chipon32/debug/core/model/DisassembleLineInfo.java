package com.chipon32.debug.core.model;

public class DisassembleLineInfo {
	//行号
	private int lineLocation;
	
	//偏移量
	private int caretOffset;
	
	//地址加上前面的空格总长度
	private int length;

	
	
	public DisassembleLineInfo(int lineLocation, int caretOffset, int length) {
		this.lineLocation = lineLocation;
		this.caretOffset = caretOffset;
		this.length = length;
	}


	public int getLineLocation() {
		return lineLocation;
	}
	public void setLineLocation(int lineLocation) {
		this.lineLocation = lineLocation;
	}
	public int getCaretOffset() {
		return caretOffset;
	}
	public void setCaretOffset(int caretOffset) {
		this.caretOffset = caretOffset;
	}


	public int getLength() {
		return length;
	}


	public void setLength(int length) {
		this.length = length;
	}
	
	

}
