package com.chipon32.debug.core.model.trace;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;

import org.eclipse.jface.util.IPropertyChangeListener;
import org.eclipse.jface.util.PropertyChangeEvent;

public class TraceTableManager implements IPropertyChangeListener{
	//private List<TraceTableItem> traceList = new ArrayList<>();
	private List<TraceTableItem> traceList = new CopyOnWriteArrayList<>();
	
	private List listeners = new ArrayList();
	private static TraceTableManager manager;
	
	public static TraceTableManager getManager(){
		if(manager == null) {
			manager = new TraceTableManager();
		}
		return manager;
	}
	
	public void addTrace(TraceTableItem[] tracetables) {
		traceList.clear();
		if(traceList.addAll(Arrays.asList(tracetables))){
			fireTraceChanged(tracetables,TraceTableItem.NONE, TraceTableItem.NONE);
		}
	}
	
	public void removeTrace(TraceTableItem[] tracetables) {
	    traceList.clear();
	    fireTraceChanged(TraceTableItem.NONE, TraceTableItem.NONE, tracetables);
		
	}
    public void modifyTrace(TraceTableItem[] tracetables) {
    	int itemcount = tracetables.length*30;
    	for(int i = 0; i < tracetables.length; i++) {   		
    		if(tracetables[i].getSampleAddress() != null) {
    			if(traceList.get(i).getSampleAddress() == tracetables[i].getSampleAddress()) {
    				if(traceList.size() > itemcount) {
    					for(int j = 0; j <tracetables.length; j++) {
    						traceList.remove(0);
    					}
    				}
    				traceList.add(new TraceTableItem(traceList.get(i).getSampleName(),traceList.get(i).getSampleAddress(),tracetables[i].getValueD(),tracetables[i].getValueH()));
    			}
    		}else {
    			if(traceList.get(i).getSampleName() == tracetables[i].getSampleName()) {
    				if(traceList.size() > itemcount) {
    					for(int j = 0; j <tracetables.length; j++) {
    						traceList.remove(0);
    					}
    				}
    				traceList.add(new TraceTableItem(traceList.get(i).getSampleName(),traceList.get(i).getSampleAddress(),tracetables[i].getValueD(),tracetables[i].getValueH()));
    			}
    		}	  
    	}
    	
    	fireTraceChanged(TraceTableItem.NONE, tracetables, TraceTableItem.NONE);
    }

	private void fireTraceChanged(TraceTableItem[] itemAdded,TraceTableItem[] itemModified,TraceTableItem[] itemRemoved) {
		TraceTableManagerEvent event = 
				new TraceTableManagerEvent(this,itemAdded,itemModified,itemRemoved);
		for(Iterator iter = listeners.iterator(); iter.hasNext(); ) {
			((TraceTableMangerListener)iter.next()).tracetableChanged(event);
		}
		
	}

	@Override
	public void propertyChange(PropertyChangeEvent event) {
		// TODO Auto-generated method stub
	}
	
	public void addTraceTableMangerListener(TraceTableMangerListener listener) {
		listeners.add(listener);
	}
	
	public void removeTraceTableMangerListener(TraceTableMangerListener listener) {
		listeners.remove(listener);
	}
	
	public TraceTableItem[] gettracetableList(){
		return (TraceTableItem[])traceList.toArray(new TraceTableItem[traceList.size()]);
	}

	
	
}
