package com.chipon32.debug.core.model.trace;

import java.util.EventObject;

public class TraceTableManagerEvent  extends EventObject{
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	private final TraceTableItem[] added;
	private final TraceTableItem[] modified;
	private final TraceTableItem[] removed;

	public TraceTableManagerEvent(TraceTableManager source,TraceTableItem[] itemAdded, TraceTableItem[] itemModified, TraceTableItem[] itemRemoved) {
		// TODO Auto-generated constructor stub
		super(source);
		added = itemAdded;
		modified = itemModified;
		removed = itemRemoved;
	}
	
	public TraceTableItem[] getItemAdded() {
		return added;
	}
	
	public TraceTableItem[] getItemModified() {
		return modified;
	}
	
	public TraceTableItem[] getItemRemoved() {
		return removed;
	}
}
