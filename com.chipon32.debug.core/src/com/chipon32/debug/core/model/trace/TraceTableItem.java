package com.chipon32.debug.core.model.trace;


/**
 * <AUTHOR>
 *
 */
public class TraceTableItem {
	
	private String sampleName;
	private String sampleAddress;
	
	/*static TraceTableItem[] NONE = new TraceTableItem[] {};
	public String getSampleName() {
		return sampleName;
	}
	public void setSampleName(String sampleName) {
		this.sampleName = sampleName;
	}
	public String getSampleAddress() {
		return sampleAddress;
	}
	public void setSampleAddress(String sampleAddress) {
		this.sampleAddress = sampleAddress;
	}
	
	public TraceTableItem(String simpleName) {
		setSampleName(simpleName);
	}
		
	public TraceTableItem(String simpleName,String simpleAddress) {
		setSampleName(simpleName);
		setSampleAddress(simpleAddress);
	}*/
	
	private String valueH;
	private Double valueD;
	static TraceTableItem[] NONE = new TraceTableItem[] {};
	
	public String getSampleName() {
		return sampleName;
	}
	public void setSampleName(String sampleName) {
		this.sampleName = sampleName;
	}
	public String getSampleAddress() {
		return sampleAddress;
	}
	public void setSampleAddress(String sampleAddress) {
		this.sampleAddress = sampleAddress;
	}
	public String getValueH() {
		return valueH;
	}
	public void setValueH(String valueH) {
		this.valueH = valueH;
	}
	public Double getValueD() {
		return valueD;
	}
	public void setValueD(Double valueD) {
		this.valueD = valueD;
	}
   
	/*public TraceTableItem(String simpleName) {
		setSampleName(simpleName);
	}
	
	public TraceTableItem(String simpleName,String simpleAddress) {
		setSampleName(simpleName);
		setSampleAddress(simpleAddress);
	}
	
	public TraceTableItem(String simpleName,String simpleAddress,Double valueH) {
		setSampleName(simpleName);
		setSampleAddress(simpleAddress);
		setValueH(valueH);
	}
	*/
	public TraceTableItem() {}
	public TraceTableItem(String simpleName,String simpleAddress,Double valueD,String valueH) {
		setSampleName(simpleName);
		setSampleAddress(simpleAddress);
		setValueD(valueD);
		setValueH(valueH);
	}
	
	

	
	
	
	
	

}
