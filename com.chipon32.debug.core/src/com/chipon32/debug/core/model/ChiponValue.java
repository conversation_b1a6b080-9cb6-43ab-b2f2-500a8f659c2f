package com.chipon32.debug.core.model;

import org.eclipse.debug.core.DebugException;
import org.eclipse.debug.core.model.IValue;
import org.eclipse.debug.core.model.IVariable;
/**
 * ChiponValue与ChiponVariable的对应关系为一对多。每个ChiponValue与ChiponVariableTree对应
 * <AUTHOR>
 */
public class ChiponValue extends ChiponDebugElement implements IValue {


	protected ChiponVariable fVariable ;
	protected ChiponVariableTree fVariableTree;
	/**
	 * 标记
	 */
	private String id;
	
	public ChiponValue(ChiponDebugTarget target,ChiponVariableTree variableTree,ChiponVariable variable) {
		super(target);
		fVariableTree = variableTree;
		fVariable = variable;
	}

	@Override
	public String getReferenceTypeName() throws DebugException {
		return fVariableTree.getType();
	}

	@Override
	public String getValueString() throws DebugException {
		if(fVariableTree.getValue() == null){
			return "";
		}
		return fVariableTree.getValue();
	}

	@Override
	public boolean isAllocated() throws DebugException {
		return false;
	}

	/**
	 * 遍历树，得到多个IVariable   复杂变量如结构体或数组获得子元素信息
	 */
	@Override
	public IVariable[] getVariables() throws DebugException {
		IVariable[] children = null;
		if(fVariableTree.hasChidren()){
			children = new IVariable[fVariableTree.getChildren().size()];
			for(int i=0;i<children.length;i++){
				children[i] = new ChiponVariable(getChiponDebugTarget(), fVariableTree.getChildren().get(i));
			}

		}
		return children;
	}
	
	
	public ChiponVariable getVariable(){
		return fVariable;
	}

	@Override
	public boolean hasVariables() throws DebugException {
		if(getVariables() != null){
			if (getVariables().length != 0) {
		        return true;
		    }
		}
		return false;
	}

	public ChiponVariableTree getfVariableTree() {
		return fVariableTree;
	}


	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}
	
	
	

}
