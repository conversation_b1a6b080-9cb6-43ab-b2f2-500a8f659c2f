package com.chipon32.debug.core.model;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class ChiponVariableTree {

	//自身Id
	private int id;
	
	//父Id
	private int pid;
	
	private List<ChiponVariableTree> children = new ArrayList<ChiponVariableTree>();
	
	//父Name，在修改变量的值时用到
	private ChiponVariableTree parent;
	
	
	/*
	 * 变量名
	 */
	private String name;
	/*
	 * 变量地址
	 */
	private String address;
	/*
	 * 变量类型
	 */
	private String type;
	/*
	 * 变量值
	 */
	private String value;
	
	
	public ChiponVariableTree getParent() {
		return parent;
	}

	public void setParent(ChiponVariableTree parent) {
		this.parent = parent;
	}

	public ChiponVariableTree(int id,int pid){
		this.id = id;
		this.pid = pid;
	}

	public int getId() {
		return id;
	}


	public int getPid() {
		return pid;
	}


	/*public List<VariableTree> getChildren() {
		return children;
	}
	
	public void setChildren(List<VariableTree> children) {
		this.children = children;
	}*/
	
	public void addChild(ChiponVariableTree child){
		children.add(child);
		child.setParent(this);
	}
	
	public List<ChiponVariableTree> getChildren() {
		return children;
	}

	public boolean hasChidren(){
		return children.size()>0;
	}
	
	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + id;
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ChiponVariableTree other = (ChiponVariableTree) obj;
		if (id != other.id)
			return false;
		return true;
	}

	public String getValue() {
		return value;
	}

	public void setValue(String value) {
		this.value = value;
	}

}
