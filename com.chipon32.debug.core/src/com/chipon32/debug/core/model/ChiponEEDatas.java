package com.chipon32.debug.core.model;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/***
 *<AUTHOR>
 *2013-6-20обнГ2:12:41
 ***/
public class ChiponEEDatas implements Serializable {
	
	private static final long serialVersionUID = 1L;
	
	private List<ChiponGroupEEData> groupEEDatas;

	public List<ChiponGroupEEData> getGroupEEDatas() {
		return groupEEDatas;
	}

	public ChiponEEDatas(){
		groupEEDatas = new ArrayList<ChiponGroupEEData>();
	}
	
	public void addGroupEEData(ChiponGroupEEData groupEEData){
		groupEEDatas.add(groupEEData);
	}
	
}
