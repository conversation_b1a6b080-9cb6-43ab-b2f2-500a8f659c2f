package com.chipon32.debug.core.model;

import org.eclipse.core.runtime.CoreException;
import org.eclipse.debug.core.DebugException;
import org.eclipse.debug.core.model.ILogicalStructureTypeDelegate;
import org.eclipse.debug.core.model.IValue;
/**
 * 定义变量视图上层的显示方式
 * <AUTHOR> @since 2013-6-15 上午9:46:21
 */
public class ChiponWordsLogicalDelegate implements ILogicalStructureTypeDelegate {


	@Override
	public boolean providesLogicalStructure(IValue value) {
		try {
			if(value == null) {
				return false;
			}
			
			String string = value.getValueString();
			String[] words = string.split("\\W+");//\\W+:一个或多个非单词字符
			for(String stringg : words){
				System.err.print("stringg = "+stringg +", ");
			}
			System.out.println();
			
			return words.length > 1;
		} catch (DebugException e) {
		}
		return false;
	}

	@Override
	public IValue getLogicalStructure(IValue value) throws CoreException {
		return new ChiponArray((ChiponValue)value);
	}

}
