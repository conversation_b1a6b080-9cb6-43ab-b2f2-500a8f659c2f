package com.chipon32.debug.core.model;

import org.eclipse.debug.core.ILaunch;
import org.eclipse.debug.core.model.IDebugTarget;
import org.eclipse.debug.core.model.IExpression;
import org.eclipse.debug.core.model.IValue;

public class ChiponExpression implements IExpression {

	@Override
	public String getModelIdentifier() {
		return null;
	}

	@Override
	public ILaunch getLaunch() {
		// TODO Auto-generated method stub
		return null;
	}

	@SuppressWarnings("rawtypes")
	@Override
	public Object getAdapter(Class adapter) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public String getExpressionText() {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public IValue getValue() {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public IDebugTarget getDebugTarget() {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public void dispose() {
		// TODO Auto-generated method stub

	}

	/*
	 * @Override public boolean isCanSetInputExpression1() { // TODO Auto-generated
	 * method stub return false; }
	 * 
	 * @Override public boolean isCanSetInputExpression2() { // TODO Auto-generated
	 * method stub return false; }
	 * 
	 * @Override public boolean isCanSetInputExpression3() { // TODO Auto-generated
	 * method stub return false; }
	 * 
	 * @Override public void setCanSetInputExpression1(boolean istrue) { // TODO
	 * Auto-generated method stub
	 * 
	 * }
	 * 
	 * @Override public void setCanSetInputExpression2(boolean istrue) { // TODO
	 * Auto-generated method stub
	 * 
	 * }
	 * 
	 * @Override public void setCanSetInputExpression3(boolean istrue) { // TODO
	 * Auto-generated method stub
	 * 
	 * }
	 */

}
