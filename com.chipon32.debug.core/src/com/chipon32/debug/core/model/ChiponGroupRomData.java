package com.chipon32.debug.core.model;

import java.util.ArrayList;
import java.util.List;

import com.chipon32.hex.core.util.ByteConvertor;

/**
 * MemoryView相关的Model
 * <AUTHOR> @since 2013-6-27 下午5:41:27
 */
public class ChiponGroupRomData {
	//~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
	private List<ChiponRomData> romDatas;
	
	private String rowNum;
	//~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
	private boolean is_virtual_lineGroup;
	
	public boolean isIs_virtual_lineGroup() {
		return is_virtual_lineGroup;
	}

	public void setIs_virtual_lineGroup(boolean is_virtual_lineGroup) {
		this.is_virtual_lineGroup = is_virtual_lineGroup;
	}
	//~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
	public ChiponGroupRomData() {
		romDatas = new ArrayList<ChiponRomData>(32);
		
		is_virtual_lineGroup=false;
	}

	public List<ChiponRomData> getRomDatas() {
		return romDatas;
	}
	
	public void addRomData(ChiponRomData romData){
		romDatas.add(romData);
	}                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   
	//~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
	/**
	 * 计算一行的Ascii值
	 * @return
	 */
	public String getAscii(){
		StringBuffer buffer = new StringBuffer();
		for(ChiponRomData romData : romDatas){
			if(romData.getValue().trim().length() == 0 || romData.getValue().trim().equals("--")){
				continue;
			}
			byte stringByte = ByteConvertor.hexToByte(romData.getValue());
			byte[] array = new byte[] { stringByte};
			for(byte b:array){
				int i = byteToint(b);
				if(i < '!' || i > '~'){
					buffer.append(".");
				}else{
					buffer.append((char) i);
				}
			}
		}
		return buffer.toString();
	}
	
	
	//~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
	private int byteToint(byte b){
		int i;
		if(b < 0){
			i = 256 + b;
		}else{
			i = b;
		}
		return i;
	}

	public String getRowNum() {
		return rowNum;
	}

	public void setRowNum(String columnNum) {
		this.rowNum = columnNum;
	}

	//~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
}
