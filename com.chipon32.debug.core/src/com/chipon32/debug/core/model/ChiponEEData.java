package com.chipon32.debug.core.model;

import org.eclipse.swt.graphics.Color;

/***
 *<AUTHOR>
 *2013-6-20下午1:09:10
 ***/
public class ChiponEEData implements Cloneable {
	private Color color;
	
	private String value;
	
	private String address;
	
	
	public ChiponEEData(String address,String value) {
		super();
		this.value = value;
		this.address = address;
	}

	/**
	 * @return 行地址
	 */
	public String getRomNum(){
		if(address != null && !"".equals(address.trim())){
			return String.valueOf(address.trim().charAt(address.length()-1));
		}
		return null;
	}
	/**
	 * @return 列地址
	 */
	public String getColumnNum(){
		if(address != null && !"".equals(address.trim()) && address.length() > 2){
			String addr = address.trim().substring(0,2);
			if(addr.length() == 1){
				return "000";
			}else if(addr.length() == 2){
			//	return "0"+addr.substring(0, 1)+"0";
				return addr;
			}else if(addr.length() == 3){
				return addr.substring(0, 2)+"0";
			}else{
				return null;
			}
		}
		return null;
	}
	/*
	public String getEEAddress(){
		if(address != null){
			String addr = address.trim().substring(2);
			if(addr.length() == 1){
				return "0"+addr+"H";
			}else if(addr.length() == 2){
				return addr+"H";
			}else if(addr.length() == 3){
				return addr+"H";
			}else{
				return null;
			}
		}
		return null;
	}
	
*/

	@Override
	protected ChiponEEData clone() throws CloneNotSupportedException {
		ChiponEEData data= (ChiponEEData)super.clone();
		data.setAddress(address);
		data.setValue(value);
		return data;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((address == null) ? 0 : address.hashCode());
		result = prime * result + ((value == null) ? 0 : value.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ChiponEEData other = (ChiponEEData) obj;
		if (address == null) {
			if (other.address != null)
				return false;
		} else if (!address.equals(other.address))
			return false;
		if (value == null) {
			if (other.value != null)
				return false;
		} else if (!value.equals(other.value))
			return false;
		return true;
	}


	public String getValue() {
//		if(value != null){
//			String val = value.trim().substring(2);
//			if(val.length() == 1){
//				return "0"+val;
//			}else{
//				return val;
//			}
//		}
		return value;
	}


	public void setValue(String value) {
		this.value = value;
	}


	public String getAddress() {
		return address;
	}


	public void setAddress(String address) {
		this.address = address;
	}

	public Color getColor() {
		return color;
	}

	public void setColor(Color color) {
		this.color = color;
	}
	
}
