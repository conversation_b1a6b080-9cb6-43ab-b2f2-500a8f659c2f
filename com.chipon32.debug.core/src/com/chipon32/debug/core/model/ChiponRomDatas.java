package com.chipon32.debug.core.model;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> @since 2013-6-4 ионГ11:08:32
 */
public class ChiponRomDatas implements Serializable{
	
	private static final long serialVersionUID = 1L;
	
	private List<ChiponGroupRomData> groupOnelineDatas;

	public List<ChiponGroupRomData> getGroupRomDatas() {
		return groupOnelineDatas;
	}

	public ChiponRomDatas(){
		groupOnelineDatas = new ArrayList<ChiponGroupRomData>();
	}
	
	public void addGroupRomData(ChiponGroupRomData groupRomData){
		groupOnelineDatas.add(groupRomData);
	}
	
}
