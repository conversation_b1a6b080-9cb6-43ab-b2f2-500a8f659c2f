package com.chipon32.debug.core.model;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.PipedReader;
import java.io.PipedWriter;
import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.commons.lang3.StringUtils;
import org.eclipse.cdt.managedbuilder.core.IConfiguration;
import org.eclipse.cdt.managedbuilder.core.IManagedBuildInfo;
import org.eclipse.cdt.managedbuilder.core.IManagedProject;
import org.eclipse.cdt.managedbuilder.core.ManagedBuildManager;
import org.eclipse.cdt.managedbuilder.macros.BuildMacroException;
import org.eclipse.cdt.managedbuilder.macros.IBuildMacroProvider;
import org.eclipse.core.resources.IFile;
import org.eclipse.core.resources.IMarker;
import org.eclipse.core.resources.IMarkerDelta;
import org.eclipse.core.resources.IProject;
import org.eclipse.core.resources.IResource;
import org.eclipse.core.runtime.CoreException;
import org.eclipse.core.runtime.FileLocator;
import org.eclipse.core.runtime.IProgressMonitor;
import org.eclipse.core.runtime.IStatus;
import org.eclipse.core.runtime.Platform;
import org.eclipse.core.runtime.Status;
import org.eclipse.core.runtime.jobs.Job;
import org.eclipse.debug.core.DebugEvent;
import org.eclipse.debug.core.DebugException;
import org.eclipse.debug.core.DebugPlugin;
import org.eclipse.debug.core.IBreakpointManager;
import org.eclipse.debug.core.IBreakpointManagerListener;
import org.eclipse.debug.core.ILaunch;
import org.eclipse.debug.core.model.IBreakpoint;
import org.eclipse.debug.core.model.IDebugTarget;
import org.eclipse.debug.core.model.IMemoryBlock;
import org.eclipse.debug.core.model.IProcess;
import org.eclipse.debug.core.model.IThread;

import com.chipon32.chiponide.core.paths.ChipOnPath;
import com.chipon32.chiponide.core.paths.SystemPathHelper;
import com.chipon32.chiponide.core.properties.ChipOnProjectProperties;
import com.chipon32.chiponide.core.properties.ProjectPropertyManager;
import com.chipon32.chiponide.core.utils.ChipNameManager;
import com.chipon32.chiponide.log.util.ChiponLogUtil;
import com.chipon32.chiponide.templates.core.TemplatesActivator;
import com.chipon32.configbit.ui.configProvider.IConfigBase;
import com.chipon32.debug.core.DebugCoreActivator;
import com.chipon32.debug.core.breakpoint.ChiponAddrBreakpoint;
import com.chipon32.debug.core.breakpoint.ChiponBreakPoint;
import com.chipon32.debug.core.breakpoint.ChiponLineBreakpoint;
import com.chipon32.debug.core.protocol.ChipOnClearBreakpointCommand;
import com.chipon32.debug.core.protocol.ChiponBreakpointCommand;
import com.chipon32.debug.core.protocol.ChiponBreakpointCommandResult;
import com.chipon32.debug.core.protocol.ChiponCommand;
import com.chipon32.debug.core.protocol.ChiponCommandResult;
import com.chipon32.debug.core.protocol.ChiponConnectCommand;
import com.chipon32.debug.core.protocol.ChiponCustomCommand;
import com.chipon32.debug.core.protocol.ChiponDebuggerRunEvent;
import com.chipon32.debug.core.protocol.ChiponDebuggerStartedEvent;
import com.chipon32.debug.core.protocol.ChiponDebuggerSuspendedEvent;
import com.chipon32.debug.core.protocol.ChiponEvent;
import com.chipon32.debug.core.protocol.ChiponGdbIDCommand;
import com.chipon32.debug.core.protocol.ChiponGdbIDCommandResult;
import com.chipon32.debug.core.protocol.ChiponMulCoreCommand;
import com.chipon32.debug.core.protocol.ChiponRunCommand;
import com.chipon32.debug.core.protocol.ChiponSoftStartCommand;
import com.chipon32.debug.core.protocol.ChiponStopLocationCommandResult;
import com.chipon32.debug.core.protocol.ChiponTerminatedEvent;
import com.chipon32.debug.core.util.ChiponDebugUtil;
import com.chipon32.util.communicate.DeviceManager;
import com.chipon32.util.communicate.HandleError;
import com.chipon32.util.communicate.WriteMessage;
import com.chipon32.util.provider.MyToolConfig;

/**
 * <AUTHOR>
 * 在我们的调试模型里，ChiponDebugTarget是大部分动作发生的地方，因为调试目标是我们与debbugger集中通信的地方
 * Job分发
 * 调试模式：硬件，软件
 * 构建参数：加载器、进程、项目
 * 启动、终止、复位、暂停、发命令返回收命令
 */
public class ChiponDebugTarget extends ChiponDebugElement implements
		IDebugTarget, IBreakpointManagerListener ,IChiponEventListener{
	
	public static boolean isDebugRevBusy = false;
	public static boolean isDebugging = false; //是否处于调试状态标识
	public boolean setSendTimeOut = false;
	private ChiponMemoryBlock fMemoryBlock;
	private boolean fTerminated = false;
	private String gdbId = null;
	//public  boolean  isConsolePrintDebugMessage = true; //是否打印信息标识
	private static List<ChiponDebugCommandObserver> obsList = new ArrayList<>();
	private int cpuNumber = 1;
	
	private ChiponDebugInfoModel chiponDebugInfoModel;

	// 构建时传递值
	private String ChipVer = "kf32r";  //$NON-NLS-1$
	private int  ChipRangeCode=0;
	
	public void setChipRange(int code){
		ChipRangeCode=code;
	}
	
	public int getChipRangeCode() {
		return ChipRangeCode;
	}
	
	private PrintWriter fRequestWriter;
	private BufferedReader fRequestReader;
	public static IProject fProject;
	private boolean isLog = true;  //过滤gdb返回的多余无用的信息

	private List<ChiponThread>  fThreads = new ArrayList<>();  //**
	private int currentThreadIndex = 0;
	
	public ChiponThread getCurrentThread() {
		if(fThreads.size() == 0) {
			return null;
		}
		return fThreads.get(currentThreadIndex);
	}
	
	public ChiponThread getThread(String uuid) {
		if(fThreads.size() == 0) {
			return null;
		}
		
		for (int i = 0; i < fThreads.size(); i++) {
			if (uuid.equals(((ChiponThread) fThreads.get(i)).getUuid())) {
				return fThreads.get(i);
			}
		}
		
		return null;
	}
	
	public int getThreadIndex(String uuid) {
		if(fThreads.size() == 0) {
			return 0;
		}
		
		for (int i = 0; i < fThreads.size(); i++) {
			if (uuid.equals(((ChiponThread) fThreads.get(i)).getUuid())) {
				return i;
			}
		}
		
		return 0;
	}
	
	public void setCurrentThreadIndex(int index) {
		this.currentThreadIndex = index;
	}
	
	private ILaunch fLaunch;
	private IProcess fProcess;
	
	private Process proc;   //runtime的执行进程

	public Process getProc() {
		return proc;
	}
//	public  List<String> messages;
	public static List<String> traceDataList = new CopyOnWriteArrayList<String>();
	
	//内部命令传递
	private PipedReader fEventReader;  //传送的字符输入流
	private PipedWriter out;           //传送的字符输出流
	
	/** 调试方式  0位硬件调试，1为软件调试 */
	private int debugMode;
	private int debugRunMode;
	
	public int getDebugRunMode() {
		return debugRunMode;
	}
	
	private EventDispatchJob fEventDispatch;
	private static List<IChiponEventListener> fEventListeners = Collections.synchronizedList(new ArrayList<IChiponEventListener>());
	
	/**
	 * 事务实现
	 * <AUTHOR>
	 *
	 */
	class EventDispatchJob extends Job {
		// 基本构架
		public EventDispatchJob() {
			super("ChipON Event Dispatch"); //$NON-NLS-1$
			setSystem(true);
		}
		
		// 运行类, 传递进度条对象
		@Override
		protected  IStatus run(IProgressMonitor monitor) {
			String message = ""; //$NON-NLS-1$
			Lock lock = new ReentrantLock(); //互斥锁
			while(!isTerminated() && message != null)
			{
				lock.lock();  //上锁
				try {
					if(fEventReader==null) {
						return Status.OK_STATUS;
					}
					//指令通过管道内传过来的指令判断是说明指令
					char a = NONE;
					try {
						a = (char) fEventReader.read();  //读取传送的字符输入流中的字符，未读到字符则阻塞在处
					} catch (IOException e1) {
//						 DebugCoreActivator.getDefault().getLog().log(
//						            new Status (IStatus.ERROR, "com.chipon32.debug.core", "Error parsing Chipon event", e1));
						        continue;
					}
					switch (a) {
					//调试开始
					case 'a':
						message = "debuggerStarted"; //$NON-NLS-1$
						break;
					case 'b':	//断点暂停
						message = "suspended$breakpoint"; //$NON-NLS-1$
						break;
					case 's'://单步跳入	
						message = "run$stepinto"; //$NON-NLS-1$
						break;
					case 'd':		//单步跳入暂停
						message = "suspended$stepinto"; //$NON-NLS-1$
						break;
					case 'h'://手动暂停	
						message = "suspended$client"; //$NON-NLS-1$
						break;
					case 'n'://单步跳过暂停
						message = "suspended$stepover"; //$NON-NLS-1$
						break;
					case 'o':	//单步跳过执行
						message = "run$stepover"; //$NON-NLS-1$
						break;
					case 'u'://单步返回
						message = "run$stepout"; //$NON-NLS-1$
						break;
					case 'f':	//单步返回暂停
						message = "suspended$stepout"; //$NON-NLS-1$
						break;
					case 'r':	//运行操作
						message = "run$client"; //$NON-NLS-1$
						break;
					case 'q':	//停止操作
						message = "stoped$client"; //$NON-NLS-1$
						break;
					case 'g':	//重置操作
						message = "suspended$reset"; //$NON-NLS-1$ 
						{
							resetBreakpoints();
						}
						break;
					case 'm':	//手动刷新其他视图
						message = "manual$manual"; //$NON-NLS-1$
						break;
						
					default:
						message = "";
						break;
					}//end switch
					
					if(message != null && !message.isEmpty()){
						System.err.println("message  = " + message); //$NON-NLS-1$
						//-------------- 单步
						if(message.trim().startsWith("suspended$stepinto")) //$NON-NLS-1$
						{
							getCurrentThread().setCanRunStepData(true);
						}
						// ------------- 暂停
						if(message.trim().startsWith("suspended$client")) //$NON-NLS-1$
						{
							//结束连续单步执行状态
							getCurrentThread().setCanRunStepSuspendData(false);
							getCurrentThread().setCanRunStepData(false);				
						}
						//--------------- 无参的信息返回，如单步和暂停，返回状态OK
						ChiponEvent event = null;
						try{
							message = message.trim();
							if("".equalsIgnoreCase(message) || message == null){ //$NON-NLS-1$
								return Status.OK_STATUS;
							}
							event = ChiponEvent.parseEvent(message);
						}catch (IllegalArgumentException e) {
					        DebugCoreActivator.getDefault().getLog().log(
						            new Status (IStatus.ERROR, "com.chipon32.debug.core", "Error parsing Chipon event", e)); //$NON-NLS-1$ //$NON-NLS-2$
						        continue;
						}						
						//-------------- 其他事务的响应-------------------------------
						Object[] listeners = fEventListeners.toArray();
						for (int i = 0; i < listeners.length; i++) {//循环执行事件
							((IChiponEventListener)listeners[i]).handleEvent(event);
						}						
					}
				} catch (Exception e) {
					e.printStackTrace();
					WriteMessage.getDefault().writeErrMessage(Messages.ChiponDebugTarget_22);
					debuggerTerminated();
				}finally{
					lock.unlock();
				}
			}
			// 信息处理完返回OK
			return Status.OK_STATUS;
		}
	}
	
	
	public int getDebugMode() {
		return debugMode;
	}
	public void setDebugMode(int debugMode) {
		this.debugMode = debugMode;
	}
	
	// 结束事务实现代码
	/**
	 * @param target 带目标的构建
	 */
	public ChiponDebugTarget(IDebugTarget target) {
		super(target);
	}
	
	// 原始多项参数的构建
	public ChiponDebugTarget(ILaunch launch, Process proc, IProject project, String chipver, int cpuNumber) throws CoreException, IOException, InterruptedException
	{
		super(null);
		
		if(chipver!=null)
			ChipVer=chipver;
		gdbId=null;
		fLaunch = launch;
		fProject = project;
		this.proc = proc;
		this.setCpuNumber(cpuNumber);
		
		out = new PipedWriter();  //创建一个尚未连接到reader的传送writer
		fEventReader = new PipedReader(out);    //创建连接到传送 writer out的 PipedReader
//		messages = new ArrayList<String>();
		//添加监听
		addEventListener(this);
		// give interpreter a chance to start
	//	Thread.sleep(1000);
		
		//  本地 读写对象实现
		fRequestWriter = new PrintWriter(proc.getOutputStream()); //输出流
		fRequestReader = new BufferedReader(new InputStreamReader(proc.getInputStream()));//输入流
		
		//新建运行job类，并启动；
		fEventDispatch = new EventDispatchJob();
		fEventDispatch.schedule();
		// 断点管理类
		IBreakpointManager breakpointManager = getBreakpointManager();
		breakpointManager.addBreakpointListener(this);
		breakpointManager.addBreakpointManagerListener(this);
		// 内存块
		fMemoryBlock = new ChiponMemoryBlock(this);
		clearThreads(cpuNumber);

		// 进程启动  **
		for (int i = 0; i < cpuNumber; i++) {
			ChiponThread fThread = new ChiponThread(this);
			fThread.setIsNeedLoadDisassembly(true);
			fThread.setUuid(UUID.randomUUID().toString());
			fThreads.add(fThread);
		}
		
		ChipOnProjectProperties copbuf= ProjectPropertyManager.getPropertyManager(fProject).getProjectProperties();
		debugMode = copbuf.getFdebugMode();  //0或1（硬件或软件）调试
		debugRunMode = copbuf.getFdebugRunMode();// 0 org 1 debugen 2 debugen &halt 3 debugen &hald &clrdog
		if(0 != debugMode)
			debugRunMode=0;
		//往管道输入中传入 调试开始 指令
		out.write('a');  //将a写入到管道输入流out中
//		messages.add("debuggerStarted"); //$NON-NLS-1$
		
	}
	
	//获取项目类别
	public String getProjectType(){
		IManagedBuildInfo bi = ManagedBuildManager.getBuildInfo(fProject);

		if (bi != null) 
		{			
			IManagedProject buildcfg = bi.getManagedProject();
			String type = buildcfg.getProjectType().getId();
			// 如果是汇编项目，创建main.asm文件
			if (type.equals(IConfigBase.CHIPON_ASM_ID)) {
				return "ASMProject"; //$NON-NLS-1$
			}
		}
		return "CProject"; //$NON-NLS-1$
	}
	
	//在打开gdb后,通过往gdb输入指令，建立连接，设置一些调试指令
	private boolean buildConnection() throws DebugException{
		if(fRequestWriter==null) {
			return false;
		}
		
		//关闭确认信息
		fRequestWriter.println("set confirm off"); //$NON-NLS-1$
		fRequestWriter.flush();
		ChiponLogUtil.printHistory("set confirm off");

		//信息全部输出，中间不暂停
		fRequestWriter.println("set pagination off"); //$NON-NLS-1$
		fRequestWriter.flush();
		ChiponLogUtil.printHistory("set pagination off");

		//关闭控制，不进入不带调试信息的函数
		fRequestWriter.println("set step-mode off"); //$NON-NLS-1$
		fRequestWriter.flush();
		ChiponLogUtil.printHistory("set step-mode off");

		//输出数组元素个数不限(GDB默认为200)  set print elements unlimited  不能无限制,char * need NULL ,if no init point ,resoured error
		fRequestWriter.println("set print elements 1024"); //$NON-NLS-1$
		fRequestWriter.flush();
		ChiponLogUtil.printHistory("set print elements 1024");

		//不输出repeat格式(GDB默认为10，重复超过10，则<repeats x times>)
		fRequestWriter.println("set print repeats unlimited"); //$NON-NLS-1$
		fRequestWriter.flush();
		ChiponLogUtil.printHistory("set print repeats unlimited");

		//-----------------------------------------------------------------------------------------
		//调试启动时传递参数获取
		String com = DeviceManager.portName;//串口号			
//		if(com!=null && com.length()>3 )
//		{
//			if(!CheckPortNum(com))
//			{
//				WriteMessage.getDefault().writeErrMessage(Messages.ChiponDebugTarget_31+com+Messages.ChiponDebugTarget_32);
//				debuggerTerminated();			
//				try {
//					//expcect to exit the gdb
//					fThread.KillGdbforExit();					
//				} catch (Exception e1) {	} 	
//				return false;
//			}
//		}
		ChipOnProjectProperties copbuf= ProjectPropertyManager.getPropertyManager(fProject).getProjectProperties();
		debugMode = copbuf.getFdebugMode();
		debugRunMode = copbuf.getFdebugRunMode();// 0 org 1 debugen 2 debugen &halt 3 debugen &hald &clrdog
		if(0 != debugMode) {
			debugRunMode=0;
		}
		
		String power = getChiponPower();		//获取电压
		int debugSpeed=Integer.parseInt(copbuf.getfDebugSpeed());
		String chipName = ChipNameManager.getChipName(fProject);
		String carrayLiboutEn = copbuf.getfDebugOutLib()+"";// (copbuf.getfDebugOutLib()==0)?"1":"0";  //默认出库，
		int iwdtEn = copbuf.getfOpenIWDT();
		int wwdtEn = copbuf.getfOpenWWDT();
		int ewdtEn = copbuf.getfOpenEWDT();
		String featureBin = "000000000"+ewdtEn+wwdtEn+iwdtEn;
		if(MyToolConfig.authenticationFalg) {
			featureBin += "0001";
		}else {
			featureBin += "1111";
		}
		String feature = String.valueOf(Integer.parseInt(featureBin, 2));//十进制数
		String driver = "0";
		String authkey = "00000000000000000000000000000000";
		int rangeCode = ChipRangeCode;
		if(ChipRangeCode<=4) {
			driver = "0";
			authkey = copbuf.getfDebugKey();
		}else {//13K
			power = "3.3";
			debugSpeed = 1;
			rangeCode = 4;
			ChipVer = getChiponDebugInfoModel().getInstructionSetType();
			carrayLiboutEn = "1";
			feature = "0x40";
			driver = "1";
			authkey = "0000000000000000000000000000000000000000000000000000000000000000";
		}
		//---------------------------------------------------------------------------------------------------------
//		String libpath =SystemPathHelper.getPath(ChipOnPath.CHIPON_SCRIPTPATHPATH,false, fProject).toOSString();
		String defFilePath = "";
		if (fProject.getFile(chipName + ".def").exists()) {
			defFilePath = fProject.getFile(chipName + ".def").getLocation().toOSString();
		} else {
			try {
				defFilePath = FileLocator.toFileURL(TemplatesActivator
						.getRelativeFilePathFromPlugin("templates/ldscripts", chipName + ".def", false)).getFile();
				defFilePath = defFilePath.substring(1);
				defFilePath = defFilePath.replace("/", "\\");
			} catch (IOException e1) {
				e1.printStackTrace();
			}
		}

		//########################################################################################
		if(0 == debugMode){		//硬件调试
			// 会存在调试器解析elf文件失败的情况，添加超时监控
			TimeOutThread top = new TimeOutThread(System.currentTimeMillis()+5*1000);
			top.start();
			if(Platform.getOS().equals(Platform.OS_WIN32))
			{
				//com = "\\\\.\\"+com;//"/dev/ttyS"+(Integer.parseInt(com.substring(3))-1)
				//com = "COM8"; // MinGW target remote COM1   isOK
				//com = "/dev/com8"; //Cygwin target remote /dev/com1
				com = "\\\\\\\\.\\\\"+com;
			}
			String DebugServerFile ;
			if(Platform.getOS().equals(Platform.OS_WIN32))
				DebugServerFile="--arch="+"\"" + defFilePath.replace("\\", "\\\\")+"\"" ;
			else
				DebugServerFile="--arch="+"\"" + defFilePath +"\"" ;
			// 发送命令 target kf32remote  
			//ChiponCommandResult connectResult = sendCommand(new ChiponConnectCommand(com, power, Integer.toString(debugSpeed), Integer.toHexString(ChipRangeCode) ,Integer.toHexString(debugRunMode) ,ChipVer, DebugServerFile));//old 
			ChiponCommandResult connectResult = sendCommand(new ChiponConnectCommand(com, power, Integer.toString(debugSpeed), Integer.toHexString(rangeCode) ,Integer.toHexString(debugRunMode) , 
					carrayLiboutEn, feature, driver, authkey,  ChipVer, DebugServerFile));   
			// 结果获取后暂停超时监控				
			if(top.isWatchTimeOut==true)
			{				
				connectResult.errorCode="0xA1"; //$NON-NLS-1$
				connectResult.resultText="failed";				 //$NON-NLS-1$
			}
			top.setStop=true;			

			
			if(!connectResult.resultText.equals("success")){ //$NON-NLS-1$
				HandleError.getDefault().handleMessage(connectResult.errorCode,connectResult.resultout);
				debuggerTerminated();
				
				/*try {//KillGdbforPause执行过了
					//expcect to exit the gdb
					fThread.KillGdbforExit();						
				} catch (Exception e1) {
					e1.printStackTrace();
				} */
				
	            requestFailed("Request failed: Debugger unconnection.  please retry", null); //$NON-NLS-1$
	            return false;
			}			
			
		}else{	//软件调试
			TimeOutThread top=new TimeOutThread(System.currentTimeMillis()
					+5*1000
					);
			top.start();
			//若目录有空格等用双引号括起来		
			String DebugServerFile ;
			if(Platform.getOS().equals(Platform.OS_WIN32))
				DebugServerFile="--arch="+"\"" + defFilePath.replace("\\", "\\\\")+"\"" ;
			else
				DebugServerFile="--arch="+"\"" + defFilePath + "\"" ;
			ChiponCommandResult connectResult = sendCommand(
					new ChiponCustomCommand("target kf32sim " +DebugServerFile +" --kfinstruct="+ChipVer+" --kfprint")); //$NON-NLS-1$ //$NON-NLS-2$ //$NON-NLS-3$ //$NON-NLS-4$ //$NON-NLS-5$ //$NON-NLS-6$ //$NON-NLS-7$ //$NON-NLS-8$ //$NON-NLS-9$
			
			// 结果获取后暂停超时监控				
			if(top.isWatchTimeOut==true)
			{				
				connectResult.errorCode="0xA1"; //$NON-NLS-1$
				connectResult.resultText="failed";				 //$NON-NLS-1$
			}
			top.setStop=true;			
			
			if(!connectResult.resultText.equals("success")){ //$NON-NLS-1$
				HandleError.getDefault().handleMessage(connectResult.errorCode,connectResult.resultout);
				debuggerTerminated();
				try {
					//expcect to exit the gdb
					getCurrentThread().KillGdbforExit();				
				} catch (Exception e1) {
					// TODO Auto-generated catch block
					e1.printStackTrace();
				} 
	            requestFailed("Request failed: Debugger unconnection.  please retry", null); //$NON-NLS-1$
	            return false;
			}	
		}
//############################################################################################################		
		//解析文件
		ChiponCommandResult loadResult = sendCommand(new ChiponCustomCommand("load")); //$NON-NLS-1$
		
		if(!loadResult.resultText.equals("success")){ //$NON-NLS-1$
			HandleError.getDefault().handleMessage(loadResult.errorCode,loadResult.resultout);
			debuggerTerminated();
            requestFailed(Messages.ChiponDebugTarget_61, null);
            return false;
		}	
		
		String pcAddress = getChiponDebugInfoModel().getPcAddress();
		if( pcAddress != null && pcAddress.length() == 10) {
			ChiponCommandResult setPCResult = sendCommand(new ChiponCustomCommand("set $pc=" + pcAddress)); //$NON-NLS-1$
			
			if(!setPCResult.resultText.equals("success")){ //$NON-NLS-1$
				debuggerTerminated();
	            requestFailed(Messages.ChiponDebugTarget_95, null);
	            return false;
			}	
		}

		//###########################################################################
		return true;
	}
	
	public PipedWriter getPipedWriter(){
		return out;
	}
	
	
	// 命令的发送，返回命令的接收
	@SuppressWarnings("rawtypes")
	private synchronized List sendRequest(String request) throws DebugException, IOException {
		//##############################################################################################
		if(fRequestReader==null || fRequestWriter==null) {
			return null;
		} 
		
		isDebugRevBusy=true;  // 因为同步进程，不能重入，这里不能判断忙碌了	
		setSendTimeOut=false;
		//##############################################################################################   
		 if(isLog){//打印启动调试时的日志信息
			try {
				String log = fRequestReader.readLine();
				ChiponLogUtil.printResponse(log);

				while(log != null){
					System.out.println(log);
					if(log.trim().contains("current process's pid:")){
						gdbId =log.substring(log.indexOf(":")+1).trim();
					}									
					log = fRequestReader.readLine();
					ChiponLogUtil.printResponse(log);
					if(log.trim().endsWith("done.")){ //$NON-NLS-1$
						System.out.println(log);
						isLog = false;
						break;
					}
				}
			} catch (IOException e) {
				requestFailed("Request failed!" , null); //$NON-NLS-1$
			}
		}
		//##############################################################################################
		boolean isRegisterDebug = false;
		if(request.startsWith("info register") || request.startsWith("x /w")) {
			isRegisterDebug = true;
		}
		
		boolean isWatchDebug = false;
		if(request.startsWith("print ")) {
			isWatchDebug = true;
		}
		 
		System.out.println(Messages.ChiponDebugTarget_62+request);   //打印发送命令
		if (isRegisterDebug) {
			ChiponLogUtil.printDebugRegisterHistory(request);
		} else if (isWatchDebug) {
			ChiponLogUtil.printDebugWatchHistory(request);
		} else {
			ChiponLogUtil.printHistory(request);
		}
		fRequestWriter.println(request);  // 目标启动无返回信息，追加自定义命令获取响应
		//刷新缓存，把流中的指令输入到命令行去执行，相当于回车键
		fRequestWriter.flush();
	    //System.out.println(Messages.ChiponDebugTarget_62+"kfcmd");   // 启动调试的平台信息显示
		fRequestWriter.println("kfcmd"); //$NON-NLS-1$
		ChiponLogUtil.printHistory("kfcmd");
		fRequestWriter.flush();
		//##############################################################################################
		boolean isHaveOK_Hidden=false;
		try {
			List<String> dataResult = new ArrayList<>();
			// wait for reply =>  fRequestReader.ready()
			String retVal = fRequestReader.readLine();		//会出现堵塞现象
			if (isRegisterDebug) {
				ChiponLogUtil.printDebugRegisterResponse(retVal);
			} else if (isWatchDebug) {
				ChiponLogUtil.printDebugWatchResponse(retVal);
			} else {
				ChiponLogUtil.printResponse(retVal);
			}
			
			if(retVal!=null && retVal.contains("operand done"))
			{
				isHaveOK_Hidden=true;
				System.out.println("response = "+retVal);
			}
			//##########################################################
			while (retVal != null && !retVal.contains("operand done"))  //$NON-NLS-1$  下位机休眠5ms返回一次数据
			{		

				//数字示波器的数据缓存
				if(retVal.startsWith("figure_oscillographdata:")|| retVal.startsWith("(gdb) figure_oscillographdata:")) //$NON-NLS-1$ //$NON-NLS-2$
				{	
					traceDataList.add(retVal);
//					System.out.println(":: "+retVal);
					if(traceDataList.size()>16) {  // 最多16个通道
						traceDataList.remove(0);						
					}								
				}
				else{
					System.out.println("response = "+retVal);  // 应答监控，用于bug调试, 不输出节省资源，问题时打开吧 //$NON-NLS-1$
					dataResult.add(retVal);
				}
				
				if(fRequestReader == null)
					break;
				
				retVal = fRequestReader.readLine();		//会出现堵塞现象
				if (isRegisterDebug) {
					ChiponLogUtil.printDebugRegisterResponse(retVal);
				} else if (isWatchDebug) {
					ChiponLogUtil.printDebugWatchResponse(retVal);
				} else {
					ChiponLogUtil.printResponse(retVal);
				}
				if(retVal != null && retVal.indexOf("operand done") != -1){ //$NON-NLS-1$
					System.out.println("response = "+retVal);
					break;
				}
				
				if(setSendTimeOut){
					//dataResult.add("Errorno:0xA0,Err In Debugger Process ,TimeOut"); //$NON-NLS-1$
					//break;
					System.out.println("response = "+"Err In Debugger Process, TimeOut\n");
				}
			}//end while
			//##########################################################
			if(dataResult==null||dataResult.size()<1){
				if(isHaveOK_Hidden==false)
				{
					dataResult.add("kf32command fail"); //$NON-NLS-1$
					dataResult.add("Errorno: 0xA0, Err In Debugger Process"); //$NON-NLS-1$
				}
				else
				{
					dataResult.add("kf32command success"); //$NON-NLS-1$
				}
			}
			isDebugRevBusy=false;
			
			//手动命令窗视图返回结果
			giveObserversResponse(dataResult); 
			
			return dataResult;
		} catch (IOException e) {
			isDebugRevBusy = false;
			requestFailed("Request failed: " + request, e); //$NON-NLS-1$
		}		
		// Should never reach this satement.
		isDebugRevBusy = false;
		
		return null;
	} 

	// 同步的命令需求
	@SuppressWarnings("unchecked")
	@Override
	public synchronized ChiponCommandResult sendCommand(ChiponCommand command) {
		synchronized (proc) 
		{
			try{
//				return command.createResult(sendRequest(command.getfRequest()));
				ChiponCommandResult result = command.createResult(sendRequest(command.getfRequest()));
				if(command.getfRequest().equals("kfreset") && ChipRangeCode == 5) {
					String setPcAddress = getChiponDebugInfoModel().getPcAddress();
					if( setPcAddress != null && setPcAddress.length() == 10) {
						if(result instanceof ChiponStopLocationCommandResult) {
							((ChiponStopLocationCommandResult)result).changeResult(setPcAddress);
							fRequestWriter.flush();
							reset();
						}
					}
				}

				notifyObservers(command, result); //手动命令窗显示调试结果信息
				return result;
			} 
			catch (DebugException e){
				e.printStackTrace();
			} catch (IOException e) {
				e.printStackTrace();
			}
			return null;
		}		
	}

	//控制关闭IDE时是否调用terminate()方法
	@Override
	public boolean canTerminate() {
		//单纯执行终止，应返回false，否则终止按键会被点亮
		if(isTerminated()){
			return false;
		}
		return true;
//		return getProcess().canTerminate();
	}

	@Override
	public boolean isTerminated() {
		return fTerminated ;
//		return fTerminated || getProcess().isTerminated();
	}
	
	private synchronized void setTerminated(boolean terminated) {
        fTerminated = terminated;
    }

	/**
	 * 终止调试时未进入该方法！！
	 */
	@Override
	public void terminate() throws DebugException {
		getCurrentThread().terminate();
		
		isDebugging = false;
		fTerminated=true;
		traceDataList.clear();
	}

	@Override
	public boolean canResume() {
//		return getThread().canResume();
		return !isTerminated() && isSuspended();
	}

	@Override
	public boolean canSuspend() {
		return !isTerminated() && !isSuspended();
	}

	@Override
	public boolean isSuspended() {
		if(getCurrentThread() != null){
			return getCurrentThread().isSuspended();
		}
		return false;
//		return !isTerminated() && fDebuggerSuspended;
	}

	@Override
	public void resume() throws DebugException {
		if(getCurrentThread() != null)
			getCurrentThread().resume();
	}

	@Override
	public void suspend() throws DebugException {
		if(getCurrentThread() != null)
			getCurrentThread().suspend();
	}
	
	/**
	 * <AUTHOR> 2017-4-14
	 * reset调试的状态
	 */
	public void reset(){
		//设置main函数的起始点
		String printNo = setMain(debugRunMode);
		
		getCurrentThread().setLocationCommandResult((ChiponStopLocationCommandResult) sendCommand(new ChiponRunCommand()));
		sendCommand(new ChipOnClearBreakpointCommand(printNo));
		installDeferredBreakpoints();
		
		try {
			out.write("g"); //$NON-NLS-1$
		} catch (IOException e) {
			
		}
	}
	
	public void resetBreakpoints() {
		sendCommand(new ChipOnClearBreakpointCommand(null)); //发送命令 delete 
		//同步存在的断点
		installDeferredBreakpoints();
		installWatchPoints();
	}
	
	/**
	 * <AUTHOR> 2017-4-3 调试开始时，断点必须全部加载
	 * @param breakpoint
	 */
	public void installBreakPoint(IBreakpoint breakpoint){
		try {
			//##############################################################
			// 断点未注册     或 使能断点下的断点管理器下的使能
			if ((breakpoint.isEnabled() && getBreakpointManager().isEnabled()) 	
					|| !breakpoint.isRegistered()
				) {
				//##############################################################//若地址断点的地址从反汇编视图中不存在，则直接删除	或 失能				
				if(breakpoint instanceof ChiponAddrBreakpoint){
					ChiponAddrBreakpoint addrBreakpoint = (ChiponAddrBreakpoint)breakpoint;
					// 获取反汇编提供的 地址 集合
					Set<String> addrSet = ChiponThread.locationData.keySet();  //***
					if(!addrSet.contains(addrBreakpoint.getAddress().toLowerCase())){
//						WriteMessage.getDefault().writeMessage(((ChiponAddrBreakpoint)breakpoint).getAddress()+"，移除该失效断点");
//						breakpoint.delete();
						breakpoint.setEnabled(false);
						return; 
					}					
				}
				//##############################################################// 断点
				ChiponBreakPoint chiponBreakpoint = (ChiponBreakPoint) breakpoint;
				//##############################################################//
				// 断点设置到芯片传输
				String result= chiponBreakpoint.install(this);
				if(result==null||!result.trim().equalsIgnoreCase("success")){ //$NON-NLS-1$
					// 行断点
					if(chiponBreakpoint instanceof ChiponLineBreakpoint){
//						WriteMessage.getDefault().writeMessage(breakpoint.getMarker().getResource().getName()+":"
//								+((LineBreakpoint) breakpoint).getLineNumber()+"，移除该失效断点");
//						breakpoint.delete();
						breakpoint.setEnabled(false);
					}
					//地址断点
					else if(chiponBreakpoint instanceof ChiponAddrBreakpoint){
//						WriteMessage.getDefault().writeErrMessage(((ChiponAddrBreakpoint)breakpoint).getAddress()+"，移除该失效断点");
//						breakpoint.delete();	
						breakpoint.setEnabled(false);
					}

				}
				
			}
			else  // give the target, and after can through change to enable
			{
				ChiponBreakPoint chiponBreakpoint = (ChiponBreakPoint) breakpoint;
				chiponBreakpoint.setDebugTarget(this);			
			}
			//##############################################################
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	@Override
	public void breakpointAdded(IBreakpoint breakpoint) {
		if(isDebugRevBusy)
		{
//			WriteMessage.getDefault().writeErrMessage(Messages.ChiponDebugTarget_76);	
//			return;
		}
		if (supportsBreakpoint(breakpoint)) {
			try {
				if ((breakpoint.isEnabled() && getBreakpointManager().isEnabled()) || !breakpoint.isRegistered()) {
//					((ChiponBreakPoint) breakpoint).install(this);				
					ChiponBreakPoint chiponBreakpoint = (ChiponBreakPoint) breakpoint;
					String result= chiponBreakpoint.install(this);
					if(!result.contains("success")) {
						System.out.println(Messages.ChiponDebugTarget_77);
						return;
					}
					
					out.write('b');
				}
			} catch (Exception e) {
				System.out.print(Messages.ChiponDebugTarget_77);
			} 
		}
		
	}

	@Override
	public void breakpointRemoved(IBreakpoint breakpoint, IMarkerDelta delta) {
		if(isDebugRevBusy)
		{
//			WriteMessage.getDefault().writeErrMessage(Messages.ChiponDebugTarget_78);
//			return;
		}	
		
		if (supportsBreakpoint(breakpoint)) {
			try {
				// 行断点
				if(breakpoint instanceof ChiponLineBreakpoint){
					ChiponLineBreakpoint chiponLineBreakpoint = (ChiponLineBreakpoint) breakpoint;
					chiponLineBreakpoint.remove(this);
				}
				// 地址断点
				else if(breakpoint instanceof ChiponAddrBreakpoint){
					ChiponAddrBreakpoint addrBreakpoint = (ChiponAddrBreakpoint)breakpoint;
					addrBreakpoint.remove(this);
				}
			} catch (Exception e) {
				System.out.print(Messages.ChiponDebugTarget_79);
			}
		}
	}

	@Override
	public void breakpointChanged(IBreakpoint breakpoint, IMarkerDelta delta) {
		
	}

	@Override
	public boolean canDisconnect() {
		return !isDisconnected();
	}

	@Override
	public void disconnect() throws DebugException {

	}

	@Override
	public boolean isDisconnected() {
		return isTerminated();
	}

	@Override
	public boolean supportsStorageRetrieval() {
		return false;
	}

	@Override
	public IDebugTarget getDebugTarget() {
		return this;
	}

	@Override
	public ILaunch getLaunch() {
		return fLaunch;
	}

	@Override
	public IMemoryBlock getMemoryBlock(long startAddress, long length)
			throws DebugException {
		return fMemoryBlock;
	}

	@Override
	public IProcess getProcess() {
		return this.fProcess;
	}

	@Override
	public String getName() throws DebugException {
		// TODO Auto-generated method stub
		return "ChipON"; //$NON-NLS-1$
	}

	@Override
	public boolean supportsBreakpoint(IBreakpoint breakpoint) {
		if (!isTerminated() && breakpoint.getModelIdentifier().equals(getModelIdentifier())) {
			try {
				String program = getLaunch().getLaunchConfiguration().getAttribute(DebugCoreActivator.ATTR_CHIP_PROGRAM,(String) null);
				if (program != null) {
					IResource resource = null;
					if (breakpoint instanceof ChiponLineBreakpoint) {	//行断点判断
						ChiponLineBreakpoint rtl = (ChiponLineBreakpoint) breakpoint;
						resource = rtl.getfSourceFile();
					} else {
						IMarker marker = breakpoint.getMarker();
						if (marker != null) {
							resource = marker.getResource();
						}
					}
					if (resource != null) {
//						IPath p = new Path(program);
//						return resource.getFullPath().equals(p);
						return true;
					}
				}
			} catch (CoreException e) {
				return false;
			}
		}
		return false;
	}

	@Override
	public void breakpointManagerEnablementChanged(boolean enabled) {
		if(isDebugRevBusy)
		{
//			WriteMessage.getDefault().writeErrMessage(Messages.ChiponDebugTarget_81);
//			return;
		}	
		
		IBreakpoint[] breakpoints = getBreakpointManager().getBreakpoints(
				getModelIdentifier());    //获取当前的断点列表
		for (int i = 0; i < breakpoints.length; i++) {
			if (enabled) {
				breakpointAdded(breakpoints[i]);
			} else {
				breakpointRemoved(breakpoints[i], null);
			}
		}
	}

	/**
	 * Returns the breakpoint manager
	 * 
	 * @return the breakpoint manager
	 */
	protected IBreakpointManager getBreakpointManager() {
		if(DebugPlugin.getDefault()!=null)
			return DebugPlugin.getDefault().getBreakpointManager();
		else 
			return null;
	}

	public static void removeEventListener(IChiponEventListener listener) {
		// TODO Auto-generated method stub
		fEventListeners.remove(listener);
	}
	
	/**
	 * Registers the given event listener. The listener will be notified of
	 * events in the program being interpretted. Has no effect if the listener
	 * is already registered.
	 *  
	 * @param listener event listener
	 */
	public static void addEventListener(IChiponEventListener listener) {
	    synchronized(fEventListeners) {
    		if (!fEventListeners.contains(listener)) {
    			fEventListeners.add(listener);
    		}
	    }
	}
	
	//获取GDB的PPID
	public String getGdbID(){
		return gdbId;
	}
	
	
	public class TimeOutThread extends Thread {

		long timeoutvalue;
		
		boolean  setStop;
		boolean  isWatchTimeOut;
		boolean  stopStartDebugProc = false;
		
		TimeOutThread(long timeout)
		{
			timeoutvalue=timeout;	
			setStop=false;
			isWatchTimeOut=false;
		}
		
		@Override
		public void run()
		{		
			while(!setStop)
			{
				try {
					Thread.sleep(100);  // 滞后执行
				} catch (InterruptedException e) {
					// TODO Auto-generated catch block					
				}
				if(System.currentTimeMillis()>timeoutvalue)
				{
					isWatchTimeOut=true;
						
					try {
						if (stopStartDebugProc) {
							// 暂停
							getCurrentThread().KillGdbforPause();
						} else {
							WriteMessage.getDefault().writeMessage(Messages.ChiponDebugTarget_83);
							WriteMessage.getDefault().writeMessage(Messages.ChiponDebugTarget_84);
							// 退出gdb
							getCurrentThread().KillGdbforExit();
						}
					} catch (Exception e) {
						// TODO Auto-generated catch block
						e.printStackTrace();
						setStop=true;
					}		

					return; 							
				}	
			}
//			WriteMessage.getDefault().writeMessage("正常退出。。。");
		}			
	}

	/***
	 * 启动调试器
	 * @throws DebugException
	 */
	private void debuggerStarted() throws DebugException {
//		sendCommand(new ChiponSetPowerCommand(getChiponPower()));    //设置电压
		isDebugging = true;
		fireCreationEvent();
		System.out.print("@@@debugRunMode:\t"+debugRunMode+" \r\n");
		// 提前获取gdbid不会答应done的主动结束，然后会读取死机，即信息不完整的逻辑异常
		// 实现调试器的启动和 配置调试器参数，并加装elf文件
		if(buildConnection()==false) {//发送gdb连接命令和load命令
			return;
		}
		
		//获取汇编文件信息，在设置地址断点时使用，这里固定选择debug下的lst文件
		getLocationData();
		
		if(Platform.getOS().equals(Platform.OS_WIN32))
		{
			// 调试器的基本信息，用来控制暂停等适用   shell task&list /FI "IMAGENAME eq kf32-gdb.exe"  /FO LIST     ::only windows linxu return error with pid null
//			ChiponGdbIDCommandResult gdbResult = (ChiponGdbIDCommandResult) sendCommand(new ChiponGdbIDCommand("shell task&list /FI \"IMAGENAME eq kf32-gdb.exe\"  /FO LIST"));//		ChiponGdbIDCommandResult gdbResult = (ChiponGdbIDCommandResult) sendCommand(new ChiponGdbIDCommand());
//			gdbId = gdbResult.ppId;    // no use，no admin run ,with dos need input password,but here can't change.............
			if(gdbId == null){
				try{
					//new String[]{"cmd", "/c",SystemPathHelper.getPath(ChipOnPath.CHIPONCC,false, fProject).toOSString()+File.separator+"task&list.exe"}	// name pid
					Process process = Runtime.getRuntime().exec(new String[]{SystemPathHelper.getPath(ChipOnPath.CHIPON_COMMON,false, fProject).toOSString()+File.separator+"ps.exe"});
	// 				if not admin task&list need input password but process no ui,use linux tool ps  to out cygwin same platmat   Not need param
	//			      PID    PPID    PGID     WINPID  TTY  UID    STIME COMMAND
	//			      168296       1  168296     168296 196608  400 19:33:09 /usr/bin/ps
					InputStream is = process.getInputStream();
					BufferedReader br = new BufferedReader(new InputStreamReader(is)); 
					String line = br.readLine();
					if(line!=null) line=line.trim();									
					while(line != null ){
						System.out.println(line);		
						if(line.contains("kf32-gdb"))
						{
							String[] message=line.split("\\s+");
							if(message!=null && message.length>1)
							{
								gdbId=message[0]; // task&list wiht 1 but ps with 0
							}
						}											
						try{
						line = br.readLine();
						if(line!=null) line=line.trim();				
						}catch (Exception e) {											
							line=null;
						}
					}
					br.close();
					is.close();
				}catch (Exception e) {
					e.printStackTrace();
				}
			}
		}
		else if(Platform.getOS().equals(Platform.OS_LINUX))
		{
			if(gdbId == null){
				ChiponGdbIDCommandResult gdbResult = (ChiponGdbIDCommandResult) sendCommand(new ChiponGdbIDCommand("shell ps -C kf32-gdb"));
				//ChiponGdbIDCommandResult gdbResult = (ChiponGdbIDCommandResult) sendCommand(new ChiponGdbIDCommand());
				gdbId = gdbResult.ppId;
			}
		}
		else
		{
			if(gdbId == null)
				gdbId=null;   // linux no have dir command to get,but ps with possable,
		}
		System.out.println("get gdbId = " + gdbId);
		//########################################################################################
// 		期望的关狗在buildConnection中失败，要先暂停，提前获取gdbid失败，放这里修改单个ram成功，后面就提示内核忙碌了，进入关狗的工作只能交给编程器驱动
// 		即暂停下写ram能控制最小时间差，这里的应用驱动下操作间隔时间较长，芯片错失修改机会。
		if(debugRunMode==0 ||  (debugRunMode==1) ){
			//设置main函数的起始点，返回使用的断点号  :break main
			String printNo = setMain(debugRunMode);
			//############################################################################################
			//软件调试的第一条执行指令为"r", 第二次执行"r"会reset
			if(1 == debugMode){	
				getCurrentThread().setLocationCommandResult((ChiponStopLocationCommandResult) sendCommand(new ChiponSoftStartCommand()));
			}else {	//硬件调试的执行指令全部为"c"
				// 启动一个超时监控，因为运行到main，会根据项目的elf打断点，如果下位机不工作，或代码不匹配，这里原来会死循环
				TimeOutThread top=new TimeOutThread(System.currentTimeMillis()
						+Integer.parseInt(ProjectPropertyManager.getPropertyManager(fProject).getProjectProperties().getfDebugMainTimeout())*1000);
				top.stopStartDebugProc = true;//
				top.start();
	
				// 发送运行，使运行到main   c
				long runstarttime=System.currentTimeMillis();
				if(cpuNumber > 1) {
					sendCommand(new ChiponMulCoreCommand(0, ChiponDebugUtil.calcuteUnite(cpuNumber)));
				}
				
				getCurrentThread().setLocationCommandResult((ChiponStopLocationCommandResult) sendCommand(new ChiponRunCommand()));

				if (cpuNumber > 1) {
					for (int i = cpuNumber - 1; i >= 0; i--) {
						sendCommand(new ChiponMulCoreCommand(i, 0));
						fThreads.get(i).stepiOver();
					}
				}
								
				// 结果获取后暂停超时监控				
				if(top.isWatchTimeOut) {//超时暂停在库里了
					//fileLocationCommandResult.errorCode="0xA1"; //$NON-NLS-1$
					//fileLocationCommandResult.resultText="failed";				 //$NON-NLS-1$
				}
				else
				{
					System.out.print("run2main time:"+(System.currentTimeMillis()-runstarttime)/1000 +"s\r\n"); //$NON-NLS-1$ //$NON-NLS-2$
				}
				top.setStop=true;	
			}
			//############################################################################################
			// 如果进入失败
			
			ChiponStopLocationCommandResult chiponStopLocationCommandResult = getCurrentThread().getLocationCommandResult();
			
			if(chiponStopLocationCommandResult.resultText.equals("failed")){ //$NON-NLS-1$
				// 主动显示识别信息内容
				//WriteMessage.getDefault().writeErrMessage(fileLocationCommandResult.);
				HandleError.getDefault().handleMessage(chiponStopLocationCommandResult.errorCode, chiponStopLocationCommandResult.resultout);
				terminate();
				return;
			}
			
			sendCommand(new ChipOnClearBreakpointCommand(printNo)); //发送命令 delete 1
		}
		// 复位调试运行到首个代码，或附加调试均需要单步一次来匹配工具实现的各种功能同步，其他2 3的暂停与清狗由调试器gdb驱动编程器接口完成，上位机操作实际间隔太长不能完成清狗与暂停，会间隔看门狗有效的复位
		else if( (debugRunMode==2)|| (debugRunMode==3))
		{
			getCurrentThread().stepInto();			
		}
		//############################################################################################
		WriteMessage.getDefault().writeBLUEMessage(Messages.ChiponDebugTarget_90);
		//11KA00硬件调试的特殊处理 —— A02移除
		//fThread.BackGroudChipModiy(ChipRangeCode, debugMode);
		
		if((debugRunMode==0)||(debugRunMode==1) || (debugRunMode==2)|| (debugRunMode==3))
		{
			// 初始同步断点
			installDeferredBreakpoints();		
			// 初始同步监控点
			installWatchPoints();
			
			try {
				out.write("h"); //$NON-NLS-1$
//				WriteMessage.getDefault().writeBLUEMessage("调试器就绪");
			} catch (IOException e) {
		
			}
		}		
	}
	

	private void installWatchPoints() {
		if(ChiponThread.watchpointDatas!=null&&ChiponThread.watchpointDatas.size()!=0){
			for(ChiponWatchpointData data : ChiponThread.watchpointDatas){
				if(data.isNowValue())
				data.setWatchPoint(getCurrentThread());
			}
		}
		
	}
	
	/**
	 * 设置main函数断点
	 * 
	 */
	private String setMain(int debugRunMode ){
// 请启动时根据调试选项可以从startup启动，但复位时，因集成到gdb中的断点并运行到main，不给予参数差异化了，复位从main执行			
		String mainValue = "main";
		
		if(ChipRangeCode == 5) {
			if (chiponDebugInfoModel.getDebugFileName().startsWith("Cpu0_Main")) {
				mainValue = "core0_main";
			}
		}
		
		String debugMain = chiponDebugInfoModel.getDebugFileMain();
		if(debugMain != null && debugMain.length() > 0) {
			mainValue = debugMain;
		}
		
		if(StringUtils.isNotEmpty(chiponDebugInfoModel.getDebugFileLine()) && Integer.parseInt(chiponDebugInfoModel.getDebugFileLine()) > 0) {
			mainValue = chiponDebugInfoModel.getDebugFileName() + ":" + chiponDebugInfoModel.getDebugFileLine();
		}
		
		ChiponBreakpointCommandResult result ;
		if(debugRunMode==1)
			 result=(ChiponBreakpointCommandResult)sendCommand(
			new ChiponBreakpointCommand("startup")); //$NON-NLS-1$
		else
			 result=(ChiponBreakpointCommandResult)sendCommand(
			new ChiponBreakpointCommand(mainValue)); //$NON-NLS-1$

		return  result.pointNo+""; //$NON-NLS-1$
	}
	
	/**
	 * Install breakpoints that are already registered with the breakpoint
	 * manager.
	 */
	private void installDeferredBreakpoints() {
		IBreakpoint[] breakpoints = getBreakpointManager().getBreakpoints(getModelIdentifier());
		for (int i = 0; i < breakpoints.length; i++) {
			installBreakPoint(breakpoints[i]);
		}
	}
	
	@Override
	public void handleEvent(ChiponEvent event) {
		// start
		if(event instanceof ChiponDebuggerStartedEvent){
			try {
				debuggerStarted();
			} catch (DebugException e) {
				WriteMessage.getDefault().writeErrMessage(Messages.ChiponDebugTarget_94);
				//e.printStackTrace();
			}
			started();
			//设置终止模式为false
			setTerminated(false);
		}
		// run  enable pause button
		else if(event instanceof ChiponDebuggerRunEvent){
			debuggerRun((ChiponDebuggerRunEvent)event);
		}
		// pasue  disable pause enable run  step xx  finish
		else if(event instanceof ChiponDebuggerSuspendedEvent){
			debuggerSuspended((ChiponDebuggerSuspendedEvent)event);
		}
		// exit
		else if(event instanceof ChiponTerminatedEvent){
			debuggerTerminated();
		}
	}
	
	private void started() {
		setTerminated(false);
		try {
			for(IThread thread : getThreads()) {
				if(thread instanceof ChiponThread) {
					((ChiponThread) thread).start();	
				}
			}
		} catch (DebugException e) {
			e.printStackTrace();
		}
	}
	
	
	private void debuggerRun(ChiponDebuggerRunEvent event) {
		   setDebuggerSuspended(false);
		   //点亮暂停按钮
		   fireResumeEvent(calcDetail(event.fReason));
	}
	
	

	private void debuggerSuspended(ChiponDebuggerSuspendedEvent event) {
	    setDebuggerSuspended(true);
	    fireSuspendEvent(calcDetail(event.fReason));
	}
	
	/**
	 * Called when this debug target terminates.
	 */
	public void debuggerTerminated() {
		isDebugging = false;
		setTerminated(true);
		traceDataList.clear();
		//如果没有终止，则终止调试
//		if(!isTerminated()){
//			try {
//				if(!isSuspended()){
//					suspend();
//				}
//				sendCommand(new ChiponTerminateCommand());
//			} catch (DebugException e) {
//			}
//		}
		
		//取消使能所有调试按钮
		fireSuspendEvent(DebugEvent.TERMINATE);
		fireChangeEvent(DebugEvent.TERMINATE);
		fireResumeEvent(DebugEvent.TERMINATE);
		fireTerminateEvent();
		
		//移除断点监听
		IBreakpointManager breakpointManager = getBreakpointManager();
        breakpointManager.removeBreakpointListener(this);
		breakpointManager.removeBreakpointManagerListener(this);
		//移除IChiponEventListener监听
		removeEventListener(this);
		
		try {
			for(IThread thread : getThreads()) {
				if(thread instanceof ChiponThread) {
					((ChiponThread) thread).exit();	
				}
			}
		} catch (DebugException e2) {
			e2.printStackTrace();
		}
		
//		关闭输入输出流
		try {
			if(out != null){
				out.close();
				out = null;
			}
			if(fEventReader != null){
				fEventReader.close();
				fEventReader = null;
			}
			if(fRequestReader != null){
				fRequestReader.close();
				fRequestReader = null;
			}
			if(fRequestWriter != null){
				fRequestWriter.close();
				fRequestWriter = null;
			}
		} catch (IOException e) {

		}
		// 终止调试进程，存在调试进程不能自己退出的情况的的删除
		try {
			getCurrentThread().KillGdbforExit();
		} catch (Exception e1) {
		} 
	}
	
	private void setDebuggerSuspended(boolean suspended) {
		//fDebuggerSuspended = suspended;
	}
		
	private int calcDetail(String reason) {
        if (reason.equals("breakpoint") || reason.equals("watch")) { //$NON-NLS-1$ //$NON-NLS-2$
            return DebugEvent.BREAKPOINT;
        } else if (reason.equals("stepover")) { //$NON-NLS-1$
            return DebugEvent.STEP_OVER;
        } else if(reason.equals("stepinto")){ //$NON-NLS-1$
        	return DebugEvent.STEP_INTO;
        }else if(reason.equals("stepout")){ //$NON-NLS-1$
        	return DebugEvent.STEP_RETURN;
        }else if (reason.equals("drop")) { //$NON-NLS-1$
            return DebugEvent.STEP_RETURN;
        } else if (reason.equals("client")) { //$NON-NLS-1$
            return DebugEvent.CLIENT_REQUEST;
        } else if (reason.equals("event")) { //$NON-NLS-1$
            return DebugEvent.BREAKPOINT;
        } 
        else {
            return DebugEvent.UNSPECIFIED;
        } 
	}

	@Override
	public IThread[] getThreads() throws DebugException {
		synchronized (fThreads) {
			if(fThreads.size() == 0){
				return new IThread[]{};
			}
			return (IThread[]) fThreads.toArray(new ChiponThread[fThreads.size()]);
		}
	}

	@Override
	public boolean hasThreads() throws DebugException {
		if(fThreads.size() != 0){
			return true;
		}else{
			return false;
		}
		
	}
	
	private String getChiponPower(){
		String power = null;
		if(fProject != null){
			ProjectPropertyManager	fPropertiesManager = ProjectPropertyManager.getPropertyManager(fProject);
			if(fPropertiesManager != null){
				ChipOnProjectProperties properties = fPropertiesManager.getProjectProperties();
				if(properties != null){
					power = properties.getfChipPower();
				}
			}
		}
		if(power == null || "".equals(power)){ //$NON-NLS-1$
			return "5"; //$NON-NLS-1$
		}
		return power;
		
	}

	/**
	 * 获取反汇编文件的内容
	 */
	public void getLocationData(){
		
//		File asmFile = new File(fProject.getLocation().toOSString() + File.separator + "Debug" + //$NON-NLS-1$
//				File.separator + fProject.getName() + ".lst"); //$NON-NLS-1$
		File asmFile;
		IManagedBuildInfo bi = ManagedBuildManager.getBuildInfo(fProject);
		if(bi==null){			
			return ;
		}
		IConfiguration buildcfg = bi.getDefaultConfiguration();
		String cfgName = buildcfg.getName();
		IBuildMacroProvider provider = ManagedBuildManager.getBuildMacroProvider();
		try {
//			String buildArtifactName = provider.resolveValue(buildcfg.getArtifactName(),
//			        "", " ", IBuildMacroProvider.CONTEXT_CONFIGURATION, buildcfg);
			String buildArtifactName =provider.getMacro("BuildArtifactFileBaseName", IBuildMacroProvider.CONTEXT_CONFIGURATION, buildcfg, true).getStringValue();	
			buildArtifactName = provider.resolveValue(buildArtifactName,
			        "", " ", IBuildMacroProvider.CONTEXT_CONFIGURATION, buildcfg);				
			
			String fileName = buildArtifactName + "." + "lst";//buildcfg.getArtifactExtension();
			IFile tmpFile = fProject.getFile(cfgName + "/" + fileName);
			if(tmpFile != null && tmpFile.exists())
			{
				asmFile = new File( tmpFile.getLocation().toOSString().toString());				
			}
			else
			{				
				return ;			
			}
		} catch (BuildMacroException e) {
//			e.printStackTrace();			
			return ;
		}	
		//##################################################################################
		if (asmFile != null) {
			BufferedReader br = null;
	
			Pattern pattern = Pattern.compile("^[0-9a-fA-F]+:.+"); //$NON-NLS-1$
	
			// 匹配地址+ ：的行,以":"分割，将地址与行号对应起来。
			try {
				br = new BufferedReader(new InputStreamReader(new FileInputStream(asmFile)));
				String str = br.readLine();
				int line = 1;
				Map<String, Integer> locationData = new HashMap<String, Integer>();
				while (str != null) {
					Matcher matcher = pattern.matcher(str.trim());
					if (matcher.matches()) {
						String[] temp = str.trim().split(":"); //$NON-NLS-1$
						locationData.put("0x" + temp[0], line); //$NON-NLS-1$
					}
	
					str = br.readLine();
					line++;
				}
				ChiponThread.locationData = locationData;
					// System.out.println(out.toString());
			} catch (IOException e) {
	
				e.printStackTrace();
			} finally {
				try {
					if (br != null) {
						br.close();
					}
	
				} catch (IOException e) {
	
					e.printStackTrace();
				}
			}
		}
	}
	
	public IProject getfProject() {
		return fProject;
	}
	
	
	public static IFile getBuildTargetELFFileFromProject() {
		IFile lFile = null;
		if (fProject != null && fProject.exists()) {
			IManagedBuildInfo bi = ManagedBuildManager.getBuildInfo(fProject);
			if (bi != null) {
				IConfiguration buildcfg = bi.getDefaultConfiguration();
				String cfgName = buildcfg.getName();
				IBuildMacroProvider provider = ManagedBuildManager.getBuildMacroProvider();
				try {
					String buildArtifactName = provider.getMacro("BuildArtifactFileBaseName", IBuildMacroProvider.CONTEXT_CONFIGURATION, buildcfg, true).getStringValue();
					buildArtifactName = provider.resolveValue(buildArtifactName, "", " ", IBuildMacroProvider.CONTEXT_CONFIGURATION, buildcfg);
					String fileName = buildArtifactName + "." + "elf"; // buildcfg.getArtifactExtension()
					String fileName2 = buildArtifactName + "." + "s19";
					IFile tmpFile = fProject.getFile(cfgName + "/" + fileName);
					if (tmpFile != null && tmpFile.exists()) {
						lFile = tmpFile;
					} else {
						tmpFile = fProject.getFile(cfgName + "/" + fileName2);
						if (tmpFile != null && tmpFile.exists()) {
							lFile = tmpFile;
						} else {
							lFile = null;
						}
					}
				} catch (BuildMacroException e) {
					e.printStackTrace();
				}
			}
		} 
		return lFile;
	}

	public boolean isBusy() throws DebugException {
		return isDebugRevBusy;
	}
	
	//添加ChiponDebugCommandObserver
	public static void addObserver(ChiponDebugCommandObserver observer){
        obsList.add(observer);
    }
	
	// 删除一个观察者
    public static void delObserver(ChiponDebugCommandObserver observer){
        obsList.remove(observer);
    }
    
    // 通知所有观察者
    public void notifyObservers(ChiponCommand command ,ChiponCommandResult result){
        for (ChiponDebugCommandObserver observer : obsList){
            observer.update(command,result);
        }
    }
    
    public void giveObserversResponse(List<String> response) {
    	for (ChiponDebugCommandObserver observer : obsList){
            observer.updateResponse(response);
        }
    }
	
	public void clearThreads(int cpuNumber) {
		fThreads.clear();
	}

	public int getCpuNumber() {
		return cpuNumber;
	}

	public void setCpuNumber(int cpuNumber) {
		this.cpuNumber = cpuNumber;
	}
	
	
	 public ChiponDebugInfoModel getChiponDebugInfoModel() {
		return chiponDebugInfoModel;
	}

	public void setChiponDebugInfoModel(ChiponDebugInfoModel chiponDebugInfoModel) {
		this.chiponDebugInfoModel = chiponDebugInfoModel;
	}
}
