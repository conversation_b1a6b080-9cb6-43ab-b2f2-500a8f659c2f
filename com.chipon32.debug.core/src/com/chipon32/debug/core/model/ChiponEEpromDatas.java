package com.chipon32.debug.core.model;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/***
 *<AUTHOR>
 *
 ***/
public class ChiponEEpromDatas implements Serializable {
	
	private static final long serialVersionUID = 1L;
	
	private List<ChiponGroupEEpromData> groupEEpromDatas;

	public List<ChiponGroupEEpromData> getGroupEEpromDatas() {
		return groupEEpromDatas;
	}

	public ChiponEEpromDatas(){
		groupEEpromDatas = new ArrayList<ChiponGroupEEpromData>();
	}
	
	public void addGroupEEData(ChiponGroupEEpromData groupEEData){
		groupEEpromDatas.add(groupEEData);
	}
	
}
