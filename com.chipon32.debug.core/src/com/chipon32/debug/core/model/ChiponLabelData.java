package com.chipon32.debug.core.model;

import java.util.List;

import org.eclipse.swt.graphics.Color;

public class ChiponLabelData{

	private String name;
	private String value;      //十进制的值
	private String address;
	private String hexValue;    //十六进制的值
	private String binaryValue;   //二进制的值
	
	//通用寄存器中子寄存器列
	private List<ChiponLabelData> ChiponDataList;
	//标签视图中每行的线程
	private ChiponThread fThread;
	
	private Color color;
	
	public ChiponLabelData() {
	}
	
	public ChiponLabelData(ChiponThread thread, String name, String binaryValue, String value, String hexValue, String address) {
		this.name=name;
		this.binaryValue=binaryValue;
		this.value=value;
		this.hexValue=hexValue;
		this.address=address;
		this.fThread=thread;
	}
	
	public String getName() {
		return name;
	}
	
	public void setName(String name) {
		this.name = name;
	}
	
	public String getValue() {
		if(value != null){
			String val = value.trim();
			if(val.length() == 2){
				return "0"+val;
			}else if(val.length() == 1){
				return "00"+val;
			}else{
				return val;
			}
		}
		return value;
	}
	
	public void setValue(String value) {
		this.value = value;
	}
	
	public List<ChiponLabelData> getChiponDataList() {
		return ChiponDataList;
	}
	
	public void setChiponDataList(List<ChiponLabelData> chiponDataList) {
		ChiponDataList = chiponDataList;
	}
	
	public String getAddress() {
		return address;
	}
	
	public void setAddress(String address) {
		this.address = address;
	}

	public ChiponThread getfThread() {
		return fThread;
	}

	public void setfThread(ChiponThread fThread) {
		this.fThread = fThread;
	}

	public String getHexValue() {
		if(hexValue != null){
			String val = hexValue.trim();
			if(val.length() == 1){
				return "0"+val;
			}else{
				return val;
			}
		}
		return hexValue;
	}

	public void setHexValue(String hexValue) {
		this.hexValue = hexValue;
	}

	public String getBinaryValue() {
		if(binaryValue != null && binaryValue.length()>0){
			String val = binaryValue.trim();
			String modeString = "00000000" ;
			val = modeString.substring(binaryValue.length())+val;
			return val.toUpperCase();
		}
		return binaryValue;
	}
	
	public void setBinaryValue(String binaryValue) {
		this.binaryValue = binaryValue;
	}

	public Color getColor() {
		return color;
	}

	public void setColor(Color color) {
		this.color = color;
	}
}
