package com.chipon32.debug.core.model;

import org.eclipse.debug.core.DebugException;
import org.eclipse.debug.core.model.IDebugTarget;
import org.eclipse.debug.core.model.IValue;
import org.eclipse.debug.core.model.IVariable;

public class ChiponStackValue extends ChiponDebugElement implements IValue {

	private ChiponThread fThread;
    private String fValue;
	
	public ChiponStackValue(IDebugTarget target) {
		super(target);
		// TODO Auto-generated constructor stub
	}
	
	public ChiponStackValue(ChiponThread thread,String value){
		super(null);
		this.fThread = thread;
		this.fValue = value;
	}
	public ChiponThread getThread() {
	    return fThread;
	}

	@Override
	public String getReferenceTypeName() throws DebugException {
		// TODO Auto-generated method stub
		return null;
	}
	
	@Override
	public String getValueString() throws DebugException {
		// TODO Auto-generated method stub
		return fValue;
	}

	@Override
	public boolean isAllocated() throws DebugException {
		// TODO Auto-generated method stub
		return true;
	}

	@Override
	public IVariable[] getVariables() throws DebugException {
		// TODO Auto-generated method stub
		return new IVariable[0];
	}

	@Override
	public boolean hasVariables() throws DebugException {
		// TODO Auto-generated method stub
		return false;
	}

}
