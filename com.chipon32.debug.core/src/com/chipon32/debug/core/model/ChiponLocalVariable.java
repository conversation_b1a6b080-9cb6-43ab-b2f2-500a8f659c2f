package com.chipon32.debug.core.model;


/**
 * 局部变量
 * <AUTHOR> 2013-5-30 下午3:03:39 被VariableTree替代。
 */
@Deprecated
public class ChiponLocalVariable {
	/*
	 * 变量名
	 */
	private String name;
	/*
	 * 变量地址
	 */
	private String address;
	/*
	 * 变量类型
	 */
	private String type;
	/*
	 * 变量值
	 */
	private String value;

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getValue() {
		return value;
	}

	public void setValue(String value) {
		this.value = value;
	}
	
	
	
	
}
