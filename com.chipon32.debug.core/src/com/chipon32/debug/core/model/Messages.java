package com.chipon32.debug.core.model;

import org.eclipse.osgi.util.NLS;

public class Messages extends NLS {
	private static final String BUNDLE_NAME = "com.chipon32.debug.core.model.messages"; //$NON-NLS-1$
	public static String ChiponDebugTarget_22;
	public static String ChiponDebugTarget_31;
	public static String ChiponDebugTarget_32;
	public static String ChiponDebugTarget_61;
	public static String ChiponDebugTarget_62;
	public static String ChiponDebugTarget_76;
	public static String ChiponDebugTarget_77;
	public static String ChiponDebugTarget_78;
	public static String ChiponDebugTarget_79;
	public static String ChiponDebugTarget_81;
	public static String ChiponDebugTarget_83;
	public static String ChiponDebugTarget_84;
	public static String ChiponDebugTarget_90;
	public static String ChiponDebugTarget_94;
	public static String ChiponDebugTarget_95;
	public static String ChiponNullValue_0;
	public static String ChiponThread_16;
	public static String ChiponThread_17;
	public static String ChiponThread_25;
	public static String ChiponThread_29;
	public static String ChiponThread_30;
	public static String ChiponThread_39;
	public static String ChiponThread_40;
	public static String ChiponThread_46;
	public static String ChiponThread_52;
	public static String ChiponThread_53;
	public static String ChiponThread_56;
	public static String ChiponThread_62;
	public static String ChiponThread_69;
	public static String ChiponThread_7;
	public static String ChiponWatchpointData_0;
	public static String ChiponWatchpointData_1;
	public static String ChiponWatchpointData_14;
	public static String ChiponWatchpointData_2;
	public static String ChiponWatchpointData_3;
	public static String ChiponWatchpointData_4;
	
	public static String ChiponWatchpointView_1;
	public static String ChiponWatchpointView_2;
	static {
		// initialize resource bundle
		NLS.initializeMessages(BUNDLE_NAME, Messages.class);
	}

	private Messages() {
	}
}
