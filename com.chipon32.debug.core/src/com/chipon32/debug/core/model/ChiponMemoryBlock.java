package com.chipon32.debug.core.model;

import org.eclipse.debug.core.DebugException;
import org.eclipse.debug.core.model.IDebugTarget;
import org.eclipse.debug.core.model.IMemoryBlock;

public class ChiponMemoryBlock extends ChiponDebugElement implements IMemoryBlock {

	public ChiponMemoryBlock(IDebugTarget target) {
		super(target);
		// TODO Auto-generated constructor stub
	}

	@Override
	public long getStartAddress() {
		// TODO Auto-generated method stub
		return 100;
	}

	@Override
	public long getLength() {
		// TODO Auto-generated method stub
		return 100;
	}

	@Override
	public byte[] getBytes() throws DebugException {
		// TODO Auto-generated method stub
		byte[] bytes = {new Byte("a"),new Byte("b"),new Byte("c")};
		return bytes;
	}

	@Override
	public boolean supportsValueModification() {
		// TODO Auto-generated method stub
		return false;
	}

	@Override
	public void setValue(long offset, byte[] bytes) throws DebugException {
		// TODO Auto-generated method stub

	}

}
