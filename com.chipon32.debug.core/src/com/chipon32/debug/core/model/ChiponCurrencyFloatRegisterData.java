package com.chipon32.debug.core.model;

import java.util.ArrayList;
import java.util.List;

public class ChiponCurrencyFloatRegisterData{

	private String name;
    // 十六进制的值
	private String value;
	//十进制的值
	private String dValue; 
	
	private boolean isFlag = false;  // 标志树内容不允许修改的
	//通用寄存器中子寄存器列
	private List<ChiponCurrencyFloatRegisterData> ChiponDataList;
	
	private String addr;			//寄存器地址，这里特指PSW寄存器
	
	/**
	 * bit位寄存器的数据
	 */
	//通用寄存器的父寄存器
	private ChiponCurrencyFloatRegisterData parentRegisterData;
	
	private int bitLength=0;  // 非零标记这是一个  位信息
	
	private String bitLocation;
	
	private boolean hasParent = false;
	
	public ChiponCurrencyFloatRegisterData() {
	}
		
	
	public ChiponCurrencyFloatRegisterData(String name, String value, String dValue) {
		this.name=name;
		//  获取或赋值实现移除空格，这里加空格的间隔16进制表示
		StringBuilder strv = new StringBuilder(value.replace(" ", ""));		
		int start = strv.length();
		int len = start/4;
		for(int i=1;i<len;i++)	
		{
			strv.insert(start-i*4, " ");
		}		
		
		this.value = strv.toString().toUpperCase();
//		this.value=value;
		
		this.dValue = dValue;
	}
	
	public String getName() {
		return name;
	}
	
	public void setName(String name) {
		this.name = name;
	}
	
	public void setdValue(String dValue) {
		this.dValue = dValue;
	}
	
	public String getdValue() {
		return dValue;
	}
	
	public String getValue() {
		if(value != null &&(value.startsWith("0x")||value.startsWith("0X")) ){
			String val = value.trim().substring(2).toUpperCase();
				return val;		
		}
		return value;
	}
	
	public void setValue(String value) {
		//  获取或赋值实现移除空格，这里加空格的间隔16进制表示
		StringBuilder strv=new StringBuilder(value.replace(" ", ""));		
		int start=strv.length();
		int len=start/4;
		for(int i=1;i<len;i++)	
		{
			strv.insert(start-i*4, " ");
		}		
		//
		this.value = strv.toString().toUpperCase();
//		this.value=value;
	}
	
	public List<ChiponCurrencyFloatRegisterData> getChiponDataList() {
		return ChiponDataList;
	}
	
	public void setChiponDataList(List<ChiponCurrencyFloatRegisterData> chiponDataList) {
		ChiponDataList = chiponDataList;
	}

	public void  AddChild(ChiponCurrencyFloatRegisterData child){
		if(child==null)return;
		if(ChiponDataList==null)
		{
			ChiponDataList=new  ArrayList<ChiponCurrencyFloatRegisterData>();
			ChiponDataList.add(child);
		}
		else
			ChiponDataList.add(child);
		child.setHasParent(true);
		child.setParentRegisterData(this);
	}
	
	public ChiponCurrencyFloatRegisterData getParentRegisterData() {
		return parentRegisterData;
	}

	public void setParentRegisterData(ChiponCurrencyFloatRegisterData parentRegisterData) {
		this.parentRegisterData = parentRegisterData;
	}

	public int getBitLength() {
		return bitLength;
	}

	public void setBitLength(int bitLength) {
		this.bitLength = bitLength;
	}

	public String getBitLocation() {
		return bitLocation;
	}

	public void setBitLocation(String bitLocation) {
		this.bitLocation = bitLocation;
	}

	public boolean isHasParent() {
		return hasParent;
	}

	public void setHasParent(boolean hasParent) {
		this.hasParent = hasParent;
	}


	public String getAddr() {
		return addr;
	}

	public void setAddr(String addr) {
		this.addr = addr;
	}

	public boolean isFlag() {
		return isFlag;
	}

	public void setFlag(boolean isFlag) {
		this.isFlag = isFlag;
	}

}
