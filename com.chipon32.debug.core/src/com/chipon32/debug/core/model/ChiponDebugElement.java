/**
 * 
 */
package com.chipon32.debug.core.model;

import org.eclipse.debug.core.model.DebugElement;
import org.eclipse.debug.core.model.IDebugTarget;

import com.chipon32.debug.core.DebugCoreActivator;
import com.chipon32.debug.core.protocol.ChiponCommand;
import com.chipon32.debug.core.protocol.ChiponCommandResult;

/**
 * 调试 自然 环境  ： 调试目标，命令发送和接收返回方法，模块ID
 * <AUTHOR>
 *
 */
public class ChiponDebugElement extends DebugElement {

	/**
	 * @param target
	 */
	public ChiponDebugElement(IDebugTarget target) {
		super(target);
		// TODO Auto-generated constructor stub
	}
	
	public ChiponDebugTarget getChiponDebugTarget() {
	    return  (ChiponDebugTarget) getDebugTarget();
	}
	
	public ChiponCommandResult sendCommand(ChiponCommand command){
		return getChiponDebugTarget().sendCommand(command);
	}

	/* (non-Javadoc)
	 * @see org.eclipse.debug.core.model.IDebugElement#getModelIdentifier()
	 */
	@Override
	public String getModelIdentifier() {
		// TODO Auto-generated method stub
		return DebugCoreActivator.ID_CHIP_DEBUG_MODEL;
	}
	

}
