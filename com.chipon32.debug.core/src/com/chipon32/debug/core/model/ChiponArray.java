package com.chipon32.debug.core.model;

import org.eclipse.debug.core.DebugException;
import org.eclipse.debug.core.model.IVariable;

public class ChiponArray extends ChiponValue {

	public ChiponArray(ChiponDebugTarget target,
			ChiponVariableTree variableTree, ChiponVariable variable) {
		super(target, variableTree, variable);
	}
	
	
	public ChiponArray(ChiponValue value){
		super(value.getChiponDebugTarget(),value.getfVariableTree(),value.getVariable());
	}


	@Override
	public IVariable[] getVariables() throws DebugException {
		// TODO Auto-generated method stub
		return super.getVariables();
	}
	
	

}
