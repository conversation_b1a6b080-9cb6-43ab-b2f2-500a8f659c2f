package com.chipon32.debug.core.util;

import java.util.List;
import java.util.Map;

import com.chipon32.debug.core.model.ChiponVariableTree;

/**
 * <AUTHOR>	解析大括号的API接口
 */
public interface IChiponBracketParse {

	/**
	 * 
	 * @param message
	 * @return 检测大括号是否匹配
	 * 解析前要先检测匹配
	 */
	public abstract boolean check(String message);

	/**
	 * 
	 * @param message
	 * @return 一组平行的Tree，没有包含和被包含,解析表达式窗口数据，全局变量
	 * @throws Exception 
	 */
	public abstract List<ChiponVariableTree> getTrees(String message, String valueName);
	
	/**
	 * <AUTHOR> 2017-4-14 解析变量窗口，局部变量的值
	 * @param message
	 * @return
	 */
	public abstract List<ChiponVariableTree> getTrees(String message);

	/**
	 * 根据{@link}getTrees得到的一组平行树的id和pid进行组合
	 * @param trees
	 * @return 递归树
	 */
	public abstract List<ChiponVariableTree> buildTrees(List<ChiponVariableTree> trees);
	
	/**
	 * 将字符串可能是数组的综合解析
	 * @param str
	 * @return
	 */
	public Map<String,List<String>> ParseStrings(String str);

}
