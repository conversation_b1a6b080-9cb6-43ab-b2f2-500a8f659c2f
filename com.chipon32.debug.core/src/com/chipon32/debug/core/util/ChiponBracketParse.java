package com.chipon32.debug.core.util;

import java.util.ArrayList;
import java.util.EmptyStackException;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Stack;

import com.chipon32.debug.core.model.ChiponBracket;
import com.chipon32.debug.core.model.ChiponVariableTree;

public class ChiponBracketParse implements IChiponBracketParse {
	

	/* (non-Javadoc)
	 * @see com.chipon32.debug.core.util.IChiponBracketParse#check(java.lang.String)
	 * 判断“{”和“}”是否匹配   匹配返回true，不匹配返回false
	 */
	@Override
	public boolean check(String message){
		Stack<Character> stack = new Stack<Character>();
		char c;
		boolean flag = true;
		//##########################################
		for(int i=0;i<message.length() && flag ;i++){
			c = message.charAt(i);
			
			switch(c){
				//++++++++++++++++++++++++++++++++++++++++++++
				case '{':
					stack.push(c);
					break;
				case '}':
					if(stack.isEmpty())
					{
						flag = false;
						break;
					}
					else if(stack.pop() != '{'){
						flag = false;
					}
					break;
				//++++++++++++++++++++++++++++++++++++++++++++
			}
		}
		//#########################################
		if(flag && !stack.isEmpty()){
			flag = false;
		}
		return flag;
	}
	
	public String getNowContexFrom(String message)
	{
		int count=0;
		char c;
		int i;
		for(i=0;i<message.length();i++){
			c = message.charAt(i);
			switch(c){
				case '{':
					count++;
					break;
				case '}':
					count--;
					if(count==0)
						return message.substring(0,i+1);
					break;
			}
		}
		return "";
	}
	
	/**
	* 	变量数组对象 列表
	volatile unsigned int  arr=02;
	volatile unsigned char arr1[20]={1,2,};
	volatile unsigned char arr2[12][2]={ {1,2},{3,4},};
	volatile unsigned char arr3[2][2][2]={ {{1,2},{3,4}},{{1,2},{3,4}}};
	volatile unsigned char arr4[2][2][2][2]={{ {{1,2},{3,4}},{{1,2},{3,4}}},{ {{1,2},{3,4}},{{1,2},{3,4}}}};

	typedef struct
	{
		unsigned char i;
		unsigned int  j;
		unsigned int ar[3];
	}Mytest1;

	typedef struct
	{
		unsigned char ii;
		unsigned int  jj;
		unsigned int arar[3];
		Mytest1   mytest;
		unsigned int arar2[3];
	}Mytest2;

	arr
	response = $4 = 2

	arr1
	response = $7 = "\001\002\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000"

	arr2
	response = $6 = {"\001\002", "\003\004", "\000\000", "\000\000", "\000\000", "\000\000", "\000\000", "\000\000", "\000\000", "\000\000", "\000\000", "\000\000"}

	arr3
	response = $8 = {{"\001\002", "\003\004"}, {"\001\002", "\003\004"}}

	arr4
	response = $14 = {{{"\001\002", "\003\004"}, {"\001\002", "\003\004"}}, {{"\001\002", "\003\004"}, {"\001\002", "\003\004"}}}
	
	t1
	response = $10 = {i = 0 '\000', j = 0, ar = 0x10000088 <t1+0x8>}

	t2
	response = $12 = {ii = 0 '\000', jj = 0, arar = 0x10000048 <t2+0x8>, mytest = {i = 0 '\000', j = 0, ar = 0x1000005c <t2+0x1c>}, arar2 = 0x10000068 <t2+0x28>}
	 * 
	 */
	public List<ChiponVariableTree> getArrTrees(String noSpaceMessage, String valueName)
	{
		List<ChiponVariableTree> list = new ArrayList<ChiponVariableTree>();
		
		ChiponVariableTree finalTree;
		finalTree = new ChiponVariableTree(0,0);
		
		finalTree.setName(valueName);		
		//finalTree.setValue(noSpaceMessage);
		//+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
		String dovalue="";
		for(int ii=0;ii<noSpaceMessage.length();)
		{
			char c= noSpaceMessage.charAt(ii);
			if( noSpaceMessage.charAt(ii) == '\\')
			{	
				if(noSpaceMessage.charAt(ii-1) != '\"')
					dovalue+=",";
				if(noSpaceMessage.charAt(ii+1)=='0'|| noSpaceMessage.charAt(ii+1)=='1'||noSpaceMessage.charAt(ii+1)=='2'||noSpaceMessage.charAt(ii+1)=='3')
				{// 特殊的无转换内容
					int vs=Integer.parseInt(noSpaceMessage.substring(ii+1,ii+4), 8);
					dovalue+=Integer.toString(vs);
					ii+=4; 
				}
				else
				{// 转移字符
					int vs=0;
					if(noSpaceMessage.charAt(ii+1)=='e')	vs=27;	
					if(noSpaceMessage.charAt(ii+1)=='a')	vs=7;	
					if(noSpaceMessage.charAt(ii+1)=='b')	vs=8;
					if(noSpaceMessage.charAt(ii+1)=='t')	vs=9;	
					if(noSpaceMessage.charAt(ii+1)=='n')	vs=10;	
					if(noSpaceMessage.charAt(ii+1)=='v')	vs=11;
					if(noSpaceMessage.charAt(ii+1)=='f')	vs=12;	
					if(noSpaceMessage.charAt(ii+1)=='r')	vs=13;	
					dovalue+=Integer.toString(vs);
					ii+=2;
				}				
			}
			else
			{
				dovalue += c;
				ii+=1; 
			}
		}
		finalTree.setValue(dovalue);
		//+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
		finalTree.setParent(null);
		list.add(finalTree);
		//#####################################################################
		int arr_x=0,arr_y=0,arr_z=0,arr_t=0;
		int arr_W_Cont=1;
		//《》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》
		if(noSpaceMessage.startsWith("{{{"))
		{
			arr_W_Cont=4;
			noSpaceMessage=noSpaceMessage.substring(1,noSpaceMessage.length()-1);
			List<String> a4R=new ArrayList<String>();
			while(true)
			{
				int oneend=noSpaceMessage.indexOf("}}");
				if(oneend<=0)
					break;
				a4R.add(noSpaceMessage.substring(0,oneend+1));
				noSpaceMessage=noSpaceMessage.substring(oneend+2);
			}
			//##############################
			for(arr_t=0;arr_t<a4R.size();arr_t++)
			{
				
				ChiponVariableTree ElefirstTree= new ChiponVariableTree(0,1);
				ElefirstTree.setName(valueName+"["+arr_t+"]");					
				String oneget=a4R.get(arr_t);
				if(oneget.startsWith(",")) oneget=oneget.substring(1);
				if(oneget.endsWith(",")) oneget=oneget.substring(0,oneget.length()-1);
				if(oneget.startsWith("{{")) 
					oneget=oneget.substring(1);
				
				List<String> a3R=new ArrayList<String>();
				while(true)
				{
					int oneend=oneget.indexOf("}");
					if(oneend<=0)
						break;
					a3R.add(oneget.substring(0,oneend));
					oneget=oneget.substring(oneend+1);
				}
				for(arr_y=0;arr_y<a3R.size();arr_y++)
				{
					ChiponVariableTree EleOneYTree= new ChiponVariableTree(0,2);
					EleOneYTree.setName(valueName+"["+arr_t+"]"+"["+arr_y+"]");	
					String vs2=a3R.get(arr_y);
					vs2=vs2.replace("{", "");
					if(vs2.startsWith(",")) vs2=vs2.substring(1);
					if(vs2.endsWith(",")) vs2=vs2.substring(0,vs2.length()-1);
					String vcount[]=vs2.split(",");
					for(arr_x=0; arr_x<vcount.length;arr_x++ )
					{
						ChiponVariableTree EleOneTree= new ChiponVariableTree(0,3);
						EleOneTree.setName(valueName+"["+arr_t+"]"+"["+arr_y+"]"+"["+arr_x+"]");					
						String vs=vcount[arr_x];
						//#######################################################################
						// 字符串或一维数组
						if(vs.contains("\""))
						{
							IChiponBracketParse bracketParse = new ChiponBracketParse();
							Map <String, List<String>> rusult=bracketParse.ParseStrings(vs);
							// 可能字符串	
							String strVaule		=rusult.get("StringVaule").get(0);
							List<String>vaules	=rusult.get("CharsVaule");
							
							// 树的建立
							//## 根树				
							EleOneTree.setValue("'"+strVaule);						
							//## 数据内容
							for( arr_z=0;arr_z<vaules.size();arr_z++)
							{							
								ChiponVariableTree TreeChild = new ChiponVariableTree(0, 4);
								TreeChild.setName( EleOneTree.getName()	+	"["+arr_z+"]");
								TreeChild.setValue(vaules.get(arr_z).trim());
								TreeChild.setParent(EleOneTree);
								EleOneTree.addChild( TreeChild);	
							}	// 维1			
						}
						//#######################################################################
						else
						{
							EleOneTree.setValue(vs);
						}
						//#######################################################################				
						EleOneYTree.addChild(EleOneTree);
					}//维2
					ElefirstTree.addChild(EleOneYTree);
				}//  维3		
				finalTree.addChild(ElefirstTree);
			}// 维4
			//##############################
		}
		//《》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》
		else if (noSpaceMessage.startsWith("{{"))
		{	arr_W_Cont=3;		
			noSpaceMessage=noSpaceMessage.substring(1,noSpaceMessage.length()-1);
			List<String> a3R=new ArrayList<String>();
			while(true)
			{
				int oneend=noSpaceMessage.indexOf("}");
				if(oneend<=0)
					break;
				a3R.add(noSpaceMessage.substring(0,oneend));
				noSpaceMessage=noSpaceMessage.substring(oneend+1);
			}
			//##############################
			for(arr_y=0;arr_y<a3R.size();arr_y++)
			{
				ChiponVariableTree EleOneYTree= new ChiponVariableTree(0,1);
				EleOneYTree.setName(valueName+"["+arr_y+"]");	
				String vs2=a3R.get(arr_y);
				vs2=vs2.replace("{", "");
				if(vs2.startsWith(",")) vs2=vs2.substring(1);
				if(vs2.endsWith(",")) vs2=vs2.substring(0,vs2.length()-1);
				String vcount[]=vs2.split(",");
				for(arr_x=0; arr_x<vcount.length;arr_x++ )
				{
					ChiponVariableTree EleOneTree= new ChiponVariableTree(0,2);
					EleOneTree.setName(valueName+"["+arr_y+"]"+"["+arr_x+"]");					
					String vs=vcount[arr_x];
					//#######################################################################
					// 字符串或一维数组
					if(vs.contains("\""))
					{
						IChiponBracketParse bracketParse = new ChiponBracketParse();
						Map <String, List<String>> rusult=bracketParse.ParseStrings(vs);
						// 可能字符串	
						String strVaule		=rusult.get("StringVaule").get(0);
						List<String>vaules	=rusult.get("CharsVaule");
						
						// 树的建立
						//## 根树				
						EleOneTree.setValue("'"+strVaule);						
						//## 数据内容
						for( arr_z=0;arr_z<vaules.size();arr_z++)
						{							
							ChiponVariableTree TreeChild = new ChiponVariableTree(0, 3);
							TreeChild.setName( EleOneTree.getName()	+	"["+arr_z+"]");
							TreeChild.setValue(vaules.get(arr_z).trim());
							TreeChild.setParent(EleOneTree);
							EleOneTree.addChild( TreeChild);	
						}	// 维3			
					}
					//#######################################################################
					else
					{
						EleOneTree.setValue(vs);
					}
					//#######################################################################				
					EleOneYTree.addChild(EleOneTree);
				}//维2
				finalTree.addChild(EleOneYTree);
			}//  维1			
			//##############################
		}
		//《》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》	
		else if (noSpaceMessage.startsWith("{"))
		{
			arr_W_Cont=2;
			noSpaceMessage=noSpaceMessage.replace("{", "");
			noSpaceMessage=noSpaceMessage.replace("}", "");
			String vcount[]=noSpaceMessage.split(",");
			for(arr_x=0; arr_x<vcount.length;arr_x++ )
			{
				ChiponVariableTree EleOneTree= new ChiponVariableTree(0,1);
				EleOneTree.setName(valueName+"["+arr_x+"]");					
				String vs=vcount[arr_x];
				//#######################################################################
				// 字符串或一维数组
				if(vs.contains("\""))
				{
					IChiponBracketParse bracketParse = new ChiponBracketParse();
					Map <String, List<String>> rusult=bracketParse.ParseStrings(vs);
					// 可能字符串	
					String strVaule		=rusult.get("StringVaule").get(0);
					List<String>vaules	=rusult.get("CharsVaule");
					
					// 树的建立
					//## 根树				
					EleOneTree.setValue("'"+strVaule);						
					//## 数据内容
					for( arr_y=0;arr_y<vaules.size();arr_y++)
					{							
						ChiponVariableTree TreeChild = new ChiponVariableTree(0, 2);
						TreeChild.setName( EleOneTree.getName()	+	"["+arr_y+"]");
						TreeChild.setValue(vaules.get(arr_y).trim());
						TreeChild.setParent(EleOneTree);
						EleOneTree.addChild( TreeChild);	
					}				
				}
				//#######################################################################
				else
				{
					EleOneTree.setValue(vs);
				}
				//#######################################################################				
				finalTree.addChild(EleOneTree);
			}// 每个一维内容
		}// 2维解析完成
		//#####################################################################
		return list;
	}
	
	/**
	 * 	变量结构体对象 列表
		typedef struct
		{
			unsigned char i;
			unsigned int  j;
			unsigned int ar[3];
		}Mytest1;
	
		typedef struct
		{
			unsigned char ii;
			unsigned int  jj;
			unsigned int arar[3];
			Mytest1   mytest;
			unsigned int arar2[3];
		}Mytest2;
	
		typedef union
		{
			struct{
			unsigned int arar2[3];
			unsigned int  jj;
			unsigned char ii;
			Mytest2 st;
			Mytest1   mytest;
			}data1;
		
			struct{
				unsigned char ii;
				unsigned int  jj;
				unsigned int arar[3];
				Mytest1   mytest;
				unsigned int arar2[3];
			}data2;
	
		}Mytest3;
	
		Mytest1  t1;
		Mytest2  t2;
		Mytest2  t3[3];
		Mytest3  t4;	
		发送命令 :print t1
		response = (gdb) kf32command success!
		response = $5 = {i = 0 '\000', j = 0, ar = 0x10000198 <t1+0x8>}
		发送命令 :print t2
		response = (gdb) kf32command success!
		response = $2 = {ii = 0 '\000', jj = 0, arar = 0x100000f8 <t2+0x8>, mytest = {i = 0 '\000', j = 0, ar = 0x1000010c <t2+0x1c>}, arar2 = 0x10000118 <t2+0x28>}
		发送命令 :print t3
		response = (gdb) kf32command success!
		response = $3 = {{ii = 0 '\000', jj = 0, arar = 0x10000058 <t3+0x8>, mytest = {i = 0 '\000', j = 0, ar = 0x1000006c <t3+0x1c>}, arar2 = 0x10000078 <t3+0x28>}, {ii = 0 '\000', jj = 0, arar = 0x1000008c <t3+0x3c>, mytest = {i = 0 '\000', j = 0, ar = 0x100000a0 <t3+0x50>}, arar2 = 0x100000ac <t3+0x5c>}, {ii = 0 '\000', jj = 0, arar = 0x100000c0 <t3+0x70>, mytest = {i = 0 '\000', j = 0, ar = 0x100000d4 <t3+0x84>}, arar2 = 0x100000e0 <t3+0x90>}}
		发送命令 :print t4
		response = (gdb) kf32command success!
		response = $7 = {data1 = {arar2 = 0x10000130 <t4>, jj = 0, ii = 0 '\000', st = {ii = 0 '\000', jj = 0, arar = 0x1000014c <t4+0x1c>, mytest = {i = 0 '\000', j = 0, ar = 0x10000160 <t4+0x30>}, arar2 = 0x1000016c <t4+0x3c>}, mytest = {i = 0 '\000', j = 0, ar = 0x10000180 <t4+0x50>}}, data2 = {ii = 0 '\000', jj = 0, arar = 0x10000138 <t4+0x8>, mytest = {i = 0 '\000', j = 0, ar = 0x1000014c <t4+0x1c>}, arar2 = 0x10000158 <t4+0x28>}}
	*/
	public List<ChiponVariableTree> getStructTrees(String noSpaceMessage, String valueName)
	{
		List<ChiponVariableTree> list = new ArrayList<ChiponVariableTree>();
		
		//##########################################################################		
		Stack<ChiponVariableTree> stack = new Stack<ChiponVariableTree>();
		int [] stackFatherON={0,0,0,0, 0,0,0,0, 0,0,0,0, 0,0,0,0, 0,0,0,0, 0,0,0,0, 0,0,0,0, 0,0,0,0 };
		char c;
		int id =0 ;
		int lastComma = -1;  //上一个“，”、“{”、“}”所在位置
		
		for(int i=0;i<noSpaceMessage.length();i++){
			c = noSpaceMessage.charAt(i);
			switch(c){
			//##########################################################################
			//##########################################################################
			case '{':
			{
				ChiponVariableTree oneSecondRoot = new ChiponVariableTree(1,1);   //父元素所在位置				
				//除了第一个“{”,剩下每一个“{”前都有name=
				if(i == 0){
					oneSecondRoot.setName(valueName);oneSecondRoot.setParent(null);
					stackFatherON[0]=0;
					stackFatherON[1]=0;
				}else 	if((i - lastComma) ==1)	{
					oneSecondRoot.setName(stack.peek().getName()+"["+	(stackFatherON[stack.size()-1]++)	+"]"); 
				}
				else{
					String findname= noSpaceMessage.substring(lastComma+1, i -1).replaceAll("=", "").trim();
					if(findname.isEmpty())
						oneSecondRoot.setName(stack.peek().getName()+"["+	(stackFatherON[stack.size()-1]++)	+"]"); 
					else							
						oneSecondRoot.setName(findname); 
				}
				
				if(stack.size()>0)
				{					
					stack.peek().addChild(oneSecondRoot);
					oneSecondRoot.setValue(getNowContexFrom(noSpaceMessage.substring(i)));  // 位置 ，最上不显示内容
				}
				else
				{
					oneSecondRoot.setParent(null);					
				}
				stack.push(oneSecondRoot);
				
				lastComma = i;
				break;				
			}// 一个新元素
			//##########################################################################
			//##########################################################################	
			case ',':
			case '}':
			{				
				if(c==',' && (i - lastComma) ==1)
				{					
					lastComma =i;continue;				
				}
				//+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
				//+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
				if((i - lastComma) >1)
				{
					ChiponVariableTree childTree = new ChiponVariableTree(2,2);
					String data = noSpaceMessage.substring(lastComma+1, i).trim();
					// 具有对象的表达
					if(data.contains("=")){
						String[] datas = data.split("=");
						if(datas[1].contains("\""))
						{ 
							Map <String, List<String>> rusult=ParseStrings(datas[1]);
							// 可能字符串	
							String strVaule=rusult.get("StringVaule").get(0);
							List<String>vaules=rusult.get("CharsVaule");
							// 树的建立
							//## 根树							
							childTree.setName(datas[0].trim());
							childTree.setValue(strVaule);							
							//## 数据内容
							for(int jj=0;jj<vaules.size();jj++)
							{							
								ChiponVariableTree TreeChild = new ChiponVariableTree(id+200+jj, id);
								TreeChild.setName(datas[0].trim()+"["+jj+"]");
								TreeChild.setValue(vaules.get(jj).trim());
								TreeChild.setParent(childTree);
								childTree.addChild( TreeChild);	
							}						
						}
						else
						{							
							childTree.setName(datas[0].trim());
							childTree.setValue(datas[1].trim());										
						}
					}
					// 其他的通用显示	
					else{
						//	childTree.setValue(data);
						//	childTree.setName(stack.peek().getName()+"["+(stackFatherON[stack.size()-1]++)+"]");
						
						if(data.contains("\""))  // 结构图里面存在二维数组，原被解析为字符串序列的拆分
						{ 
							Map <String, List<String>> rusult=ParseStrings(data);
							// 可能字符串	
							String strVaule=rusult.get("StringVaule").get(0);
							List<String>vaules=rusult.get("CharsVaule");
							// 树的建立
							//## 根树				
							String NameHere=stack.peek().getName()+"["+(stackFatherON[stack.size()-1]++)+"]";
							childTree.setName(NameHere);
							childTree.setValue(strVaule);							
							//## 数据内容
							for(int jj=0;jj<vaules.size();jj++)
							{							
								ChiponVariableTree TreeChild = new ChiponVariableTree(id+200+jj, id);
								TreeChild.setName(NameHere+"["+jj+"]");
								TreeChild.setValue(vaules.get(jj).trim());
								TreeChild.setParent(childTree);
								childTree.addChild( TreeChild);	
							}						
						}
						else
						{							
							childTree.setValue(data);
							childTree.setName(stack.peek().getName()+"["+(stackFatherON[stack.size()-1]++)+"]");								
						}
					}
					stack.peek().addChild(childTree);
				}// 具有元素
				//+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
				//+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
				if(c=='}')
				{
					if(stack.size()>1)
						stackFatherON[stack.size()-1]=0;
					else
						stackFatherON[0]=0;
					if(stack.size()>1)
					{
					   stack.pop();
					}
				}				
				//#########################################################				
				lastComma = i;break;
			} // , } 元素或结尾
//##########################################################################//##########################################################################	
			}// 特殊字符  {  ， } 匹配
		}// 输入字符遍历
		
		list.add(stack.peek());
		return list;
	}
	/**
	 * 监控变量，基于名字和返回内容结果
	 */
	@Override
	public List<ChiponVariableTree> getTrees(String message, String valueName){
		// 为啥替换 移除 \s  \s代表空白  空格和制表符都移除的  但是 char字符串 或数组的内容的空格也会被替换造成内容错误，故不做替换
		// 仅使用trim的移除左边和右边空格,所以解析部分的字符串都要使用trim方法
		String noSpaceMessage =message.trim(); 			//message.replaceAll("\\s+", "");
		if(noSpaceMessage.contains("="))
		{
			return getStructTrees(noSpaceMessage,valueName);
		}
		else
		{
			return getArrTrees(noSpaceMessage,valueName);
		}		
	}
	/**
	 * 局部变量，内容带有名字和值  解析数据www = {1, 3, 4, 5}
	 */
	@Override
	public List<ChiponVariableTree> getTrees(String message){		
		//#####################################################################
		String tryMessage =message.trim(); 			//message.replaceAll("\\s+", "");
		if(tryMessage.contains("="))
		{
			String valueName=tryMessage.substring(0,tryMessage.indexOf("=")).trim();
			tryMessage = tryMessage.substring(tryMessage.indexOf("=")+1).trim();
			if(tryMessage.contains("="))
			{
				return getStructTrees(tryMessage,valueName);
			}
			else
			{
				return getArrTrees(tryMessage,valueName);
			}	
		}		
		// 为啥替换 移除 \s  \s代表空白  空格和制表符都移除的  但是 char字符串 或数组的内容的空格也会被替换造成内容错误，故不做替换
		// 仅使用trim的移除左边和右边空格,所以解析部分的字符串都要使用trim方法
		String noSpaceMessage =message.trim(); 			//message.replaceAll("\\s+", "");
		// 总的待返回结果
		List<ChiponVariableTree> list = new ArrayList<ChiponVariableTree>();
		//  名  和  id的对象，分析子关系
		Stack<ChiponBracket> stack = new Stack<ChiponBracket>();
		
		//##################################################################
		char c;
		int id =0 ;
		int lastComma = -1;  //上一个“，”、“{”、“}”所在位置
		int count = -1;		//数组编号
		
		
		for(int i=0;i<noSpaceMessage.length();i++){
			// 读入一个字符
			c = noSpaceMessage.charAt(i);
			switch(c){
			//#######################每一个有子集的起始
			case '{':
				ChiponBracket bracket = new ChiponBracket(i,id);   //父元素所在位置   所谓为位置，基本为id
				stack.push(bracket);				
				bracket.setName(noSpaceMessage.substring(lastComma+1, i -1).replaceAll("=", "").trim());  // { 前为名字
				//#########################################################
				lastComma = i;  //  当前 {  的位置，作为
				id++;			//   对应的归属级别++，初值为0的，第一个就是1了。  遇到}进行--的还原
				break;
			// 前面一个元素结束
			case ',':
			{
				// 遇到空元素了，不应该发生的，更新位置使解析名字时不会包含这个","
				if((i - lastComma) ==1){
					lastComma =i;
					break;
				}
				// 每发现一个，号，就是一个基本的元素。
				ChiponVariableTree childTree;
				childTree = new ChiponVariableTree(id,stack.peek().getId());  // id为所在级别，父id来自于上一push的节点对象
				String data = noSpaceMessage.substring(lastComma+1, i).trim();
				
				if(data.contains("=")){
					
					String[] datas = data.split("=");
//					childTree.setName(datas[0].trim());
//					childTree.setValue(datas[1].trim());
					if(datas[1].contains("\""))
					{ 
						Map <String, List<String>> rusult=ParseStrings(datas[1]);
						// 可能字符串	
						String strVaule=rusult.get("StringVaule").get(0);
						List<String>vaules=rusult.get("CharsVaule");
						// 树的建立
						//## 根树							
						childTree.setName(datas[0].trim());
						childTree.setValue(strVaule);							
						//## 数据内容
						for(int jj=0;jj<vaules.size();jj++)
						{							
							ChiponVariableTree TreeChild = new ChiponVariableTree(id+200+jj, id);
							TreeChild.setName(datas[0].trim()+"["+jj+"]");
							TreeChild.setValue(vaules.get(jj).trim());
							TreeChild.setParent(childTree);
							childTree.addChild( TreeChild);	
						}						
					}
					else
					{							
						childTree.setName(datas[0].trim());
						childTree.setValue(datas[1].trim());										
					}	
				}else{// 直接的元素值，如数组
					count++;
					childTree.setValue(data);
					childTree.setName(stack.peek().getName()+"["+count+"]");
					
				}

				list.add(childTree);
				id++;
				lastComma = i;
				break;
			}
			//"}"结尾都是父元素，无需赋值
			case '}':
				//两个标记之前>2,说明中间有元素;即最后1个元素不是通过,独立的
				if(i - lastComma>1){
					ChiponVariableTree finalTree;
					finalTree = new ChiponVariableTree(id,stack.peek().getId());

					String data = noSpaceMessage.substring(lastComma+1, i).trim();
					if(data.contains("=")){
						String[] datas = data.split("=");
//						finalTree.setName(datas[0].trim());
//						finalTree.setValue(datas[1].trim());
						if(datas[1].contains("\""))
						{ 
							Map <String, List<String>> rusult=ParseStrings(datas[1]);
							// 可能字符串	
							String strVaule=rusult.get("StringVaule").get(0);
							List<String>vaules=rusult.get("CharsVaule");
							// 树的建立
							//## 根树							
							finalTree.setName(datas[0].trim());
							finalTree.setValue(strVaule);							
							//## 数据内容
							for(int jj=0;jj<vaules.size();jj++)
							{							
								ChiponVariableTree TreeChild = new ChiponVariableTree(id+200+jj, id);
								TreeChild.setName(datas[0].trim()+"["+jj+"]");
								TreeChild.setValue(vaules.get(jj).trim());
								TreeChild.setParent(finalTree);
								finalTree.addChild( TreeChild);	
							}
							
						}
						else
						{							
							finalTree.setName(datas[0].trim());
							finalTree.setValue(datas[1].trim());										
						}						
					}else{ // 直接的元素值，如数组
						count++;
						finalTree.setValue(data);
						finalTree.setName(stack.peek().getName()+"["+count+"]");
					}
					list.add(finalTree);
					id++;
				}
				//###########################一个集合体分析关闭的出栈，这是才能对这个集合体定型，即添加根树，和元素数量
				ChiponBracket popBracket = stack.pop();
				ChiponVariableTree tree;
				try {
					tree = new ChiponVariableTree(popBracket.getId(),getPid(stack));
				} catch (EmptyStackException e) {
					tree = new ChiponVariableTree(popBracket.getId(),popBracket.getId());
				}
				if(count > 0){
					tree.setName(popBracket.getName()+"["+(count+1)+"]");	
					count = -1;
				}else{
					tree.setName(popBracket.getName());
				}			
				lastComma = i;
				list.add(tree);
				break;
			}
			//###############################################################
		}
		return list;
	}
	
	
	
	private int getPid(Stack<ChiponBracket> stack){
		return 5;
	}
	/* (non-Javadoc)
	 * @see com.chipon32.debug.core.util.IChiponBracketParse#buildTrees(java.util.List)
	 * 
	 * 根据pid将子元素添加到父元素的child中，并删除，解析成树结构，只保留最上层的父元素
	 */
	@Override
	public List<ChiponVariableTree> buildTrees(List<ChiponVariableTree> trees){
		
		flag:for(Iterator<ChiponVariableTree> it = trees.iterator();it.hasNext();){
			ChiponVariableTree tree = it.next();
			// 级别
			if(tree.getId() != tree.getPid()){
				
				for(int i=0;i<trees.size();i++){
					
						if(trees.get(i).getId() == tree.getPid()){
								trees.get(i).addChild(tree);
								tree.setParent(trees.get(i));
								trees.remove(tree);
								buildTrees(trees);
							break flag;		//避免多次循环
						}
				}
				
			}
		}
		return trees;
		
	}

	/**
	 * 将字符串可能是数组的综合解析
	 * @param str
	 * @return
	 */
	@Override
	public Map<String,List<String>> ParseStrings(String str)
	{
		Map <String, List<String>> rusult=new HashMap<String,List<String>>();
		
		int ii=0;
		//#########################################
		boolean isStrReadEnd=false;
		boolean isStartData=false;
		String  strVaule="";
		List<String>vaules= new ArrayList<String>();
		
		str=str.trim();
		if(str.startsWith("0x"))
		{
			int lengths=str.length();
			String lelfstr=str;
			try{
				for(ii=0;ii<lengths;)
				{
					if(lelfstr.charAt(ii)=='\"')
					{
						isStartData=true;
						ii++;
						strVaule+=new String(new byte[]{(byte)'\"'});
						continue;
					}
					
					if(lelfstr.charAt(ii)=='\\')
					{
						if(lelfstr.charAt(ii+1)=='0'|| lelfstr.charAt(ii+1)=='1'||lelfstr.charAt(ii+1)=='2'||lelfstr.charAt(ii+1)=='3')
						{// especial no ECS   \0xx \1xx \2xx \3xx    that means octal data
							int vs=Integer.parseInt(lelfstr.substring(ii+1,ii+4), 8);
							if(vs==0)
							{
								isStrReadEnd=true;
							}
							else if(!isStrReadEnd)
							{
								strVaule+=new String(new byte[]{(byte)vs});
							}
							if(isStartData)
							vaules.add(Integer.toString(vs));
							ii+=4;
						}
						else
						{// ESC ,like /e /a /r /n  
							int vs=0;
							if(lelfstr.charAt(ii+1)=='e')	vs=27;	
							else if(lelfstr.charAt(ii+1)=='a')	vs=7;	
							else if(lelfstr.charAt(ii+1)=='b')	vs=8;
							else if(lelfstr.charAt(ii+1)=='t')	vs=9;	
							else if(lelfstr.charAt(ii+1)=='n')	vs=10;	
							else if(lelfstr.charAt(ii+1)=='v')	vs=11;
							else if(lelfstr.charAt(ii+1)=='f')	vs=12;	
							else if(lelfstr.charAt(ii+1)=='r')	vs=13;	
							else vs =lelfstr.charAt(ii+1);   // like  \" to print  ,result "
							
							if(!isStrReadEnd)
							{
								strVaule+=new String(new byte[]{(byte)vs});
							}
							if(isStartData)
							vaules.add(Integer.toString(vs));
							ii+=2;
						}
						
					}
					else
					{// readinchar 
						if(isStartData)
						vaules.add(Integer.toString((int)lelfstr.charAt(ii)));
						if(!isStrReadEnd)
							strVaule+=lelfstr.substring(ii, ii+1);
						ii++;
					}
				}				
			}catch(Exception e) {
				// TODO: handle exception
				System.out.print("");
			}
		}
		else
		{
		int lengths=str.length()-2;// "  "  sub  get lengthe -2 to real
		isStartData=true;
		//remove \"  first and end
		String lelfstr=str.trim().substring(1,lengths+1);
		try{
				for(ii=0;ii<lengths;)
				{
					if(lelfstr.charAt(ii)=='\\')
					{
						if(lelfstr.charAt(ii+1)=='0'|| lelfstr.charAt(ii+1)=='1'||lelfstr.charAt(ii+1)=='2'||lelfstr.charAt(ii+1)=='3')
						{// 特殊的无转换内容
							int vs=Integer.parseInt(lelfstr.substring(ii+1,ii+4), 8);
							if(vs==0)
							{
								isStrReadEnd=true;
							}
							else if(!isStrReadEnd)
							{
								strVaule+=new String(new byte[]{(byte)vs});
							}
							vaules.add(Integer.toString(vs));
							ii+=4;
						}
						else
						{// 转移字符
							int vs=0;
							if(lelfstr.charAt(ii+1)=='e')	vs=27;	
							else if(lelfstr.charAt(ii+1)=='a')	vs=7;	
							else if(lelfstr.charAt(ii+1)=='b')	vs=8;
							else if(lelfstr.charAt(ii+1)=='t')	vs=9;	
							else if(lelfstr.charAt(ii+1)=='n')	vs=10;	
							else if(lelfstr.charAt(ii+1)=='v')	vs=11;
							else if(lelfstr.charAt(ii+1)=='f')	vs=12;	
							else if(lelfstr.charAt(ii+1)=='r')	vs=13;	
							else vs =lelfstr.charAt(ii+1);   // 如会有 \"的打印输出
							
							if(!isStrReadEnd)
							{
								strVaule+=new String(new byte[]{(byte)vs});
							}
							vaules.add(Integer.toString(vs));
							ii+=2;
						}
						
					}
					else
					{// readinchar 
						vaules.add(Integer.toString((int)lelfstr.charAt(ii)));
						if(!isStrReadEnd)
							strVaule+=lelfstr.substring(ii, ii+1);
						ii++;
					}
				}
			}catch (Exception e) {
				// TODO: handle exception
				System.out.print("");
			}
		}
		//
		List<String>vaules1= new ArrayList<String>();
		vaules1.add(strVaule);
		rusult.put("StringVaule", vaules1);
		rusult.put("CharsVaule", vaules);
		//#########################################
		return rusult;
	}	
}
