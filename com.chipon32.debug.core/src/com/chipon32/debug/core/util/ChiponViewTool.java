package com.chipon32.debug.core.util;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.core.resources.IProject;
import org.eclipse.swt.graphics.Color;

import com.chipon32.chiponide.core.chipondescription.ICategory;
import com.chipon32.chiponide.core.chipondescription.IDeviceDescription;
import com.chipon32.chiponide.core.chipondescription.IEntry;
import com.chipon32.chiponide.core.chipondescription.chipio.Bits;
import com.chipon32.chiponide.ui.ChipOniohDeviceDescriptionProvider;
import com.chipon32.chiponide.core.chipondescription.chipio.Register;
import com.chipon32.chiponide.core.properties.ChipOnProjectProperties;
import com.chipon32.chiponide.core.properties.ProjectPropertyManager;
import com.chipon32.debug.core.model.ChiponCurrencyRegisterData;
import com.chipon32.debug.core.model.ChiponDebugTarget;
import com.chipon32.debug.core.model.ChiponGroupRomData;
import com.chipon32.debug.core.model.ChiponLabelData;
import com.chipon32.debug.core.model.ChiponRomData;
import com.chipon32.debug.core.model.ChiponThread;
import com.chipon32.debug.core.protocol.ChiponSingleRegisterCommand;
import com.chipon32.debug.core.protocol.ChiponSingleRegisterCommandResult;

/***
 *<AUTHOR>
 ***/
public class ChiponViewTool {
	private static ChiponViewTool viewTool;
	public static ChiponViewTool getViewTool(){
		if(viewTool==null)
			viewTool=new ChiponViewTool();
		return viewTool;
	}

	/*
	 * 
	 * 根据输入的数据，返回新的视图模型数据
	 * */
	public List<ChiponRomData> getNewThreadList(List<ChiponRomData> list,ChiponGroupRomData groupRomData,int columnIndex, String tmp) {
		List<ChiponRomData> newList =new ArrayList<ChiponRomData>();
		for(int i=0;i<list.size();i++){
			ChiponRomData oldRomData=list.get(i);
			String address=oldRomData.getAddress();
			String oldValue=oldRomData.getOldValue();
			
			if(groupRomData.getRomDatas().get(columnIndex-1).getAddress().equals(oldRomData.getAddress())){
				oldValue=tmp;
			}
			ChiponRomData romData=new ChiponRomData(address, oldValue);
			newList.add(romData);
		}
		return newList;
	}
	/*
	 * 
	 * 根据输入的数据，返回新的视图模型数据
	 * */
	public List<ChiponRomData> getNewThreadList(List<ChiponRomData> list,ChiponLabelData groupRomData,int columnIndex, String tmp) {
		List<ChiponRomData> newList =new ArrayList<ChiponRomData>();
		for(int i=0;i<list.size();i++){
			ChiponRomData oldRomData=list.get(i);
			String address=oldRomData.getAddress().substring(2);
			String oldValue=oldRomData.getOldValue();
			
			if(address.equals(Integer.toHexString(Integer.parseInt(groupRomData.getAddress().substring(2),16)))){
				switch (columnIndex) {
				case 2:
					oldValue=Integer.toHexString(Integer.parseInt(tmp,2));
					break;
				case 3:
					oldValue=Integer.toHexString(Integer.parseInt(tmp));
					break;
				case 4:
					oldValue=tmp;
					break;

				default:
					break;
				}
				
			}
			ChiponRomData romData=new ChiponRomData(oldRomData.getAddress(), oldValue);
			newList.add(romData);
		}
		return newList;
	}
	
	/*
	 * 
	 * 根据输入的数据，返回新的寄存器视图的Thread模型数据
	 * */
	public List<ChiponRomData> getNewThreadList(List<ChiponRomData> list,Register register,int columnIndex, String tmp) {
		List<ChiponRomData> newList =new ArrayList<ChiponRomData>();
		String newAddress = register.getAddr().substring(0,register.getAddr().length()-1);
		if(newAddress.startsWith("0")){
			newAddress=newAddress.substring(1);
		}
		newAddress="0x"+newAddress.toLowerCase();
		for(int i=0;i<list.size();i++){
			ChiponRomData oldRomData=list.get(i);
			String address=oldRomData.getAddress();
			String oldValue=oldRomData.getOldValue();
			
			if(newAddress.equals(address)){
				oldValue=tmp;
			}
			ChiponRomData romData=new ChiponRomData(address,oldValue);
			newList.add(romData);
		}
		return newList;
	}
	
	/**
	 * 比较TreeViewer的上一次和这一次的input，如果不相同的话，设置为红色显示
	 * @param oldObj
	 * @param newObj
	 * @return
	 */

	/*
	 * 
	 * 返回新的寄存器视图模型数据
	 * 
	 * */
	public ICategory getNewCategory(final ICategory oldcategory,ChiponThread fThread){
		final List<IEntry> registerlist = new ArrayList<IEntry>();
		List<IEntry> entryList = oldcategory.getChildren();
		for (IEntry entry : entryList) {
			if (entry instanceof Register) {
				final Register oldRegister = (Register) entry;
				Register newRegister = new Register();
				//获取单个寄存器的值
				ChiponSingleRegisterCommandResult registerResult = 
						(ChiponSingleRegisterCommandResult)fThread.sendCommand(
								new ChiponSingleRegisterCommand("x /w "+oldRegister.getAddr()));
				
				if(null!=oldRegister.getAddr()&&!"".equals(oldRegister.getAddr()))newRegister.setAddr(oldRegister.getAddr());
				if(null!=oldRegister.getfColor())newRegister.setfColor(oldRegister.getfColor());
				if(null!=oldRegister.getName()&&!"".equals(oldRegister.getName()))newRegister.setName(oldRegister.getName());
				if(null!=oldRegister.getDescription()&&!"".equals(oldRegister.getDescription()))newRegister.setDescription(oldRegister.getDescription());
				if(null!=oldRegister.getParent())newRegister.setParent(oldRegister.getParent());
				
				newRegister.setValue(Integer.toHexString(registerResult.value));
				if(oldRegister.getColumnCount() > 0){
					for(int i = 0; i < oldRegister.getColumnCount(); i++){
						newRegister.setColumnData(i, oldRegister.getColumnData(i));
					}
				}	
				//获取寄存器内bit的值
				if(oldRegister.hasChildren()){
					for(IEntry bitEntry: oldRegister.getChildren()){
						Bits bit = new Bits();
						if(null!=bitEntry.getAddr()&&!"".equals(bitEntry.getAddr()))bit.setAddr(bitEntry.getAddr());
						if(null!=bitEntry.getfColor())bit.setfColor(bitEntry.getfColor());
						if(null!=bitEntry.getName()&&!"".equals(bitEntry.getName()))bit.setName(bitEntry.getName());
						if(null!=bitEntry.getDescription()&&!"".equals(bitEntry.getDescription()))bit.setDescription(bitEntry.getDescription());
						if(null!=bitEntry.getParent())bit.setParent(bitEntry.getParent());
						
						int bitLength = Integer.parseInt(bitEntry.getBitLength());  //位宽
						int startLocation = Integer.parseInt(bitEntry.getAddr());	//位的起始位置
						
						//解析单个bit的值
						int num = 0;  //比较位
						for(int i = 0; i < bitLength; i++){
							num += 1<<i;
						}
						int bitValue = ((registerResult.value&(num<<startLocation))>>startLocation)&num;
						bit.setValue(Integer.toHexString(bitValue));
						newRegister.addChild(bit);
					}
				}
				registerlist.add(newRegister);		
			}
		}
		ICategory newCategory =new ICategory() {
			
			@Override
			public void setfColor(Color fColor) {
				// TODO Auto-generated method stub
				
			}
			
			@Override
			public void setValue(String value) {
				// TODO Auto-generated method stub
				
			}
			
			@Override
			public void setParent(IEntry parent) {
				// TODO Auto-generated method stub
				
			}
			
			@Override
			public void setName(String name) {
				// TODO Auto-generated method stub
				
			}
			
			@Override
			public void setColumnData(int index, String data) {
				// TODO Auto-generated method stub
				
			}
			
			@Override
			public void setAddr(String addr) {
				// TODO Auto-generated method stub
				
			}
			
			@Override
			public boolean hasChildren() {
				// TODO Auto-generated method stub
				return oldcategory.hasChildren();
			}
			
			@Override
			public Color getfColor() {
				// TODO Auto-generated method stub
				return oldcategory.getfColor();
			}
			
			@Override
			public String getValue() {
				// TODO Auto-generated method stub
				return oldcategory.getValue();
			}
			
			@Override
			public IEntry getParent() {
				// TODO Auto-generated method stub
				return oldcategory.getParent();
			}
			
			@Override
			public String getName() {
				// TODO Auto-generated method stub
				System.out.println("====================="+oldcategory.getName());
				return oldcategory.getName();
			}
			
			@Override
			public String getColumnData(int index) {
				// TODO Auto-generated method stub
				return oldcategory.getColumnData(index);
			}
			
			@Override
			public List<IEntry> getChildren() {
				// TODO Auto-generated method stub
				return registerlist;
			}
			
			@Override
			public String getAddr() {
				// TODO Auto-generated method stub
				return oldcategory.getAddr();
			}
			
			@Override
			public void addChild(IEntry child) {
				// TODO Auto-generated method stub
				
			}
			
			@Override
			public String[] getColumnLabels() {
				// TODO Auto-generated method stub
				return oldcategory.getColumnLabels();
			}
			
			@Override
			public int[] getColumnDefaultWidths() {
				// TODO Auto-generated method stub
				return oldcategory.getColumnDefaultWidths();
			}
			
			@Override
			public int getColumnCount() {
				// TODO Auto-generated method stub
				return oldcategory.getColumnCount();
			}

			@Override
			public String getDescription() {
				// TODO Auto-generated method stub
				return oldcategory.getDescription();
			}

			@Override
			public void setDescription(String description) {
				// TODO Auto-generated method stub
				
			}

			@Override
			public void setBitLength(String bitLength) {
				// TODO Auto-generated method stub
				
			}

			@Override
			public String getBitLength() {
				// TODO Auto-generated method stub
				return null;
			}
		};
		
		
		
		
		return newCategory;
	}
	

	/**
	 * 获取当前芯片的寄存器列表
	 * */
	public List<ICategory> getCategorys(ChiponThread fThread){
		IProject project=null;
		if(fThread!=null)
			project =((ChiponDebugTarget)fThread.getDebugTarget()).getfProject();
		String chip;
		List<ICategory> categorys = null;  
		if (project != null && project.exists()) {
			ProjectPropertyManager ppm = ProjectPropertyManager.getPropertyManager(project);
			ChipOnProjectProperties fTargetProps = ppm.getProjectProperties();
			chip = fTargetProps.getChipName();// 获取当前项目的芯片型号
			IDeviceDescription des = ChipOniohDeviceDescriptionProvider.getDefault().getDeviceDescription(chip);
			categorys = des.getCategories();
		}
		return categorys;
	}
	
	
	/**
	 * 2017-3-18 wanfz添加
	 * @param thread
	 * @param name 寄存器名称
	 * @return
	 */
	
	/**
	 * 获取所有的寄存器的值
	 */
	
	public List<ChiponCurrencyRegisterData> getAllChiponCurrencyRegisterData(ChiponThread fThread){
		List<ChiponCurrencyRegisterData> registerAllDatas = new ArrayList<>();
		List<ICategory> categories = getCategorys(fThread);
		List<IEntry> entryList = categories.get(0).getChildren();
		for(IEntry entry :entryList){
			if(entry instanceof Register){
				Register register = (Register)entry;
				List<ChiponCurrencyRegisterData> chiponDatasChildren = new ArrayList<>();
				ChiponSingleRegisterCommandResult registerResult = 
						(ChiponSingleRegisterCommandResult)fThread.sendCommand(
								new ChiponSingleRegisterCommand("x /w "+register.getAddr()));
				if(register.hasChildren()){
					for(IEntry chipBit : register.getChildren()){
						int bitLength = Integer.parseInt(chipBit.getBitLength());  //位宽
						int startLocation = Integer.parseInt(chipBit.getAddr());	//位的起始位置
						
						//解析单个bit的值
						int num = 0;  //比较位
						for(int i = 0; i < bitLength; i++){
							num += 1<<i;
						}
						int bitValue = ((registerResult.value&(num<<startLocation))>>startLocation)&num;									
						ChiponCurrencyRegisterData chiponData=new ChiponCurrencyRegisterData(
								chipBit.getName(), Integer.toHexString(bitValue), Integer.toString(bitValue));
					
						chiponDatasChildren.add(chiponData);
					}
				}
				ChiponCurrencyRegisterData data = new ChiponCurrencyRegisterData(
						register.getName(), Integer.toHexString(registerResult.value), Integer.toString(registerResult.value));
				data.setChiponDataList(chiponDatasChildren);
				registerAllDatas.add(data);
			}
		}
		return registerAllDatas;
	}
	
	public ChiponCurrencyRegisterData getSingleChiponCurrencyRegisterData(ChiponThread fThread, String name){
//		ChiponCurrencyRegisterData data = new ChiponCurrencyRegisterData(name, "", "");
		ChiponCurrencyRegisterData data = null;
		List<ICategory> categories = getCategorys(fThread);
		List<IEntry> entryList = categories.get(0).getChildren();
		List<ChiponCurrencyRegisterData> chiponDatasChildren=new ArrayList<ChiponCurrencyRegisterData>();
		for (IEntry entry : entryList) {
			if (entry instanceof Register) {
				final Register oldRegister = (Register) entry;
				if(oldRegister.getName().trim().equals(name)){
					String address = oldRegister.getAddr();
					//获取单个寄存器的值
					ChiponSingleRegisterCommandResult registerResult = 
							(ChiponSingleRegisterCommandResult)fThread.sendCommand(new ChiponSingleRegisterCommand("x /w "+address));
					//获取SFR寄存器内部bit数据
					for(IEntry chipBit : oldRegister.getChildren()){
						int bitLength = Integer.parseInt(chipBit.getBitLength());  //位宽
						int startLocation = Integer.parseInt(chipBit.getAddr());	//位的起始位置
						
						//解析单个bit的值
						int num = 0;  //比较位
						for(int i = 0; i < bitLength; i++){
							num += 1<<i;
						}
						int bitValue = ((registerResult.value&(num<<startLocation))>>startLocation)&num;									
						ChiponCurrencyRegisterData chiponData=new ChiponCurrencyRegisterData(
								chipBit.getName(), Integer.toHexString(bitValue), Integer.toString(bitValue));
					
						chiponDatasChildren.add(chiponData);
					}
					data = new ChiponCurrencyRegisterData(
							name, Integer.toHexString(registerResult.value), Integer.toString(registerResult.value));
					data.setChiponDataList(chiponDatasChildren);
					break;
				}
			}
		}	
		return data;
	}
	
	
	
	
	
	
	

}
