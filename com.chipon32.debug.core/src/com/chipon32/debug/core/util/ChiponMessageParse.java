package com.chipon32.debug.core.util;

import java.util.ArrayList;
import java.util.List;
import java.util.StringTokenizer;

/**
 * 解析Socket返回的字符串
 * <AUTHOR>
 */
public class ChiponMessageParse {

	/**
	 * 
	 * @param message的格式应该为string$string$string$string..
	 * @return
	 */
	public static List<String> parseEventMessage(String message){
		List<String> list = new ArrayList<String>();
		if(message != null && !"".equals(message)){
			StringTokenizer st = new StringTokenizer(message, "$");  //以$符分解字符串
			while(st.hasMoreTokens()){
				list.add(st.nextToken());
			}
		}
		return list;
	}
	
	/**
	 * 
	 * @param message的格式应该为string|string|string|string..
	 * @return
	 */
	public static List<String> parseLocalVariableMessage(String message){
		List<String>  list = new ArrayList<String>();
		if(message != null && !"".equals(message)){
			StringTokenizer st = new StringTokenizer(message, "|");
			while(st.hasMoreTokens()){
				list.add(st.nextToken());
			}
		}
		return list;
		
	}
	
	/**
	 * @param text
	 * @return text中"{"的起始所在下标
	 */
	public static int getBracketBeginNum(String text){
		if(text.contains("{")){
			return text.indexOf("{");
		}
		return -1;
	}
	
	/**
	 * @param list
	 * @return list中"{"的起始所在下标
	 */
	public static int getBracketBeginNum(List<String> list){
		for(int i=0;i<list.size();i++){
			String string = list.get(i);
			if(string.contains("{")){
				return i;
			}
		}
		return -1;
	}
	
}

