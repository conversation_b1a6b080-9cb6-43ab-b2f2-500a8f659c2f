package com.chipon32.debug.core.util;

import java.util.List;

import com.chipon32.debug.core.model.ChiponEEpromData;
import com.chipon32.debug.core.model.ChiponEEpromDatas;
import com.chipon32.debug.core.model.ChiponGroupEEpromData;

/***
 *<AUTHOR>
 *
 ***/
public class PackEEpromDataList {
	/**
	 * RomData的List转化成ChiponGroupRomData的List
	 * @param romDataList
	 * @return
	 */
	public ChiponEEpromDatas packList(List<ChiponEEpromData> eepromDataList){
		
		if(eepromDataList == null||eepromDataList.size() == 0) return null;
		ChiponEEpromDatas eepromDatas=new ChiponEEpromDatas();
		ChiponGroupEEpromData groupEEpromData = new ChiponGroupEEpromData();
		
		//确定第一个元素所在位置
		ChiponEEpromData firstEEpromData =  eepromDataList.get(0);
		groupEEpromData.setRowNum(firstEEpromData.getRomNum());
		int location = Integer.parseInt(firstEEpromData.getAddress().charAt(firstEEpromData.getAddress().length()-1)+"", 16);
		for(int i = 1; i <= location; i++){
			ChiponEEpromData eEpromData = new ChiponEEpromData((Integer.parseInt(firstEEpromData.getAddress(),16)-i)+"", "--");
			groupEEpromData.addEEpromData(eEpromData);
		}
		for(ChiponEEpromData eepromData : eepromDataList){
			
//			System.out.println(eepromData.getColumnNum());
//			System.out.println(groupEEData.getColumnNum());
//			System.out.println(eepromData.getColumnNum());
			if(eepromData.getRomNum().equals(groupEEpromData.getRowNum())){
				groupEEpromData.addEEpromData(eepromData);
			}else{
				eepromDatas.addGroupEEData(groupEEpromData);
				groupEEpromData = new ChiponGroupEEpromData();
				groupEEpromData.setRowNum(eepromData.getRomNum());
				groupEEpromData.addEEpromData(eepromData);
			}
		}
		eepromDatas.addGroupEEData(groupEEpromData);
		return eepromDatas;
		
	}
}
