package com.chipon32.debug.core.util;

import java.util.List;

import com.chipon32.debug.core.model.ChiponGroupRomData;
import com.chipon32.debug.core.model.ChiponRomData;
import com.chipon32.debug.core.model.ChiponRomDatas;

/**
 * <AUTHOR> @since 2013-6-5 上午10:33:45
 */
public class PackRomDataList {
	/**
	 * RomData的List转化成ChiponGroupRomData的List
	 * @param romDataList
	 * @return
	 */
	public ChiponRomDatas packList(List<ChiponRomData> romDataList){
		if(romDataList == null||romDataList.size() == 0) 
			return null;
		
		ChiponRomDatas romDatas = new ChiponRomDatas();
		ChiponGroupRomData groupRomData = new ChiponGroupRomData();
		
		//确定第一个元素所在位置
		ChiponRomData firstRomData = romDataList.get(0);
		groupRomData.setRowNum(firstRomData.getRomNum());
		//获取地址最后一位的元素，判断所在列，前面补空
		int location = Integer.parseInt(firstRomData.getAddress().charAt(firstRomData.getAddress().length()-1)+"",16);
		//System.out.println(firstRomData.getAddress());
		for(int i = 1; i<=location; i++){
			long address = (Long.parseLong(firstRomData.getRomNum(),16)+i-1);
			ChiponRomData romData = new ChiponRomData(Long.toHexString(address), "--");
			//System.out.println(romData.getAddress() +"  "+romData.getValue());
			groupRomData.addRomData(romData);
		}
		
		boolean isFindNameDoSwitch=true;
		for(int i=0;i<romDataList.size(); i++)
		{
			ChiponRomData romData= romDataList.get(i);
//			System.out.println("内存行数："+romData.getRomNum()+ "排布：" + groupRomData.getRowNum());
			if(romData.getRomNum().equals(groupRomData.getRowNum())){
				groupRomData.addRomData(romData);
			}else{
				romDatas.addGroupRomData(groupRomData);
				groupRomData = new ChiponGroupRomData();
				groupRomData.setRowNum(romData.getRomNum());
				groupRomData.addRomData(romData);
				isFindNameDoSwitch=true;
			}		
			//------------------------------------------------------------------------
			//------------------------------------------------------------------------
			if(isFindNameDoSwitch==true){
				boolean isNeedVirule=false;
				
				for(int j=0;j< 16-location;j++)
				{
					if((i+j) < romDataList.size())				{
					if(romDataList.get(i+j).getIsHaveName())	{
						isNeedVirule=true;
						break;
					}
					}
				}	
				if(isNeedVirule)
				{
					ChiponGroupRomData groupVarName = new ChiponGroupRomData();		
					groupVarName.setRowNum("Symbol");
					groupVarName.setIs_virtual_lineGroup(true);
					for(int k = 1; k<=location; k++){
						ChiponRomData skipData = new ChiponRomData("  ", "  ");						
						groupVarName.addRomData(skipData);
					}
					for(int j=0;j< 16-location;j++)
					{
						if((i+j) < romDataList.size()){
							if(romDataList.get(i+j).getIsHaveName())
							{
								ChiponRomData skipData = new ChiponRomData("  ", romDataList.get(i+j).getName());			
								groupVarName.addRomData(skipData);
							}
							else
							{
								ChiponRomData skipData = new ChiponRomData("  ", "  ");			
								groupVarName.addRomData(skipData);
							}
						}
					}
					romDatas.addGroupRomData(groupVarName);
				}
				location=0;
				isFindNameDoSwitch=false;
			}
		}
		
		romDatas.addGroupRomData(groupRomData);
		return romDatas;
		
	}
}
