package com.chipon32.debug.core.elf;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

import org.eclipse.core.runtime.Path;

import com.chipon32.chiponide.core.paths.win32.SystemPathsWin32;
import com.chipon32.debug.core.elf.dwarf.DwarfParse;
import com.chipon32.debug.core.elf.stabs.Stabs;

public class ELFLoadAndParser {
	
	public static  List<DebugGlobalSymbol> globalSymbols;  //记录elf文件中全局符号对象
	static File ELFFile;  //记录上一次的elf文件

	public static void  elfParser(File elfFile) {
		String elfFilePath = elfFile.getAbsolutePath();
		if(globalSymbols==null) {//首次解析
			globalSymbols = new ArrayList<>();
		}else {
			//判断ELF文件是否变动了，如果变动了，则重新解析ELF文件
			if(ELFFile.getAbsolutePath().equals(elfFilePath)) {//相同文件，则比较文件内容是否改变；不同文件则直接往下解析
				if(compareFile(ELFFile, elfFile)) {
					return;
				}
			}
		}
		
		globalSymbols.clear();
		ELFFile = new File(elfFilePath);
		//判断是stabs调试格式还是dwarf调试格式
		boolean isDwarf = false;  //默认非dwarf调试格式
		Path chainToolPath = (Path) SystemPathsWin32.getWinKF32BasePath();
		String chiponToolpath = chainToolPath.toOSString();
		String strCmd = chiponToolpath + "\\ccr1_issue\\bin\\kf32-objdump.exe";
//		String command = strCmd + " -h "+elfFilePath";
		String[] command = {strCmd,
				"-h", 
				elfFilePath
		};
		
		String line = "";
		BufferedReader buffReader = null;
		Process process;
		try {
			process = Runtime.getRuntime().exec(command);  //查看ELF文件的段信息，以此区分是stabs调试格式还是dwarf格式
			buffReader = new BufferedReader(new InputStreamReader(process.getInputStream()));
			while((line = buffReader.readLine()) != null){
//				System.out.println(line);
				if(line.contains(".debug_info")) {
					isDwarf = true;
					break;
				}
			}
			int proc = process.waitFor(); //状态: 0表示执行成功，其他表示没有执行成功
			if(proc!=0){  //cmd执行失败
				System.out.println("执行cmd失败.");
				return;
			}
			
			buffReader.close();
		} catch (IOException e) {
			e.printStackTrace();
		} catch (InterruptedException e) {
			e.printStackTrace();
		} 
		
		if(isDwarf) {//Dwarf调试格式
			DwarfParse dwarf = new DwarfParse(elfFilePath);
			dwarf.parse();
			globalSymbols = dwarf.getAllGlobalVariables();
			
		}else {//stabs调试格式
			try {
				Stabs stabs = new Stabs(elfFilePath);
				stabs.parse(new DebugSymsRequestor());
				globalSymbols = stabs.getGlobalSymbols();
				
			} catch (IOException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
		
		//打印观察。。。
//		if(!globalSymbols.isEmpty()) {
//			for(DebugGlobalSymbol globalVar:globalSymbols) {
//				DebugType varDebugType = globalVar.getDebugtype();
//				if(varDebugType!=null) {
//					System.out.println(globalVar.getVariableAddress()+" "+globalVar.getVariableName() +" "+ varDebugType.toString());
//				}
//			}
//		}
		
		//全局变量按照地址排序
		if(!globalSymbols.isEmpty()) {
			Collections.sort(globalSymbols, new Comparator<DebugGlobalSymbol>() {

				@Override
				public int compare(DebugGlobalSymbol o1, DebugGlobalSymbol o2) {
					// TODO Auto-generated method stub
					return o1.getVariableAddress().compareTo(o2.getVariableAddress());
				}
				
			});
		}
		
	}

	/**
	 * 比较两个文件是否改变
	 * @param elfFile1
	 * @param elfFile2
	 * @return
	 * @throws IOException 
	 * @throws FileNotFoundException 
	 */
	private static boolean compareFile(File elfFile1, File elfFile2) {
		if (elfFile1.length() != elfFile2.length()) {
            return false;
        }
        if (elfFile1.lastModified() != elfFile2.lastModified()) {
            return false;
        }
        
        // 检查 ELF 文件的 magic number 是否相同
        try (FileInputStream fis1 = new FileInputStream(elfFile1);
             FileInputStream fis2 = new FileInputStream(elfFile2);
        ) {
            byte[] magicNumber1 = new byte[4];
            byte[] magicNumber2 = new byte[4];
            fis1.read(magicNumber1);
            fis2.read(magicNumber2);

            for (int i = 0; i < 4; i++) {
                if (magicNumber1[i] != magicNumber2[i]) {
                    return false;
                }
            }
        }catch(IOException e) {
        	e.printStackTrace();
        }
       
        // 比较文件内容
        try (FileInputStream fis1 = new FileInputStream(elfFile1);
             FileInputStream fis2 = new FileInputStream(elfFile2);
        ) {
            byte[] buffer1 = new byte[4096];
            byte[] buffer2 = new byte[4096];
            int bytesRead1 = -1;
            int bytesRead2 = -1;
            while ((bytesRead1 = fis1.read(buffer1)) != -1 && (bytesRead2 = fis2.read(buffer2)) != -1) {
        		 if (!compareBuffers(buffer1, buffer2, bytesRead1, bytesRead2)) {
                     return false;
                 }
            }

            if (bytesRead1 != -1 || bytesRead2 != -1) {
                return false;
            }
        }catch(IOException e) {
        	e.printStackTrace();
        }
       
        return true;
	}
	
	private static boolean compareBuffers(byte[] buffer1, byte[] buffer2, int length1, int length2) {
        if (length1 != length2) {
            return false;
        }
        for (int i = 0; i < length1; i++) {
            if (buffer1[i] != buffer2[i]) {
                return false;
            }
        }
        return true;
    }

	public static List<IVariableEntry> getGlobalVariablesTree(File elfFile){
		//解析ELF文件
		elfParser(elfFile);
		
		List<IVariableEntry> globalVariablesList = new ArrayList<>();
		int globalSymbolsSize = globalSymbols.size();
		for(int i=0; i<globalSymbolsSize; i++) {
			DebugGlobalSymbol globalVariable = globalSymbols.get(i);
			DebugType variableDebugType = globalVariable.getDebugtype();
			if(variableDebugType==null) {//过滤类型为空的全局变量
				continue;
			}
			
			String variableName = globalVariable.getVariableName();
			String address = globalVariable.getVariableAddress();
			IVariableEntry variable = toTreeEntry(variableName, address, variableDebugType, false);
			if(variable!=null) {//过滤空变量对象
				globalVariablesList.add(variable);
//				System.out.println(address+" "+variableName +" tlen:"+variable.getTypeLen()+" vlen:"+variable.getVariableLen()+" isBit:"+variable.isBitVariable()+" offset:"+variable.getOffset()+ " "+variableDebugType.toString());
			}
			
		}//end for
		
		return globalVariablesList;
	}

	/**
	 * 将全局变量对象转化为树节点显示对象
	 * @param globalVariable
	 * @return
	 */
	private static IVariableEntry toTreeEntry(String variableName, String address, DebugType variableDebugType, boolean isPointer) {
		boolean pointer = isPointer;
		
		if(variableDebugType instanceof DebugArrayType) {
			DebugArrayType arrayType = (DebugArrayType) variableDebugType;
			int varDebugTypeLen = arrayType.debugTypeSize();
			Variable arrayVarEntry = new Variable(variableDebugType.toString(),variableName,varDebugTypeLen*8/arrayType.getSize(),varDebugTypeLen*8,address,pointer);
			arrayVarEntry.setArray(true);
			
			//数组元素类型
			DebugType varDebugType = arrayType.getComponentType();
			if(varDebugType!=null) {
				for(int i=0; i<arrayType.getSize(); i++) {
					String arrVarElemName = variableName+"["+i+"]";
					String elemAddress = Integer.toHexString(Integer.parseInt(address, 16) + i*varDebugType.debugTypeSize());
					arrayVarEntry.addChild(toTreeEntry(arrVarElemName,elemAddress,varDebugType,false));
				}
			}
			
			return arrayVarEntry;
		}else if(variableDebugType instanceof DebugDerivedType){
			DebugDerivedType derived = (DebugDerivedType) variableDebugType;
			if(derived instanceof DebugPointerType) {
				pointer = true;
			}
			
			DebugType varDebugType = derived.getComponentType();
			if(varDebugType!=null) {
				if(varDebugType instanceof DebugFunctionType) { //递归定义，变量不进行数值监控
					return null;
				}else {
					return toTreeEntry(variableName, address, varDebugType, pointer);
				}
			}
			
		}else if(variableDebugType instanceof DebugBaseType) {
			DebugBaseType varDebugType = (DebugBaseType) variableDebugType;
			if(varDebugType.getTypeName().equals("void")) {  //该变量没有返回值，不进行监控
				return null;
			}else {
				return (new Variable(varDebugType.getTypeName(),variableName,varDebugType.sizeof()*8,varDebugType.sizeof()*8,address,pointer));
			}
			
		
		}else if(variableDebugType instanceof DebugStructType) {
			DebugStructType structType = (DebugStructType) variableDebugType;
			Variable structVarEntry = new Variable(structType.toString(),variableName,structType.getSize()*8,structType.getSize()*8,address,pointer);
			
			boolean isUnion = ((DebugStructType) variableDebugType).isUnion();
			DebugField[] structFields = structType.getDebugFields();
			String lastFieldAddress = address;
			for(int i=0; i<structFields.length; i++) {
				DebugField field = structFields[i];
				String fieldName = field.getName();
				DebugType fieldDebugType = field.getDebugType();
				int fieldSize = field.getBits();
				int addressOffset = field.getOffset();
				String fieldAddress = lastFieldAddress;
				
				if(!isUnion) {
					fieldAddress = Integer.toHexString(Integer.parseInt(address, 16) + addressOffset/8);
				}
				
				boolean isBitVar = false;
				if(fieldSize%8!=0) {//bit位变量
					isBitVar = true;
				}
				
				if(structType.equals(fieldDebugType)) {
					//structVarEntry.addChild(new Variable(structType.toString(),variableName,structType.getSize()*8,structType.getSize()*8,fieldAddress,pointer));
					//递归定义，该变量不进行数值监控
					lastFieldAddress = fieldAddress;
					continue;
				}else if(fieldDebugType instanceof DebugDerivedType){
					DebugType bufDebugType = ((DebugDerivedType)fieldDebugType).getComponentType();
					if(bufDebugType==null || structType.equals(bufDebugType) ) {
						//递归定义，该变量不进行数值监控
						lastFieldAddress = fieldAddress;
						continue;
					}
				}
				
				IVariableEntry childVarEntry = toTreeEntry(fieldName,fieldAddress,fieldDebugType, false);
				if(childVarEntry!=null) {
					childVarEntry.setBitVariable(isBitVar);
					childVarEntry.setOffset(addressOffset);
					childVarEntry.setVariableLen(fieldSize);
					
					structVarEntry.addChild(childVarEntry);
				}
				lastFieldAddress = fieldAddress;
			}//end for
			
			return structVarEntry;
		}else if(variableDebugType instanceof DebugEnumType) {
			DebugEnumType enumType = (DebugEnumType) variableDebugType;
			Variable enumVarEntry = new Variable(enumType.toString(), variableName, 32, 32, address, pointer);
			
			DebugEnumField[] enumFields = enumType.getDebugEnumFields();
			for(int i = 0; i<enumFields.length; i++) {
				String enumFieldName = enumFields[i].getName();
				enumVarEntry.addChild(new Variable("unsigned int", enumFieldName, 32, 32, address, false));
			}
			
			return enumVarEntry;
			
		}else if(variableDebugType instanceof DebugFunctionType) {
//			DebugType bufVarDebugType = ((DebugFunctionType)variableDebugType).getReturnType() ;
//			if(bufVarDebugType!=null) {
//				return toTreeEntry(variableName, address, bufVarDebugType, pointer);
//			}
			//函数类型不进行监控
			return null;
		}
		
		return null;
	}

	
	
	public List<DebugGlobalSymbol> getGlobalSymbols() {
		return globalSymbols;
	}

	public void setGlobalSymbols(List<DebugGlobalSymbol> globalSymbols) {
		this.globalSymbols = globalSymbols;
	}
	
	
	
}
