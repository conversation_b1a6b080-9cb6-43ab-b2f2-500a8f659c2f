package com.chipon32.debug.core.elf;

import java.util.ArrayList;
import java.util.List;

public class Variable implements IVariableEntry {
	
	private String typeName;
	private String varName;
	private int typeLen;  //变量类型的字节数*8  => bit位长度
	private int varLen;  //变量bit位长度
	private String startAddr; //变量地址
	private boolean pointer = false;//是否为指针类型
	
	private boolean isArray = false;
	
	private boolean isBitVariable; //是否为bit位变量
	private int offset;//bit位变量的位偏移
	
	private String value;
	private boolean isSymbol = false; //区分是地址还是变量
	private int commandNum;
	
	private IVariableEntry parent;
	private List<IVariableEntry> children = new ArrayList<>();
	
	public Variable() {
		
	}
	
	public Variable(String tName,String vName, int tLen, int vLen, String sAddr, boolean isPointer){
		this.typeName = tName;
		this.varName = vName;
		this.typeLen = tLen;
		this.varLen = vLen;
		this.startAddr = sAddr;
		this.pointer = isPointer;
	}


	@Override
	public String getTypeName() {
		// TODO Auto-generated method stub
		return typeName;
	}

	@Override
	public void setTypeName(String typename) {
		// TODO Auto-generated method stub
		this.typeName = typename;
	}

	@Override
	public int getTypeLen() {
		// TODO Auto-generated method stub
		return typeLen;
	}

	@Override
	public void setTypeLen(int typeLen) {
		// TODO Auto-generated method stub
		this.typeLen = typeLen;
	}

	@Override
	public String getName() {
		// TODO Auto-generated method stub
		return varName;
	}

	@Override
	public void setName(String name) {
		// TODO Auto-generated method stub
		this.varName = name;
	}

	@Override
	public String getAddress() {
		// TODO Auto-generated method stub
		return startAddr;
	}

	@Override
	public void setAddress(String address) {
		// TODO Auto-generated method stub
		this.startAddr = address;
	}

	@Override
	public int getVariableLen() {
		// TODO Auto-generated method stub
		return varLen;
	}

	@Override
	public void setVariableLen(int varLen) {
		// TODO Auto-generated method stub
		this.varLen = varLen;
	}

	@Override
	public void setChildren(List<IVariableEntry> children) {
		// TODO Auto-generated method stub
		this.children = children;
	}

	@Override
	public List<IVariableEntry> getChildren() {
		return children;
	}

	@Override
	public IVariableEntry getParent() {
		// TODO Auto-generated method stub
		return parent;
	}

	@Override
	public void setParent(IVariableEntry parent) {
		// TODO Auto-generated method stub
		this.parent = parent;
	}

	@Override
	public void addChild(IVariableEntry child) {
		// TODO Auto-generated method stub
		if(child != null){
			children.add(child);
			child.setParent(this);
		}
	}

	public boolean isPointer() {
		return pointer;
	}

	public void setPointer(boolean pointer) {
		this.pointer = pointer;
	}

	@Override
	public boolean isBitVariable() {
		return isBitVariable;
	}

	@Override
	public void setBitVariable(boolean isBitVariable) {
		// TODO Auto-generated method stub
		this.isBitVariable = isBitVariable;
	}

	@Override
	public int getOffset() {
		// TODO Auto-generated method stub
		return offset;
	}

	@Override
	public void setOffset(int offset) {
		// TODO Auto-generated method stub
		this.offset = offset;
	}

	@Override
	public boolean isSymbol() {
		return isSymbol;
	}

	@Override
	public void setSymbol(boolean isSymbol) {
		this.isSymbol = isSymbol;
	}

	public String getValue() {
		return value;
	}

	public void setValue(String value) {
		this.value = value;
	}

	public int getCommandNum() {
		return commandNum;
	}

	public void setCommandNum(int commandNum) {
		this.commandNum = commandNum;
	}
	
	public boolean isArray() {
		return isArray;
	}

	public void setArray(boolean isArray) {
		this.isArray = isArray;
	}
	
	
	public String getVariableNameStr() {
		if(getParent()==null) {
			return getName();
		}else {
			String variableNameStr = getName();
			IVariableEntry parent = getParent();
			while(parent!=null) {
				if(!parent.isArray()) {
					variableNameStr = parent.getName()+"."+variableNameStr;
				}
				parent = parent.getParent();
			}
			
			return variableNameStr;
		}
	}
	
}
