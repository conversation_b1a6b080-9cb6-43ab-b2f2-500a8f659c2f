package com.chipon32.debug.core.elf;

import java.util.List;

public interface IVariableEntry {
	public String getTypeName();
	public void setTypeName(String typename);
	
	public int getTypeLen();
	public void setTypeLen(int typeLen);

	public String getName();
	public void setName(String name);
	
	public String getAddress();
	public void setAddress(String address);
	
	public int getVariableLen(); 
	public void setVariableLen(int varLen);

	public void setChildren(List<IVariableEntry> children);	
	public List<IVariableEntry> getChildren(); 

	public IVariableEntry getParent();
	public void setParent(IVariableEntry parent);
	
	public void addChild(IVariableEntry child);
	
	public boolean isBitVariable();
	public void setBitVariable(boolean isBitVariable);
	
	public int getOffset();
	public void setOffset(int offset);
	
	public boolean isSymbol();
	public void setSymbol(boolean isSymbol);
	
	public void setCommandNum(int currCommandNum);
	public int getCommandNum();
	
	public String getVariableNameStr();
	public boolean isArray();
}
