/*******************************************************************************
 * Copyright (c) 2000, 2016 QNX Software Systems and others.
 *
 * This program and the accompanying materials
 * are made available under the terms of the Eclipse Public License 2.0
 * which accompanies this distribution, and is available at
 * https://www.eclipse.org/legal/epl-2.0/
 *
 * SPDX-License-Identifier: EPL-2.0
 *
 * Contributors:
 *     QNX Software Systems - Initial API and implementation
 *******************************************************************************/

package com.chipon32.debug.core.elf;


/**
 * DebugType
 *
 */
public class DebugType {

	/**
	 *
	 */
	protected DebugType() {
	}

	@Override
	public String toString() {
		StringBuilder sb = new StringBuilder();
		if (this instanceof DebugArrayType) {
			DebugArrayType arrayType = (DebugArrayType) this;
			int size = arrayType.getSize();
			DebugType type = arrayType.getComponentType();
			sb.append(type.toString());
			sb.append(" [").append(size).append("]"); //$NON-NLS-1$
		} else if (this instanceof DebugDerivedType) {
			DebugDerivedType derived = (DebugDerivedType) this;
			DebugType component = derived.getComponentType();
			if (component instanceof DebugStructType) {
				DebugStructType structType = (DebugStructType) component;
				sb.append(structType.getName());
			} else if (component != null) {
				sb.append(component.toString());
			}
			
			if (this instanceof DebugPointerType) {
				sb.append(" *"); //$NON-NLS-1$
			} else if (this instanceof DebugReferenceType) {
				sb.append(" &"); //$NON-NLS-1$
			} else if (this instanceof DebugCrossRefType && component == null) {
				DebugCrossRefType crossRef = (DebugCrossRefType) this;
				sb.append(crossRef.getCrossRefName());
				//sb.append(crossRef.getName());
			}
		} else if (this instanceof DebugBaseType) {
			DebugBaseType base = (DebugBaseType) this;
			String typeName = base.getTypeName();
			sb.append(typeName);
			sb.append(" "+base.sizeof()+" "); //ÀàÐÍ³¤¶È
//			sb.append(" "+base.sizeof()+" "+base.isUnSigned());
		} else if (this instanceof DebugFunctionType) {
			DebugFunctionType function = (DebugFunctionType) this;
			DebugType type = function.getReturnType();
			sb.append(type.toString());
			sb.append(" (*())"); //$NON-NLS-1$
		} else if (this instanceof DebugEnumType) {
			sb.append("enum ");
			DebugEnumType enumarator = (DebugEnumType) this;
			DebugEnumField[] fields = enumarator.getDebugEnumFields();
			sb.append(enumarator.getName()).append(" {"); //$NON-NLS-1$ //$NON-NLS-2$
			for (int i = 0; i < fields.length; i++) {
				if (i > 0) {
					sb.append(',');
				}
				sb.append(' ').append(fields[i].getName());
				sb.append(" = ").append(fields[i].getValue()); //$NON-NLS-1$
			}
			sb.append(" }"); //$NON-NLS-1$
		} else if (this instanceof DebugStructType) {
			DebugStructType struct = (DebugStructType) this;
//			sb.append(struct.getSize()+" ");
			if (struct.isUnion()) {
				sb.append("union "); //$NON-NLS-1$
			} else {
				sb.append("struct "); //$NON-NLS-1$
			}
			sb.append(struct.getName()).append(" {"); //$NON-NLS-1$
			DebugField[] fields = struct.getDebugFields();
			for (int i = 0; i < fields.length; i++) {
				if (i > 0) {
					sb.append(';');
				}
				
				sb.append(' ').append(fields[i].getDebugType());
				sb.append(' ').append(fields[i].getName());
				sb.append(' ').append(fields[i].getBits());
				sb.append(' ').append(fields[i].getOffset());
			}
			sb.append(" }"); //$NON-NLS-1$
		} else if (this instanceof DebugUnknownType) {
			DebugUnknownType unknown = (DebugUnknownType) this;
			sb.append(unknown.getName());
		}else {
			sb.append("unknown");
		}
		
		return sb.toString();
	}
	
	/**
	 * type length
	 * @return
	 */
	public int debugTypeSize() {
		if (this instanceof DebugArrayType) {
			DebugArrayType arrayType = (DebugArrayType) this;
			DebugType arrElemType = arrayType.getComponentType();
			return (arrayType.getSize())*(arrElemType.debugTypeSize());
			
		} else if (this instanceof DebugDerivedType) {
			DebugDerivedType derived = (DebugDerivedType) this;
			DebugType component = derived.getComponentType();
			if (component != null) {
				return component.debugTypeSize();
			}
			
		} else if (this instanceof DebugBaseType) {
			DebugBaseType base = (DebugBaseType) this;
			return base.sizeof();
			
		} else if (this instanceof DebugStructType) {
			DebugStructType struct = (DebugStructType) this;
			return struct.getSize();
			
		} else if(this instanceof DebugEnumType) {
			return 4;
		}
		
		return 0;
	}
	
	public String getDebugTypeName() {
		if (this instanceof DebugArrayType) {
			DebugArrayType arrayType = (DebugArrayType) this;
			DebugType arrElemType = arrayType.getComponentType();
			return arrElemType.getDebugTypeName();
			
		} else if (this instanceof DebugDerivedType) {
			DebugDerivedType derived = (DebugDerivedType) this;
			DebugType component = derived.getComponentType();
			if (component != null) {
				return component.getDebugTypeName();
			}
			
		} else if (this instanceof DebugBaseType) {
			DebugBaseType base = (DebugBaseType) this;
			return base.getTypeName();
			
		} else if (this instanceof DebugStructType) {
			DebugStructType struct = (DebugStructType) this;
			return struct.getName();
			
		} else if(this instanceof DebugEnumType) {
			DebugEnumType enumType = (DebugEnumType) this;
			return enumType.getName();
			
		}else {
			return "";
		}
		
		return "";
	}

	@Override
	public boolean equals(Object obj) {
//		return super.equals(obj);
		
		return (this.toString().equals(((DebugType)obj).toString()));
		
	}
	
	
}
