package com.chipon32.debug.core.protocol;

import java.util.List;

public class ChiponWatchCommandResult extends ChiponCommandResult {
	public String value;
	
	public ChiponWatchCommandResult(List<String> result) {
		super(result);
		
		//  特殊样例，包括 }  {  ,但不是匹配的结构，而是可能为字符数组
//		result.remove(1);
//		result.add("$17 = \"\\001\b\\367A\020${\004E5\\202\\034K\\035K\\344\\326T\\205\\035\\\\000\\000\\200%\\000@\\002E(L\\035\\\\000\\000\\200%\\000@Q\\020\\256{\\345\\230\\200{\\003C\\344\\324 \\327\\354\\240\\035\\\\000\\000\\377\\377\\377\\360\\003E\\000\\070\\006\\361,N\\035\\\\000\\000\f\\000\\000@,L\\035\\\\003E\\000\\070\\006\\361)N\\035\\\\000\\000\f\\000\\000@)L\\035\\Q\\020\\256{\\205\\230\\004|Q\\020@\\325\\035\\Q\\020\\256{\\205\\230\\001|Q\\020@\\325\\035\\\\276\\200\"");
		//处理掉char类型后面的字符'';
		StringBuffer datas = new StringBuffer();
		int k = -1;	//换行之后叠加,判断行
		for(int i = 0; i < result.size(); i++){
			//############################## 发现内容行
			if(result.get(i).startsWith("$")){
				
				String str = result.get(i).substring(result.get(i).indexOf("=")+1).trim();
				// 过滤单引号的字符
				int start = str.indexOf("'");
				while(start != -1){
					int end = str.indexOf("'", start +1);
					// 普通字符
					if(end==start+2)
					{						
						str = str.substring(0, start)+str.substring(end+1);
						start = str.indexOf("'");
						continue;
					}
					// 8进制表达
					if( end==start+5  && str.charAt(start+1)=='\\' )
					{
						if(	(str.charAt(start+2)>='0' && str.charAt(start+2)<='3')
								&&(str.charAt(start+3)>='0' && str.charAt(start+3)<='7')
								&&(str.charAt(start+4)>='0' && str.charAt(start+4)<='7')
							)						{
							str = str.substring(0, start)+str.substring(end+1);
							start = str.indexOf("'");
							continue;
						}
					}
					// 转移字符
					if( end==start+3  && str.charAt(start+1)=='\\' )
					{
						if(str.charAt(start+2)=='e'|| str.charAt(start+2)=='a'||str.charAt(start+2)=='b'||str.charAt(start+2)=='t'
								||str.charAt(start+2)=='n'||str.charAt(start+2)=='v'||str.charAt(start+2)=='f'||str.charAt(start+2)=='r')
						{
							str = str.substring(0, start)+str.substring(end+1);
							start = str.indexOf("'");
							continue;
						}
					}					
					break;
				}
				// 记录值列表，记录值序号
				datas .append(str); 
				k = i;
				
			}
			//##############################后面还有内容
			if(k != -1 && i > k){
				String s = result.get(i);
				int start = s.indexOf("'");
				while(start != -1){
					int end = s.indexOf("'", start +1);
					if(end==start+2)
					{
						s = s.substring(0, start)+s.substring(end+1);
						start = s.indexOf("'");
						continue;
					}
					break;
				}
				// 内容
				datas.append(s);
			}
			//#############################################
		}
		if(datas.length()==0)
			if(result!=null &&  result.size()>0 &&  result.get(0).contains("No global symbol"))
				value = "Err:No global symbol...";
			else
				value = "NULL";
		else 
			value = datas.toString();
	}

}
