/**
 * 
 */
package com.chipon32.debug.core.protocol;

import java.util.List;

/**
 * <AUTHOR>
 * 没特殊内容  就是ChiponCommand的 原型用法
 */
public class ChiponCustomCommand extends ChiponCommand{



	public ChiponCustomCommand(String request) {
		super(request);
		
	}

	@Override
	public ChiponCustomCommandResult createResult(List<String> resultText) {
		return new ChiponCustomCommandResult(resultText);
	}
	
	

}
