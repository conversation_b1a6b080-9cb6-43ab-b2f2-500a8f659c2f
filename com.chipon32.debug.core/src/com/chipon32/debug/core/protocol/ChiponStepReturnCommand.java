/**
 * 
 */
package com.chipon32.debug.core.protocol;

import java.util.List;

/**
 * <AUTHOR>
 *
 */
public class ChiponStepReturnCommand extends ChiponCommand {

	public ChiponStepReturnCommand() {
		super("finish");
	}

	/* (non-Javadoc)
	 * @see com.chipon32.debug.core.protocol.ChiponCommand#createResult(java.lang.String)
	 */
	@Override
	public ChiponStopLocationCommandResult createResult(List<String> resultText) {
		return new ChiponStopLocationCommandResult(resultText);
	}

}
