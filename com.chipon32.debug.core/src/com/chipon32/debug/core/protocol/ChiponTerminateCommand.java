/**
 * 
 */
package com.chipon32.debug.core.protocol;


import java.util.List;

/**
 * <AUTHOR>
 *
 */
public class ChiponTerminateCommand extends ChiponCommand {

	
	public ChiponTerminateCommand() {
		super("quit");
	}

	/* (non-Javadoc)
	 * @see com.chipon32.debug.core.protocol.ChiponCommand#createResult(java.lang.String)
	 */
	@Override
	public ChiponCommandResult createResult(List<String> resultText) {
		return new ChiponCommandResult(resultText);
	}

}
