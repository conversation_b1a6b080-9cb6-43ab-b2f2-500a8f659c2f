package com.chipon32.debug.core.protocol;

import java.util.ArrayList;
import java.util.List;

/**
 * 
 * <AUTHOR>	局部变量的值
 *
 */
public class ChiponStackCommandResult extends ChiponCommandResult {

	public List<String> stackValue= new ArrayList<>();
	
	public ChiponStackCommandResult(List<String> response) {
		super(response);
		boolean begin = false; //数据记录开始值
		for(int i = 0; i < response.size(); i++){
			
			if(response.get(i).contains("=")){
				begin = true;
			}
			if(begin){
				begin = false;
				String str = response.get(i);
				//##################################
//				int start = str.indexOf("'");
//				while(start != -1){
				// 错误的字符串 end会小于start，会引起错误，不配对情况
//					int end = str.indexOf("'", start +1);
//					str = str.substring(0, start)+str.substring(end+1);
//					start = str.indexOf("'");
//				}
				int start = str.indexOf("'");
				while(start != -1)
				{
					int end = str.indexOf("'", start +1);
					// 普通字符
					if(end==start+2)
					{						
						str = str.substring(0, start)+str.substring(end+1);
						start = str.indexOf("'");
						continue;
					}
					// octal
					if( end==start+5  && str.charAt(start+1)=='\\' )
					{
						if(		  (str.charAt(start+2)>='0' && str.charAt(start+2)<='3')
								&&(str.charAt(start+3)>='0' && str.charAt(start+3)<='7')
								&&(str.charAt(start+4)>='0' && str.charAt(start+4)<='7'))
						{
							str = str.substring(0, start)+str.substring(end+1);
							start = str.indexOf("'");
							continue;
						}
					}
					// ESC character like \n \r \t \e \a
					if( end==start+3  && str.charAt(start+1)=='\\' )
					{
						if(str.charAt(start+2)=='e'|| str.charAt(start+2)=='a'||str.charAt(start+2)=='b'||str.charAt(start+2)=='t'
								||str.charAt(start+2)=='n'||str.charAt(start+2)=='v'||str.charAt(start+2)=='f'||str.charAt(start+2)=='r')
						{
							str = str.substring(0, start)+str.substring(end+1);
							start = str.indexOf("'");
							continue;
						}
					}
					break;					
				}
				//##################################
				if(str.contains("(gdb)")){
					str = str.replace("(gdb)", "");
				}
				stackValue.add(str);
			}
			
		}
	}
	

}
