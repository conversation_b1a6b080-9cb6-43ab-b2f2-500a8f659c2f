package com.chipon32.debug.core.protocol;

import java.util.ArrayList;
import java.util.List;

import com.chipon32.debug.core.model.ChiponRomData;

/**
 * <AUTHOR> @since 2013-6-1 上午10:21:25
 */
public class ChiponPrintMemoryCommandResult extends ChiponCommandResult{
	
	public List<ChiponRomData> romDataList;

	public ChiponPrintMemoryCommandResult(List<String> result) {
		super(result);
		List<ChiponRomData> romDatas = new ArrayList<ChiponRomData>();		
		int finddatestar=0;
		for(int i = 0; i < result.size(); i++)
		{
			if(result.get(i).contains("success"))
			{
				finddatestar=i+1;
				break;
			}			
		}
		// 是否成功
		if(finddatestar!=0){
			// 
			for(int i = finddatestar; i < result.size(); i++){
				// 属于地址开头的数据
				if(result.get(i).startsWith("0x")){
					
					String address = "";
					String[] datas = result.get(i).split(":");
					int bufferAddress=0;
					int offaddress=0;
					String bufferName=null;
					////////////////////////////////////////////////
					if(datas[0].contains("<")){
						address = datas[0].substring(0, datas[0].indexOf("<")-1).trim();						
						{
							//<bb> <ff+0x2>
							bufferAddress 	= Integer.parseInt(address.substring(2),16);
							bufferName 		= datas[0].substring(datas[0].indexOf("<")+1,datas[0].indexOf(">") ).trim();
							if(bufferName.contains("+"))
							{
								String off=bufferName.substring(bufferName.indexOf("+")+1);
								bufferName=bufferName.substring(0,bufferName.indexOf("+"));
								if(off.startsWith("0x")) 		offaddress =  Integer.parseInt(off.substring(2),16);
								else if(off.startsWith("0X")) 	offaddress =  Integer.parseInt(off.substring(2),16);
								else 							offaddress =  Integer.parseInt(off);
																
								bufferAddress=bufferAddress-offaddress;										
							}
						}
					}else{
						address = datas[0].trim();
					}
					String[] values = datas[1].trim().split("\\s");
					for(int k = 0; k < values.length; k++){
						Long addr = Long.parseLong(address.substring(2), 16);	//过滤地址开头的0x	
						ChiponRomData romData = new ChiponRomData(Long.toHexString(addr + k), values[k]);
						
						romData.setIsHaveName(false);
						if(bufferName!=null && (addr+k) ==bufferAddress ){
							romData.setIsHaveName(true);
							romData.setName(bufferName);
						}
						else if(offaddress>0)
						{
							if(romDatas.size()>=offaddress)
							{
								if(Integer.parseInt(romDatas.get(romDatas.size()-offaddress).getAddress(),16) == (bufferAddress)){
								romDatas.get(romDatas.size()-offaddress).setIsHaveName(true);
								romDatas.get(romDatas.size()-offaddress).setName(bufferName);
								}
							}
						}
						
						romDatas.add(romData);						
					}
				}
				
			}
		}
		romDataList = romDatas;
	}

}
