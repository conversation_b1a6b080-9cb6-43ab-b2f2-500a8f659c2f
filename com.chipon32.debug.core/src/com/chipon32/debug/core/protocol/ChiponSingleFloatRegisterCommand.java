package com.chipon32.debug.core.protocol;

import java.util.List;

/**
 * 
 * ²éÑ¯µ¥¸ö¼Ä´æÆ÷µÄÃüÁî 	x /Nu addr
 *
 */
public class ChiponSingleFloatRegisterCommand extends ChiponCommand {

	public ChiponSingleFloatRegisterCommand(String request) {
		super(request);
		// TODO Auto-generated constructor stub
	}

	@Override
	public ChiponCommandResult createResult(List<String> resultText) {
		
		return new ChiponSingleRegisterCommandResult(resultText);
	}

}
