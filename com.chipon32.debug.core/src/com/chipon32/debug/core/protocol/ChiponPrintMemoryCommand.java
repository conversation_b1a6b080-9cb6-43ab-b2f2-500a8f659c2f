package com.chipon32.debug.core.protocol;


import java.util.List;

/**
 * <AUTHOR> @since 2013-6-1 ионГ9:53:42
 */
public class ChiponPrintMemoryCommand extends ChiponCommand{

	public ChiponPrintMemoryCommand(String start, int length) {
		super("x /"+length+ "w "+start);
	}

	@Override
	public ChiponPrintMemoryCommandResult createResult(List<String> resultText) {
		return new ChiponPrintMemoryCommandResult(resultText);
	}

}
