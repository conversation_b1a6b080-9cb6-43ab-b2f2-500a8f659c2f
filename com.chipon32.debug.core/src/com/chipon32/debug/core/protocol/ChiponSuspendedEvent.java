package com.chipon32.debug.core.protocol;

import com.chipon32.debug.core.util.ChiponMessageParse;

public class ChiponSuspendedEvent extends ChiponRunControlEvent {

	public ChiponSuspendedEvent(String message) {
		super(message);
	}
	
	 public static boolean isEventMessage(String message) {
	        return ChiponMessageParse.parseEventMessage(message).get(0).trim()
					.equalsIgnoreCase("suspended");
	    }

}
