package com.chipon32.debug.core.protocol;


import java.util.List;

/**
 * 
 * <AUTHOR>	get gdb pid  for send sign to pause or kill 
 */
public class ChiponGdbIDCommand extends ChiponCommand {

	public ChiponGdbIDCommand(String command) {
		//super("shell tasklist /FI \"IMAGENAME eq kf32-gdb.exe\"  /FO LIST");
		//super("shell ps -C kf32-gdb");
		super(command);
//		super("shell echo $PPID");
	}

	@Override
	public ChiponGdbIDCommandResult createResult(List<String> resultText) {
		return new ChiponGdbIDCommandResult(resultText);
	}

}
