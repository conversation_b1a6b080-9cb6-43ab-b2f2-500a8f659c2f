/**
 * 
 */
package com.chipon32.debug.core.protocol;


import java.util.List;

/**
 * <AUTHOR>
 *
 */
public class ChiponStepOverCommand extends ChiponCommand {

	/**
	 * @param threadId
	 */
	public ChiponStepOverCommand(String request) {
        super(request);
	}

	/* (non-Javadoc)
	 * @see com.chipon32.debug.core.protocol.ChiponCommand#createResult(java.lang.String)
	 */
	@Override
	public ChiponStopLocationCommandResult createResult(List<String> resultText) {
		return new ChiponStopLocationCommandResult(resultText);
	}

}
