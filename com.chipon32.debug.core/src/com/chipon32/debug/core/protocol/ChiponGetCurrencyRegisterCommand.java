package com.chipon32.debug.core.protocol;


import java.util.List;

/**
 * ÐÞ¸Ä¼Ä´æÆ÷µÄÖµ
 * <AUTHOR> @since 2017-6- 29
 */
public class ChiponGetCurrencyRegisterCommand extends ChiponCommand{

	public ChiponGetCurrencyRegisterCommand(String name) {
		super("info registers  " + name);
	}

	/* (non-Javadoc)
	 * @see com.chipon32.debug.core.protocol.ChiponCommand#createResult(java.lang.String)
	 */
	@Override
	public ChiponGetCurrencyRegisterCommandResult createResult(List<String> resultText) {
		return new ChiponGetCurrencyRegisterCommandResult(resultText);
	}

}
