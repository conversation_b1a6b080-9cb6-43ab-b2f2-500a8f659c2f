package com.chipon32.debug.core.protocol;

import java.util.List;

/**
 * 
 * <AUTHOR> 2017-2-8 10:26:10
 *	远程连接命令
 *
 * 2018-4-28修改  
 * 	a)硬件调试命令target 添加选项 --arch=file，同软件调试命令选项相同，解析同一文件
 * 	b)硬件调试启动命令中串口信息修改为“/dev/ttySX”格式，X为串口序号减一，如COM17为/dev/ttyS16
 */
public class ChiponConnectCommand extends ChiponCommand {

	public ChiponConnectCommand(String com, String power, String speed, 
			String kftCode, String kfdCode, String kfinstruct, String deFile) {

		super("target kf32remote "+com +" --kfvoltage=" + power + 
				" --kfd-speed="+speed + " --kft-code="+kftCode +
				" --kfd-code="+ kfdCode+ " --kfinstruct="+ kfinstruct +
				" --kfprint "+ deFile
				);

	}
	
	/**
	 * 
	 * @param com
	 * @param power
	 * @param speed
	 * @param kftCode
	 * @param kfdCode
	 * @param carrayLiboutEn
	 * @param feature
	 * @param driver   0:9K，9K1，11K接口, 1:JTAG接口(13K)
	 * @param authkey
	 * @param kfinstruct
	 * @param deFile
	 */
	public ChiponConnectCommand(String com, String power, String speed, 
			String kftCode, String kfdCode, String carrayLiboutEn, String feature, String driver, String authkey, String kfinstruct, String deFile) {

		super("target kf32remote "+com +" --kfvoltage=" + power + 
				" --kfd-speed="+speed + " --kft-code="+kftCode +
				" --kfd-code="+ kfdCode+ " --enable_control_libout="+carrayLiboutEn+ " --kf-feature="+feature+ 
				" --kf-driver="+driver+ " --kf-authkey="+authkey+" --kfinstruct="+ kfinstruct +
				" --kfprint "+ deFile
				); //--kf-driver，后面改回来

	}

	@Override
	public ChiponCommandResult createResult(List<String> resultText) {
		return new ChiponCommandResult(resultText);
	}

}
