package com.chipon32.debug.core.protocol;


import java.util.List;

/**
 * 表达式命令
 * <AUTHOR> @since 2013-6-17 上午10:29:48
 * 
 */
public class ChiponWatchCommand extends ChiponCommand{

	/**
	 * 
	 * @param expression Expressions视图中的表达式
	 */
	public ChiponWatchCommand(String expression) {
		super("print "+expression);
	}

	/* (non-Javadoc)
	 * @see com.chipon32.debug.core.protocol.ChiponCommand#createResult(java.lang.String)
	 */
	@Override
	public ChiponWatchCommandResult createResult(List<String> resultText) {
		return new ChiponWatchCommandResult(resultText);
	}

}
