package com.chipon32.debug.core.protocol;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.eclipse.core.resources.IProject;
import org.eclipse.core.runtime.IPath;
import org.eclipse.core.runtime.Path;

import com.chipon32.debug.core.model.ChiponVariableTree;
import com.chipon32.debug.core.util.ChiponBracketParse;
import com.chipon32.debug.core.util.IChiponBracketParse;

public class ChiponFrameData{
	
	public static Map<String, IPath> fFilePathMap = new HashMap<>();
	public static Map<String, String> fFunctionMap = new HashMap<>();
	public static Map<String, Integer> fLineMap = new HashMap<>();
	public static Map<String, String> addrMap = new HashMap<>();
	
	public ChiponVariableTree[] fVariableTree;

	
	public ChiponFrameData(List<String> list, List<String> stackValue, IProject project, String uuid) {
		
			if(list!=null && list.size()>3){
				fFilePathMap.put(uuid, new Path(list.get(0)));
				fLineMap.put(uuid, Integer.parseInt(list.get(1)));
				fFunctionMap.put(uuid, list.get(2));
				addrMap.put(uuid, list.get(3));
			}
		
		// 特殊样例，包括 }  {  ,但不是匹配的结构，而是可能为字符数组
//		stackValue.add("FLASH_BUFFER = \"|\\024_\\256\\025pKW\\r\\223/\\006\\017p\\020\\244v\\310\\244$.\\222>\\372\\323\\225uV\\344j%\\230\\363\\001\\bv\\242\\236" +
//				"@:V\\355\\207AG\\227\\375\\262\\333\\374\\301\\227\\300=>\\030\\252\\n\\334m\\336\\026\\227\\034Bp\\256m1\\335Uvo\\200(\\303X3QWl\\326\\064\\n\\274" +
//				"\\234\\330(\\250N\\355\\003\\002\\354J\\376pEk\\027\\313\\323yvv\\363\\241D\\310I\\001\\r\\327\\036:\\000a\\035\\210\\325N\\001\\343Hg\\360\\374\\361\\236 \\265" +
//				"\\251?)\\235\\343\\270#\\343\\232xW\\335G\\247\\033\\211\\333:\\217\\371\\277S\\035\\002\\273\\354%,\\006QM\\252<\\302\\222#n\\024[\\337\\325h\\036\\n\\317\\225\\205*Q" +
//				"\\326^E\\224\\n\\254\\022n\\302\\370\\337\\220\\n*\\204\\210\\275\\034=\\\\j\\246b\\303d&\\317M\\257!\\355\\333\\333\\253\\313\\236'>\\363\\365o(\\240j\\367U?[\\246" +
//				"\\312\\216\\334\\313\\031,\\310\\334\\252R\\216\\345\\343\\246W\\205+H\\264\"\\210\\234\\304E3>\\t\\214\\270$\\000\\221C\\037wj\\202\\340\\020wi\\336\\227\\211N\\227." +
//				"\\310\\270\\071\\342\\323\\070r\\251\\b,\\222\\242'\\325_.\\246(\\204\\360\\000\\370_r\\314\\006\\211\\300\\027\\004\\213\\277@\\023\\tn\\236\\253,\\017\\266O\\346eO\\253j" +
//				"\\230\\242U\\v_\\315\\316\\031*U\\225<\\027+\\360\\v\\253\\026(\\253\\273\\342%>\\230\\260/\\366e\\324\\276\\233-\\266\\203\\031:\\320E\\037\\312\\270\\355\\330\\245x\\305" +
//				"\\066\\177\\a\\005\\061\\356\\346\\370\\214$\\227\\323\\347\\225\\203\\267\\n1\\370\\361\\317\\257s\\206m:7\\253\\030[\\033k\\203\\250\\317N\\204\\b\\321\\225\\261\\365%\\246" +
//				"\\063H\\025\\063\\277<\\006\\351\\030\\032\\305C\\203eS\\256\\213\\205x\\325\\302\\024\\302\\024G\\220\\330\\277\\232$J\\303M\\361\\352\\235\\001\\r\\363aQ\\326b\\036O\\200]" +
//				"\\265r\\337\\250\\322\\033\\211\\330\\252\\342\\030\\341R\\032]\\224\\242\\305`a\\371?t\\302\\251h\\211\\363\\206\\202\\372\\a\\257J\\336\\005\\065\\245\\004\\202\\256\\003" +
//				"\\340;U\\302\\273g\\344#\\272\\026\\206\\312\\262}\\325Q\\376H\\036@(G\\201\\367\\333\\242\\260\\250\\242\\322\\256\\032\\202\\025\\227\\324D/\\323\\320\\222\\345\\024\\213?" +
//				"\\260@6\\300W\\302{\\304\\222\\343\\313\\365;a\\256N\\330\\370\\242O\\202/y\\352P\\206\\325D\\214\\317\\341\\262\\020\\265\\025P\\373\\070\\331kR\\260\\261U\\241\\006-\\006" +
//				"\\203\\203>\\000\\024\\257\\235\\n\\353]\\302\\301[\\003|\\305\\276zo\\227\\350\\t|xRB_\\320T7\\251CI\\267\\310R\\356jp\\376\\231Lu_\\205\\322\\032\\321\\304\\326O7\\342" +
//				"\\n(GU2~\\031\\342l\\372\\357!\\324\\320\\222\\242\\230y=ow\\223+\\034\\210\\324e\\210\\253\\226\\017\\337s\\a\\277\\374\\177\\035=v\\005i\\207;5\\204\\062v\\350\\216" +
//				"\\033\\061\\324rs\\251h\\376\\231=\\367\\004rn\\303\\320\\263#`VK\\343\\374\\210\\205\\225\\071e\\300\\023\\212\\221u$\\210\\365\\276\\035\\271\\256\\253e\\322r\\240EU" +
//				"\\325\\307\\f\\236\\255\\312a}\\354tV\\t\\266^\\b;\\362\\247\\352\\300\\337\\237+\\t\\353\\034$\\225\\232\\231/o\\202\\262\\331itq\\252f\\244\\254\\224\\211\\242\\221M" +
//				"\\367l\\227:\\004a\\213\\270I\\331\\001(\\203\\342\\226m\\231vK\\265\\017CX\\303I,b^\\325\\211P\\236j\\242\\312\\336\\346~\\022Y\\253\\016\\273\\366F\\304\\022\\305" +
//				"\\267Ex\\307\\345\\002h*\\240\\334H\\306\\365\\345 \\345\\223\\327\\231^\\214\\207\\024\\335YQ\\305\\276\\067\\246\\220\\325\\300\\241u\\353\\370\\321\\206\\316" +
//				"\\205S|\\377\\201\\261\\365=\\356\\271\\004\\313\\063a\\202\\265\\061\\352\\206\\235\\021\\335E\\257v~\\252'\\035\\307\\277\\032\\374(\\253\\020D\\260\\244Wg}\\000" +
//				"\\342\\214\\071\\006\\357t\\375E\\327m6\\261\\223S\\311q\\f\\321\\352\\302\\361z\\301\\326A\\003\\220\\303\\367B\\022<\\310\\306W\\363\\370`\\357\\332\\035BXE\\034&" +
//				"\\353\\354\\254\\001\\343\\231\\213\\256\\342\\232\\303\\334\\261\\210\\303\\aR\\224\\222\\207\\352\\376/:\\325K\\337\\254\\316\\211\\325\\002b<\\024\\307\\257\\213" +
//				"\\255F!o\\343z\\036\\214\\343\\246B\\315\\035\\327\\374\\067\"");
		
		//局部变量
		List<ChiponVariableTree> values = new ArrayList<ChiponVariableTree>();
		
		IChiponBracketParse bracketParse = new ChiponBracketParse();
//		int i = ChiponMessageParse.getBracketBeginNum(stackValue);
		StringBuffer str = new StringBuffer();

		if(stackValue != null){
			for(int i = 0; i<stackValue.size(); i++){
//				response = gjztest = 3 '\003'     ## 如果为8时，为’\b’
//				response = gjztest1 = 3
//				response = gjzarr2 = {0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0}
//				response = newStudio = {studio1 = {name = "g\000\000\200\001\000\000\000\001\000c", id = 5746, yuwenfenshu = 6578}, techer1 = {name = 103 'g', id = 1, yuwenfenshu = 1, shuxuefenshu = 99}}				
				// 字符会进行转码，
				//------------------------------处理变量换行显示流程------------------------------------------
				str.append(stackValue.get(i).trim());
				
				// 个例会解析错误的提前退出而不影响其他，不然如特殊输入会错。	
				try{
					String dealparsestring= str.toString();
					boolean isLikeStruct=false;
					//通过"{"与"}"是否匹配判断判断这一行是否内容没有显示完整 				
					if(dealparsestring.contains("{")){   
						if(!bracketParse.check(dealparsestring))
						{
							int a=dealparsestring.indexOf("{");
							String buf= dealparsestring.substring(0,a);
							if(!buf.contains("\\") )
								continue;
						}
						else
							isLikeStruct=true;
					}
					//#######################################################################
					//#######################################################################
					//#######################################################################	
				//---------------------------------------------------------------------------------------------			
					//处理单行显示完整复杂变量
					if(isLikeStruct){
						// 替换掉多余的空格，但是又不能替换char数组类型中的空格,交由方法识别中过滤
						try{
							//调用综合解析方法进行构建
							values.addAll(bracketParse.buildTrees(bracketParse.getTrees(str.toString())));
						}catch (Exception e) {
							// TODO: handle exception
							// 异常的不识别返回的内容，但别throw异常退出调试器的捕获处理，本质返回内容不合法或其他原因
						}
					}				
					//处理简单变量  或字符串数组
					else{
	//					String[] datas = stackValue.get(i).split("=");
	//					ChiponVariableTree onlyTree = new ChiponVariableTree(0, 0);
	//					 onlyTree.setName(datas[0].trim());
	//					onlyTree.setValue(datas[1].trim());
	//					values.add( onlyTree);
	//					str = new StringBuffer();
						
						//String[] datas = stackValue.get(i).split("=");// 字符串不按常规表达，内容会有=的内容
						int a=dealparsestring.indexOf("=");
						if(a<0)
							continue;
						String[] datas =new String[2];
						datas[0]=dealparsestring.substring(0,a);
						datas[1]=dealparsestring.substring(a+1);
						
						// 可能字符串	的解析
						if(datas[1].contains("\""))
						{ 
							Map <String, List<String>> rusult=bracketParse.ParseStrings(datas[1]);
							
							String strVaule=rusult.get("StringVaule").get(0);
							List<String>vaules=rusult.get("CharsVaule");
							// 树的建立
							//## 根树
							ChiponVariableTree onlyTree = new ChiponVariableTree(0, 0);
							onlyTree.setName(datas[0].trim());
							onlyTree.setValue("'"+strVaule);
							values.add( onlyTree);	
							//## 数据内容
							for(int jj=0;jj<vaules.size();jj++)
							{							
								ChiponVariableTree TreeChild = new ChiponVariableTree(0, 0);
								TreeChild.setName(datas[0].trim()+"["+jj+"]");
								TreeChild.setValue(vaules.get(jj).trim());
								TreeChild.setParent(onlyTree);
								onlyTree.addChild( TreeChild);	
							}
							
						}
						// 普通对象
						else
						{
							ChiponVariableTree onlyTree = new ChiponVariableTree(0, 0);
							onlyTree.setName(datas[0].trim());
							onlyTree.setValue(datas[1].trim());
							values.add( onlyTree);					
						}					
					}
				}catch (Exception e) {
				// TODO: handle exception
				
				}
				// 等待新的读取
				str = new StringBuffer();
			}//end for
	
		}
		
		fVariableTree = values.toArray(new ChiponVariableTree[values.size()]);
	}
	

}
