/**
 * 
 */
package com.chipon32.debug.core.protocol;

import java.util.List;

/**
 * <AUTHOR> 
 * 	控制运行按钮的命令
 *
 */
public class ChiponResumeCommand extends ChiponCommand {

	/**
	 * @param resultId
	 */
	public ChiponResumeCommand() {
		super("c");
	}

	/* (non-Javadoc)
	 * @see com.chipon32.debug.core.protocol.ChiponCommand#createResult(java.lang.String)
	 */
	@Override
	public ChiponStopLocationCommandResult createResult(List<String> resultText) {
		return new ChiponStopLocationCommandResult(resultText);
	}

}
