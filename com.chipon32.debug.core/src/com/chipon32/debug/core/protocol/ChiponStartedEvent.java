package com.chipon32.debug.core.protocol;

import com.chipon32.debug.core.util.ChiponMessageParse;

public class ChiponStartedEvent extends ChiponRunControlEvent{

	public ChiponStartedEvent(String message) {
		super(message);
		// TODO Auto-generated constructor stub
	}

	public static boolean isEventMessage(String message) {
        return ChiponMessageParse.parseEventMessage(message).get(0).trim()
				.equalsIgnoreCase("started");
    }
	
}
