package com.chipon32.debug.core.protocol;

import java.util.ArrayList;
import java.util.List;

import com.chipon32.debug.core.model.ChiponWatchpointData;
import com.chipon32.debug.core.model.Messages;

public class ChiponInfoWatchpointCommandResult extends ChiponCommandResult {
	
	public List<ChiponWatchpointData> watchDatas = new ArrayList<>();

	public ChiponInfoWatchpointCommandResult(List<String> result) {
		super(result);
		for(String str : result){
			if(str.contains("watchpoint") &&!str.contains("No watchpoints") ){
				ChiponWatchpointData tmp =new 	ChiponWatchpointData();
				 // characteristic mathc  1
				if(str.contains("read watchpoint"))
				{
					str=str.replaceFirst("read watchpoint", "");
					tmp.setDefaultselid(0);
					tmp.setType(Messages.ChiponWatchpointData_0);
				}
				if(str.contains("hw watchpoint"))
				{
					str=str.replaceFirst("hw watchpoint", "");
					tmp.setDefaultselid(1);
					tmp.setType(Messages.ChiponWatchpointData_1);
				}
				if(str.contains("acc watchpoint"))
				{
					str=str.replaceFirst("acc watchpoint", "");
					tmp.setDefaultselid(2);
					tmp.setType(Messages.ChiponWatchpointData_2);
				}
				if(str.contains("pc watchpoint"))
				{
					str=str.replaceFirst("pc watchpoint", "");
					tmp.setDefaultselid(3);
					tmp.setType(Messages.ChiponWatchpointData_3);
				}
				// characteristic mathc 2
				str=str.replaceFirst("keep y   none", "");
				// split to get usefull message
				String[] message=str.split("\\s+");
				tmp.setId(message[0]);
				tmp.setIsNowValue(true);
				
				if(message.length<6)
				{
					String namebuf=message[1];
					for(int i=2; i<message.length;i++)
					{
						namebuf+=message[i];
					}
					tmp.setName(namebuf);
					tmp.setMessage(Messages.ChiponWatchpointView_2+namebuf+"~"+"unknow");
				}
				else
				{
//					response = 4       [read watchpoint keep] y   dwt_comp0 0x10000000 a
//					response = 6       [read watchpoint keep] y   dwt_comp1 0x000000db *a
//					response = 6       [read watchpoint keep] y   dwt_comp1 0x000000db *0x10000000
					message[4]=message[4].substring(2);  // 0xXX address
					String namebuf=message[5];
					if(namebuf.startsWith("("))
					{
						namebuf=namebuf.replace("(", "");
						namebuf=namebuf.replace(")", "");
					}				
					if( (namebuf.startsWith("*0x") 	)	)
					{
						namebuf=namebuf.substring(3);	
						tmp.setName(namebuf);
						tmp.setMessage(Messages.ChiponWatchpointView_2+namebuf+"~"+"unknow");
					}
					else if( (namebuf.startsWith("*(char*)0x") 	)	)
					{
						namebuf=namebuf.substring(10);	
						tmp.setName(namebuf);
						tmp.setMessage(Messages.ChiponWatchpointView_2+namebuf+"~"+"unknow");
					}
					else
					{
						tmp.setName(namebuf);
						tmp.setMessage(Messages.ChiponWatchpointView_1+namebuf);
					}
				}
				
				watchDatas.add(tmp);
			}
		}
	}

}
