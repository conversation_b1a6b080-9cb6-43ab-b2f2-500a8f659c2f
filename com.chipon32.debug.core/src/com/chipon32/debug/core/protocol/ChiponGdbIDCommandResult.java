package com.chipon32.debug.core.protocol;

import java.util.List;

import org.eclipse.core.runtime.Platform;

public class ChiponGdbIDCommandResult extends ChiponCommandResult	 {

	public String ppId;
	
	public ChiponGdbIDCommandResult(List<String> result) {
		super(result);
		if(Platform.getOS().equals(Platform.OS_WIN32))
		{
			for(String str: result){
				
				if(str.contains("PID")){
					String[] data= str.split(":");
					ppId = data[1].trim();
				}
			}
		}
		if(Platform.getOS().equals(Platform.OS_LINUX))
		{
//			Send Command :shell ps -C kf32-gdb 
//			response = (gdb)    PID TTY          TIME CMD
//			response =  50829 ?        00:00:00 kf32-gdb
			for(String str: result){
				if(str.contains("?")){
					String data = str.substring(0,str.indexOf('?'));
					ppId = data.trim();
				}
			}
		}
	}

}
