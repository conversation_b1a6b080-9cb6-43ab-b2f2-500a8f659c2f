package com.chipon32.debug.core.protocol;

import java.util.List;

/***
 *<AUTHOR>
 *2013-11-27
 ***/
public class ChiponPrintEEpromCommand extends ChiponCommand {

	/**/
	 
	public ChiponPrintEEpromCommand(String start, int length) {
		
		super("x /"+length+ "w "+start);
	}

	@Override
	public ChiponPrintEEpromCommandResult createResult(List<String> resultText) {
		return new ChiponPrintEEpromCommandResult(resultText);
	}

}
