package com.chipon32.debug.core.protocol;

import java.util.List;

/**
 * <AUTHOR> 
 */
public class ChiponMulCoreCommand extends ChiponCommand {

	public ChiponMulCoreCommand(int cpuIndex, int cpuUnite) {
		super("mulcore "  + cpuIndex + " " + cpuUnite);
	}

	@Override
	public ChiponStopLocationCommandResult createResult(List<String> resultText) {
		return new ChiponStopLocationCommandResult(resultText);
	}

}
