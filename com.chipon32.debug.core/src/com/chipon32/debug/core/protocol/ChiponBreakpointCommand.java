package com.chipon32.debug.core.protocol;

import java.util.List;

/**
 * 添加断点命令
 * <AUTHOR>
 *
 */
public class ChiponBreakpointCommand extends ChiponCommand{

	/**
	 * 根据文件名和行号添加断点
	 * @param resourceName
	 * @param lineNum
	 */
	public ChiponBreakpointCommand(String resourceName, int lineNum) {
		super("break "+resourceName+":"+String.valueOf(lineNum));
	}
	
	/**
	 * 添加地址断点
	 * @param address
	 */
	public ChiponBreakpointCommand(String address) {
		super("break " +address );
	}

	/**
	 *  添加条件断点（文件名、行号、条件）
	 * @param resourceName
	 * @param lineNum
	 * @param condition
	 */
	public ChiponBreakpointCommand(String resourceName, int lineNum, String condition) {
		super("break "+resourceName+":"+String.valueOf(lineNum) + " if "+condition);
	}
	
	/**
	 * 添加条件断点（地址、条件）
	 * @param address
	 * @param condition
	 */
	public ChiponBreakpointCommand(String address, String condition) {
		super("break " + address + " if " + condition);
	}
	
	/**
	 * 关键断点号进行断点设置（设置或修改断点条件）  ！在启动调试器后进行
	 * @param breakpointNo
	 * @param condition
	 */
	public ChiponBreakpointCommand(int breakpointNo, String condition){
		super("cond "+String.valueOf(breakpointNo)+" "+condition);
	}
	
	@Override
	public ChiponBreakpointCommandResult createResult(List<String> resultText) {
		return new ChiponBreakpointCommandResult(resultText);
	}

}
