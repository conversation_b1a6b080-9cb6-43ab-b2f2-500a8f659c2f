package com.chipon32.debug.core.protocol;


import java.util.List;

/**
 * ÐÞ¸Ä¼Ä´æÆ÷µÄÖµ
 * <AUTHOR> @since 2017-6- 29
 */
public class ChiponSetCurrencyRegisterCommand extends ChiponCommand{

	public ChiponSetCurrencyRegisterCommand(String name,String value) {
		super("set $" + name + "=" + value);
	}

	/* (non-Javadoc)
	 * @see com.chipon32.debug.core.protocol.ChiponCommand#createResult(java.lang.String)
	 */
	@Override
	public ChiponCommandResult createResult(List<String> resultText) {
		return new ChiponCommandResult(resultText);
	}

}
