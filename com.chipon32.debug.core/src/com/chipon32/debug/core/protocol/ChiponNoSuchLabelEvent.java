package com.chipon32.debug.core.protocol;

public class ChiponNoSuchLabelEvent extends ChiponEvent{

	public final String fLabel;
	
	public ChiponNoSuchLabelEvent(String message) {
		super(message);
		 fLabel = message.substring(getName(message).length() + 1);
	}
	
	  public static boolean isEventMessage(String message) {
	        return message.startsWith("no such label");
	    }
	    
	    @Override
		protected String getName(String message) {
	        if (isEventMessage(message)) {
	            return "no such label";
	        }
	        throw new IllegalArgumentException("Invalid event: " + message);
	    }

}
