/**
 * 
 */
package com.chipon32.debug.core.protocol;


import java.util.List;


/**
 * <AUTHOR>
 *
 */
public class ChiponCommandResult {
	public String resultText = "";
	public String errorCode;
	
	public List<String> resultout;
	
	public ChiponCommandResult(List<String> result){
		if(result != null && result.size() != 0){
			resultout = result;
			
			for(int i = 0; i < result.size();i++){
				if (result.get(i).contains("kf32command fail")) {
					resultText = "failed";
					if(i+1 < result.size()){
						String[] errs = result.get(i+1).split("\\W+");
						if(errs.length > 2){
							errorCode = errs[1];
						}		
					}	
					//break;
				} else if(result.get(i).contains("kf32command success")) {
					resultText = "success";
				}

			}
		}
		
	}
	
	public void changeResult(Object newResult) {
		
	}
}
