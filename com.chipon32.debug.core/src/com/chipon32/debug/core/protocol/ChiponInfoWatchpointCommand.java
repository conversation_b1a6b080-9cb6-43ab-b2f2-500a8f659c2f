package com.chipon32.debug.core.protocol;

import java.util.List;

/**
 * 
 * <AUTHOR>	2017-4-24 	Ìí¼Ó¼à¿Øµã
 *
 */

public class ChiponInfoWatchpointCommand extends ChiponCommand {
	
	public ChiponInfoWatchpointCommand() {
		super("info watchpoint");
	}

	@Override
	public ChiponInfoWatchpointCommandResult createResult(List<String> resultText) {
		return new ChiponInfoWatchpointCommandResult(resultText);
	}

}
