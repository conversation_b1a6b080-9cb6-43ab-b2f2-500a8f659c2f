<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
  <meta content="text/html; charset=ISO-8859-1"
 http-equiv="content-type">
  <title>DSF Filesystem Browser Example</title>
</head>
<body>
<h2>DSF Filesystem Browser Example</h2>
<h3>Goals</h3>
This example demonstrates an implementation of a viewer model with a
layout node that has itself as a child.&nbsp; Such layout nodes are
needed to represents elements which themselves have a natural tree
structures.&nbsp; This example uses filesystem folders as the
tree-structured data, which is retrieved directly from the java.io.File
class.&nbsp; This example also demonstrates a viewer model
implementation which does not retrieve data using DSF services and
associated data model interfaces.&nbsp; <br>
<h3><span style="font-weight: bold;">Design</span></h3>
<span style="text-decoration: underline;">Model Adapter Hookup</span><br>
A flexible-hierarchy tree viewer {@link
org.eclipse.debug.internal.ui.viewers.model.provisional.TreeModelViewer}
is created within a model dialog.&nbsp; Corresponding {@link
FileBrowserModelAdapter} and {@link FileBrowserVMProvider} classes are
instanciated, and the root element object created by
FileBrowserVMProvider is set as input to the tree viewer.&nbsp; From
there FileBrowserModelAdapter is returned as the {@link
IElementContentProvier} and {@link IModelProxyFactory} for all elements
in the tree.<br>
<br>
<p><span style="text-decoration: underline;">Layout Nodes</span><br>
There are three layout nodes:<br>
</p>
<ul>
  <li>{@link FileBrowserVMProvider.VMRootLayoutNode} is just a root
node, which generates the input element for the viewer.</li>
  <li>{@link FilesystemRootsLayoutNode} retrieves the roots of the
filesystem hierarchy ("C:\", "D:\", etc on Windows).&nbsp; <br>
  </li>
  <li>{@link FileLayoutNode} is a child of <span
 style="font-family: monospace;">FilesystemRootsLayoutNode</span> and
it recursively retrieves all folders and files under the given parent
file element.&nbsp; This layout node does not allow any children nodes
to be added to it, and it returns only itself as a child node (through
a call to <span style="font-family: monospace;">IVMLayoutNode.getChildLayoutNodes</span>).<br>
  </li>
</ul>
Both <span style="font-family: monospace;">FilesystemRootsLayoutNode</span>
and <span style="font-family: monospace;">FileLayoutNode</span> create
elements of the same type: {@link FileVMContext}.&nbsp; Additionally,
when populating elements in the tree, the <span
 style="font-family: monospace;">FileLayoutNode</span> requires that a <span
 style="font-family: monospace;">FileVMContext</span> element be the
parent element in order to be able to retrieve its children.&nbsp; <br>
<span style="font-family: monospace;"></span>
<p><span style="font-family: monospace;"></span></p>
<span style="text-decoration: underline;">Event Handling/Generating
Model Deltas</span><br>
The view model responds to events generated by a text box in the
dialog, where the user can type in a filesystem path.&nbsp; If the
entered path resolves to a file on the filesystem, the view model
generates a delta to select and reveal the given file in the
tree.&nbsp; The two file layout nodes handle generating the delta in
different ways:<br>
<ul>
  <li><span style="font-family: monospace;">FilesystemRootsLayoutNode</span>
is a standard layout node.&nbsp; <br>
  </li>
  <ol>
    <li>In the event handler implementation {@link
org.eclipse.cdt.dsf.ui.viewermodel.IVMLayoutNode#buildDelta}, the user
specified file-path is compared to the list of file-system roots.&nbsp;
      <br>
    </li>
    <li>If the user file-path contains one of the filesystem roots, a
new delta node is added for that root and the child layout node is
called to continue the delta processing.&nbsp; <br>
    </li>
    <li>If the user file-path points to one of the filesystem roots,
the <span style="font-family: monospace;">IModelDelta.SELECT</span>
and <span style="font-family: monospace;">IModelDelta.EXPAND</span>
flags are also added to the delta so that the root will be selected in
the viewer.<br>
    </li>
  </ol>
  <li><span style="font-family: monospace;">FileLayoutNode</span> is
the special case, because it is a recusrive node.&nbsp; This node does
not call any child nodes to process the delta, instead it calculates
the delta for all file elements in user file-path, starting at the
parent element. <br>
  </li>
  <ol>
    <li>First the parent <span style="font-family: monospace;">FileVMContext</span>
element is retrieved from the delta.&nbsp; <br>
    </li>
    <li>Then the user file-path is broken down into {@link
java.io.File} objects representing each segment in the path, starting
at the parent file element retrieved in step 1.</li>
    <li>Then a delta node is added for each segment of the calculated
path.&nbsp; <br>
    </li>
    <li><span style="font-family: monospace;">IModelDelta.SELECT</span>
and <span style="font-family: monospace;">IModelDelta.EXPAND</span>
flags are added to the last delta.<br>
    </li>
  </ol>
</ul>
<h3>How to use</h3>
<ol>
  <li>Make sure that the DSF examples menu is visible in the perspective</li>
  <ul>
    <li>Go to Windows -&gt; Customize Perspective...</li>
    <li>Select Commands tab</li>
    <li>Check the "DSF Examples" in the "Available command groups"
table.</li>
  </ul>
  <li>Open the dialog by selecting DSF Examples-&gt;Open File Browser
Dialog menu item.</li>
  <li>Expand the items in the tree to see filesystem contents.</li>
  <li>Select elements in the tree, to fill in text box with selected
file's path.</li>
  <li>Type in a file path in text box and have the tree expand to the
specified element.<br>
  </li>
</ol>
</body>
</html>
