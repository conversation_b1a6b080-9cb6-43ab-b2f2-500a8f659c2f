package com.chipon32.configtool.enums;

import com.chipon32.configtool.util.Constants;

public enum Key {
	MASTER_ECU_KEY(Constants.MASTER_ECU_KEY, "0x1", Constants.MASTER_ECU_KEY_ADDR) {
		@Override
		public String[] getNext() {
			return new String[] {MASTER_ECU_KEY.getName(), BOOT_MAC_KEY.getName(), KEY_01.getName(), KEY_02.getName(), KEY_03.getName(), KEY_04.getName(), KEY_05.getName(), KEY_06.getName(), KEY_07.getName(), KEY_08.getName(), KEY_09.getName(), KEY_10.getName()};
		}
	},
	BOOT_MAC_KEY(Constants.BOOT_MAC_KEY, "0x2", Constants.BOOT_MAC_KEY_ADDR) {
		@Override
		public String[] getNext() {
			return new String[] {BOOT_MAC_KEY.getName()};
		}
	},
	KEY_01(Constants.KEY_01, "0x4", Constants.KEY_01_ADDR) {
		@Override
		public String[] getNext() {
			return new String[] {KEY_01.getName()};
		}
	},
	KEY_02(Constants.KEY_02, "0x5", Constants.KEY_02_ADDR) {
		@Override
		public String[] getNext() {
			return new String[] {KEY_02.getName()};
		}
	},
	KEY_03(Constants.KEY_03, "0x6", Constants.KEY_03_ADDR) {
		@Override
		public String[] getNext() {
			return new String[] {KEY_03.getName()};
		}
	},
	KEY_04(Constants.KEY_04, "0x7", Constants.KEY_04_ADDR) {
		@Override
		public String[] getNext() {
			return new String[] {KEY_04.getName()};
		}
	},
	KEY_05(Constants.KEY_05, "0x8", Constants.KEY_05_ADDR) {
		@Override
		public String[] getNext() {
			return new String[] {KEY_05.getName()};
		}
	},
	KEY_06(Constants.KEY_06, "0x9", Constants.KEY_06_ADDR) {
		@Override
		public String[] getNext() {
			return new String[] {KEY_06.getName()};
		}
	},
	KEY_07(Constants.KEY_07, "0xA", Constants.KEY_07_ADDR) {
		@Override
		public String[] getNext() {
			return new String[] {KEY_07.getName()};
		}
	},
	KEY_08(Constants.KEY_08, "0xB", Constants.KEY_08_ADDR) {
		@Override
		public String[] getNext() {
			return new String[] {KEY_08.getName()};
		}
	},
	KEY_09(Constants.KEY_09, "0xC", Constants.KEY_09_ADDR) {
		@Override
		public String[] getNext() {
			return new String[] {KEY_09.getName()};
		}
	},
	KEY_10(Constants.KEY_10, "0xD", Constants.KEY_10_ADDR) {
		@Override
		public String[] getNext() {
			return new String[] {KEY_10.getName()};
		}
	};
	
	
	Key(String name, String keyID, String startAddr) {
		this.name = name + "(" + keyID + ")";
		this.keyID = keyID;
		this.addr = startAddr;
	}
	
	public String getKeyID() {
		return keyID;
	}
	
	public String getName() {
		return name;
	}
	
	public String getStartAddr() {
		return addr;
	}

	private String keyID;
	private String name;
	private String addr;
	
	public abstract String[] getNext();
	
	public static Key getKey(String name) {
		for(Key k : Key.values()) {
			if(k.getName().equals(name)) {
				return k;
			}
		}
		return null;
	}
}
