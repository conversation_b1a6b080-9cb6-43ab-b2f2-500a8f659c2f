package com.chipon32.configtool.enums;

import com.chipon32.configtool.util.Constants;

public enum DebugMode {
	DEBUG_MODE_NO(Constants.DEBUG_MODE_NO,"00", "FFFFFFFF"),
	DEBUG_MODE_A(Constants.DEBUG_MODE_A,"01", "3254CDAB"),
	DEBUG_MODE_B(Constants.DEBUG_MODE_B,"10", "CDAB3254"),
	DEBUG_MODE_C(Constants.DEBUG_MODE_C,"11", "960369FC");
	
	DebugMode(String name, String keyID,String keyCode) {
		this.name = name;
		this.keyID = keyID;
		this.code = keyCode;
	}
	
	public String getKeyID() {
		return keyID;
	}
	
	public String getName() {
		return name;
	}
	
	public String getcode() {
		return code.toUpperCase();
	}

	private String keyID;
	private String name;
	private String code;
	
	public static DebugMode getMode(String name) {
		for(DebugMode k : DebugMode.values()) {
			if(k.getName().equals(name)) {
				return k;
			}
		}
		return null;
	}
}
