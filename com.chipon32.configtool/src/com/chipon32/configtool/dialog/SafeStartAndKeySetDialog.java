package com.chipon32.configtool.dialog;


import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.jface.dialogs.IDialogConstants;
import org.eclipse.jface.dialogs.MessageDialog;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.FocusEvent;
import org.eclipse.swt.events.FocusListener;
import org.eclipse.swt.events.ModifyEvent;
import org.eclipse.swt.events.ModifyListener;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.events.SelectionListener;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Button;
import org.eclipse.swt.widgets.Combo;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Event;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.Listener;
import org.eclipse.swt.widgets.Shell;
import org.eclipse.swt.widgets.Text;
import org.eclipse.swt.widgets.Widget;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONReader;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.chipon32.configtool.adaptor.ProAndIDEAdaptor;
import com.chipon32.configtool.enums.Key;
import com.chipon32.configtool.util.Constants;
import com.chipon32.configtool.util.FileUtil;
import com.chipon32.configtool.util.FormatUtil;

public class SafeStartAndKeySetDialog extends CustomDialog implements SelectionListener, ModifyListener, FocusListener {

	private static final String ENABLE_TITTLE = Messages.SafeStartAndKeySetDialog_11; 
	private static final String DEFAULT_CONFIG_FILE_NAME = "SafeStartAndKeySet"; //$NON-NLS-1$
	private static final String CID_TITTLE = "*CID"; //$NON-NLS-1$
	private static final String KEY_TITTLE = "*KEY(16byte)"; //$NON-NLS-1$
	private static final String FID_TITTLE = "FID"; //$NON-NLS-1$
	private static final String MODE_TITTLE = "Mode(4byte)"; //$NON-NLS-1$
	private static final String START_ADDR_TITTLE = Messages.SafeStartAndKeySetDialog_13; 
	private static final String LENGTH_TITTLE = Messages.SafeStartAndKeySetDialog_14; 
	private static final String NON_STRICT_MODE = "\u975e\u4e25\u683c\u6a21\u5f0f"; //$NON-NLS-1$
	private static final String STRICT_MODE = "\u4e25\u683c\u6a21\u5f0f"; //$NON-NLS-1$

	private Set<KeyEntry> keySet = new HashSet<KeyEntry>();
	private Text startAddrT;
	private Text lenT;
	private Combo modeC;
	private Button applySafeStartButton;
	private Button applyKeyConfigButton;

	public SafeStartAndKeySetDialog(Shell shell, ProAndIDEAdaptor adaptor) {
		super(shell, Messages.ConfigToolDialog_SafeStartTittle, adaptor);
	}
	
	@Override
	public Control createDialogArea(Composite parent) {
		
		final Composite container = new Composite(parent, SWT.NONE);
		container.setLayoutData(new GridData(GridData.FILL_BOTH));
		container.setLayout(new GridLayout(1, true));
		
		createSafeStartArea(container);
		createKeyConfigArea(container);
		return container;
	}

	private void createKeyConfigArea(final Composite container) {
		Composite group = new Composite(container, SWT.BORDER);
		group.setLayout(new GridLayout(8, false));
		group.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, false,1,1));
		
		Label enableL = new Label(group, SWT.NONE);
		enableL.setLayoutData(new GridData(SWT.CENTER, SWT.CENTER, false, false,1,1));
		enableL.setText(ENABLE_TITTLE);
		
		Label keyNL = new Label(group, SWT.NONE);
		keyNL.setLayoutData(new GridData(SWT.LEFT, SWT.CENTER, false, false,1,1));
		keyNL.setText(Messages.SafeStartAndKeySetDialog_12); 
		
		Label keyL = new Label(group, SWT.NONE);
		keyL.setText(KEY_TITTLE);
		keyL.setLayoutData(new GridData(SWT.LEFT, SWT.CENTER,false, false, 3, 1));
		
		Label CIDLabel = new Label(group, SWT.NONE);
		CIDLabel.setText(CID_TITTLE);
		CIDLabel.setLayoutData(new GridData(SWT.LEFT, SWT.CENTER,false, false, 1, 1));
		
		Label FIDLabel = new Label(group, SWT.NONE);
		FIDLabel.setText(FID_TITTLE);
		FIDLabel.setLayoutData(new GridData(SWT.LEFT, SWT.CENTER,false, false, 1, 1));
		
		Label emptyLabel = new Label(group, SWT.NONE);
		emptyLabel.setLayoutData(new GridData(SWT.LEFT, SWT.CENTER,false, false, 1, 1));
		
		addKeyEntryArea(Key.MASTER_ECU_KEY, group, "0f0e0d0c0b0a09080706050403020100"); //$NON-NLS-1$
		addKeyEntryArea(Key.BOOT_MAC_KEY, group, "12340000000000000000000000000000"); //$NON-NLS-1$
		addKeyEntryArea(Key.KEY_01, group, "2FF8B03C5C5405465A9C94BD2D863279"); //$NON-NLS-1$
		addKeyEntryArea(Key.KEY_02, group, "85852FF8E7860C89B3AB9D63B8D6288F"); //$NON-NLS-1$
		addKeyEntryArea(Key.KEY_03, group, "A36FF144FB6D5E2CDA0D2894DA0D2894"); //$NON-NLS-1$
		addKeyEntryArea(Key.KEY_04, group, "86078C1ABCDCC6B6C52C851DE5652BF5"); //$NON-NLS-1$
		addKeyEntryArea(Key.KEY_05, group, "043A1A50DB3954D222FEB37F1F678FCA"); //$NON-NLS-1$
		addKeyEntryArea(Key.KEY_06, group, "4B9577504B9577506F75C3E05C8DCD59"); //$NON-NLS-1$
		addKeyEntryArea(Key.KEY_07, group, "2b7e151628aed2a6abf7158809cf4f3c"); //$NON-NLS-1$
		addKeyEntryArea(Key.KEY_08, group, "10AF4B5B024195B91730D7F594C87E19"); //$NON-NLS-1$
		addKeyEntryArea(Key.KEY_09, group, "93346F4C6A8ABCCD37D52249291F4138"); //$NON-NLS-1$
		addKeyEntryArea(Key.KEY_10, group, "68B674CB8198A2503A285100F4DDC40A"); //$NON-NLS-1$
		
        applyKeyConfigButton = new Button(group, SWT.PUSH);
        applyKeyConfigButton.setLayoutData(new GridData(SWT.RIGHT, SWT.CENTER, false, false, 8, 1));
        applyKeyConfigButton.setText(Messages.KeyGenerateDialog_WriteButtonText); //$NON-NLS-1$
        applyKeyConfigButton.addSelectionListener(new SelectionAdapter() {
			public void widgetSelected(SelectionEvent event) {
				boolean res = true;
				res &=  checkKeyConfigArea();
				if(!res) {
					adaptor.printErrorMsg(Messages.KeyGenerateDialog_CheckFormFailedInfo); //$NON-NLS-1$
					return;
				}
				res &=adaptor.powerOnBeforeWriteData(chipType);
				if(!res) {
					adaptor.printErrorMsg(Messages.KeyGenerateDialog_ConnectHardWareFailedInfo); //$NON-NLS-1$
					return;
				}
				res &= writeKeyConfigs();
				if(!res) {
					adaptor.printErrorMsg(Messages.SafeStartAndKeySetDialog_1);
				}else {
					adaptor.printInfoMsg(Messages.SafeStartAndKeySetDialog_2);
				}
				
				if(!adaptor.powerOff()) {
					adaptor.printInfoMsg(Messages.SafeStartAndKeySetDialog_0);
				}
			}
		});
        applyKeyConfigButton.setEnabled(checkKeyConfigArea());
	}

	private void createSafeStartArea(final Composite container) {
		Composite gSafeStartConfig = new Composite(container, SWT.BORDER);
		gSafeStartConfig.setLayout(new GridLayout(9, false));
		gSafeStartConfig.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, false,1,1));
		
		Label startAddrL = new Label(gSafeStartConfig, SWT.NONE);
		startAddrL.setText(String.format("%s:", START_ADDR_TITTLE)); //$NON-NLS-1$
		startAddrL.setLayoutData(new GridData(SWT.LEFT, SWT.CENTER, false, false, 1, 1));
		startAddrT = new Text(gSafeStartConfig, SWT.BORDER);
		startAddrT.setToolTipText(Messages.SafeStartAndKeySetDialog_3);
		startAddrT.setLayoutData(new GridData(SWT.FILL, SWT.CENTER, true, false, 2, 1));	
		startAddrT.addModifyListener(this);
		startAddrT.addFocusListener(this);
		
		Label lenL = new Label(gSafeStartConfig, SWT.NONE);
		lenL.setText(String.format("%s:", LENGTH_TITTLE)); //$NON-NLS-1$
		lenL.setLayoutData(new GridData(SWT.LEFT, SWT.CENTER, false, false, 1, 1));
		lenT = new Text(gSafeStartConfig, SWT.BORDER);
		lenT.setToolTipText(Messages.SafeStartAndKeySetDialog_4);
		lenT.setLayoutData(new GridData(SWT.FILL, SWT.CENTER, true, false, 2, 1));	
		lenT.addModifyListener(this);
		lenT.addFocusListener(this);

		Label modeL = new Label(gSafeStartConfig, SWT.NONE);
		modeL.setText(String.format("%s:", MODE_TITTLE)); //$NON-NLS-1$
		modeL.setLayoutData(new GridData(SWT.LEFT, SWT.CENTER, false, false, 1, 1));
        modeC = new Combo(gSafeStartConfig, SWT.READ_ONLY);
        modeC.setLayoutData(new GridData(SWT.FILL, SWT.CENTER, false, false, 2, 1));
        modeC.setItems(new String[] {STRICT_MODE, NON_STRICT_MODE});
        modeC.select(0);
        
        applySafeStartButton = new Button(gSafeStartConfig, SWT.PUSH);
        applySafeStartButton.setLayoutData(new GridData(SWT.RIGHT, SWT.CENTER, false, false, 9, 1));
        applySafeStartButton.setText(Messages.KeyGenerateDialog_WriteButtonText); //$NON-NLS-1$
        applySafeStartButton.addSelectionListener(new SelectionAdapter() {
			public void widgetSelected(SelectionEvent event) {
				boolean res = true;
				res &= checkSafeStartArea();
				if(!res) {
					adaptor.printErrorMsg(Messages.KeyGenerateDialog_CheckFormFailedInfo); //$NON-NLS-1$
					return;
				}
				res &=adaptor.powerOnBeforeWriteData(chipType);
				if(!res) {
					adaptor.printErrorMsg(Messages.KeyGenerateDialog_ConnectHardWareFailedInfo); //$NON-NLS-1$
					return;
				}
				res &= writeSafeStartArea();
				if(!res) {
					adaptor.printErrorMsg(Messages.SafeStartAndKeySetDialog_5);
				}else {
					adaptor.printInfoMsg(Messages.SafeStartAndKeySetDialog_6);
				}
				
				if(!adaptor.powerOff()) {
					adaptor.printInfoMsg(Messages.SafeStartAndKeySetDialog_0); //$NON-NLS-1$
				}		
			}
		});
        applySafeStartButton.setEnabled(checkSafeStartArea());
	}
	
//	@Override
//	protected Point getInitialSize() {
//		return new Point(1600, 700);
//	}
	
	protected boolean checkSafeStartArea() {
		boolean res = true;
		res &= checkStartAddr();
		res &= checkLen();
		return res;
	}

	@Override
	protected boolean checkFormValid() {
		boolean res = true;
		res &= checkKeyConfigArea();
		res &= checkSafeStartArea();
		return res;
	}

	private boolean checkKeyConfigArea() {
		boolean res = true;
		for(KeyEntry keyentry : keySet) {
			if(!keyentry.isSelected()) {
				continue;
			}
			res &= UserKeyUpdateDialog.checkKey(keyentry.getKeyText());
			res &= UserKeyUpdateDialog.checkCID(keyentry.getcIDText());
		}
		return res;
	}

	private boolean writeKeyConfigs() {
		boolean res = true;
		res &= checkAllKeyEmpty();
		if(!res) {
			adaptor.printErrorMsg(Messages.SafeStartAndKeySetDialog_7);
			return false;
		}
		for(KeyEntry keyEntry : keySet) {
			if(!keyEntry.isSelected()) {
				continue;
			}
			res &= writeKey(keyEntry);
		}
		return res;
		
	}

	private boolean checkAllKeyEmpty() {
		boolean res = true;
		for(KeyEntry keyEntry : keySet) {
			List<String> data = adaptor.readData(adaptor.getAimStorageCFGUser(), keyEntry.getStartAddr(), Constants.KEY_LEN+Constants.CID_LEN+Constants.FID_LEN);
			if(data == null) {
				adaptor.printErrorMsg(String.format("Read %s failed.", keyEntry.getKeyName())); //$NON-NLS-1$
				res &= false;
				continue;
			}
			if(!FormatUtil.isEmpty(data)) {
				adaptor.printErrorMsg(String.format("key: %s is not empty.", keyEntry.getKeyName())); //$NON-NLS-1$
				res &= false;
				continue;
			}
		}
		return res;
	}

	private boolean writeKey(KeyEntry keyEntry) {
		String key = FormatUtil.replaceString(keyEntry.getKeyTextStr(), Constants.KEY_SPERATOR_REGEX, ""); //$NON-NLS-1$
		String cid = FormatUtil.replaceString(keyEntry.getcIDTextStr(), Constants.KEY_SPERATOR_REGEX, ""); //$NON-NLS-1$
		String fidBinStr = keyEntry.getFID();
		return UserKeyUpdateDialog.updateKey(keyEntry.getStartAddr(), key, cid, fidBinStr, adaptor);
		
	}

	private boolean writeSafeStartArea() {
		if(!FormatUtil.isEmpty(adaptor.readData(adaptor.getAimStorageCFGUser(), Constants.SAFE_START_CONFIG_ADDR, 16))) {
			adaptor.printInfoMsg(Messages.SafeStartAndKeySetDialog_8);
			return false;
		}
		String startAddr = FormatUtil.replaceString(startAddrT.getText(), Constants.KEY_SPERATOR_REGEX, ""); //$NON-NLS-1$
		startAddr = FormatUtil.list2String(FormatUtil.reWriteReverseData(FormatUtil.string2List(startAddr)));
		String startAddrTrim = FormatUtil.getInverseCode(startAddr);
		String lenHex = FormatUtil.toEight(FormatUtil.decimalStr2HexStr(lenT.getText()));
		lenHex = FormatUtil.list2String(FormatUtil.reWriteReverseData(FormatUtil.string2List(lenHex)));
		String lenTrim = FormatUtil.getInverseCode(lenHex);
		String mode = modeC.getText().equals(STRICT_MODE) ? Constants.SAFE_START_STRICT_MODE : Constants.SAFE_START_NORMAL_MODE;
		String modeTrim = FormatUtil.getInverseCode(mode);
		List<String> data = FormatUtil.string2List(startAddr + startAddrTrim + lenHex + lenTrim + mode + modeTrim);
		return adaptor.writeData(data, Constants.SAFE_START_CONFIG_ADDR);
	}

	private KeyEntry addKeyEntryArea(Key keyEntry, Composite group, String defaultKeyValue) {
		
		Button checkB = new Button(group, SWT.CHECK);
		checkB.setLayoutData(new GridData(SWT.CENTER, SWT.CENTER, false, false, 1, 1));
		checkB.setSelection(true);
		checkB.addSelectionListener(this);
		
		Label keyName = new Label(group, SWT.NONE);
		keyName.setText(keyEntry.getName());
		keyName.setLayoutData(new GridData(SWT.LEFT, SWT.CENTER, false, false, 1, 1));

		Text keyText = new Text(group, SWT.BORDER);
		keyText.setToolTipText(Messages.SafeStartAndKeySetDialog_9);
		keyText.setText(defaultKeyValue);
		keyText.setLayoutData(new GridData(SWT.FILL, SWT.CENTER, true, false, 3, 1));
		keyText.addModifyListener(this);
		keyText.addFocusListener(this);;
		
		Text CIDText = new Text(group, SWT.BORDER);
		CIDText.setLayoutData(new GridData(SWT.FILL, SWT.CENTER, true, false, 1, 1));
		CIDText.setText("0000000"); //$NON-NLS-1$
		CIDText.setToolTipText(Messages.SafeStartAndKeySetDialog_10);
		CIDText.addModifyListener(this);
		CIDText.addFocusListener(this);
		
		final Text FIDText = new Text(group, SWT.BORDER);
		FIDText.setLayoutData(new GridData(SWT.FILL, SWT.CENTER, true, false, 1, 1));
		FIDText.setEditable(false);
		FIDText.setText("000000"); //$NON-NLS-1$

		final Button setFIDButton = new Button(group, SWT.None);
		setFIDButton.setLayoutData(new GridData(SWT.LEFT, SWT.CENTER, false, false, 1, 1));
		setFIDButton.setText(String.format("Set %s", FID_TITTLE)); //$NON-NLS-1$
		setFIDButton.setData(FIDConfigDialog.DATA_KEY_FID, "000000"); //$NON-NLS-1$
		setFIDButton.addListener(SWT.Selection, new Listener() {

			@Override
			public void handleEvent(Event event) {
				String FID = (String) setFIDButton.getData(FIDConfigDialog.DATA_KEY_FID);
				FIDConfigDialog fidSetDialog = new FIDConfigDialog(getShell(), FID);
				if(fidSetDialog.open() == Dialog.OK) {
					setFIDButton.setData(FIDConfigDialog.DATA_KEY_FID, fidSetDialog.getFID());
					FIDText.setText(fidSetDialog.getFID());
				}
			}
			
		});
		KeyEntry keyObject = new KeyEntry(keyEntry, checkB, keyText, CIDText, setFIDButton);
		keySet.add(keyObject);
		return keyObject;
	}


	private boolean checkLen() {
		if(lenT.getText().isEmpty()) {
			return false;
		}
	    try {
	        int value = Integer.parseInt(lenT.getText(), 10);
	        if(value < 16 || value > 2097152) {
	        	return false;
	        }
	    } catch (NumberFormatException e) {
	        return false;
	    }
		return true;
	}

	private boolean checkStartAddr() {
		return FormatUtil.checkHexStr(startAddrT, 8);
		
	}
	
	@Override
	protected void createButtonsForButtonBar(Composite parent) {
		if(needImportButton) {
			createButton(parent, IMPORT_ID, Messages.CustomDialog_ImportButtonText, false); //$NON-NLS-1$
		}
		if(needExportButton) {
			createButton(parent, EXPORT_ID, Messages.CustomDialog_ExportButtonText, false); //$NON-NLS-1$
		}
		createButton(parent, IDialogConstants.CANCEL_ID,
				IDialogConstants.CANCEL_LABEL, false);
	}
	
	@Override
	protected void exportConfigFile() {
		JSONObject jsonRoot = new JSONObject();
		jsonRoot.put(START_ADDR_TITTLE, startAddrT.getText());
		jsonRoot.put(LENGTH_TITTLE, lenT.getText());
		jsonRoot.put(MODE_TITTLE, modeC.getText().equals(STRICT_MODE) ? Constants.SAFE_START_STRICT_MODE : Constants.SAFE_START_NORMAL_MODE);
		
		for(KeyEntry key : keySet) {
			JSONObject jsonKey = new JSONObject();
			jsonKey.put(KEY_TITTLE, key.getKeyTextStr());
			jsonKey.put(CID_TITTLE, key.getcIDTextStr());
			jsonKey.put(ENABLE_TITTLE, key.isSelected());
			jsonKey.put(FID_TITTLE, key.getFID());
			jsonRoot.put(key.getKeyName(), jsonKey);
		}
		File file = FileUtil.openSaveFileDialog(getShell(), DEFAULT_CONFIG_FILE_NAME, adaptor);
		if(file == null) {
			return;
		}
		try(FileWriter fileWriter = new FileWriter(file, StandardCharsets.UTF_8)) {
			JSON.writeJSONStringTo(jsonRoot, fileWriter, SerializerFeature.PrettyFormat);
		} catch (IOException e) {
			e.printStackTrace();
		}
		super.exportConfigFile();
	}


	
	@Override
	protected void importConfigFile() {
		File file = FileUtil.openOpenFileDIalog(getShell(), adaptor);
		if(file == null) {
			return;
		}
		try (
			BufferedReader fileReader = new BufferedReader( new InputStreamReader(new FileInputStream(file), StandardCharsets.UTF_8));
            //FileReader fileReader = new FileReader(file);		
			JSONReader jsonReader = new JSONReader(fileReader);)  {
			JSONObject jsonRoot = JSON.parseObject(jsonReader.readObject().toString());
			if(jsonRoot.getString(START_ADDR_TITTLE)==null || jsonRoot.getString(LENGTH_TITTLE)==null || jsonRoot.getString(MODE_TITTLE)==null) {
				MessageDialog.openError(null, Messages.UserKeyUpdateDialog_6, Messages.UserKeyUpdateDialog_7);
				return;
			}
			startAddrT.setText(jsonRoot.getString(START_ADDR_TITTLE));
			lenT.setText(jsonRoot.getString(LENGTH_TITTLE));
			modeC.select(
					modeC.indexOf(
							jsonRoot.getString(MODE_TITTLE).equals(Constants.SAFE_START_STRICT_MODE) ? STRICT_MODE : NON_STRICT_MODE));
			
    		for(KeyEntry key : keySet) {
    			JSONObject jsonKey = jsonRoot.getJSONObject(key.getKeyName());
    			key.getKeySel().setSelection(jsonKey.getBooleanValue(ENABLE_TITTLE));
    			key.getKeyText().setText(jsonKey.getString(KEY_TITTLE));
    			key.getcIDText().setText(jsonKey.getString(CID_TITTLE));
    			key.getSetFIDButton().setData(FIDConfigDialog.DATA_KEY_FID, jsonKey.getString(FID_TITTLE));
    		}
        } catch (IOException e) {
            e.printStackTrace();
        }
		super.importConfigFile();
	}
	
	@Override
	protected void handleChanged() {
		applyKeyConfigButton.setEnabled(checkKeyConfigArea());
		applySafeStartButton.setEnabled(checkSafeStartArea());
	}

	@Override
	public void widgetSelected(SelectionEvent e) {
		Widget widget = e.widget;
		if(widget instanceof Button) {
			boolean isSelected = ((Button) widget).getSelection();
			for(KeyEntry key : keySet) {
				key.getKeySel().setSelection(isSelected);
			}
		    handleChanged();
		}
		
	}

	@Override
	public void widgetDefaultSelected(SelectionEvent e) {
		// do nothing
	}

	@Override
	public void modifyText(ModifyEvent e) {
		handleChanged();
	}
	

	@Override
	public void focusGained(FocusEvent e) {
	}

	@Override
	public void focusLost(FocusEvent e) {
		Widget widget = e.widget;
		if(widget == startAddrT) {
			boolean checkRes= checkStartAddr();
			if(!checkRes) {
				startAddrT.setText(""); //$NON-NLS-1$
			}
		} else if (widget == lenT) {
			boolean checkRes= checkLen();
			if(!checkRes) {
				lenT.setText(""); //$NON-NLS-1$
			}			
		} else {
			for(KeyEntry keyEntry : keySet) {
				if(widget == keyEntry.getKeyText() && !UserKeyUpdateDialog.checkKey(keyEntry.getKeyText())) {
					keyEntry.getKeyText().setText(""); //$NON-NLS-1$
					break;
				} else if(widget == keyEntry.getcIDText() && !UserKeyUpdateDialog.checkCID(keyEntry.getcIDText())) {
					keyEntry.getcIDText().setText(""); //$NON-NLS-1$
					break;
				}
			}
		}
		handleChanged();
	}
}
