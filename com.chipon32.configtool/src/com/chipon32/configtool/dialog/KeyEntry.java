package com.chipon32.configtool.dialog;

import org.eclipse.swt.widgets.Button;
import org.eclipse.swt.widgets.Text;

import com.chipon32.configtool.enums.Key;

public class KeyEntry {
	private Text keyText;
	private Text cIDText;
	private Button keySel;
	private Button setFIDButton;


	private Key keyEntry;

	public KeyEntry(Key keyEntry, Button keyL, Text keyText, Text cIDText, Button setFIDButton) {
		this.keyEntry = keyEntry;
		this.keyText = keyText;
		this.cIDText = cIDText;
		this.keySel = keyL;
		this.setFIDButton = setFIDButton;
	}

	public String getKeyName() {
		return keyEntry.getName();
	}
	
	public String getKeyID() {
		return keyEntry.getKeyID();
	}

	public Button getKeySel() {
		return keySel;
	}

	public Button getSetFIDButton() {
		return setFIDButton;
	}

	public Text getKeyText() {
		return keyText;
	}

	public Text getcIDText() {
		return cIDText;
	}
	
	public String getKeyTextStr() {
		return keyText.getText();
	}

	public String getcIDTextStr() {
		return cIDText.getText();
	}
	
	public boolean isSelected() {
		return keySel.getSelection();
	} 
	
	public String getFID() {
		return (String) setFIDButton.getData(FIDConfigDialog.DATA_KEY_FID);
	}

	public String getStartAddr() {
		return keyEntry.getStartAddr();
	}
	
}
