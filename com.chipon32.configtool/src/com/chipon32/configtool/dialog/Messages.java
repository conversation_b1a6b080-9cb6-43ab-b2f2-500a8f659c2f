package com.chipon32.configtool.dialog;

import org.eclipse.osgi.util.NLS;

public class Messages extends NLS {
	private static final String BUNDLE_NAME = "com.chipon32.configtool.dialog.messages"; //$NON-NLS-1$
	public static String ConfigToolDialog_0;
	public static String ConfigToolDialog_1;
	public static String ConfigToolDialog_14;
	public static String ConfigToolDialog_15;
	public static String ConfigToolDialog_17;
	public static String ConfigToolDialog_18;
	public static String ConfigToolDialog_19;
	public static String ConfigToolDialog_20;
	public static String ConfigToolDialog_KeyGenerateTittle;
	public static String ConfigToolDialog_SafeStartTittle;
	public static String ConfigToolDialog_TootTittle;
	public static String ConfigToolDialog_UserKeyUpdateTittle;
	public static String CustomDialog_0;
	public static String CustomDialog_1;
	public static String CustomDialog_ExportButtonText;
	public static String CustomDialog_ImportButtonText;
	
	public static String KeyGenerateDialog_0;
	public static String KeyGenerateDialog_1;
	public static String KeyGenerateDialog_2;
	public static String KeyGenerateDialog_3;
	public static String KeyGenerateDialog_4;
	public static String KeyGenerateDialog_5;
	public static String KeyGenerateDialog_6;
	public static String KeyGenerateDialog_8;
	public static String KeyGenerateDialog_9;
	public static String KeyGenerateDialog_10;
	public static String KeyGenerateDialog_11;
	public static String KeyGenerateDialog_12;
	public static String KeyGenerateDialog_13;
	public static String KeyGenerateDialog_14;
	public static String KeyGenerateDialog_15;
	public static String KeyGenerateDialog_7;
	public static String KeyGenerateDialog_AddInfoRule;
	public static String KeyGenerateDialog_CheckFormFailedInfo;
	public static String KeyGenerateDialog_ConnectHardWareFailedInfo;
	public static String KeyGenerateDialog_GenCipherButtonText;
	public static String KeyGenerateDialog_WriteButtonText;
	
	public static String SafeStartAndKeySetDialog_0;
	public static String SafeStartAndKeySetDialog_1;
	public static String SafeStartAndKeySetDialog_10;
	public static String SafeStartAndKeySetDialog_11;
	public static String SafeStartAndKeySetDialog_12;
	public static String SafeStartAndKeySetDialog_13;
	public static String SafeStartAndKeySetDialog_14;
	public static String SafeStartAndKeySetDialog_2;
	public static String SafeStartAndKeySetDialog_3;
	public static String SafeStartAndKeySetDialog_4;
	public static String SafeStartAndKeySetDialog_5;
	public static String SafeStartAndKeySetDialog_6;
	public static String SafeStartAndKeySetDialog_7;
	public static String SafeStartAndKeySetDialog_8;
	public static String SafeStartAndKeySetDialog_9;
	
	public static String UserKeyUpdateDialog_0;
	public static String UserKeyUpdateDialog_1;
	public static String UserKeyUpdateDialog_2;
	public static String UserKeyUpdateDialog_29;
	public static String UserKeyUpdateDialog_3;
	public static String UserKeyUpdateDialog_30;
	public static String UserKeyUpdateDialog_31;
	public static String UserKeyUpdateDialog_35;
	public static String UserKeyUpdateDialog_37;
	public static String UserKeyUpdateDialog_38;
	public static String UserKeyUpdateDialog_39;
	public static String UserKeyUpdateDialog_4;
	public static String UserKeyUpdateDialog_46;
	public static String UserKeyUpdateDialog_5;
	public static String UserKeyUpdateDialog_6;
	public static String UserKeyUpdateDialog_7;
	static {
		// initialize resource bundle
		NLS.initializeMessages(BUNDLE_NAME, Messages.class);
	}

	private Messages() {
	}
}
