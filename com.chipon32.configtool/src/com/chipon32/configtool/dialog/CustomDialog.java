package com.chipon32.configtool.dialog;


import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.jface.dialogs.IDialogConstants;
import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.layout.FillLayout;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.Scrollable;
import org.eclipse.swt.widgets.Shell;
import org.eclipse.swt.widgets.Text;

import com.chipon32.configtool.adaptor.ProAndIDEAdaptor;
import com.chipon32.configtool.util.Constants;


public abstract class CustomDialog extends Dialog {


	protected static final int IMPORT_ID = 3;
	protected static final int EXPORT_ID = 4;
	protected boolean needImportButton;
	protected boolean needExportButton;
	protected ProAndIDEAdaptor adaptor;
	private String tittle;
	protected String chipType;
	protected Shell parentShell;
	
	protected CustomDialog(Shell parentShell, String tittle, ProAndIDEAdaptor adaptor) {
		super(parentShell);
		this.parentShell = parentShell;
		this.tittle = tittle;
		this.adaptor = adaptor;
		setShellStyle(getShellStyle() | SWT.RESIZE);
	}
	
	public CustomDialog init(boolean needImportButton, boolean needExportButton){
		this.needImportButton = needImportButton;
		this.needExportButton = needExportButton;
		return this;
	} 
	
	@Override
	protected Control createContents(Composite parent) {
		getShell().setText(tittle);
		return super.createContents(parent);
	}
	
	@Override
	protected void createButtonsForButtonBar(Composite parent) {
		if(needImportButton) {
			createButton(parent, IMPORT_ID, Messages.CustomDialog_ImportButtonText, false);
		}
		if(needExportButton) {
			createButton(parent, EXPORT_ID, Messages.CustomDialog_ExportButtonText, false);
		}
		super.createButtonsForButtonBar(parent);
	}
	
	
	
	@Override
	protected void cancelPressed() {
		parentShell.setVisible(true);
		super.cancelPressed();
	}

	@Override
	public boolean close() {
		parentShell.setVisible(true);
		return super.close();
	}

	@Override
	protected void buttonPressed(int buttonId) {
		super.buttonPressed(buttonId);
		if(buttonId == 3) {
			importConfigFile();
		} else if (buttonId == 4) {
			exportConfigFile();
		}
	}
	
	protected void exportConfigFile() {
		adaptor.printInfoMsg(Messages.CustomDialog_0);
	}

	protected void importConfigFile() {
		adaptor.printInfoMsg(Messages.CustomDialog_1);
		handleChanged();
	}

	protected void handleChanged() {
		getButton(IDialogConstants.OK_ID).setEnabled(checkFormValid());
	}

	protected abstract boolean checkFormValid();
	
	public static Label getValidatorLabel(Scrollable t) {
		Object vLabel = t.getData(Constants.VALIDATOR_KEY);
		if(vLabel instanceof Label) {
			return (Label)  vLabel;
		}
		return null;
	}
	
	public static Text addTextWithVarifier(Composite composite, int len) {
		Text text = new Text(composite, SWT.BORDER);
		text.setLayoutData(new GridData(SWT.FILL, SWT.CENTER, true, false, len, 1));
		addVerifier(text);
		return text;
	}
	
	public static boolean addVerifier(Scrollable ele2Verify) {
		Composite container = ele2Verify.getParent();
		if(!(ele2Verify.getLayoutData() instanceof GridData)) {
			return false;
		}
		GridData oldGridData = (GridData)ele2Verify.getLayoutData();

		Composite innerContainer = new Composite(container, SWT.NONE);
		innerContainer.setLayoutData(oldGridData);
		GridLayout innerGL = new GridLayout();
		innerGL.marginHeight= 0;
		innerGL.marginWidth= 0;
		innerContainer.setLayout(innerGL);
		
		ele2Verify.setParent(innerContainer);
		ele2Verify.setLayoutData(new GridData(oldGridData.horizontalAlignment, oldGridData.verticalAlignment, 
				oldGridData.grabExcessHorizontalSpace, oldGridData.grabExcessVerticalSpace, 1, 1));
		
		Label validatorLabel = new Label(innerContainer, SWT.NONE);
		validatorLabel.setForeground(new Color(null, 255,0,0));
		validatorLabel.setLayoutData(new GridData(SWT.FILL, SWT.CENTER, true, false, 1, 1));
		ele2Verify.setData(Constants.VALIDATOR_KEY, validatorLabel);
		return true;
	}

	public String getChipType() {
		return chipType;
	}

	public void setChipType(String chipType) {
		this.chipType = chipType;
	}
	
	
}
