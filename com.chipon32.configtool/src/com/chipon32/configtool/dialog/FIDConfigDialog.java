package com.chipon32.configtool.dialog;

import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Button;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Event;
import org.eclipse.swt.widgets.Listener;
import org.eclipse.swt.widgets.Shell;
import org.eclipse.swt.widgets.Widget;

public class FIDConfigDialog extends Dialog implements Listener {
	public static final String DATA_KEY_FID = "BUTTON_DATA_FID";
	private static final String WILDCARD = "WILDCARD";
	private static final String VERIFY_ONLY = "Verify_only";
	private static final String KEY_USAGE = "KEY_USAGE";
	private static final String DEBUG_PROTECTION = "DEBUG_PROTECTION";
	private static final String BOOT_PROTECTION = "BOOT_PROTECTION";
	private static final String WRITE_PROTECTION = "WRITE_PROTECTION";
	private boolean writeProtection;
	private boolean bootProtection;
	private boolean debugProtection;
	private boolean keyUsage;
	private boolean verifyOnly;
	private boolean wildcard;

	protected FIDConfigDialog(Shell parentShell, String FID) {
		super(parentShell);
		resolveFID(FID);
	}
	
	
	private void resolveFID(String fID) {
		if(fID == null || fID.isEmpty()) {
			fID = "00000000";
		}
		this.writeProtection = getWriteProtection(fID);
		this.bootProtection = getBootProtection(fID);
		this.debugProtection = getDebugProtection(fID);
		this.keyUsage = getKeyUsage(fID);
		this.verifyOnly = getVerifyOnly(fID);
		this.wildcard = getWildCard(fID);
		
	}
	
	public static boolean getWriteProtection(String str) {
		if(str.length()<6) {
			return false;
		}
		return str.charAt(str.length()-6)=='0' ? false : true;
	}
	
	public static boolean getBootProtection(String str) {
		if(str.length()<5) {
			return false;
		}
		return str.charAt(str.length()-5)=='0' ? false : true;
	}
	
	public static boolean getDebugProtection(String str) {
		if(str.length()<4) {
			return false;
		}
		return str.charAt(str.length()-4)=='0' ? false : true;
	}
	
	public static boolean getKeyUsage(String str) {
		if(str.length()<3) {
			return false;
		}
		return str.charAt(str.length()-3)=='0' ? false : true;
	}
	
	public static boolean getVerifyOnly(String str) {
		if(str.length()<2) {
			return false;
		}
		return str.charAt(str.length()-2)=='0' ? false : true;
	}
	
	public static boolean getWildCard(String str) {
		if(str.length()<1) {
			return false;
		}
		return str.charAt(str.length()-1)=='0' ? false : true;
	}


	@Override
	protected Control createDialogArea(Composite parent) {
		Composite group = new Composite(parent, SWT.DIALOG_TRIM | SWT.CLOSE);
		group.setLayout(new GridLayout(2, true));
		group.setLayoutData(new GridData(GridData.FILL_BOTH));
		
		final Button writeProtection = new Button(group, SWT.CHECK);
		writeProtection.setText(WRITE_PROTECTION);
		writeProtection.setLayoutData(new GridData(SWT.LEFT, SWT.CENTER, true, false, 1, 1));
		writeProtection.addListener(SWT.Selection, this);
		writeProtection.setSelection(this.writeProtection);
		
		Button bootProtection = new Button(group, SWT.CHECK);
		bootProtection.setText(BOOT_PROTECTION);
		bootProtection.setLayoutData(new GridData(SWT.LEFT, SWT.CENTER, true, false, 1, 1));
		bootProtection.addListener(SWT.Selection, this);
		bootProtection.setSelection(this.bootProtection);
		
		Button debugProtection = new Button(group, SWT.CHECK);
		debugProtection.setText(DEBUG_PROTECTION);
		debugProtection.setLayoutData(new GridData(SWT.LEFT, SWT.CENTER, true, false, 1, 1));
		debugProtection.addListener(SWT.Selection, this);
		debugProtection.setSelection(this.debugProtection);
		
		Button keyUsage = new Button(group, SWT.CHECK);
		keyUsage.setText(KEY_USAGE);
		keyUsage.setLayoutData(new GridData(SWT.LEFT, SWT.CENTER, true, false, 1, 1));
		keyUsage.addListener(SWT.Selection, this);
		keyUsage.setSelection(this.keyUsage);
		
		Button verifyOnly = new Button(group, SWT.CHECK);
		verifyOnly.setText(VERIFY_ONLY);
		verifyOnly.setLayoutData(new GridData(SWT.LEFT, SWT.CENTER, true, false, 1, 1));
		verifyOnly.addListener(SWT.Selection, this);
		verifyOnly.setSelection(this.verifyOnly);
		
		Button wildCard = new Button(group, SWT.CHECK);
		wildCard.setText(WILDCARD);
		wildCard.setLayoutData(new GridData(SWT.LEFT, SWT.CENTER, true, false, 1, 1));
		wildCard.addListener(SWT.Selection, this);
		wildCard.setSelection(this.wildcard);
		
		return group;
		
	}
	
	public String getFID() {
		StringBuilder sBuilder = new StringBuilder();
		sBuilder.append(writeProtection? "1" : "0");
		sBuilder.append(bootProtection? "1" : "0");
		sBuilder.append(debugProtection? "1" : "0");
		sBuilder.append(keyUsage? "1" : "0");
		sBuilder.append(verifyOnly? "1" : "0");
		sBuilder.append(wildcard? "1" : "0");
		return sBuilder.toString();
	}

	@Override
	public void handleEvent(Event event) {
		Widget wg = event.widget;
		if(!(wg instanceof Button)) {
			return;
		}
		Button bt = (Button) wg;
		switch (bt.getText()) {
		case WRITE_PROTECTION:
			this.writeProtection = bt.getSelection();
			break;
		case BOOT_PROTECTION:
			this.bootProtection = bt.getSelection();
			break;
		case DEBUG_PROTECTION:
			this.debugProtection = bt.getSelection();
			break;
		case KEY_USAGE:
			this.keyUsage = bt.getSelection();
			break;
		case VERIFY_ONLY:
			this.verifyOnly = bt.getSelection();
			break;
		case WILDCARD:
			this.wildcard = bt.getSelection();
			break;
		default:
			break;
		}
		
	}

}
