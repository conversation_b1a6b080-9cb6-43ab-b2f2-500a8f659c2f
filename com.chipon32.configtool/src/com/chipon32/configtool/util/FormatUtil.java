package com.chipon32.configtool.util;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import org.eclipse.swt.widgets.Text;

public class FormatUtil {

	private static final String EMPTY_CHECK_DATA = "00";
	private static final String EMPTY_DATA = "FF";
	
	/**
	 * replace sourceString every found in rule: regex change to newStr
	 * @param sourceString
	 * @param regex: string to find
	 * @param newStr: find string replace to
	 * @return string after replace.
	 */
    public static String replaceString(String sourceString, String regex, String newStr) {
        String deletedString = sourceString.replaceAll(regex, newStr);
        return deletedString;
    }
    
    /**
     * change string to hex(one char one hex)
     * @param input
     * @return
     */
    public static String toHex(String input) {
        StringBuilder output = new StringBuilder();
        for (char c : input.toCharArray()) {
            output.append(String.format("%02X", (int) c)); //$NON-NLS-1$
	    }
	    return output.toString();
	}
	
    /**
     * binary string format rule
     * @param str: input string to validate
     * @return
     */
	public static boolean isBinary(String str) {
	    return str.matches("^[01]+$");
	}
	
	/**
	 * hex string format rule
	 * @param hex: input string to validate
	 * @return
	 */
	public static boolean isValidHex(String hex) {
        String hexPattern = "^[0-9a-fA-F]+$";
        return hex.matches(hexPattern);
    }

	public static Long hexStrList2Decimal(List<String> hex) {
		return hexStr2Decimal(list2String(hex));
	}
	
	/**
	 * 16 -> 10
	 * @param hex
	 * @return decimal
	 */
	public static Long hexStr2Decimal(String hex) {
		try {
			return Long.parseLong(hex,16);
		} catch (NumberFormatException e) {
			return null;
		}
	}
	
	public static String list2String(List<String> hex) {
		if(hex == null) {
			return null;
		}
		if(hex.isEmpty()) {
			return "";
		}
		StringBuilder sBuilder = new StringBuilder();
		for(String s : hex) {
			sBuilder.append(s);
		}
		return sBuilder.toString();
	}

	/**
	 * get start address of input startAddr's container page.
	 * @param startAddr: input addr
	 * @return start address of container page
	 */
	public static String getStartAddrByLine(String startAddr) {
		if(startAddr == null || startAddr.isEmpty()) {
			return null;
		}
		long addr = Long.parseLong(startAddr,16);
		long res = addr-addr%Constants.ONE_KB_LEN;
		return Long.toHexString(res);
	}
	
	/**
	 * rever list for Little-Endian and Big-Endian convert
	 * @param data
	 * @return
	 */
	public static List<String> reWriteReverseData(List<String> data) {
		Collections.reverse(data);
		return data;
	}
	
	public static List<String> string2List(String str) {
		if(str == null || str.isEmpty()) {
			return new ArrayList<>();
		}
		if(str.length()%2!=0) {
			str = Constants.EMPTY_DATA_HEX_CHAR + str;
		}
		List<String> res = new ArrayList<>();
		for (int i=0;i<str.length()-1;i+=2) {
			res.add(str.substring(i,i+2));
		}
		return res;
		
	} 
	
	/**
	 * hex string to bytes
	 * @param str: input hex string list
	 * @return bytes array
	 */
	public static byte[] hexStrList2Bytes(List<String> str) {
		if(str == null || str.isEmpty()) {
			return null;
		}
		byte[] data = new byte[str.size()];
		for (int i = 0; i < str.size(); i ++) {
            data[i] = (byte) ((Character.digit(str.get(i).charAt(0), 16) << 4)
                    + Character.digit(str.get(i).charAt(1), 16));
        }
		return data;
	}
	
	/**
	 * bytes to hex list
	 * @param str: input bytes
	 * @return hex array
	 */
	public static List<String> bytes2StrHexList(byte[] bytes) {
		if(bytes == null || bytes.length<0) {
			return null;
		}
		List<String> data = new ArrayList<>();
		for (int i = 0; i < bytes.length; i ++) {
            data.add(String.format("%02X",bytes[i]));
        }
		return data;
	}

	public static boolean isSame(List<String> cMAC_Result, List<String> old_boot_mac) {
		if(cMAC_Result.size()!=old_boot_mac.size()) {
			return false;
		}
		for(int i=0;i<cMAC_Result.size();i++) {
			if(!cMAC_Result.get(i).toUpperCase().equals(old_boot_mac.get(i).toUpperCase())) {
				return false;
			}
		}
		return true;
	}

	public static boolean keyIsEmpty(List<String> input) {
		if(input == null) {
			return true;
		}
		Boolean res1 = true;
		for(int i=0;i<input.size();i++) {
			if(!input.get(i).toUpperCase().equals(EMPTY_DATA)) {
				res1 = false;
				break;
			}
		}
		Boolean res2 = true;
		for(int i=0;i<input.size();i++) {
			if(!input.get(i).toUpperCase().equals(EMPTY_CHECK_DATA)) {
				res2 = false;
				break;
			}
		}
		return res1 | res2;
	}
	
	/**
	 * 判断空的标志是否为全FF
	 * @param input
	 * @return
	 */
	public static boolean isEmpty(List<String> input) {
		if(input == null) {
			return true;
		}
		boolean res1 = true;
		for(int i=0;i<input.size();i++) {
			if(!input.get(i).toUpperCase().equals(EMPTY_DATA)) {
				res1 = false;
				break;
			}
		}
		
		return res1;
	}

	public static String getInverseCode(String hex) {
		if(hex == null || hex.isEmpty()) {
			return null;
		}
        long decimal = Long.parseLong(hex,16);
        long negation = ~decimal;
        String res = Long.toHexString(negation);
        return res.substring(res.length()-hex.length());

	}
	
	/**
	 * fill high bit with 0 to length:8.
	 * @param input
	 * @return
	 */
	public static String toEight(String input) {
		if(input == null || input.isEmpty()) {
			return null;
		}
		String tmp = input.toUpperCase();
		while(tmp.length()<8)
			tmp="0"+tmp;
		
		return tmp.substring(tmp.length()-8);
	}

	public static String decimalStr2HexStr(String decimalStr) {
		if(decimalStr == null || decimalStr.isEmpty()) {
			return null;
		}
		long decimal = Long.parseLong(decimalStr);
		return Long.toHexString(decimal);
	}

	public static String binaryStr2HexStr(String bString) {
		if(bString == null || bString.isEmpty()) {
			return null;
		}
        StringBuilder sb = new StringBuilder();
        int len = bString.length();
        for (int i = 0; i <= len / 4; i++){
            //每4个二进制位转换为1个十六进制位
            String temp = "";
            if(len-(i+1) * 4 <= 0) {
                temp = bString.substring(0, len-i * 4);
            } else {
                temp = bString.substring(len-(i+1) * 4, len-i * 4);
            }
            if(temp.isEmpty()) {
            	break;
            }
            int tempInt = Integer.parseInt(temp, 2);
            String tempHex = Integer.toHexString(tempInt).toUpperCase();
            sb.append(tempHex);
        }
        return sb.reverse().toString();
	}
	
	public static String hexString2binaryString(String hexString) {
	    if (hexString == null || hexString.length() == 0) {
	    	return null;
	    }
	    String bString = "", tmp;
	    for(int i = 0; i < hexString.length(); i++) {
	    	tmp = "0000" + Integer.toBinaryString(Integer.parseInt(hexString.substring(i, i + 1), 16));
	    	bString += tmp.substring(tmp.length() - 4);
	    }
	    return bString;
	  }

	/**
	 * hex -> short
	 * @param hexString
	 * @return
	 */
	public static short[] hexStr2ShortArr(String hexString) {
	    if (hexString == null || hexString.isEmpty()) {
	        return new short[0];
	    }
	    if (hexString.startsWith("0x") || hexString.startsWith("#")) {
	        hexString = hexString.substring(2);
	    }
	    if (hexString.length() % 2 != 0) {
	        hexString = "0" + hexString;
	    }
	 
	    byte[] bytes = new byte[hexString.length() / 2];
	    for (int i = 0; i < hexString.length(); i += 2) {
	        bytes[i / 2] = (byte) ((Character.digit(hexString.charAt(i), 16) << 4)
	                              + Character.digit(hexString.charAt(i+1), 16));
	    }
	 
	    short[] shorts = new short[bytes.length];
	    for (int i = 0; i < shorts.length; i++) {
	        shorts[i] = (short) (bytes[i] & 0xFF);
	    }
	    return shorts;
	}
	
	/**
	 * short -> hex
	 * @param array
	 * @return
	 */
    public static String shortArrToHexStr(short[] array) {
        StringBuilder hexString = new StringBuilder();
        for (short value : array) {
            hexString.append(String.format("%02X", value & 0xFF));
        }
        return hexString.toString();
    }
    
    public static String byteArrToHexStr(byte[] b){
    	StringBuffer sbf = new StringBuffer();
		for (int i = 0; i < b.length; i++) { 
			String hex = Integer.toHexString(b[i] & 0xFF); 
			if (hex.length() == 1) { 
				hex = '0' + hex; 
			} 
			sbf.append(hex.toUpperCase()+" ");
		}
		return sbf.toString().trim();
    }

    /**
     * check hex string with input length. warp for key cid uid check
     * @param text: intput SWT.Text 
     * @param len: length expect
     * @return if is valid hex string 
     */
	public static boolean checkHexStr(Text text, int len) {
		if(text.getText().isEmpty()) {
			return false;
		}
		String textTrim = FormatUtil.replaceString(text.getText(), Constants.KEY_SPERATOR_REGEX, "");

		if(textTrim.length()!=len) {
			return false;
		}
		if(!FormatUtil.isValidHex(textTrim)) {
			return false;
		}
		
		return true;
	}

	public static List<String> reVerseDataForCMAC(List<String> data) {
		List<String> nowresult = new ArrayList<>();
		int thisContenLength = data.size();
		for(int i=0; i<thisContenLength ;)
		{
			if(i<=(thisContenLength-16))
			{
				int j=16;
				while((--j)>=0)
				{
					nowresult.add(data.get(i+j));
				}
				i+=16;continue;	// 
			}
			else
			{
				int j=thisContenLength-i;
				while((--j)>=0)
				{
					nowresult.add(data.get(i+j));
				}
				i+=thisContenLength;
				break;	
			}	
		}
		return nowresult;
	}
	
	/**
	 * @param fill high bits with char c (if longger cut; else fill)
	 * @param i
	 * @param c
	 * @return
	 */
	public static String FillHighBits(String str, int len, char c) {
		if(str.length()==len) {
			return str;
		}
		str = str.substring(Math.max(0, str.length()-len));
		StringBuilder sb = new StringBuilder(str);
		while(sb.length()<len) {
			sb.insert(0, c);
		}
		return sb.toString();
	}
	
	public static String bytesToHex(byte[] bytes) {
	    StringBuilder sb = new StringBuilder();
	    for (byte b : bytes) {
	        sb.append(String.format("%02X", b));
	    }
	    return sb.toString();
	}
}
