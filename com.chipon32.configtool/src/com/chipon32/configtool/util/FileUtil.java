package com.chipon32.configtool.util;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

import org.eclipse.swt.SWT;
import org.eclipse.swt.widgets.FileDialog;
import org.eclipse.swt.widgets.Shell;

import com.chipon32.configtool.adaptor.ProAndIDEAdaptor;

import java.io.FileFilter;

public class FileUtil {
	public static boolean makeDir(String rootPath) {
        File dir = new File(rootPath);
        if(dir.exists()) {
           return true;
        }
        return dir.mkdirs();
    }
    
    public static List<File> searchFile(File folder, final String keyWord) {

        File[] subFolders = folder.listFiles(new FileFilter() {
            @Override
            public boolean accept(File pathname) {
                if (pathname.isDirectory()
                        || (pathname.isFile() && pathname.getName().toLowerCase().contains(keyWord.toLowerCase())))
                    return true;
                return false;
            }
        });

        List<File> result = new ArrayList<File>();// 声明一个集合
        assert subFolders != null;
        for (File subFolder : subFolders) {
            if (subFolder.isFile()) {
                result.add(subFolder);
            } else {
                List<File> foldResult = searchFile(subFolder, keyWord);
                result.addAll(foldResult);
            }
        }
        return result;
    }
    
	public static File openSaveFileDialog(Shell shell, String defaultFileName, ProAndIDEAdaptor adaptor) {
		FileDialog fileDialog = new FileDialog(shell, SWT.SAVE);
		fileDialog.setFilterExtensions(new String[]{"*.txt","*.json"});
		fileDialog.setFileName(defaultFileName);
		fileDialog.setOverwrite(true);
		fileDialog.setText("Save");
		String filePath = fileDialog.open();
		if(filePath==null) {
			return null;
		}
		File file = new File(filePath);
		if(!FileUtil.makeDir(file.getParent())) {
			adaptor.printErrorMsg(String.format("Save file %s failed, no access when generate diectory.", filePath));
			return null;
		}
		return file;
	}
	
	public static File openOpenFileDIalog(Shell shell, ProAndIDEAdaptor adaptor) {
		FileDialog fileDialog = new FileDialog(shell, SWT.OPEN);
		fileDialog.setFilterExtensions(new String[]{"*.txt","*.json"});
		fileDialog.setText("Open");
		String filePath = fileDialog.open();
		if(filePath==null) {
			return null;
		}
		File file = new File(filePath);
		if(!file.exists()) {
			adaptor.printErrorMsg(String.format("File: %s not exist.", filePath));
			return null;
		}
		return file;
	}
    
}
