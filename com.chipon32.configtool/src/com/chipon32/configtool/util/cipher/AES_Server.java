package com.chipon32.configtool.util.cipher;

import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.List;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.spec.SecretKeySpec;

import org.bouncycastle.crypto.CipherParameters;
import org.bouncycastle.crypto.Mac;
import org.bouncycastle.crypto.engines.AESEngine;
import org.bouncycastle.crypto.macs.CMac;
import org.bouncycastle.crypto.params.KeyParameter;

import com.chipon32.configtool.util.FormatUtil;

//import com.chipon32.configtool.util.FormatUtil;

public class AES_Server {

	/**
	 * @param text  明文/base64明文
	 * @param key  密钥
	 * @param transformation  
	 * @param mode  加密/解密
	 * @throws BadPaddingException 
	 * @throws IllegalBlockSizeException 
	 * @throws NoSuchPaddingException 
	 * @throws NoSuchAlgorithmException 
	 * @throws InvalidKeyException 
	 */
	public static String base64EncodeOrDecode(String text, byte[] key, String transformation, boolean mode) throws IllegalBlockSizeException, BadPaddingException, NoSuchAlgorithmException, NoSuchPaddingException, InvalidKeyException {
		Cipher cipher = Cipher.getInstance(transformation);
		SecretKeySpec secretKeySpec = new SecretKeySpec(key,transformation);
		cipher.init(mode ? cipher.ENCRYPT_MODE : cipher.DECRYPT_MODE, secretKeySpec);
		if(mode){
			text = padZero(text);
		}
		byte[] byteText= text.getBytes(StandardCharsets.UTF_8);
		byte[] resultBytes = cipher.doFinal(mode ? byteText : Base64Util.decodeBase64(text));
//		String resultString = new String(resultBytes, StandardCharsets.UTF_8);
//		System.out.println(FormatUtil.byteArrToHexStr(resultBytes));
		return mode? Base64Util.encodeBase64String(resultBytes): unpadZero(new String(resultBytes,StandardCharsets.UTF_8));
//		return mode? Base64Util.encodeBase64String(resultString) : unpadZero(resultString);
	}
	
	
	public static String padZero(String plainText) {
        int blockSize = 16;
        int paddingLength = blockSize - (plainText.length() % blockSize);
        if(paddingLength==16){
        	return plainText;
        }
        StringBuilder sb = new StringBuilder(plainText);
        for (int i = 0; i < paddingLength; i++) {
            sb.append('\0');
        }
        return sb.toString();
    }
	
	 public static String unpadZero(String paddedText) {
	      return paddedText.replaceAll("\0+$", "");
	 }
	 
	 public static String padCharacter(String plainText) {
	        int blockSize = 16;
	        int paddingLength = blockSize - (plainText.length() % blockSize);
	        if(paddingLength==16){
	        	return plainText;
	        }
	        StringBuilder sb = new StringBuilder(plainText);
	        for (int i = 0; i < paddingLength; i++) {
	            sb.append('#');
	        }
	        return sb.toString();
	 }
	 
	 
	 public static List<String> calcCMAC(List<String> key, String text) {
			byte[] cmacValue = calculateAESCMAC(key, text.getBytes());
			List<String> CMAC_Result = FormatUtil.bytes2StrHexList(cmacValue);
			
			return CMAC_Result;
		}
		

		private static byte[] calculateAESCMAC(List<String> key, byte[] textBytes) {
			 // create AES engine
		    AESEngine aesEngine = new AESEngine();
		    // create CMAC Object
		    Mac cmac = new CMac(aesEngine);
		    // init CMAC Object
		    CipherParameters params = new KeyParameter(FormatUtil.hexStrList2Bytes(key));
		    cmac.init(params);
		    // update CMAC object
		    cmac.update(textBytes, 0, textBytes.length);
		    // cal CMAC value
		    byte[] output = new byte[cmac.getMacSize()];
		    cmac.doFinal(output, 0);
		    return output;
		}
	
}
