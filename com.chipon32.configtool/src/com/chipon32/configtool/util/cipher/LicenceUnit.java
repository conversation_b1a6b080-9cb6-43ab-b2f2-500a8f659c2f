package com.chipon32.configtool.util.cipher;


public class LicenceUnit {
	static public String publicKey="MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCJwL5lbcINpQ5qW3todr64m7LeiUF9kNsNnTVWUzUHgXUpFhzKI1LPSVbGd/o4P5Q61tq3ulYCuUgujo0yAK7EnepOiyK6c5CJZMMpgEC698u2XchqJwXBwWWz6dQKPLO/gQhSnDOvF8W/Ex4WHV8BFDqOu37NOZV+ruJUr5ncLwIDAQAB";
	static public String priveteKey="MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAInAvmVtwg2lDmpbe2h2vribst6JQX2Q2w2dNVZTNQeBdSkWHMojUs9JVsZ3+jg/lDrW2re6VgK5SC6OjTIArsSd6k6LIrpzkIlkwymAQLr3y7ZdyGonBcHBZbPp1Ao8s7+BCFKcM68Xxb8THhYdXwEUOo67fs05lX6u4lSvmdwvAgMBAAECgYAG4j3hfbGj0uq8f+PT3ZM8UHOI0V0XdESuEnwW20OcI2kCa5t2snsx5p1Sf65eXzb+FbyKT+J6cl3cDb2akRQqpjpcTryBjPv2Yff9yYBH7saYo7Z8pm85NtkvX5ZmHa7qtiXe3W8PxGlgTZeXaMZHb9zn4kcfwekqifDtW/hA2QJBAKV9uYa8VUaOjVla8vXvOooR9pIXEJY57U6nyKiWS5KvVoUIFFDCwNXKWCJC353JVhN40yRuAwUON4LbA64ssYUCQQDVF2pUdWGKsOVNlgneL+8IJOKOZjO9DJSUUqe8S5qLS28Tj+o9iHGK+syPx6u4v/C3uJte3Lotk9Pxrcpoj2sjAkEAiWn17Wzgmi+wKuwcJh44y4DS7sd8HGGJLPCg62uw4OH6w4fL5qtwF5VmzTweFhSiBpUjLXKzyzTc3ykgyzWNSQJBALQDp+vZjaFgdxXoHkX5MHEs18d8R1D5g7sZaGIFbHUoMJnSBgwda+4AmTcNcuAMJgrOhVLKHO3L6ExOiKN0JQkCQHbwtR/NHpYFg1wT4svVMqg4RYOTmAuM9HnY5nouV6+gYZEJ7GqZJ9Si6NYOFH4NGLZsDIpHZlJJS0jcSxKAQkI=";
	
	public static String[] cmakKey = {"2B", "7E", "15", "16", 
									"28", "AE", "D2", "A6", 
									"AB", "F7", "15", "88", 
									"09", "CF", "4F", "3C"};
	public static String[] aes128Key = {"2B", "7E", "15", "16", 
										"28", "AE", "D2", "A6", 
										"AB", "F7", "15", "88", 
										"09", "CF", "4F", "3C"};
	
	public static String[] aes256Key = {"2B", "7E", "15", "16", 
										"28", "AE", "D2", "A6", 
										"AB", "F7", "15", "88", 
										"09", "CF", "4F", "3C",
										"10", "11", "12", "13", 
										"14","15", "16", "17",
										"18", "19", "1A", "1B",
										"1C", "1D", "1E", "1F"};
}
