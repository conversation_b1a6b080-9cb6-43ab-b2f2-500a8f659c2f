package com.chipon32.configtool.util.cipher;


import java.io.BufferedWriter;
import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStreamWriter;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.security.Key;
import java.security.KeyFactory;
import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.Signature;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import org.apache.commons.codec.binary.Base64;

import java.util.HashMap;
import java.util.Map;

import javax.crypto.Cipher;


public class RSA2048_Server {

    private final static String RSA_ALGORITHM = "RSA";
    private final static String SIGNATURE_ALGORITHM = "MD5withRSA";
    private final static String RSA_PUBLIC_KEY = "RSAPublicKey";
    private final static String RSA_PRIVATE_KEY = "RSAPrivateKey";
//    ################################################################

    public static String encodeBase64(byte[] binaryData) {
        return Base64.encodeBase64String(binaryData);
    }

    public static byte[] decodeBase64(String encoded) {
        return Base64.decodeBase64(encoded);
    }
//  ################################################################
    //...................
	public static Map<String,Object> getKey(int level)  {
        // ...................
		Map<String,Object> keyMap = new HashMap<>(2);
		try {        
	        // ..................
	        KeyPairGenerator keyPairGen = KeyPairGenerator.getInstance(RSA_ALGORITHM);
	        // 1024......
	        keyPairGen.initialize(level);
	        // ...................
	        KeyPair keyPair = keyPairGen.generateKeyPair();
	        RSAPublicKey publicKey = (RSAPublicKey) keyPair.getPublic();
	        RSAPrivateKey privateKey = (RSAPrivateKey) keyPair.getPrivate();
	
	        keyMap.put(RSA_PUBLIC_KEY, publicKey);
	        keyMap.put(RSA_PRIVATE_KEY, privateKey);
	        //++++++++++++++++++++++++++++++++++++++++++++++
		}catch (NoSuchAlgorithmException e) {
			// TODO: handle exception
		}        
        return keyMap;
	}
	
	public static PrivateKey getPrivateKey(Map<String,Object> keyMap)
	{
        return (PrivateKey) keyMap.get(RSA_PRIVATE_KEY);
	}
	public static PublicKey getPublicKey(Map<String,Object> keyMap)
	{
        return (PublicKey) keyMap.get(RSA_PUBLIC_KEY);
	}
//  ################################################################
    public static byte[] encryptPrivateKey(byte[] binaryData, String privateKey) {
    	try {
	        byte[] keyBytes = decodeBase64(privateKey);
	        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(keyBytes);
	
	        KeyFactory keyFactory = KeyFactory.getInstance(RSA_ALGORITHM);
	        PrivateKey priKey = keyFactory.generatePrivate(keySpec);
	
	        Cipher cipher = Cipher.getInstance(keyFactory.getAlgorithm());
	        cipher.init(Cipher.ENCRYPT_MODE, priKey);
	        return cipher.doFinal(binaryData);
    	}catch (Exception e) {
    		System.out.print("@@@encyrpt RSA Failed:"+e.getMessage()+"\r\n");
    		return null;
		}  
    }
    public static byte[] encryptPrivateKey(String binaryData, String privateKey) {
    	try {
	        byte[] keyBytes = decodeBase64(privateKey);
	        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(keyBytes);
	
	        KeyFactory keyFactory = KeyFactory.getInstance(RSA_ALGORITHM);
	        PrivateKey priKey = keyFactory.generatePrivate(keySpec);
	
	        Cipher cipher = Cipher.getInstance(keyFactory.getAlgorithm());
	        cipher.init(Cipher.ENCRYPT_MODE, priKey);
	        return cipher.doFinal(binaryData.getBytes());
    	}catch (Exception e) {
    		System.out.print("@@@encyrpt RSA Failed:"+e.getMessage()+"\r\n");
    		return null;
		}  
    }

    public static byte[] encryptPublicKey(byte[] binaryData, String publicKey)  {
    	try {
	        byte[] keyBytes = decodeBase64(publicKey);
	        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(keyBytes);
	
	        KeyFactory keyFactory = KeyFactory.getInstance(RSA_ALGORITHM);
	        PublicKey pubKey = keyFactory.generatePublic(keySpec);
	
	        Cipher cipher = Cipher.getInstance(keyFactory.getAlgorithm());
	        cipher.init(Cipher.ENCRYPT_MODE, pubKey);
	        return cipher.doFinal(binaryData);
    	}catch (Exception e) {
    		System.out.print("@@@encyrpt RSA Failed:"+e.getMessage()+"\r\n");
    		return null;
		}    	
    }
    
    public static byte[] encryptPublicKey(String binaryData, String publicKey)  {
    	try {
	        byte[] keyBytes = decodeBase64(publicKey);
	        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(keyBytes);
	
	        KeyFactory keyFactory = KeyFactory.getInstance(RSA_ALGORITHM);
	        PublicKey pubKey = keyFactory.generatePublic(keySpec);
	
	        Cipher cipher = Cipher.getInstance(keyFactory.getAlgorithm());
	        cipher.init(Cipher.ENCRYPT_MODE, pubKey);
	        return cipher.doFinal(binaryData.getBytes());
    	}catch (Exception e) {
    		System.out.print("@@@encyrpt RSA Failed:"+e.getMessage()+"\r\n");
    		return null;
		}    	
    }

    public static byte[] encryptRSA(String plainText, PublicKey publicKey) {
    	try {
	        Cipher cipher = Cipher.getInstance("RSA");
	        cipher.init(Cipher.ENCRYPT_MODE, publicKey);
	        return cipher.doFinal(plainText.getBytes());
		}catch (Exception e) {
			System.out.print("@@@Decrypty RSA Failed:"+e.getMessage()+"\r\n");
			return null;
		} 
    }
    public static byte[] encryptRSA(String plainText, PrivateKey privateKey) {
    	try {
	        Cipher cipher = Cipher.getInstance("RSA");
	        cipher.init(Cipher.ENCRYPT_MODE, privateKey);
	        return cipher.doFinal(plainText.getBytes());
		}catch (Exception e) {
			System.out.print("@@@Decrypty RSA Failed:"+e.getMessage()+"\r\n");
			return null;
		} 
    }
//  ################################################################

    public static byte[] decryptPrivateKey(byte[] binaryData, String privateKey)  {
    	try {
	        byte[] keyBytes = decodeBase64(privateKey);
	        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(keyBytes);
	
	        KeyFactory keyFactory = KeyFactory.getInstance(RSA_ALGORITHM);
	        PrivateKey priKey = keyFactory.generatePrivate(keySpec);
	
	        Cipher cipher = Cipher.getInstance(keyFactory.getAlgorithm());
	        cipher.init(Cipher.DECRYPT_MODE, priKey);
	        return cipher.doFinal(binaryData);
		}catch (Exception e) {
			System.out.print("@@@Decrypty RSA Failed:"+e.getMessage()+"\r\n");
			return null;
		} 
    }
    
    public static byte[] decryptPrivateKey(String plainText, String privateKey)  {
    	try {
        byte[] keyBytes = decodeBase64(privateKey);
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(keyBytes);

        KeyFactory keyFactory = KeyFactory.getInstance(RSA_ALGORITHM);
        PrivateKey priKey = keyFactory.generatePrivate(keySpec);

        Cipher cipher = Cipher.getInstance(keyFactory.getAlgorithm());
        cipher.init(Cipher.DECRYPT_MODE, priKey);
        return cipher.doFinal(plainText.getBytes());
		}catch (Exception e) {
			System.out.print("@@@Decrypty RSA Failed:"+e.getMessage()+"\r\n");
			return null;
		} 
    }

    public static byte[] decryptPublicKey(byte[] binaryData, String publicKey) {
    	try {
        byte[] keyBytes = decodeBase64(publicKey);
        X509EncodedKeySpec x509KeySpec = new X509EncodedKeySpec(keyBytes);

        KeyFactory keyFactory = KeyFactory.getInstance(RSA_ALGORITHM);
        PublicKey pubKey = keyFactory.generatePublic(x509KeySpec);

        Cipher cipher = Cipher.getInstance(keyFactory.getAlgorithm());
        cipher.init(Cipher.DECRYPT_MODE, pubKey);
        return cipher.doFinal(binaryData);
		}catch (Exception e) {
			System.out.print("@@@Decrypty RSA Failed:"+e.getMessage()+"\r\n");
			return null;
		} 
    }
    
    public static byte[] decryptPublicKey(String plainText, String publicKey) {
    	try {
        byte[] keyBytes = decodeBase64(publicKey);
        X509EncodedKeySpec x509KeySpec = new X509EncodedKeySpec(keyBytes);

        KeyFactory keyFactory = KeyFactory.getInstance(RSA_ALGORITHM);
        PublicKey pubKey = keyFactory.generatePublic(x509KeySpec);

        Cipher cipher = Cipher.getInstance(keyFactory.getAlgorithm());
        cipher.init(Cipher.DECRYPT_MODE, pubKey);
        return cipher.doFinal(plainText.getBytes());
		}catch (Exception e) {
			System.out.print("@@@Decrypty RSA Failed:"+e.getMessage()+"\r\n");
			return null;
		} 
    }

    public static byte[] decryptRSA(byte[] encryptedText, PrivateKey privateKey)  {
    	try {
    	Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.DECRYPT_MODE, privateKey);
        byte[] decryptedBytes = cipher.doFinal(encryptedText);
        return decryptedBytes;
		}catch (Exception e) {
			System.out.print("Decrypty RSA Failed:"+e.getMessage()+"\r\n");
			return null;
		} 
    }
    
    public static byte[] decryptRSA(byte[] encryptedText, PublicKey publicKey)  {
    	try {
    	Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.DECRYPT_MODE, publicKey);
        byte[] decryptedBytes = cipher.doFinal(encryptedText);
        return decryptedBytes;
		}catch (Exception e) {
			System.out.print("@@@Decrypty RSA Failed:"+e.getMessage()+"\r\n");
			return null;
		} 
    }
    
//  ################################################################
    public static String sign(byte[] binaryData, String privateKey) {
    	try {
        byte[] keyBytes = decodeBase64(privateKey);
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(keyBytes);

        KeyFactory keyFactory = KeyFactory.getInstance(RSA_ALGORITHM);
        PrivateKey priKey = keyFactory.generatePrivate(keySpec);

        Signature signature = Signature.getInstance(SIGNATURE_ALGORITHM);
        signature.initSign(priKey);
        signature.update(binaryData);
        return encodeBase64(signature.sign());
		}catch (Exception e) {
			System.out.print("@@@sign RSA Failed:"+e.getMessage()+"\r\n");
			return null;
		} 
    }

//  ################################################################
    public static boolean verify(byte[] binaryData, String publicKey, String sign){
    	try {
        byte[] keyBytes = decodeBase64(publicKey);
        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(keyBytes);

        KeyFactory keyFactory = KeyFactory.getInstance(RSA_ALGORITHM);
        PublicKey pubKey = keyFactory.generatePublic(keySpec);


        Signature signature = Signature.getInstance(SIGNATURE_ALGORITHM);
        signature.initVerify(pubKey);
        signature.update(binaryData);
        return signature.verify(decodeBase64(sign));
		}catch (Exception e) {
			System.out.print("@@@verify RSA Failed:"+e.getMessage()+"\r\n");
			return false;
		} 
    }
//  ################################################################
//  ################################################################

    public static KeyPair generateRSAKeyPair(int level) throws Exception {
        KeyPairGenerator keyGen = KeyPairGenerator.getInstance("RSA");
        keyGen.initialize(level); // 
        return keyGen.generateKeyPair();
    }
//  ################################################################
    private static final String UTF8 = "UTF-8";
    private static final String PUBLIC_KEY_FILE = "D:/RSA/" + "PUBLIC_request.key";
    private static final String PRIVATE_KEY_FILE = "D:/RSA/" + "PRIVATE_response.key";
    
    private static void writeDataBuffer(            File file,            String content    ) {
        try {
            if (file.getParentFile() != null) {            
                file.getParentFile().mkdirs();
            }           
            file.createNewFile();
            FileOutputStream stream =  new FileOutputStream(file);          
            OutputStreamWriter outputStreamWriter =    new OutputStreamWriter(stream, UTF8);
            BufferedWriter writer = new BufferedWriter(outputStreamWriter);
            writer.write(content);
            writer.flush();
            writer.close();
        } catch (Exception e) {
            e.printStackTrace();
        }

    }
    //################################################################
    public static String loadPublicKey(PublicKey publicKey) {
        return keyToString(publicKey);
    }
    public static String loadPrivateKey(PrivateKey privateKey) {
        return keyToString(privateKey);
    }
    //################################################################
    private static String keyToString(Key key) {
        try {
            byte[] keyBytes = key.getEncoded();
            return new String(Base64.encodeBase64(keyBytes), UTF8);
        } catch(Exception e) {
            e.printStackTrace();
            return null;
        }
    }
    //################################################################
    public static PublicKey loadPublicKey(String key) {
        try {          
            if (key == null) {
                throw new RuntimeException("public-key is null.");
            }
            //X509EncodedKeySpec
            X509EncodedKeySpec keySpec = new X509EncodedKeySpec(
                    Base64.decodeBase64(key.getBytes())
            );
            KeyFactory keyFactory = KeyFactory.getInstance(RSA_ALGORITHM);
            return keyFactory.generatePublic(keySpec);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }
    public static PrivateKey loadPrivateKey(String key) {
        try {            
            assert key!=null : "private-key is null.";           
            PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(
                    Base64.decodeBase64(key.getBytes())
            );
            KeyFactory keyFactory = KeyFactory.getInstance(RSA_ALGORITHM);
            return keyFactory.generatePrivate(keySpec);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }
    
    //################################################################
    public static void TestRSA2048(String[] args) throws Exception {
//###############################################################
//            KeyPair keyPair = generateRSAKeyPair(1024);
            
    		Map<String,Object> map = new HashMap<>(2);
//            RSAPublicKey publicKey = (RSAPublicKey) keyPair.getPublic();
//            RSAPrivateKey privateKey = (RSAPrivateKey) keyPair.getPrivate();
          RSAPublicKey publicKey =	(RSAPublicKey)loadPublicKey("MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCJwL5lbcINpQ5qW3todr64m7LeiUF9kNsNnTVWUzUHgXUpFhzKI1LPSVbGd/o4P5Q61tq3ulYCuUgujo0yAK7EnepOiyK6c5CJZMMpgEC698u2XchqJwXBwWWz6dQKPLO/gQhSnDOvF8W/Ex4WHV8BFDqOu37NOZV+ruJUr5ncLwIDAQAB");
          RSAPrivateKey privateKey = (RSAPrivateKey) loadPrivateKey("MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAInAvmVtwg2lDmpbe2h2vribst6JQX2Q2w2dNVZTNQeBdSkWHMojUs9JVsZ3+jg/lDrW2re6VgK5SC6OjTIArsSd6k6LIrpzkIlkwymAQLr3y7ZdyGonBcHBZbPp1Ao8s7+BCFKcM68Xxb8THhYdXwEUOo67fs05lX6u4lSvmdwvAgMBAAECgYAG4j3hfbGj0uq8f+PT3ZM8UHOI0V0XdESuEnwW20OcI2kCa5t2snsx5p1Sf65eXzb+FbyKT+J6cl3cDb2akRQqpjpcTryBjPv2Yff9yYBH7saYo7Z8pm85NtkvX5ZmHa7qtiXe3W8PxGlgTZeXaMZHb9zn4kcfwekqifDtW/hA2QJBAKV9uYa8VUaOjVla8vXvOooR9pIXEJY57U6nyKiWS5KvVoUIFFDCwNXKWCJC353JVhN40yRuAwUON4LbA64ssYUCQQDVF2pUdWGKsOVNlgneL+8IJOKOZjO9DJSUUqe8S5qLS28Tj+o9iHGK+syPx6u4v/C3uJte3Lotk9Pxrcpoj2sjAkEAiWn17Wzgmi+wKuwcJh44y4DS7sd8HGGJLPCg62uw4OH6w4fL5qtwF5VmzTweFhSiBpUjLXKzyzTc3ykgyzWNSQJBALQDp+vZjaFgdxXoHkX5MHEs18d8R1D5g7sZaGIFbHUoMJnSBgwda+4AmTcNcuAMJgrOhVLKHO3L6ExOiKN0JQkCQHbwtR/NHpYFg1wT4svVMqg4RYOTmAuM9HnY5nouV6+gYZEJ7GqZJ9Si6NYOFH4NGLZsDIpHZlJJS0jcSxKAQkI=");
          KeyPair keyPair =new KeyPair(publicKey, privateKey);
    		
            map.put(RSA_PUBLIC_KEY, publicKey);
            map.put(RSA_PRIVATE_KEY, privateKey);
 
    		System.out.println("public: "+getPublicKey(map));
    		System.out.println("private:"+getPrivateKey(map));
    		
	        writeDataBuffer(new File(PUBLIC_KEY_FILE), loadPublicKey(keyPair.getPublic()));
	        writeDataBuffer(new File(PRIVATE_KEY_FILE), loadPrivateKey(keyPair.getPrivate()));
	        
	        String one="";String two="";
	        
	        try {
	            byte[] bytes = Files.readAllBytes(Paths.get(PUBLIC_KEY_FILE));
	            System.out.println("鍏挜锛?"+new String(bytes, StandardCharsets.UTF_8));
	            one =  new String(bytes, StandardCharsets.UTF_8);
	        } catch (Exception e) {
	            e.printStackTrace();	
	           
	        }
	       
	        try {
	            byte[] bytes = Files.readAllBytes(Paths.get(PRIVATE_KEY_FILE));
	            System.out.println("绉侀挜锛?"+new String(bytes, StandardCharsets.UTF_8));
	            two =  new String(bytes, StandardCharsets.UTF_8);
	        } catch (Exception e) {
	            e.printStackTrace();	
	        }
	        RSAPublicKey rsaPublicKey = (RSAPublicKey) loadPublicKey(one);
	        RSAPrivateKey rsaPrivateKey = (RSAPrivateKey) loadPrivateKey(two);
    		System.out.println("鍏挜1锛?"+rsaPublicKey);
    		System.out.println("绉侀挜2锛?"+rsaPrivateKey);
    		//+++++++++++++++++++++++++++++++++++++++++++++++++++++++
            String plainText = "178BFBFF00860F01!active!2599-01-01@2023-1021@gjz";
          //+++++++++++++++++++++++++++++++++++++++++++++++++++++++
            byte[] encryptedText2 = encryptRSA(plainText, keyPair.getPrivate());
            String enOut2=encodeBase64(encryptedText2);
            System.out.println("Encrypted Text2: " + enOut2);
            encryptedText2=decodeBase64(enOut2);

            String decryptedText2 = new String(decryptRSA(encryptedText2, keyPair.getPublic()));
            System.out.println("Decrypted Text2: " + decryptedText2);
          //+++++++++++++++++++++++++++++++++++++++++++++++++++++++
            byte[] encryptedText = encryptRSA(plainText, keyPair.getPublic());
            String enOut=encodeBase64(encryptedText);
            String enOut3=encodeBase64(keyPair.getPublic().getEncoded());
            System.out.println("Encrypted Text1: " + enOut);
            System.out.println("pK Text1: " + enOut3);
            String decryptedText = new String(decryptRSA(encryptedText, keyPair.getPrivate()));
            System.out.println("Decrypted Text1: " + decryptedText);

          //+++++++++++++++++++++++++++++++++++++++++++++++++++++++
    		//###############################################################
    		String content = "鏈姞瀵嗘暟鎹?";

    		System.out.println("============   鍒嗛殧绗?     ===========");
    		// 2.浣跨敤绉侀挜鍔犲瘑
    		byte[] encodeContent = encryptPrivateKey(content.getBytes(),loadPrivateKey(getPrivateKey(map)));
    		System.out.println("绉侀挜鍔犲瘑鍚庣殑鏁版嵁锛?"+new String(encodeContent));

    		// 3.浣跨敤鍏挜瑙ｅ瘑
    		byte[] decodeContent = decryptPublicKey(encodeContent,loadPublicKey(getPublicKey(map)));
    		System.out.println("鍏挜瑙ｅ瘑鍚庣殑鏁版嵁锛?"+new String(decodeContent));

    		System.out.println("============   鍒嗛殧绗?     ===========");
    		// 4.浣跨敤鍏挜鍔犲瘑
    		byte[] encodeContent2 = encryptPublicKey(content.getBytes(),loadPublicKey(getPublicKey(map)));
    		System.out.println("鍏挜鍔犲瘑鍚庣殑鏁版嵁锛?"+new String(encodeContent2));

    		// 5.浣跨敤绉侀挜瑙ｅ瘑
    		byte[] decodeContent2 = decryptPrivateKey(encodeContent2,loadPrivateKey(getPrivateKey(map)));
    		System.out.println("绉侀挜瑙ｅ瘑鍚庣殑鏁版嵁锛?"+new String(decodeContent2));

    		System.out.println("============   鍒嗛殧绗?     ===========");
    		// 6.鍔犵
    		String sign = sign(content.getBytes(),loadPrivateKey(getPrivateKey(map)));
    		System.out.println("鍔犵鍚庣殑鏁版嵁锛?"+sign);

    		// 7.楠岀
    		boolean result = verify(content.getBytes(),loadPublicKey(getPublicKey(map)),sign);
    		System.out.println("楠岀缁撴灉锛?"+result);
    	}
}
