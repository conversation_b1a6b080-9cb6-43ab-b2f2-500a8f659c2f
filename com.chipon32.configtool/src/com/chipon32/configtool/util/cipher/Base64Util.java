package com.chipon32.configtool.util.cipher;

import java.nio.charset.StandardCharsets;

import org.apache.commons.codec.binary.Base64;

public class Base64Util {

	public static String encodeBase64String(String data) {
		byte[] encodedBytes = Base64.encodeBase64(data.getBytes(/*StandardCharsets.UTF_8*/));
	    return new String(encodedBytes, StandardCharsets.UTF_8);
    }
//
//    public static String decodeBase64(String encodedData) {
//    	byte[] decodedBytes = Base64.decodeBase64(encodedData.getBytes(StandardCharsets.UTF_8));
//        return new String(decodedBytes, StandardCharsets.UTF_8);
//    }
    
    public static byte[] encodeBase64(String data) {
		byte[] encodedBytes = Base64.encodeBase64(data.getBytes(/*StandardCharsets.UTF_8*/));
	    return encodedBytes;
    }

    public static byte[] decodeBase64(String encodedData) {
    	byte[] decodedBytes = Base64.decodeBase64(encodedData.getBytes(/*StandardCharsets.UTF_8*/));
        return decodedBytes;
    }
	
    public static String encodeBase64String(byte[] binaryData) {
        return Base64.encodeBase64String(binaryData);
    }
}
