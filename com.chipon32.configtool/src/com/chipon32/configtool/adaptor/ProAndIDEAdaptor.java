package com.chipon32.configtool.adaptor;

import java.util.List;

public interface ProAndIDEAdaptor {
	public void printErrorMsg(String msg);

	void printInfoMsg(String msg);

	public boolean powerOnBeforeWriteData(String chipType);

	public List<String> readData(String aimstoragecfguser, String keyAddr, int keyLen);

	public List<String> calcCMAK(List<String> key, Long startAddr, Long len, List<String> data);

	public boolean powerOff();

	/*
	 * this method should erase the last 1KB alignment,and then, do reverse high and low position of each byte before write data.
	 */
	public boolean writeData(List<String> data, String addr);

	public String getAimStorageCFGUser();
	
	public String getAimStorageFlash();

	public void rePower();

	public void KDF(short[] hexStr2ShortArr, short[] keyUpdateEncC, short[] k1Arr);

	public void CBC( short[] in1, short[] key1, short [] IV, int inputlength, short out[]);

	public void aes_cmac(short[] in,  int length,  short[] out,  short[] key);

	public void aes_128_encrypt(short in[],  short out[],  short key[]);
	
	public String getCipherDebugKey();

	public void setDebugCipherKey(String debugKey);
}
