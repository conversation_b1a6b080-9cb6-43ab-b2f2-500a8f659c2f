package com.chipon32.debug.ui.dilaogs;


import org.eclipse.core.resources.IResource;
import org.eclipse.core.resources.ResourcesPlugin;
import org.eclipse.core.runtime.CoreException;
import org.eclipse.core.runtime.Path;
import org.eclipse.debug.core.model.Breakpoint;
import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.jface.dialogs.IDialogConstants;
import org.eclipse.jface.dialogs.MessageDialog;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.events.SelectionListener;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Shell;
import org.eclipse.swt.widgets.Combo;
import org.eclipse.swt.widgets.Text;
import org.eclipse.ui.PlatformUI;
import org.eclipse.swt.SWT;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.layout.GridData;


import com.chipon32.debug.core.breakpoint.ChiponAddrBreakpoint;
import com.chipon32.debug.core.breakpoint.ChiponLineBreakpoint;
import com.chipon32.debug.core.model.ChiponThread;
import com.chipon32.debug.ui.views.disassembly.ChiponDisassemblyView;


public class AddBreakpointDialog extends Dialog {
	
	private Combo combo;
	private Text text;
	Breakpoint breakpoint;
	

	private String[] breakPointTypes = new String[]{
			Messages.AddBreakpointDialog_0,Messages.AddBreakpointDialog_1
	};
	
	private Label lblConditions;
	private Text txtConditions;

	/**
	 * Create the dialog.
	 * @param parentShell
	 */
	public AddBreakpointDialog(Shell parentShell) {
		super(parentShell);
	}

	/**
	 * Create contents of the dialog.
	 * @param parent
	 */
	@Override
	protected Control createDialogArea(Composite parent) {
		Composite container = (Composite) super.createDialogArea(parent);
		GridLayout gridLayout = (GridLayout) container.getLayout();
		gridLayout.numColumns = 2;
		combo = new Combo(container, SWT.READ_ONLY|SWT.DROP_DOWN);
		
		text = new Text(container, SWT.BORDER);
		GridData gd_text = new GridData(SWT.LEFT, SWT.CENTER, true, false, 1, 1);
		gd_text.widthHint = GridData.FILL_HORIZONTAL;
		text.setLayoutData(gd_text);
		text.setToolTipText(Messages.AddBreakpointDialog_2);
		text.setMessage(Messages.AddBreakpointDialog_3);
		
		text.setFocus();
		combo.setItems(breakPointTypes);
		combo.select(1);

		
		final Label tipsLabel = new Label(container, SWT.NONE);
		GridData lData = new GridData(SWT.FILL, SWT.UP, true, true, 2, 1);
		lData.heightHint = 17;
		
		tipsLabel.setLayoutData(lData);
		
		tipsLabel.setText(Messages.AddBreakpointDialog_4+"\n\t"); //$NON-NLS-2$

		lblConditions = new Label(container, SWT.NONE);
		lblConditions.setLayoutData(new GridData(SWT.LEFT, SWT.CENTER, true, false, 1, 1));
		lblConditions.setText(Messages.AddBreakpointDialog_lblConditions_text);
		
		txtConditions = new Text(container, SWT.BORDER);
		txtConditions.setText("");
		txtConditions.setLayoutData(new GridData(SWT.FILL, SWT.CENTER, true, false, 1, 1));
		txtConditions.setEnabled(true);
		
		combo.addSelectionListener(new SelectionListener() {
			@Override
			public void widgetSelected(SelectionEvent e) {
				if(combo.getSelectionIndex() == 1){
					text.setToolTipText(Messages.AddBreakpointDialog_6);
					text.setMessage(Messages.AddBreakpointDialog_7);
					tipsLabel.setText(Messages.AddBreakpointDialog_8+"\n\t"); //$NON-NLS-2$
				}
				else if(combo.getSelectionIndex() == 0){
					
					text.setToolTipText(Messages.AddBreakpointDialog_10);
					text.setMessage(Messages.AddBreakpointDialog_11);
					
					tipsLabel.setText(Messages.AddBreakpointDialog_12);
				}
				
			}
			
			@Override
			public void widgetDefaultSelected(SelectionEvent e) {

			}
		});
		return container;
	}

	/**
	 * Create contents of the button bar.
	 * @param parent
	 */
	@Override
	protected void createButtonsForButtonBar(Composite parent) {
		createButton(parent, IDialogConstants.OK_ID, IDialogConstants.OK_LABEL, true);
		createButton(parent, IDialogConstants.CANCEL_ID, IDialogConstants.CANCEL_LABEL, false);
	}

	
	@Override
	protected void configureShell(Shell newShell) {
		// TODO Auto-generated method stub
		super.configureShell(newShell);
		newShell.setText(Messages.AddBreakpointDialog_13);
	}

	/**
	 * Return the initial size of the dialog.
	 */
	@Override
	protected Point getInitialSize() {
		return new Point(660, 240);
	}
	
	@Override
	protected void okPressed() {	
		
		if(text.getText().trim().length() == 0||text.getText() == null){
			MessageDialog.openWarning(Display.getCurrent().getActiveShell(), Messages.AddBreakpointDialog_14, Messages.AddBreakpointDialog_15);
			return;
		}
		if(combo.getSelectionIndex() == 1 && text.getText().trim().contains("*")){ //$NON-NLS-1$
			MessageDialog.openWarning(Display.getCurrent().getActiveShell(), Messages.AddBreakpointDialog_17, Messages.AddBreakpointDialog_18);
			return;
		}
		if(combo.getSelectionIndex() == 0 && !text.getText().trim().contains(":")){ //$NON-NLS-1$
			MessageDialog.openWarning(Display.getCurrent().getActiveShell(), Messages.AddBreakpointDialog_20, Messages.AddBreakpointDialog_21);
			return;
		}
			
		int line = 0;
		if(combo.getSelectionIndex() == 0){ //添加行断点
			String[] datas = text.getText().trim().split(":"); //$NON-NLS-1$
			try {
				line = Integer.parseInt(datas[1]);
			} catch (NumberFormatException e1) {
				MessageDialog.openWarning(Display.getCurrent().getActiveShell(), Messages.AddBreakpointDialog_25, Messages.AddBreakpointDialog_26);
				return;
			}
			IResource fileResource = ResourcesPlugin.getWorkspace().getRoot().findMember(new Path(datas[0]));
			if(fileResource == null ){
				MessageDialog.openWarning(Display.getCurrent().getActiveShell(), Messages.AddBreakpointDialog_27, Messages.AddBreakpointDialog_28);
				return;
			}
			try {
				breakpoint = new ChiponLineBreakpoint(fileResource, line, txtConditions.getText().trim());	
			} catch (CoreException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
		
		if(combo.getSelectionIndex() == 1){ //添加地址断点
			String addr = text.getText().trim();
			if(!addr.startsWith("0x")) //$NON-NLS-1$
			{
				addr="0x"+addr; //$NON-NLS-1$
			}
				
//				IEditorPart editor = PlatformUI.getWorkbench().getActiveWorkbenchWindow().getActivePage().getActiveEditor();
//				if (editor != null && editor.getEditorInput() instanceof FileEditorInput) {
//					FileEditorInput fe = (FileEditorInput) editor.getEditorInput();
//					IFile file = fe.getFile();
//					IProject project = file.getProject();
//					IResource resource = ResourcesPlugin.getWorkspace().getRoot().findMember(
//						project.getName()+File.separator+
//						"Debug"+File.separator+project.getName()+".lst");
//					if(resource != null){
//						breakpoint = new ChiponAddrBreakpoint(resource, addr);
//					}
//				}
			
			ChiponDisassemblyView disassemblyView= (ChiponDisassemblyView) PlatformUI.getWorkbench().getActiveWorkbenchWindow().getActivePage().findView("com.chipon32.debug.ui.view.disassembly");
			IResource resource = disassemblyView.getResource();
			//获取行号
			line = ChiponThread.locationData.get(addr);
//			IResource resource = ResourcesPlugin.getWorkspace().getRoot();
			if(resource != null){
				try {
					breakpoint = new ChiponAddrBreakpoint(resource, line-1, addr, txtConditions.getText().trim());	
				} catch (CoreException e) {
					e.printStackTrace();
				}
			}
		}
		
		super.okPressed();
	}
	
	public Breakpoint getBreakpoint() {
		return breakpoint;
	}
}
