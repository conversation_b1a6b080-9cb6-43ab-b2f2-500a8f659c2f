AddBreakpointDialog_0=File Line Break Point
AddBreakpointDialog_1=Address Break Point
AddBreakpointDialog_10=Project Name/Path/File Name:Line Number,Like Demo/main.c:30
AddBreakpointDialog_11=Project Name/Path/File Name:Line Number,Like Demo/main.c:30
AddBreakpointDialog_12=\#\#Input "Project Name/Path/File Name:Line Number", Like Demo/main.c:30
AddBreakpointDialog_13=Add Breakpoint
AddBreakpointDialog_14=Prompt
AddBreakpointDialog_15=Empty Input no-allowed
AddBreakpointDialog_17=Prompt
AddBreakpointDialog_18=Please Input Right Value Followed Prompt
AddBreakpointDialog_2=Input Address,Like:252 Means 0x252
AddBreakpointDialog_20=Prompt
AddBreakpointDialog_21=Please Input Right Value Followed Format
AddBreakpointDialog_22=Prompt
AddBreakpointDialog_23=All Break Point Used, Delete No Use And Do Again
AddBreakpointDialog_25=Prompt
AddBreakpointDialog_26=Please Input Right Line Number Followed Prompt
AddBreakpointDialog_27=Prompt
AddBreakpointDialog_28=Please Input Right File And Path Followed Prompt
AddBreakpointDialog_3=Input Address,Like:252 Means 0x252
AddBreakpointDialog_4=\#\#\Input Address, Like:252 Means 0x252
AddBreakpointDialog_6=Input Address,Like:252 Means 0x252
AddBreakpointDialog_7=Input Address,Like:252 Means 0x252
AddBreakpointDialog_8=\#\#Input Address,Like:252 Means 0x252
AddBreakpointDialog_lblConditionTips_text=Please enter a condition
AddBreakpointDialog_btnCheckButton_text=Conditional
AddBreakpointDialog_lblConditions_text=Conditions\:
