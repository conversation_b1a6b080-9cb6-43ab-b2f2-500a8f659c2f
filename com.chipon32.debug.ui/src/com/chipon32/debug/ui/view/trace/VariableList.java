package com.chipon32.debug.ui.view.trace;

import java.util.ArrayList;
import java.util.List;

import com.chipon32.debug.core.elf.IVariableEntry;


public class VariableList {
	//用以存储追踪的变量集合
	private static List<List<IVariableEntry>> variablesList = new ArrayList<List<IVariableEntry>>();
	
	private static List<IVariableEntry> traceVariables = new ArrayList<>();
	
	public static List<List<IVariableEntry>> getVariableList(){
		return variablesList;
	}
	
	public static void addVariables(List<IVariableEntry> variables){
		variablesList.add(variables);
		traceVariables = variables;
	}
	
	public static void removeVariables(List<IVariableEntry> variables){
		variablesList.remove(variables);
	}
	
	public static boolean removeVariables(int index){
		if(index >= 0 && index < variablesList.size()){
		    if(variablesList.get(index) == traceVariables){
	            variablesList.remove(index);
	            traceVariables.clear();
                return true;
		    }
            variablesList.remove(index);
            return true;
		}
		return false;
	}
	
	public static void initVariables(){
		variablesList = new ArrayList<List<IVariableEntry>>();
	}
	
	public static void setTraceVariables(List<IVariableEntry> variables){
	    traceVariables = variables;
	}
	
	public static List<IVariableEntry> getTraceVariables(){
	    return traceVariables;
	}
}

