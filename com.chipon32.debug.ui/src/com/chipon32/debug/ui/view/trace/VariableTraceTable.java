package com.chipon32.debug.ui.view.trace;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.swt.widgets.Display;

import com.chipon32.debug.core.elf.IVariableEntry;
import com.chipon32.debug.core.model.trace.TraceTableItem;
import com.chipon32.debug.core.model.trace.TraceTableManager;

public class VariableTraceTable {
	
	private List<IVariableEntry> traceVariables = new ArrayList<>();
	//public static TraceTableItem[] tracetableItems= null;
	public static TraceTableItem[] tracetableList = null;
	
	public VariableTraceTable() {
	}
	
	public VariableTraceTable(List<IVariableEntry> variables) {
		//在Table上设置
		setVariablesTraceInTable(variables);
		
		TraceRunnable.traceRunning = true;
		Display.getCurrent().asyncExec(TraceVariableView.updater);
	}
	
	private void setVariablesTraceInTable(List<IVariableEntry> variables) {
		if(variables != null) {
			traceVariables = variables;
			TraceRunnable.traceVariables = variables;
			//tracetableItems = new TraceTableItem[variables.size()];
			tracetableList = new TraceTableItem[variables.size()];
			for(int i = 0; i < variables.size(); i++) {
				String sampleName = variables.get(i).getName();
				String sampleAddr = variables.get(i).getAddress();
				TraceTableItem item = new TraceTableItem(sampleName,sampleAddr,0.0,"0");
				tracetableList[i] = item;
			}	
			TraceTableManager.getManager().addTrace(tracetableList);
		}
	}

	public void stop() {
		Display.getCurrent().disposeExec(TraceVariableView.updater);
	}
	
	//更新监控变量
	public void updateVariableTrace(List<IVariableEntry> variables) {
		if(TraceRunnable.traceRunning) {
			TraceRunnable.traceRunning = false; //暂停updater线程执行
		}
		
		/*if(tracetableItems != null) {
			tracetableItems = null;
		}*/
		//移除上一次的tracetableList
		if(tracetableList != null) {
			TraceTableManager.getManager().removeTrace(tracetableList);
		}
		if(variables == null) {
			return;
		}
		
		//重新在Table上更新变量
		setVariablesTraceInTable(variables);
		
		if(!TraceRunnable.traceRunning) {
			Display.getCurrent().timerExec(20, TraceVariableView.updater);
			TraceRunnable.traceRunning = true;
		}
	}
}
