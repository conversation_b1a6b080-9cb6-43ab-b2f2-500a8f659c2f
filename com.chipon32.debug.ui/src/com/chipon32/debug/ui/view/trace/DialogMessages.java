package com.chipon32.debug.ui.view.trace;

import org.eclipse.osgi.util.NLS;

public class DialogMessages extends NLS {

	static {
		initializeMessages(DialogMessages.class.getName(), DialogMessages.class);
	}
	
	//Trace Variable View
	public static String ADD_TRACE_VARIABLES;
	public static String REMOVE_TRACE_VARIABLES;
	public static String VARIABLE;
	public static String TRACE_VARIABLE_DIALOG;
	public static String CHOOSE;
	public static String VARIABLE_NAME;
	public static String VARIABLE_TYPE;
	public static String SELECT_TRACE_VARIABLES;
	public static String TIME;
	public static String VALUE;
	public static String VARIABLE_TRACE;
	public static String DEBUG_NOT_START;
	public static String DIALOG_WARNING;
	public static String REMOVE_ALL_TRACE_VARIABLES;
	public static String MONITOR_VARIABLES_EXCEED;
	
}
