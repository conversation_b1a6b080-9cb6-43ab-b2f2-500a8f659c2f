package com.chipon32.debug.ui.view.trace;

import java.util.List;

import org.eclipse.jface.viewers.ITreeContentProvider;
import org.eclipse.jface.viewers.Viewer;

import com.chipon32.debug.core.elf.IVariableEntry;


public class TracedVariableDialogContentProvider implements ITreeContentProvider {

    @Override
    public void dispose() {
        
    }

    @Override
    public void inputChanged(Viewer viewer, Object oldInput, Object newInput) {
        
    }

    @Override
    public Object[] getElements(Object inputElement) {
		if(inputElement instanceof List) {
			return ((List<?>) inputElement).toArray();
		}
        return null;
    }

    @Override
    public Object[] getChildren(Object parentElement) {
    	if(parentElement instanceof IVariableEntry) {
    		return ((IVariableEntry) parentElement).getChildren().toArray();
    	}
        return null;
    }

    @Override
    public Object getParent(Object element) {
    	if(element instanceof IVariableEntry) {
    		return ((IVariableEntry) element).getParent();
    	}
        return null;
    }

    @Override
    public boolean hasChildren(Object element) {
    	if(element instanceof IVariableEntry) {
    		return ((IVariableEntry) element).getChildren().size() > 0;
    	}
        return false;
    }
    
	
}
