package com.chipon32.debug.ui.view.trace;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.eclipse.draw2d.Figure;
import org.eclipse.draw2d.MouseEvent;
import org.eclipse.draw2d.MouseListener;
import org.eclipse.nebula.visualization.xygraph.dataprovider.CircularBufferDataProvider;
import org.eclipse.nebula.visualization.xygraph.dataprovider.Sample;
import org.eclipse.nebula.visualization.xygraph.figures.ToolbarArmedXYGraph;
import org.eclipse.nebula.visualization.xygraph.figures.Trace;
import org.eclipse.nebula.visualization.xygraph.figures.Trace.BaseLine;
import org.eclipse.nebula.visualization.xygraph.figures.Trace.ErrorBarType;
import org.eclipse.nebula.visualization.xygraph.figures.Trace.PointStyle;
import org.eclipse.nebula.visualization.xygraph.figures.Trace.TraceType;
import org.eclipse.nebula.visualization.xygraph.figures.XYGraph;
import org.eclipse.nebula.visualization.xygraph.linearscale.Range;
import org.eclipse.nebula.visualization.xygraph.util.XYGraphMediaFactory;
import org.eclipse.swt.widgets.Display;

import com.chipon32.debug.core.elf.IVariableEntry;
import com.chipon32.debug.core.model.ChiponDebugTarget;
import com.chipon32.debug.core.model.trace.TraceTableItem;
import com.chipon32.debug.core.model.trace.TraceTableManager;

public class VariableTraceGraph extends Figure {

	private XYGraph xyGraph;
	//private Runnable updater;
	private long t = 0;
	private double value;
	private ToolbarArmedXYGraph toolbarArmedXYGraph;

	private final String TITLE_NAME = DialogMessages.VARIABLE_TRACE;
	private final String TIME = DialogMessages.TIME;
	private final String VALUE = DialogMessages.VALUE;
	
	Trace[] traces = null;
	public static CircularBufferDataProvider[] traceProviders = null;
	private List<IVariableEntry> traceVariables = new ArrayList<>();

	public VariableTraceGraph() {
		xyGraph = new XYGraph();
		xyGraph.setTitle(TITLE_NAME);
		xyGraph.setFont(XYGraphMediaFactory.getInstance().getFont(XYGraphMediaFactory.FONT_TAHOMA));
		xyGraph.getPrimaryXAxis().setTitle(TIME);
		xyGraph.getPrimaryYAxis().setTitle(VALUE);
		xyGraph.getPrimaryXAxis().setRange(new Range(0, 200));
		xyGraph.getPrimaryXAxis().setDateEnabled(true);
		xyGraph.getPrimaryXAxis().setAutoScale(true);
		xyGraph.getPrimaryYAxis().setAutoScale(true);
		xyGraph.getPrimaryXAxis().setShowMajorGrid(true);
		xyGraph.getPrimaryYAxis().setShowMajorGrid(true);
		xyGraph.getPrimaryXAxis().setAutoScaleThreshold(0);
		xyGraph.getPrimaryXAxis().setAutoFormat(false);
//		xyGraph.getPrimaryYAxis().setAutoFormat(false);
		xyGraph.getPrimaryXAxis().setFormatPattern("HH:mm:ss");
//		xyGraph.getPrimaryYAxis().setFormatPattern("##.##");
		
		toolbarArmedXYGraph = new ToolbarArmedXYGraph(xyGraph);
		add(toolbarArmedXYGraph);
		xyGraph.setFocusTraversable(true);
		xyGraph.setRequestFocusEnabled(true);
		
	}

	public VariableTraceGraph(List<IVariableEntry> variables) {
		xyGraph = new XYGraph();
		xyGraph.setTitle(TITLE_NAME);
		xyGraph.setFont(XYGraphMediaFactory.getInstance().getFont(XYGraphMediaFactory.FONT_TAHOMA));
		xyGraph.getPrimaryXAxis().setTitle(TIME);
		xyGraph.getPrimaryYAxis().setTitle(VALUE);
		xyGraph.getPrimaryXAxis().setRange(new Range(0, 200));//new Range(0,20)
		xyGraph.getPrimaryXAxis().setDateEnabled(true);
		xyGraph.getPrimaryXAxis().setAutoScale(true);
		xyGraph.getPrimaryYAxis().setAutoScale(true);
		xyGraph.getPrimaryXAxis().setShowMajorGrid(true);
		xyGraph.getPrimaryYAxis().setShowMajorGrid(true);
		xyGraph.getPrimaryXAxis().setAutoScaleThreshold(0);
		xyGraph.getPrimaryXAxis().setAutoFormat(false);
//		xyGraph.getPrimaryYAxis().setAutoFormat(false);
		xyGraph.getPrimaryXAxis().setFormatPattern("HH:mm:ss");
//		xyGraph.getPrimaryYAxis().setFormatPattern("##.##");
		
		//在xyGraph上设置Traces
		setVariablesTraceInXyGraph(variables);
		
		toolbarArmedXYGraph = new ToolbarArmedXYGraph(xyGraph);
		add(toolbarArmedXYGraph);
		
		xyGraph.setFocusTraversable(true);
		xyGraph.setRequestFocusEnabled(true);
		
		xyGraph.getPlotArea().addMouseListener(new MouseListener.Stub() {
			@Override
			public void mousePressed(final MouseEvent me) {
				xyGraph.requestFocus();
			}
		});
		
		//示波器接收数据线程 ——提取出作为独立的线程了，与tableViewer共享
//		updater = new Runnable() {
//			public void run() {
//				if(traceVariables==null || traceVariables.isEmpty()) {
//					running = false;
//					return;
//				}
//				if (!ChiponDebugTarget.isDebugging){//调试终止
//					//System.out.println("stop updater...");
//					running = false;
//					return;
//				}
////				System.out.println("run updater..."+updater);
//				
//				if(!ChiponDebugTarget.fThread.isSuspended()) {//缓存调试器返回的监控变量的值
//					Map<String,String> valueMap = new HashMap<>();
//					for(String str : ChiponDebugTarget.traceDataList) {
////						System.out.println(str);
//						//tracedata返回格式：figure_oscillographdata:0-Addr:268442648_4:value: 0
//						//                figure_oscillographdata:1-Symbol:adc_current_scale:value: 0              
//						String[] strs = str.split(":");
//						if(strs.length == 5) {
//							if(strs[1].contains("Symbol")) {
//								valueMap.put(strs[2], strs[4]);
//							}else if(strs[1].contains("Addr")) {
//								String addrStr = strs[2];
//								String[] strArray = addrStr.split("_");
//								if(strArray.length == 2) {
//									String addr = Long.toHexString(Long.parseLong(strArray[0]));
//									valueMap.put(addr, strs[4]);
//								}
//							}
//							
//						}
//					}
//					
//					if(traceVariables.size() == traceProviders.length) { //屏蔽增加监控变量后traceProviders没及时更新导致两者长度不一致问题，跳过一次赋值
//						t = System.currentTimeMillis();
//						for (int i = 0; i < traceVariables.size(); i++) {
//							if(!traceVariables.get(i).isSymbol()) {
//								if(valueMap.get(traceVariables.get(i).getAddress()) != null) {
//									value = Double.parseDouble(valueMap.get(traceVariables.get(i).getAddress()).trim()));
//									Sample sample = new Sample(t,
//											value/*, 0.1 * Math.random(), 0.1 * Math.random(), t*0.0000001* Math.random(),
//													  t*0.0000001* Math.random(), "Sample"*/
//													 );		
//									traceProviders[i].addSample(sample);
//									traceProviders[i].setCurrentYDataTimestamp(t);								
//								}
//							}else {
//								if(valueMap.get(traceVariables.get(i).getName()) != null) {
//									value = Double.parseDouble(valueMap.get(traceVariables.get(i).getName()));
//									Sample sample = new Sample(t,
//											value/*, 0.1 * Math.random(), 0.1 * Math.random(), t*0.0000001* Math.random(),
//													  t*0.0000001* Math.random(), "Sample"*/
//													 );
//									
//									traceProviders[i].addSample(sample);
//									//xyGraph.performAutoScale();
//									traceProviders[i].setCurrentYDataTimestamp(t);	
//								}
//							}
//							
//						}
//					}
//				}
//				if(running) {
//					Display.getCurrent().timerExec(20, this);  //每20ms执行一次updater
//				}
//			}
//		};

		TraceRunnable.traceRunning = true;
		Display.getCurrent().asyncExec(TraceVariableView.updater);
		
	}

	/**
	 * 根据观察的变量在示波器图上添加Traces
	 * @param variables
	 */
	private void setVariablesTraceInXyGraph(List<IVariableEntry> variables) {
		if(variables!=null) {
			traceVariables = variables;
			TraceRunnable.traceVariables = variables;
			traces = new Trace[variables.size()];
			traceProviders = new CircularBufferDataProvider[variables.size()];
	
			for (int i = 0; i < variables.size(); i++) {
				traceProviders[i] = new CircularBufferDataProvider(true);
				traceProviders[i].setBufferSize(100);
				traceProviders[i].setUpdateDelay(100);
				traces[i] = new Trace(variables.get(i).getVariableNameStr(), xyGraph.getPrimaryXAxis(), xyGraph.getPrimaryYAxis(),
						traceProviders[i]);
				traces[i].setDataProvider(traceProviders[i]);
				traces[i].setTraceType(TraceType.SOLID_LINE);
				traces[i].setLineWidth(1);
				traces[i].setPointStyle(PointStyle.POINT );
				traces[i].setPointSize(4);
				traces[i].setBaseLine(BaseLine.NEGATIVE_INFINITY);
				traces[i].setAreaAlpha(100);
				traces[i].setAntiAliasing(true);
				traces[i].setErrorBarEnabled(false);
				traces[i].setYErrorBarType(ErrorBarType.BOTH);
				traces[i].setXErrorBarType(ErrorBarType.NONE);
				traces[i].setErrorBarCapWidth(3);
				xyGraph.addTrace(traces[i]);
			}
		}
		
	}

	
	public void stop() {
		Display.getCurrent().disposeExec(TraceVariableView.updater);
	}

	@Override
	protected void layout() {
		toolbarArmedXYGraph.setBounds(bounds.getCopy().shrink(5, 5));
		super.layout();
	}
	
	/**
	 * 更新监控变量
	 * @param variables
	 */
	public void updateVariableTrace(List<IVariableEntry> variables) {
		if(TraceRunnable.traceRunning) {
			TraceRunnable.traceRunning = false;//暂停updater线程执行
		}
		
		//移除上一次观察的trace信息
		if(traces!=null && traceProviders!=null) {
			for(int i=0; i<traces.length; i++) {
				traceProviders[i].clearTrace();
				xyGraph.removeTrace(traces[i]);
			}
		}
		if(variables==null) {
			return;
		}
		//重新在graph上设置Trace
		setVariablesTraceInXyGraph(variables);
		
		if(!TraceRunnable.traceRunning) {
			Display.getCurrent().timerExec(20, TraceVariableView.updater);
			TraceRunnable.traceRunning = true;
		}
	}

}
