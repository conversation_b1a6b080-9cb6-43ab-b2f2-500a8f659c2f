package com.chipon32.debug.ui.view.trace;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

import org.eclipse.core.resources.IFile;
import org.eclipse.jface.dialogs.MessageDialog;
import org.eclipse.jface.dialogs.TitleAreaDialog;
import org.eclipse.jface.viewers.ITreeViewerListener;
import org.eclipse.jface.viewers.TableViewer;
import org.eclipse.jface.viewers.TreeExpansionEvent;
import org.eclipse.jface.viewers.TreeViewer;
import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Shell;
import org.eclipse.swt.widgets.Tree;
import org.eclipse.swt.widgets.TreeColumn;
import org.eclipse.swt.widgets.TreeItem;
import org.eclipse.ui.PlatformUI;
import org.eclipse.ui.dialogs.ContainerCheckedTreeViewer;

import com.chipon32.debug.core.elf.ELFLoadAndParser;
import com.chipon32.debug.core.elf.IVariableEntry;
import com.chipon32.debug.core.model.ChiponDebugTarget;
import com.chipon32.debug.core.model.trace.TraceTableItem;
import com.chipon32.debug.core.model.trace.TraceTableManager;
import com.chipon32.debug.ui.util.TraceCommand;

import org.eclipse.swt.graphics.Point;

public class TracedVariableDialog extends TitleAreaDialog {

    private final String HELP_PLUGIN_ID = "com.chipon32.debug.ui.help.TraceView";
    private final String SHELL_NAME = DialogMessages.TRACE_VARIABLE_DIALOG;
    private final String TITLE_NAME = DialogMessages.SELECT_TRACE_VARIABLES;
    private TableViewer tableViewer;
    private VariableTraceGraph variableTraceGraph;
    private VariableTraceTable variableTraceTable;
    private TreeViewer treeViewer; //显示ELF文件中全局变量树
    private Shell dialogShell;
    

    protected TracedVariableDialog(Shell parentShell, TableViewer tableViewer,
    		VariableTraceGraph variableTraceGraph, VariableTraceTable variableTraceTable) {
        super(parentShell);
        this.tableViewer = tableViewer;
        this.variableTraceGraph = variableTraceGraph;
        this.variableTraceTable = variableTraceTable;
        parentShell.setLayoutData(new GridData(GridData.FILL_BOTH));
	}

    @Override
    protected void configureShell(Shell newShell) {
        dialogShell = newShell;
        newShell.setMinimumSize(new Point(450, 600));
        super.configureShell(newShell);
        newShell.setText(SHELL_NAME);
        PlatformUI.getWorkbench().getHelpSystem().setHelp(newShell, HELP_PLUGIN_ID);
    }
	
    @Override
    protected Control createDialogArea(Composite parent) {
    	setTitle(TITLE_NAME);
        Composite container = (Composite) super.createDialogArea(parent);
        treeViewer = new ContainerCheckedTreeViewer(container, SWT.BORDER | SWT.FULL_SELECTION);
        Tree tree = treeViewer.getTree();
        tree.setHeaderVisible(true);
        tree.setLinesVisible(true);
        tree.setDragDetect(true);
        tree.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, true, 1, 1));
        
        TreeColumn chooseColumn = new TreeColumn(tree, SWT.NONE);
        chooseColumn.setResizable(false);
        chooseColumn.setText(DialogMessages.VARIABLE_NAME);
        chooseColumn.setWidth(300);

        treeViewer.setLabelProvider(new TracedVariableDialogLabelProvider());
        treeViewer.setContentProvider(new TracedVariableDialogContentProvider());
        treeViewer.setInput(getTracedVariableList());
        treeViewer.addTreeListener(new TreeViewerAutoFitListener());
//        ViewerFilter[] viewerFilters = {
//                new TraceVariableDialogFilter()
//        };
//        treeViewer.setFilters(viewerFilters);

        return container;
    }

    /**
     * 从ELF文件中获取全局变量信息
     * @return
     */
    private List<IVariableEntry> getTracedVariableList() {
    	List<IVariableEntry> symbolList = new ArrayList<>();
    	IFile targetFile = ChiponDebugTarget.getBuildTargetELFFileFromProject();
    	File elfFile = targetFile.getLocation().toFile().getAbsoluteFile();
    	if(elfFile.exists()) {
    		symbolList = ELFLoadAndParser.getGlobalVariablesTree(elfFile);
    	} 
        return symbolList;
    }


    @Override
    protected void okPressed() {
        List<IVariableEntry> variables = new ArrayList<>();
        for (TreeItem item : treeViewer.getTree().getItems()) {
            variables.addAll(getChildItems(item));
        }
        
        if (!variables.isEmpty()) {
        	//添加监控变量数量控制，最多不能超过16个
        	if(variables.size()>16-TraceCommand.commandCount) {
        		MessageDialog.openWarning(dialogShell, DialogMessages.DIALOG_WARNING, DialogMessages.MONITOR_VARIABLES_EXCEED);
        		return;
        	}
        	
            for (int i = 0; i < variables.size(); i++) {
            	Long startAddr = Long.parseLong(variables.get(i).getAddress(),16);
            	//发送数字示波器监控变量命令
            	boolean isCommandSuccess = TraceCommand.dealCommand("kf32figure_oscillograph addr "+startAddr+" "+ variables.get(i).getTypeLen()/8, 3);
            	if(!isCommandSuccess) {
		        	return;
		        }
            	variables.get(i).setCommandNum(TraceCommand.currCommandNum);
			}
            VariableList.addVariables(variables);
            //更新左侧tableviewer上显示的可观察的变量列表
            tableViewer.setInput(VariableList.getVariableList());
            //更新右侧示波器图
            variableTraceGraph.updateVariableTrace(variables);
            variableTraceTable.updateVariableTrace(variables);
            /*TraceTableItem[] tracetableList = new TraceTableItem[variables.size()];
            //向List中添加Item
			for(int i = 0; i < variables.size(); i++) {
				String sampleName = variables.get(i).getName();
				String sampleAddr = variables.get(i).getAddress();
				TraceTableItem item = new TraceTableItem(sampleName,sampleAddr,0.0,"0");
				tracetableList[i] = item;
			}
            TraceTableManager.getManager().addTrace(tracetableList);*/
            
        }
        
        super.okPressed();
    }

    private List<IVariableEntry> getChildItems(TreeItem item) {
        List<IVariableEntry> variables = new ArrayList<>();
        for(TreeItem child : item.getItems()) {
        	variables.addAll(getChildItems(child));
        }
        if (item.getChecked() && item.getData() instanceof IVariableEntry) {
        	IVariableEntry variable = (IVariableEntry) item.getData();
        	if(variable.getChildren().isEmpty()) {
//        		System.err.println(variable.getName());
        		variables.add(variable);
        	}
        }
        return variables;
    }

    /**
     * 自动调整列宽
     * <AUTHOR>
     *
     */
    public class TreeViewerAutoFitListener implements ITreeViewerListener {
        public void treeExpanded(TreeExpansionEvent event) {
            packColumns((TreeViewer) event.getSource());
        }

        public void treeCollapsed(TreeExpansionEvent event) {
            packColumns((TreeViewer) event.getSource());
        }

        private void packColumns(final TreeViewer treeViewer) {
            treeViewer.getControl().getShell().getDisplay().asyncExec(new Runnable() {
                public void run() {
                    TreeColumn[] treeColumns = treeViewer.getTree().getColumns();
                    treeColumns[0].pack();
                    int size = treeColumns[0].getWidth();
                    if (size < 200) {
                        int width = (dialogShell.getSize().x - size) / treeColumns.length;
                        for (int i = 1; i < treeColumns.length; i++) {
                            treeColumns[i].setWidth(width);
                        }
                    } else {
                        for (int i = 1; i < treeColumns.length; i++) {
                            treeColumns[i].pack();
                        }
                    }
                }
            });
        }
    }


	@Override
	protected boolean isResizable() {
		return true;
	}

	
}
