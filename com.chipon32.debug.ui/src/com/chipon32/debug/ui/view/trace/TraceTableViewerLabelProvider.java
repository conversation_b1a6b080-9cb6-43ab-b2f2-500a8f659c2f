package com.chipon32.debug.ui.view.trace;

import java.util.List;

import org.eclipse.jface.viewers.ILabelProviderListener;
import org.eclipse.jface.viewers.ITableLabelProvider;
import org.eclipse.swt.graphics.Image;

import com.chipon32.debug.core.elf.IVariableEntry;


public class TraceTableViewerLabelProvider implements ITableLabelProvider{

	@Override
	public void addListener(ILabelProviderListener listener) {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void dispose() {
		// TODO Auto-generated method stub
		
	}

	@Override
	public boolean isLabelProperty(Object element, String property) {
		return false;
	}

	@Override
	public void removeListener(ILabelProviderListener listener) {
		// TODO Auto-generated method stub
		
	}

	@Override
	public Image getColumnImage(Object element, int columnIndex) {
		return null;
	}

	@Override
	public String getColumnText(Object element, int columnIndex) {
		if(element instanceof List){
			List<?> list = (List<?>) element;
			String text = "";
			for(int i=0; i<list.size(); i++){
				if(list.get(i) instanceof IVariableEntry){
					IVariableEntry variable = (IVariableEntry)list.get(i);
					String variableName = variable.getVariableNameStr();
					
					if(i < list.size() -1){
						text += variableName +  ", ";
					}else {
						text += variableName;
					}
				}
			}
			return text;
		}
		return null;
	}
}
