package com.chipon32.debug.ui.view.trace;

import java.util.List;

import org.eclipse.jface.action.Action;
import org.eclipse.jface.action.IMenuManager;
import org.eclipse.jface.action.MenuManager;
import org.eclipse.jface.dialogs.MessageDialog;
import org.eclipse.jface.viewers.TableViewer;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Menu;
import org.eclipse.swt.widgets.Table;
import org.eclipse.ui.actions.ActionGroup;

import com.chipon32.debug.core.elf.IVariableEntry;
import com.chipon32.debug.core.model.ChiponDebugTarget;
import com.chipon32.debug.core.util.ChiponDebugUtil;
import com.chipon32.debug.ui.util.TraceCommand;

/**
 * @author: 
 */
public class TraceActionGroup extends ActionGroup {
	private TableViewer tableViewer, traceTableViewer;
	private VariableTraceGraph variableTraceGraph;
	private VariableTraceTable variableTraceTable;

	public void fillContextMenu(IMenuManager mgr) {
		MenuManager menuManager = (MenuManager) mgr;
		menuManager.add(new AddAction());
		menuManager.add(new RemoveAction());
		menuManager.add(new RemoveAllAction());
		
		Table table = tableViewer.getTable();
		Menu menu = menuManager.createContextMenu(table);
		table.setMenu(menu);
		
		
	}

	/**
	 * @param tableViewer 
	 */
	public TraceActionGroup(TableViewer tableViewer,TableViewer traceTableViewer, VariableTraceGraph variableTraceGraph, VariableTraceTable variableTraceTable) {
		this.tableViewer = tableViewer;
		this.traceTableViewer = traceTableViewer;
		this.variableTraceGraph = variableTraceGraph;
		this.variableTraceTable = variableTraceTable;
	}

	/**
	 * <AUTHOR> 添加待观察的变量
	 */
	private class AddAction extends Action {
		public AddAction() {
			setText(DialogMessages.ADD_TRACE_VARIABLES);
		}

		@Override
		public void run() {
			if (ChiponDebugTarget.isDebugging && ChiponDebugUtil.isCurrentChiponThreadSuspended()) {
				//Shell shell = new Shell(SWT.RESIZE | SWT.MAX);
				TracedVariableDialog tracedVariableDialog = new TracedVariableDialog(Display.getDefault().getActiveShell(), tableViewer, variableTraceGraph, variableTraceTable);
				tracedVariableDialog.open();
				
			} else {
				MessageDialog.openInformation(Display.getDefault().getActiveShell(), DialogMessages.DIALOG_WARNING, DialogMessages.DEBUG_NOT_START);
			}
		}
	}

	/**
	 * <AUTHOR> 移除需观察的变量
	 */
	private class RemoveAction extends Action {
		public RemoveAction() {
			setText(DialogMessages.REMOVE_TRACE_VARIABLES);
		}

		public void run() {
			if (ChiponDebugTarget.isDebugging && ChiponDebugUtil.isCurrentChiponThreadSuspended()) {
			}else {
				MessageDialog.openInformation(Display.getDefault().getActiveShell(), DialogMessages.DIALOG_WARNING, DialogMessages.DEBUG_NOT_START);
				return;
			}
			
			Table table = tableViewer.getTable();
			if (table.getSelection() != null) {
				//发送删除当前监控命令
				Object selectedObj = tableViewer.getStructuredSelection().getFirstElement();
				
				if(selectedObj instanceof List) {
					List<?> objList = (List<?>)selectedObj;
//					objList.forEach(obj ->{ // *forEach中return跳出本次循环
//						if(obj instanceof IVariableEntry) {
//							IVariableEntry variable = (IVariableEntry) obj;
//							boolean isCommandSuccess = TraceCommand.dealCommand("kf32figure_oscillograph del "+variable.getCommandNum(), 1);
//							if(!isCommandSuccess) {
//						        return;
//						    }
//						}
//					});
					for(Object obj:objList) {
						if(obj instanceof IVariableEntry) {
							IVariableEntry variable = (IVariableEntry) obj;
							boolean isCommandSuccess = TraceCommand.dealCommand("kf32figure_oscillograph del "+variable.getCommandNum(), 1);
							if(!isCommandSuccess) {
						        return;
						    }
						}
					}
				}
				
				//删除右侧示波器中的监控变量.   如果多选则删除第一行
				int[] selIndexs = table.getSelectionIndices();
				if(selIndexs.length==0) {
					return;
				}
				if (VariableList.removeVariables(selIndexs[0])) {
					if(VariableList.getTraceVariables().isEmpty() || VariableList.getVariableList().isEmpty()) {
						variableTraceGraph.updateVariableTrace(null);//更新右侧示波器图
						variableTraceTable.updateVariableTrace(null);
					}
				}
				
				//删除表格中显示
				table.remove(selIndexs[0]);
			}
				
		}
	}
	
	/**
	 * <AUTHOR>
	 * 移除全部需观察的变量
	 */
	private class RemoveAllAction extends Action {
		public RemoveAllAction() {
			setText(DialogMessages.REMOVE_ALL_TRACE_VARIABLES);
		}

		public void run() {
			if(VariableList.getVariableList().isEmpty()) {
				return;
			}
			
			if (ChiponDebugTarget.isDebugging && ChiponDebugUtil.isCurrentChiponThreadSuspended()) {
			}else {
				MessageDialog.openInformation(Display.getDefault().getActiveShell(), DialogMessages.DIALOG_WARNING, DialogMessages.DEBUG_NOT_START);
				return;
			}
			
			//发送并执行删除全部监控的命令
			boolean isCommandSuccess = TraceCommand.dealCommand("kf32figure_oscillograph del",0);
		    if(!isCommandSuccess) {
	        	return;
	        }
			//移除左侧表格中的监控变量
			Table table = tableViewer.getTable();
			table.removeAll();
			
			VariableList.getVariableList().clear();
			VariableList.getTraceVariables().clear();
			
			//更新右侧示波器监控变量
			variableTraceGraph.updateVariableTrace(null);
			variableTraceTable.updateVariableTrace(null);
		}
	}
	
	
	
}
