package com.chipon32.debug.ui.view.trace;

import org.eclipse.jface.viewers.ITableLabelProvider;
import org.eclipse.jface.viewers.LabelProvider;
import org.eclipse.swt.graphics.Image;

import com.chipon32.debug.core.elf.IVariableEntry;


public class TracedVariableDialogLabelProvider extends LabelProvider implements ITableLabelProvider {


    @Override
	public String getColumnText(Object element, int columnIndex) {
	    if (element instanceof IVariableEntry) {
	    	IVariableEntry symbol = (IVariableEntry) element;
	        // 设置初始属性。
	        switch (columnIndex) {
	        case 0:// 选择列
	            return symbol.getName();
	        default:// 一般不会发生。
	            throw new IndexOutOfBoundsException("property index out of bounds.");
	        }
        }
        return null;
	}

    @Override
    public Image getColumnImage(Object element, int columnIndex) {
        return null;
    }

}
