package com.chipon32.debug.ui.view.trace;
import org.eclipse.jface.viewers.IStructuredContentProvider;
import org.eclipse.jface.viewers.TableViewer;
import org.eclipse.jface.viewers.Viewer;

import com.chipon32.debug.core.model.trace.TraceTableManager;
import com.chipon32.debug.core.model.trace.TraceTableManagerEvent;
import com.chipon32.debug.core.model.trace.TraceTableMangerListener;

public class TraceTableShowViewerContentProvider implements IStructuredContentProvider,TraceTableMangerListener{
    private TableViewer viewer;
    private TraceTableManager manager;
	@Override
	public Object[] getElements(Object inputElement) {
		// TODO Auto-generated method stub
		if(manager==null) {
			return null;
		}
			
		return manager.gettracetableList();
	}
	
	@Override
	public void inputChanged(Viewer viewer, Object oldInput, Object newInput) {
		this.viewer = (TableViewer)viewer;
		if(manager != null) {
			manager.removeTraceTableMangerListener(this);
		}
		manager = (TraceTableManager) newInput;
		if(manager != null) {
			manager.addTraceTableMangerListener(this);
		}
		
//		viewer.refresh();//¹Ø±ÕÊ±Å×Òì³£
	}

	@Override
	public void tracetableChanged(TraceTableManagerEvent event) {
		// TODO Auto-generated method stub
		viewer.getTable().setRedraw(false);
		try {
			viewer.add(event.getItemAdded());
			viewer.update(event.getItemModified(), null);
			viewer.remove(event.getItemRemoved());
		}
		finally {
			viewer.getTable().setRedraw(true);
		}
//		System.out.println("=======================================");
		viewer.refresh();
		
	}
}
