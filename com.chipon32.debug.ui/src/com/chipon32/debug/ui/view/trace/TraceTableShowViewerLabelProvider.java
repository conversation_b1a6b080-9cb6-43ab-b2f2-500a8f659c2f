package com.chipon32.debug.ui.view.trace;

import org.eclipse.jface.viewers.ITableLabelProvider;
import org.eclipse.jface.viewers.LabelProvider;
import org.eclipse.swt.graphics.Image;

import com.chipon32.debug.core.model.trace.TraceTableItem;

public class TraceTableShowViewerLabelProvider extends LabelProvider implements ITableLabelProvider{

	@Override
	public Image getColumnImage(Object element, int columnIndex) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public String getColumnText(Object element, int columnIndex) {
		// TODO Auto-generated method stub
		switch(columnIndex) {
		case 0:
			if(element != null) {
				return ((TraceTableItem) element).getSampleName();
			}
			return "";
		case 1:
			if(element != null)
				return ((TraceTableItem)element).getSampleAddress();
			return "";
		case 2:
			if(element != null)
				return (((TraceTableItem) element).getValueD()).toString();
			return "";
		case 3:
			if(element != null)
				return ((TraceTableItem)element).getValueH();
			return "";
		default:
			return "";
		}
	}
}
