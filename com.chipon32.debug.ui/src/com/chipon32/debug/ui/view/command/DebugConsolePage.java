package com.chipon32.debug.ui.view.command;

import java.util.List;

import org.eclipse.debug.core.DebugException;
import org.eclipse.debug.ui.DebugUITools;
import org.eclipse.jface.layout.GridDataFactory;
import org.eclipse.jface.viewers.ISelection;
import org.eclipse.jface.viewers.IStructuredSelection;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.events.SelectionListener;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Text;

import com.chipon32.debug.core.model.ChiponDebugCommandObserver;
import com.chipon32.debug.core.model.ChiponDebugTarget;
import com.chipon32.debug.core.model.ChiponStackFrame;
import com.chipon32.debug.core.model.ChiponThread;
import com.chipon32.debug.core.protocol.ChiponCommand;
import com.chipon32.debug.core.protocol.ChiponCommandResult;


public class DebugConsolePage extends DebuggerPage implements ChiponDebugCommandObserver{
	
	private ChiponThread  fThread;
	
	private LineBufferControl mConsoleOut;
	
	private DebugCommandLine mConsoleIn;
	
	private Composite control;
	
	private ChiponDebugTarget fTarget;

	public DebugConsolePage(DebuggerPagedView view) {
		super(view);
	}


	@Override
	public void setFocus() {
		this.control.setFocus();
	}

	@Override
	public void createPage(Composite parent) {
		 control = new Composite(parent, SWT.NONE);
		 GridDataFactory.fillDefaults().grab(true, true).applyTo(control);
		 control.setLayout(new FrameLayout());
		 
		 mConsoleOut = new LineBufferControl(control);
//		 mConsoleOut.append("normal\n");
//		 mConsoleOut.appendInput("input\n");
//		 mConsoleOut.appendError("error\n");
		 mConsoleOut.setDebugConsoleInstance();
		 
		 mConsoleIn = new DebugCommandLine(control);
		 mConsoleIn.setLayoutData(FrameLayout.BOTTOM);
		 Text textWidget = this.mConsoleIn.getTextWidget();
		 
		 //向ChipOnDebugTarget中添加CommandObserver
		 ChiponDebugTarget.addObserver(this);
		
		 mConsoleIn.getmGoButton().addSelectionListener(new SelectionListener() {
			@Override
			public void widgetSelected(SelectionEvent e) {
				String command = textWidget.getText();
				if(command != null && !"".equals(command)) {
					try {
						fThread = getfThread();
						if(fThread != null) {
							fTarget = fThread.getChiponDebugTarget();
							if(fTarget.isTerminated()) {
								return;
							}
							//fThread.manual(command);
							switch(command){
								case "continue":  //$NON-NLS-1$
								case "c":  //$NON-NLS-1$
									break;
								case "quit":  //$NON-NLS-1$
									fThread.terminate();
									fTarget.debuggerTerminated();
									break;	
								case "step":  //$NON-NLS-1$
								case "s":  //$NON-NLS-1$
									fThread.stepInto();
									break;
								case "stepi":  //$NON-NLS-1$
								case "si":         //$NON-NLS-1$
									fThread.stepiInto();
									break;	
								case "next": //$NON-NLS-1$
								case "n":    //$NON-NLS-1$
									fThread.stepOver();
									break;
								case "nexti": //$NON-NLS-1$
								case "ni":   //$NON-NLS-1$
									fThread.stepiOver();
									break;	
								case "finish":  //$NON-NLS-1$
									fThread.stepReturn();
									break;
								case "reset": //$NON-NLS-1$
									fThread.reset();
									break;
								case "pause": //$NON-NLS-1$
									fThread.suspend();
									break;
								default:
									fThread.manual(command);
									break;					
							}
						}
					} catch (DebugException e1) {
						e1.printStackTrace();
					}
				}
			}
			
			@Override
			public void widgetDefaultSelected(SelectionEvent e) {
				
			}
		});
		
	}


	@Override
	public Object getAnchor() {
		return null;
	}
	
	 /**
     * 
     * @return
     */    
	@SuppressWarnings("static-access")
	public ChiponThread getfThread() {
		fThread = null;  // 可能换调试程序
		{
			ISelection selection = DebugUITools.getDebugContextManager().getContextService(getmView().getSite().getWorkbenchWindow()).getActiveContext();
			 if (selection instanceof IStructuredSelection) {
		         Object element = ((IStructuredSelection)selection).getFirstElement();
		         if(element instanceof ChiponDebugTarget){
		         	fThread = ((ChiponDebugTarget)element).getCurrentThread();
		         }
		         if (element instanceof ChiponThread) {
		         	fThread = (ChiponThread)element;
		         } else if (element instanceof ChiponStackFrame) {
		         	fThread = (ChiponThread) ((ChiponStackFrame) element).getThread();
		         }
			 }
		}

		return fThread;
	}


	@Override
	public void update(ChiponCommand command, ChiponCommandResult result) {
		if(command.getfRequest() != null) {
			mConsoleOut.appendInput(command.getfRequest()+"\n");
		}
		if(result.resultText != null) {
			mConsoleOut.append(result.resultText+"\n");
		}
		if(result.errorCode != null) {
			mConsoleOut.appendError(result.errorCode+"\n");
		}
	}


	@Override
	public void updateResponse(List<String> response) {
		response.forEach(str -> mConsoleOut.append(str+"\n"));
	}


	@Override
	public void dispose() {
		ChiponDebugTarget.delObserver(this); 
		super.dispose();
	}
	
	
}
