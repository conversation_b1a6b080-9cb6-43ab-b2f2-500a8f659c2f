package com.chipon32.debug.ui.view.command;

import java.util.concurrent.locks.ReentrantReadWriteLock;
import org.eclipse.swt.graphics.Font;
import org.eclipse.swt.widgets.Display;

public class TextLine {
  private String mText;
  
  private final STD mColor;
  
  private int mWidth = -1;
  
  private ReentrantReadWriteLock mLock = new ReentrantReadWriteLock();
  
  public TextLine(String string, STD colorID) {
    this.mText = string;
    this.mColor = colorID;
  }
  
  public void updateWidth(Font font) {
    try {
      this.mLock.writeLock().lock();
      int textWidth = FontInfo.get(font).getWidth(this.mText);
      if (textWidth > this.mWidth)
        this.mWidth = textWidth; 
    } finally {
      this.mLock.writeLock().unlock();
    } 
  }
  
  public int getWidth(final Font font) {
    int width = 0;
    try {
      this.mLock.readLock().lock();
      width = this.mWidth;
      if (width == -1)
        try {
          this.mLock.readLock().unlock();
          if (Display.getCurrent() != null) {
            updateWidth(font);
          } else {
            UITask.blockingCallOnUIThread(new Runnable() {
                  public void run() {
                    TextLine.this.updateWidth(font);
                  }
                },  "Update Font Width");
          } 
        } finally {
          this.mLock.readLock().lock();
        }  
      width = (this.mWidth == -1) ? 0 : this.mWidth;
    } finally {
      this.mLock.readLock().unlock();
    } 
    return width;
  }
  
  public STD getColor() {
    return this.mColor;
  }
  
  public String getText() {
    try {
      this.mLock.readLock().lock();
      return this.mText;
    } finally {
      this.mLock.readLock().unlock();
    } 
  }
  
  public int getWidth() {
    try {
      this.mLock.readLock().lock();
      return this.mWidth;
    } finally {
      this.mLock.readLock().unlock();
    } 
  }
  
  public void reset() {
    try {
      this.mLock.writeLock().lock();
      this.mWidth = -1;
    } finally {
      this.mLock.writeLock().unlock();
    } 
  }
  
  public void setText(String text) {
    try {
      this.mLock.writeLock().lock();
      this.mText = text;
      this.mWidth = -1;
    } finally {
      this.mLock.writeLock().unlock();
    } 
  }
  
  public void append(String text) {
    try {
      this.mLock.writeLock().lock();
      this.mText = String.valueOf(this.mText) + text;
      this.mWidth = -1;
    } finally {
      this.mLock.writeLock().unlock();
    } 
  }
  
  public String toString() {
    return getText();
  }
}

