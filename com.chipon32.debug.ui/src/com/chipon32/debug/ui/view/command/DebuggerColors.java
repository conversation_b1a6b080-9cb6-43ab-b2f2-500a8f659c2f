package com.chipon32.debug.ui.view.command;

import org.eclipse.swt.graphics.Color;

public class DebuggerColors {

	private static DebuggerColors INSTANCE = null;

	Color mInstructionHighlight;

	Color mInstructionHighlightLighter;

	Color mInstructionHighlightDarker;

	Color mChangedHighlight;

	Color mConsoleBackground;

	Color mConsoleInput;

	Color mConsoleOutput;

	Color mConsoleError;

	public static synchronized DebuggerColors getInstance() {
		if (INSTANCE == null)
			INSTANCE = new DebuggerColors();
		return INSTANCE;
	}

	public Color getConsoleInput() {
		return this.mConsoleInput;
	}

}
