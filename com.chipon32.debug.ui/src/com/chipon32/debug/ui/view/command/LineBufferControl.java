package com.chipon32.debug.ui.view.command;

import java.io.IOException;
import java.util.ArrayList;
import java.util.NoSuchElementException;
import java.util.Queue;
import java.util.concurrent.ConcurrentLinkedQueue;
import org.eclipse.core.runtime.IProgressMonitor;
import org.eclipse.core.runtime.IStatus;
import org.eclipse.core.runtime.NullProgressMonitor;
import org.eclipse.core.runtime.Status;
import org.eclipse.core.runtime.jobs.Job;
import org.eclipse.jface.layout.GridDataFactory;
import org.eclipse.jface.text.BadLocationException;
import org.eclipse.jface.text.DocumentEvent;
import org.eclipse.jface.text.IDocument;
import org.eclipse.jface.text.IDocumentListener;
import org.eclipse.jface.text.ITextListener;
import org.eclipse.jface.text.TextEvent;
import org.eclipse.jface.util.IPropertyChangeListener;
import org.eclipse.jface.util.PropertyChangeEvent;
import org.eclipse.jface.viewers.ISelection;
import org.eclipse.jface.viewers.ISelectionChangedListener;
import org.eclipse.jface.viewers.ISelectionProvider;
import org.eclipse.swt.custom.ExtendedModifyEvent;
import org.eclipse.swt.custom.ExtendedModifyListener;
import org.eclipse.swt.custom.StyleRange;
import org.eclipse.swt.custom.VerifyKeyListener;
import org.eclipse.swt.events.DisposeEvent;
import org.eclipse.swt.events.DisposeListener;
import org.eclipse.swt.events.FocusListener;
import org.eclipse.swt.events.KeyEvent;
import org.eclipse.swt.events.KeyListener;
import org.eclipse.swt.events.VerifyEvent;
import org.eclipse.swt.events.VerifyListener;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.graphics.Font;
import org.eclipse.swt.graphics.RGB;
import org.eclipse.swt.graphics.Rectangle;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Event;
import org.eclipse.swt.widgets.Menu;
import org.eclipse.ui.console.IOConsole;
import org.eclipse.ui.console.IOConsoleOutputStream;
import org.eclipse.ui.console.TextConsole;
import org.eclipse.ui.internal.console.IOConsolePartitioner;
import org.eclipse.ui.internal.console.IOConsoleViewer;

public class LineBufferControl implements DisposeListener, IPropertyChangeListener, ISelectionProvider {
  private static final char BACKSPACE_CHAR = '\b';
  
  private static final String BACKSPACE_STRING = new String(new char[] { '\b' });
  
  private static final Color COLOR_WARNING = Colors.COLORS.create(new RGB(204, 139, 10));
  
  private static final Color COLOR_DEBUG = Colors.COLORS.create(new RGB(155, 14, 194));
  
  private static final String[] EXPORT_NAMES = new String[] { "All Files (*.*)", "Text (*.txt)" };
  
  private static final String[] EXPORT_EXTENSIONS = new String[] { "*", "*.txt" };
  
  public static final int SIZE_MINIMUM = 100;
  
  public static final int SIZE_MAXIMUM = 1000000;
  
  private DataBufferManagerJob mDataBufferManagerJob;
  
  private IOConsole mMessageConsole;
  
  private IOConsoleViewer mConsoleViewer;
  
  private IOConsoleOutputStream mOutStream;
  
  private IOConsoleOutputStream mErrorStream;
  
  private IOConsoleOutputStream mInStream;
  
  private IOConsoleOutputStream mWarningStream;
  
  private IOConsoleOutputStream mDebugStream;
  
  private int mMaxLinesCount = 1000000;
  
  private int mLinesDelta = 5;
  
  private KeyListener mKeyListener;
  
  private KeyForwarder mKeyForwarder = new KeyForwarder();
  
  private IDocumentListener mDocumentListener;
  
  private ITextListener mTextListener;
  
  private boolean mWaitingForInput;
  
  private int mOutstandingAppendCount = 0;
  
  private Object mOutstandingAppendLock = new Object();
  
//  private AppConsoleProcessNotifier mChildProcessNotifier;
  
  private boolean mDebugConsoleInstance;
  
  private boolean mSemihostingInstance;
  
  private class InternalStyleRange {
    private final int mStart;
    
    private final int mLength;
    
    private final STD mColor;
    
    public InternalStyleRange(int start, int end, STD color) {
      this.mStart = start;
      this.mLength = end;
      this.mColor = color;
    }
  }
  
  private class KeyForwarder implements VerifyListener, VerifyKeyListener, KeyListener, ExtendedModifyListener {
    private String mText;
    
    private boolean mForwardUsingKey;
    
    private VerifyEvent mVerifyKeyEvent;
    
    private KeyForwarder() {
      this.mForwardUsingKey = false;
      this.mVerifyKeyEvent = null;
    }
    
    public void verifyKey(VerifyEvent event) {
      if (LineBufferControl.this.mWaitingForInput) {
        this.mVerifyKeyEvent = event;
        this.mForwardUsingKey = true;
      } else {
        event.doit = false;
        this.mVerifyKeyEvent = null;
        this.mForwardUsingKey = false;
      } 
    }
    
    public void verifyText(VerifyEvent e) {
      try {
      
      } finally {
        this.mText = e.doit ? e.text : null;
      } 
      this.mText = e.doit ? e.text : null;
    }
    
    public void keyPressed(KeyEvent e) {
      if (LineBufferControl.this.mKeyListener == null || !this.mForwardUsingKey)
        return; 
      LineBufferControl.this.mKeyListener.keyPressed(e);
    }
    
    public void keyReleased(KeyEvent e) {
      if (LineBufferControl.this.mKeyListener == null || !this.mForwardUsingKey)
        return; 
      LineBufferControl.this.mKeyListener.keyReleased(e);
      this.mForwardUsingKey = false;
      this.mVerifyKeyEvent = null;
    }
    
    public void modifyText(ExtendedModifyEvent event) {
      if (LineBufferControl.this.mKeyListener == null || this.mForwardUsingKey || this.mText == null || this.mText.isEmpty())
        return; 
      Event e = new Event();
      e.widget = event.widget;
      byte b;
      int i;
      char[] arrayOfChar;
      for (i = (arrayOfChar = this.mText.toCharArray()).length, b = 0; b < i; ) {
        char c = arrayOfChar[b];
        e.character = c;
        this.mForwardUsingKey = true;
        LineBufferControl.this.mKeyForwarder.keyPressed(new KeyEvent(e));
        LineBufferControl.this.mKeyForwarder.keyReleased(new KeyEvent(e));
        b++;
      } 
    }
  }
  
  private class DataBufferManagerJob extends Job {
    private boolean mStop = false;
    
    private boolean mProcessDataInJob = true;
    
    private Queue<TextLine> mDataBuffer = new ConcurrentLinkedQueue<>();
    
    public DataBufferManagerJob(boolean processDataInJob) {
      super("Start...");
      this.mProcessDataInJob = processDataInJob;
      setSystem(true);
    }
    
    protected IStatus run(IProgressMonitor monitor) {
      while (!monitor.isCanceled() && !this.mStop) {
        TextLine line = null;
        try {
          line = this.mDataBuffer.remove();
        } catch (NoSuchElementException noSuchElementException) {
          break;
        } 
        if (line == null)
          continue; 
        IOConsoleOutputStream stream = null;
        switch (line.getColor()) {
          case IN:
            stream = LineBufferControl.this.mInStream;
            break;
          case OUT:
            stream = LineBufferControl.this.mOutStream;
            break;
          case ERR:
            stream = LineBufferControl.this.mErrorStream;
            break;
          case WARNING:
            stream = LineBufferControl.this.mWarningStream;
            break;
          default:
            stream = LineBufferControl.this.mDebugStream;
            break;
        } 
        if (stream != null && !stream.isClosed())
          try {
            stream.write(line.getText());
          } catch (IOException e) {
        	  e.printStackTrace();
          }  
      } 
      if (this.mStop) {
        closeStreamSilently(LineBufferControl.this.mOutStream);
        closeStreamSilently(LineBufferControl.this.mInStream);
        closeStreamSilently(LineBufferControl.this.mErrorStream);
        closeStreamSilently(LineBufferControl.this.mWarningStream);
        closeStreamSilently(LineBufferControl.this.mDebugStream);
      } 
      return Status.OK_STATUS;
    }
    
    private void closeStreamSilently(IOConsoleOutputStream stream) {
      try {
        if (stream != null) {
          stream.setColor(null);
          if (!stream.isClosed())
            stream.close(); 
        } 
      } catch (IOException iOException) {}
    }
    
    public void add(TextLine line) {
      String text = line.getText();
      if (text == null || text.isEmpty() || LineBufferControl.BACKSPACE_STRING.equals(text))
        return; 
      if (text.indexOf('\r') >= 0)
        line.setText(text.replaceAll("\r", "")); 
      this.mDataBuffer.add(line);
      if (this.mProcessDataInJob) {
        schedule();
      } else {
        run((IProgressMonitor)new NullProgressMonitor());
      } 
    }
    
    public void setLines(TextLine[] textLines) {
      reset();
      byte b;
      int i;
      TextLine[] arrayOfTextLine;
      for (i = (arrayOfTextLine = textLines).length, b = 0; b < i; ) {
        TextLine line = arrayOfTextLine[b];
        add(line);
        b++;
      } 
    }
    
    public void reset() {
      this.mDataBuffer.clear();
    }
    
    public void stop() {
      this.mStop = true;
      schedule();
    }
  }
  
	public LineBufferControl(Composite parent, /* ConnectionController controller, */ boolean inJob) {
    this.mDataBufferManagerJob = new DataBufferManagerJob(inJob);
    this.mMessageConsole = new IOConsole("", null);
    this.mConsoleViewer = new RevealingIOConsoleViewer(parent, (TextConsole)this.mMessageConsole);
    this.mOutStream = this.mMessageConsole.newOutputStream();
    this.mInStream = this.mMessageConsole.newOutputStream();
    this.mErrorStream = this.mMessageConsole.newOutputStream();
    this.mWarningStream = this.mMessageConsole.newOutputStream();
    this.mWarningStream.setColor(COLOR_WARNING);
    this.mDebugStream = this.mMessageConsole.newOutputStream();
    this.mDebugStream.setColor(COLOR_DEBUG);
    this.mConsoleViewer.getTextWidget().setLayoutData(GridDataFactory.fillDefaults().grab(true, true).create());
    this.mConsoleViewer.getTextWidget().setAlwaysShowScrollBars(false);
    this.mConsoleViewer.getTextWidget().setEditable(false);
    this.mConsoleViewer.getTextWidget().addVerifyListener(this.mKeyForwarder);
    this.mConsoleViewer.getTextWidget().addVerifyKeyListener(this.mKeyForwarder);
    this.mConsoleViewer.getTextWidget().addKeyListener(this.mKeyForwarder);
    this.mConsoleViewer.getTextWidget().addExtendedModifyListener(this.mKeyForwarder);
    this.mConsoleViewer.getDocument().addDocumentListener(this.mDocumentListener = new IDocumentListener() {
          public void documentChanged(DocumentEvent event) {
            LineBufferControl.this.checkOverflow();
          }
          
          public void documentAboutToBeChanged(DocumentEvent event) {}
        });
    this.mConsoleViewer.addTextListener(this.mTextListener = new ITextListener() {
          public void textChanged(TextEvent event) {
            LineBufferControl.this.mConsoleViewer.getTextWidget().setCaretOffset(LineBufferControl.this.mConsoleViewer.getTextWidget().getCharCount());
          }
        });
    parent.addDisposeListener(this);
//    this.mChildProcessNotifier = controller.getAppConsolePipeCleanser();
//    DebuggerPlugin.getPreferences().addPropertyChangeListener(this);
//    ThemeTracker.getInstance().register(this);
    updatePreferences();
  }
  
	public LineBufferControl(Composite parent/* , ConnectionController controller */) {
		this(parent/* , controller */, true);
  }
  
  public Control getControl() {
    return (Control)this.mConsoleViewer.getTextWidget();
  }
  
  public void paste(String text) {
    if (this.mConsoleViewer.getTextWidget() == null)
      return; 
    this.mConsoleViewer.getTextWidget().insert(text);
  }
  
  public boolean isDisposed() {
    if (this.mConsoleViewer.getTextWidget() == null)
      return true; 
    return this.mConsoleViewer.getTextWidget().isDisposed();
  }
  
  public boolean isVisible() {
    if (this.mConsoleViewer.getTextWidget() == null)
      return false; 
    return this.mConsoleViewer.getTextWidget().isVisible();
  }
  
  public boolean isEnabled() {
    if (this.mConsoleViewer.getTextWidget() == null)
      return false; 
    return this.mConsoleViewer.getTextWidget().isEnabled();
  }
  
  public Font getFont() {
    if (this.mConsoleViewer.getTextWidget() == null)
      return null; 
    return this.mConsoleViewer.getTextWidget().getFont();
  }
  
  public Rectangle getBounds() {
    if (this.mConsoleViewer.getTextWidget() == null)
      return null; 
    return this.mConsoleViewer.getTextWidget().getBounds();
  }
  
  public void setMenu(Menu menu) {
    if (this.mConsoleViewer.getTextWidget() == null)
      return; 
    this.mConsoleViewer.getTextWidget().setMenu(menu);
  }
  
  public ISelection getSelection() {
    return this.mConsoleViewer.getSelection();
  }
  
  public void addSelectionChangedListener(ISelectionChangedListener listener) {
    this.mConsoleViewer.addSelectionChangedListener(listener);
  }
  
  public void removeSelectionChangedListener(ISelectionChangedListener listener) {
    this.mConsoleViewer.removeSelectionChangedListener(listener);
  }
  
  public void setSelection(ISelection selection) {
    this.mConsoleViewer.setSelection(selection);
  }
  
  public void addFocusListener(FocusListener listener) {
    if (this.mConsoleViewer.getTextWidget() == null)
      return; 
    this.mConsoleViewer.getTextWidget().addFocusListener(listener);
  }
  
  public boolean isFocusControl() {
    if (this.mConsoleViewer.getTextWidget() == null)
      return false; 
    return this.mConsoleViewer.getTextWidget().isFocusControl();
  }
  
  public boolean forceFocus() {
    if (this.mConsoleViewer.getTextWidget() == null)
      return false; 
    return this.mConsoleViewer.getTextWidget().forceFocus();
  }
  
  public void addKeyListener(KeyListener listener) {
    this.mKeyListener = listener;
  }
  
  public void incrementOutstandingAppends() {
//    if (this.mChildProcessNotifier == null)
//      return; 
    synchronized (this.mOutstandingAppendLock) {
      this.mOutstandingAppendCount++;
//      this.mChildProcessNotifier.setStartedConsuming();
    } 
  }
  
  public void decrementOutstandingAppends() {
//    if (this.mChildProcessNotifier == null)
//      return; 
    synchronized (this.mOutstandingAppendLock) {
      this.mOutstandingAppendCount--;
		/*
		 * if (this.mOutstandingAppendCount == 0) {
		 * this.mChildProcessNotifier.setFinishedConsuming(); } else
		 */ if (this.mOutstandingAppendCount < 0) {
        this.mOutstandingAppendCount = 0;
      } 
    } 
  }
  
  public boolean isWaitingForInput() {
    return this.mWaitingForInput;
  }
  
  public void setWaitingForInput(boolean waitingForInput) {
    this.mWaitingForInput = waitingForInput;
  }
  
  public void append(String string) {
    addLine(string, STD.OUT);
  }
  
  public void appendError(String string) {
    addLine(string, STD.ERR);
  }
  
  public void appendInput(String string) {
    addLine(string, STD.IN);
  }
  
  void addLine(String string, STD colorID) {
    this.mDataBufferManagerJob.add(new TextLine(string, colorID));
  }
  
  public void getMemento(final IMementoCallback callback) {
    if (callback == null)
      return; 
    if (this.mConsoleViewer == null || this.mMessageConsole == null)
      return; 
    Job job = new Job("Memnto Job") {
        protected IStatus run(IProgressMonitor monitor) {
          if (LineBufferControl.this.mConsoleViewer.getDocument() == null) {
            callback.run(null);
            return Status.OK_STATUS;
          } 
          IDocument document = LineBufferControl.this.mConsoleViewer.getDocument();
          IOConsolePartitioner documentPartitioner = (IOConsolePartitioner)document.getDocumentPartitioner();
          if (documentPartitioner == null) {
            callback.run(null);
            return Status.OK_STATUS;
          } 
          String text = document.get();
          if (text.isEmpty()) {
            callback.run(new Object[0]);
            return Status.OK_STATUS;
          } 
          ArrayList<LineBufferControl.InternalStyleRange> internalStyleRanges = new ArrayList<>();
          StyleRange[] styleRanges = documentPartitioner.getStyleRanges(0, text.length());
          byte b;
          int i;
          StyleRange[] arrayOfStyleRange1;
          for (i = (arrayOfStyleRange1 = styleRanges).length, b = 0; b < i; ) {
            StyleRange sr = arrayOfStyleRange1[b];
            STD colorID = STD.DEBUG;
//            if (DebuggerColors.getInstance().getConsoleInput().equals(sr.foreground) || sr.foreground == null) {
//              colorID = STD.IN;
//            } else if (DebuggerColors.getInstance().getConsoleOutput().equals(sr.foreground)) {
//              colorID = STD.OUT;
//            } else if (DebuggerColors.getInstance().getConsoleError().equals(sr.foreground)) {
//              colorID = STD.ERR;
//            } else if (LineBufferControl.COLOR_WARNING.equals(sr.foreground)) {
//              colorID = STD.WARNING;
//            } 
            internalStyleRanges.add(new LineBufferControl.InternalStyleRange(sr.start, sr.length, colorID));
            b++;
          } 
          callback.run(new Object[] { text, internalStyleRanges.toArray(new LineBufferControl.InternalStyleRange[0]) });
          return Status.OK_STATUS;
        }
      };
    job.setRule(this.mMessageConsole.getSchedulingRule());
    job.setSystem(true);
    job.schedule();
  }
  
  public void setMemento(Object[] memento) {
    if (memento == null || memento.length != 2 || !(memento[0] instanceof String) || !(memento[1] instanceof InternalStyleRange[]))
      return; 
    if (this.mDataBufferManagerJob == null)
      return; 
    String text = (String)memento[0];
    if (text.isEmpty())
      return; 
    int endIndex = 0;
    ArrayList<TextLine> textLines = new ArrayList<>();
    byte b;
    int i;
    InternalStyleRange[] arrayOfInternalStyleRange;
    for (i = (arrayOfInternalStyleRange = (InternalStyleRange[])memento[1]).length, b = 0; b < i; ) {
      InternalStyleRange sr = arrayOfInternalStyleRange[b];
      if (sr.mStart + sr.mLength > text.length())
        break; 
      endIndex = sr.mStart + sr.mLength;
      String str = text.substring(sr.mStart, endIndex);
      textLines.add(new TextLine(str, sr.mColor));
      b++;
    } 
    if (endIndex < text.length())
      textLines.add(new TextLine(text.substring(endIndex), STD.OUT)); 
    if (textLines.size() > 0)
      this.mDataBufferManagerJob.setLines(textLines.<TextLine>toArray(new TextLine[0])); 
  }
  
  public void widgetDisposed(DisposeEvent event) {
//    ThemeTracker.getInstance().unregister(this);
//    DebuggerPlugin.getPreferences().removePropertyChangeListener(this);
    this.mKeyListener = null;
//    this.mChildProcessNotifier = null;
    if (this.mConsoleViewer.getTextWidget() != null) {
      this.mConsoleViewer.getTextWidget().setForeground(null);
      this.mConsoleViewer.getTextWidget().setBackground(null);
      this.mConsoleViewer.getTextWidget().setFont(null);
    } 
    this.mConsoleViewer.removeTextListener(this.mTextListener);
    if (this.mConsoleViewer.getDocument() != null)
      this.mConsoleViewer.getDocument().removeDocumentListener(this.mDocumentListener); 
    this.mDataBufferManagerJob.stop();
  }
  
  public void setMaxLinesCount(int maxLinesCount) {
    this.mMaxLinesCount = maxLinesCount;
    checkOverflow();
  }
  
  private void checkOverflow() {
    IDocument document = this.mConsoleViewer.getDocument();
    if (document == null)
      return; 
    if (!(document.getDocumentPartitioner() instanceof IOConsolePartitioner))
      return; 
    IOConsolePartitioner documentPartitioner = (IOConsolePartitioner)document.getDocumentPartitioner();
    int numberOfLines = document.getNumberOfLines();
    if (numberOfLines <= this.mMaxLinesCount) {
      documentPartitioner.setWaterMarks(0, 0);
      return;
    } 
    try {
      int highWaterMark = document.getLineOffset(this.mMaxLinesCount);
      int lowWaterMark = document.getLineOffset(this.mMaxLinesCount - this.mLinesDelta);
      documentPartitioner.setWaterMarks(lowWaterMark, highWaterMark);
    } catch (BadLocationException badLocationException) {}
  }
  
  public void selectAll() {
    this.mConsoleViewer.doOperation(7);
  }
  
  public void setScrollLock(boolean scrollLock) {
    this.mConsoleViewer.setAutoScroll(!scrollLock);
  }
  
  private void updatePreferences() {
    if (this.mConsoleViewer.getTextWidget() != null) {
//      this.mConsoleViewer.getTextWidget().setForeground();
//      this.mConsoleViewer.getTextWidget().setBackground();
//      this.mConsoleViewer.getTextWidget().setFont();
    } 
    this.mOutStream.setColor(Colors.getSystemDarkGray());
    this.mInStream.setColor(Colors.getSystemBlue());
    this.mErrorStream.setColor(Colors.getSystemRed());
    if (isSemihostingInstance()) {
      setSemihostingInstance();
    } else if (isDebugConsoleInstance()) {
      setDebugConsoleInstance();
    } 
  }
  
  
  public void propertyChange(PropertyChangeEvent event) {
    Object propertyName = event.getProperty();
    if (propertyName == "com.arm.debugger.plugin.debuglimit" && isDebugConsoleInstance()) {
      updatePreferences();
    } else if (propertyName == "com.arm.debugger.plugin.semihosting" && isSemihostingInstance()) {
      updatePreferences();
    } 
  }
  
  public void setDebugConsoleInstance() {
//    DebuggerPlugin plugin = DebuggerPlugin.getInstance();
//    if (plugin != null) {
      this.mDebugConsoleInstance = true;
//      setMaxLinesCount(plugin.getPreferenceStore().getInt("com.arm.debugger.plugin.debuglimit"));
//    } 
  }
  
  public void setSemihostingInstance() {
//    DebuggerPlugin plugin = DebuggerPlugin.getInstance();
//    if (plugin != null) {
//      this.mConsoleViewer.getTextWidget().setEditable(this.mSemihostingInstance = true);
//      setMaxLinesCount(plugin.getPreferenceStore().getInt("com.arm.debugger.plugin.semihosting"));
//    } 
  }
  
  public boolean isSemihostingInstance() {
    return this.mSemihostingInstance;
  }
  
  public boolean isDebugConsoleInstance() {
    return this.mDebugConsoleInstance;
  }
  
  public static interface IMementoCallback {
    void run(Object[] param1ArrayOfObject);
  }
}

