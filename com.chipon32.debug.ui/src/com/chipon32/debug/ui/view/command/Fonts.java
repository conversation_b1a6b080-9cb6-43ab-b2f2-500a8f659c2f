package com.chipon32.debug.ui.view.command;

import java.util.HashMap;
import java.util.Map;

import org.eclipse.jface.resource.JFaceResources;
import org.eclipse.swt.graphics.Font;
import org.eclipse.swt.graphics.FontData;

public class Fonts {
	
	private static Font font = JFaceResources.getDialogFont();
	private static final Map<FontData, Font> SIMPLE_CACHE = new HashMap<>();
	
	public static final synchronized Font getNormal() {
	    return getFontWithStyle(font, 0);
	  }
	
	public static final Font getFontWithStyle(Font font, int style) {
	    Font result;
	    if (font.getFontData()[0].getStyle() == style)
	      return font; 
	    FontData[] fontData = font.getFontData();
	    byte b;
	    int i;
	    FontData[] arrayOfFontData1;
	    for (i = (arrayOfFontData1 = fontData).length, b = 0; b < i; ) {
	      FontData element = arrayOfFontData1[b];
	      element.setStyle(style & 0x3);
	      b++;
	    } 
	    synchronized (SIMPLE_CACHE) {
	      result = SIMPLE_CACHE.get(fontData[0]);
	      if (result == null) {
	        result = new Font(font.getDevice(), fontData);
	        SIMPLE_CACHE.put(fontData[0], result);
	      } 
	    } 
	    return result;
	  }

}
