package com.chipon32.debug.ui.view.command;

import java.util.HashMap;
import java.util.Map;
import java.util.StringTokenizer;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.graphics.Device;
import org.eclipse.swt.graphics.Drawable;
import org.eclipse.swt.graphics.Font;
import org.eclipse.swt.graphics.FontData;
import org.eclipse.swt.graphics.GC;
import org.eclipse.swt.graphics.Image;
import org.eclipse.swt.graphics.ImageData;
import org.eclipse.swt.graphics.Point;

public class FontInfo {
  private static final Map<FontData, FontInfo> CACHE = new HashMap<>();
  
  private GC mMeasurementGC;
  
  private FontData mFontData;
  
  private int mLeading;
  
  private int mAscent;
  
  private int mDescent;
  
  private int mAverageCharWidth;
  
  public static final FontInfo get(Font font) {
    FontInfo result;
    FontData fontData = font.getFontData()[0];
    synchronized (CACHE) {
      result = CACHE.get(fontData);
      if (result == null) {
        result = new FontInfo(font, fontData);
        CACHE.put(fontData, result);
      } 
    } 
    return result;
  }
  
  public static final int prepareGCForTextDrawing(GC gc) {
    int savedAA = gc.getAntialias();
    gc.setAntialias(0);
    return savedAA;
  }
  
  public static final void restoreGCFromTextDrawing(GC gc, int prepareGCForTextDrawingResult) {
    gc.setAntialias(prepareGCForTextDrawingResult);
  }
  
  private FontInfo(Font font, FontData fontData) {
    this.mMeasurementGC = new GC((Drawable)new Image(null, 8, 8));
    this.mMeasurementGC.setFont(new Font(this.mMeasurementGC.getDevice(), fontData));
    prepareGCForTextDrawing(this.mMeasurementGC);
    this.mAverageCharWidth = this.mMeasurementGC.getFontMetrics().getAverageCharWidth();
    this.mFontData = fontData;
    int size = this.mFontData.getHeight();
    int width = size * 5;
    int height = size * 4;
    Image img = new Image(null, width, height);
    ImageData data = getImageData(img, width, height, font, "MTyg");
    this.mLeading = findFirstPixel(data, width, height);
    int last = findLastPixel(data, width, height);
    int baseline = findLastPixel(getImageData(img, width, height, font, "MT"), width, height);
    this.mAscent = 1 + baseline - this.mLeading;
    this.mDescent = last - baseline;
    img.dispose();
  }
  
  private static final ImageData getImageData(Image img, int width, int height, Font font, String text) {
    GC gc = new GC((Drawable)img);
    prepareGCForTextDrawing(gc);
    Device device = gc.getDevice();
    Color white = new Color(device, 255, 255, 255);
    gc.setBackground(white);
    gc.fillRectangle(0, 0, width, height);
    Color black = new Color(device, 0, 0, 0);
    gc.setForeground(black);
    gc.setFont(font);
    gc.drawString(text, 2, 2);
    gc.dispose();
    white.dispose();
    black.dispose();
    return img.getImageData();
  }
  
  private static final int findFirstPixel(ImageData data, int width, int height) {
    int pattern = data.getPixel(0, 0);
    for (int y = 2; y < height; y++) {
      for (int x = 0; x < width; x++) {
        if ((data.getPixel(x, y) & pattern) != pattern)
          return y - 2; 
      } 
    } 
    return 2;
  }
  
  private static final int findLastPixel(ImageData data, int width, int height) {
    int pattern = data.getPixel(0, 0);
    for (int y = height - 1; y >= 2; y--) {
      for (int x = 0; x < width; x++) {
        if ((data.getPixel(x, y) & pattern) != pattern)
          return y - 2; 
      } 
    } 
    return height - 3;
  }
  
  public final int getLeading() {
    return this.mLeading;
  }
  
  public final int getAscent() {
    return this.mAscent;
  }
  
  public final int getDescent() {
    return this.mDescent;
  }
  
  public final int getBaseline() {
    return this.mLeading + this.mAscent - 1;
  }
  
  public final int getHeight() {
    return this.mLeading + this.mAscent + this.mDescent;
  }
  
  public final int getMaxWidth() {
    return getWidth("M");
  }
  
  public final int getAverageCharWidth() {
    return this.mAverageCharWidth;
  }
  
  public final int getWidth(String text) {
    return (this.mMeasurementGC.stringExtent(text)).x;
  }
  
  public final Point getExtent(String text) {
    if (text != null) {
      boolean lastWasReturn = true;
      int width = 0;
      int height = 0;
      int fontHeight = getHeight();
      StringTokenizer tokenizer = new StringTokenizer(text, "\n", true);
      while (tokenizer.hasMoreTokens()) {
        String token = tokenizer.nextToken();
        if (token.equals("\n")) {
          if (lastWasReturn) {
            height += fontHeight;
            continue;
          } 
          lastWasReturn = true;
          continue;
        } 
        lastWasReturn = false;
        height += fontHeight;
        int tokenWidth = getWidth(token);
        if (tokenWidth > width)
          width = tokenWidth; 
      } 
      return new Point(width, height);
    } 
    return new Point(0, 0);
  }
  
  public final GC getMeasurementGC() {
    return this.mMeasurementGC;
  }
  
  public final int getCharOffset(String text, int position) {
    if (position <= 0)
      return 0; 
    int count = text.length();
    if (count > 0) {
      int lastWidth = 0;
      for (int i = 1; i <= count; i++) {
        int width = getWidth(text.substring(0, i));
        int half = lastWidth + (width - lastWidth) / 2;
        if (position >= lastWidth && position <= half)
          return i - 1; 
        if (position < width && position > half)
          return i; 
        lastWidth = width;
      } 
    } 
    return count;
  }
  
  public final String toString() {
    StringBuilder buffer = new StringBuilder();
    buffer.append("FontInfo[name: ");
    buffer.append(this.mFontData.getName());
    buffer.append(", point size: ");
    buffer.append(this.mFontData.getHeight());
    buffer.append(", style: ");
    buffer.append(this.mFontData.getStyle());
    buffer.append(", leading: ");
    buffer.append(this.mLeading);
    buffer.append(", ascent: ");
    buffer.append(this.mAscent);
    buffer.append(", descent: ");
    buffer.append(this.mDescent);
    buffer.append(", baseline: ");
    buffer.append(getBaseline());
    buffer.append(", height: ");
    buffer.append(getHeight());
    buffer.append(", maximum character width: ");
    buffer.append(getMaxWidth());
    buffer.append("]");
    return buffer.toString();
  }
}
