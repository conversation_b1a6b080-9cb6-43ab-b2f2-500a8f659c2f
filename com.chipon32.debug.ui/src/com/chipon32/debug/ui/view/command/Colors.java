package com.chipon32.debug.ui.view.command;

import java.util.HashMap;
import java.util.Map;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.graphics.Device;
import org.eclipse.swt.graphics.RGB;
import org.eclipse.swt.widgets.Display;

public class Colors {
  public static final Colors COLORS = new Colors();
  
  public static final RGB BLACK = new RGB(0, 0, 0);
  
  public static final RGB WHITE = new RGB(255, 255, 255);
  
  public static final RGB RED = new RGB(255, 0, 0);
  
  public static final RGB DARK_RED = new RGB(128, 0, 0);
  
  public static final RGB GREEN = new RGB(0, 255, 0);
  
  public static final RGB DARK_GREEN = new RGB(0, 128, 0);
  
  public static final RGB YELLOW = new RGB(255, 255, 0);
  
  public static final RGB DARK_YELLOW = new RGB(128, 128, 0);
  
  public static final RGB BLUE = new RGB(0, 0, 255);
  
  public static final RGB DARK_BLUE = new RGB(0, 0, 128);
  
  public static final RGB MAGENTA = new RGB(255, 0, 255);
  
  public static final RGB DARK_MAGENTA = new RGB(128, 0, 128);
  
  public static final RGB CYAN = new RGB(0, 255, 255);
  
  public static final RGB DARK_CYAN = new RGB(0, 128, 128);
  
  public static final RGB GRAY = new RGB(192, 192, 192);
  
  public static final RGB DARK_GRAY = new RGB(128, 128, 128);
  
  private static final RGB SECONDARY_BANDING = new RGB(232, 248, 255);
  
  private static final RGB NON_EDITABLE_CELL = new RGB(244, 244, 244);
  
  private static final RGB OUTLINE_DIVIDER = new RGB(224, 224, 224);
  
  private static final RGB OUTLINE_HIERARCHY = new RGB(170, 170, 170);
  
  private static final RGB GRID = new RGB(232, 248, 255);
  
  private static final RGB GROUP_TITLE = new RGB(0, 70, 213);
  
  private static final RGB PROGRESS_BAR_FILL = new RGB(0, 208, 0);
  
  private static final RGB FUNCTION_FILL_RGB = new RGB(232, 232, 232);
  
  private static final RGB INSTRUCTION_HIGHLIGHT_RGB = new RGB(216, 255, 216);
  
  private static final RGB TEXT_BORDER = new RGB(127, 157, 185);
  
  private static final Map<Integer, Color> SYSTEM_COLORS = new HashMap<>();
  
  private HashMap<RGB, Color> mColors = new HashMap<>();
  
  public static Color getSystemBlack() {
    return getSystemColor(2);
  }
  
  public static Color getSystemBlue() {
    return getSystemColor(9);
  }
  
  public static Color getSystemCyan() {
    return getSystemColor(13);
  }
  
  public static Color getSystemDarkBlue() {
    return getSystemColor(10);
  }
  
  public static Color getSystemDarkCyan() {
    return getSystemColor(14);
  }
  
  public static Color getSystemDarkGray() {
    return getSystemColor(16);
  }
  
  public static Color getSystemDarkGreen() {
    return getSystemColor(6);
  }
  
  public static Color getSystemDarkMagenta() {
    return getSystemColor(12);
  }
  
  public static Color getSystemDarkRed() {
    return getSystemColor(4);
  }
  
  public static Color getSystemDarkYellow() {
    return getSystemColor(8);
  }
  
  public static Color getSystemGray() {
    return getSystemColor(15);
  }
  
  public static Color getSystemGreen() {
    return getSystemColor(5);
  }
  
  public static Color getSystemMagenta() {
    return getSystemColor(11);
  }
  
  public static Color getSystemRed() {
    return getSystemColor(3);
  }
  
  public static Color getSystemWhite() {
    return getSystemColor(1);
  }
  
  public static Color getSystemYellow() {
    return getSystemColor(7);
  }
  
  public static Color getSystemInfoBackground() {
    return getSystemColor(29);
  }
  
  public static Color getSystemInfoForeground() {
    return getSystemColor(28);
  }
  
  public static Color getSystemListBackground() {
    return getSystemColor(25);
  }
  
  public static Color getSystemListForeground() {
    return getSystemColor(24);
  }
  
  public static Color getSystemListSelection() {
    return getSystemColor(26);
  }
  
  public static Color getSystemListSelectionText() {
    return getSystemColor(27);
  }
  
  public static Color getSystemTitleBackground() {
    return getSystemColor(31);
  }
  
  public static Color getSystemTitleBackgroundGradient() {
    return getSystemColor(32);
  }
  
  public static Color getSystemTitleForeground() {
    return getSystemColor(30);
  }
  
  public static Color getSystemTitleInactiveBackground() {
    return getSystemColor(34);
  }
  
  public static Color getSystemTitleInactiveBackgroundGradient() {
    return getSystemColor(35);
  }
  
  public static Color getSystemTitleInactiveForeground() {
    return getSystemColor(33);
  }
  
  public static Color getSystemWidgetBackground() {
    return getSystemColor(22);
  }
  
  public static Color getSystemWidgetBorder() {
    return getSystemColor(23);
  }
  
  public static Color getSystemWidgetDarkShadow() {
    return getSystemColor(17);
  }
  
  public static Color getSystemWidgetForeground() {
    return getSystemColor(21);
  }
  
  public static Color getSystemWidgetHighlightShadow() {
    return getSystemColor(20);
  }
  
  public static Color getSystemWidgetLightShadow() {
    return getSystemColor(19);
  }
  
  public static Color getSystemWidgetNormalShadow() {
    return getSystemColor(18);
  }
  
  public static Color getSystemLink() {
    return getSystemColor(36);
  }
  
  public static Color getSystemPressedLink() {
    return COLORS.createDarker(getSystemLink(), 50);
  }
  
  private static synchronized Color getSystemColor(int swtSystemColorConstant) {
    Color color = SYSTEM_COLORS.get(Integer.valueOf(swtSystemColorConstant));
    if (color == null) {
      Display display = Display.getDefault();
      if (display != null) {
        color = display.getSystemColor(swtSystemColorConstant);
        if (color != null)
          SYSTEM_COLORS.put(Integer.valueOf(swtSystemColorConstant), color); 
      } 
    } 
    return color;
  }
  
  private static final int blend(int first, int second, int percentage) {
    int blendedValue = (first * (100 - percentage) + second * percentage) / 100;
    return Math.max(0, Math.min(blendedValue, 255));
  }
  
  public static final RGB blend(RGB rgb1, RGB rgb2, int percentage) {
    return new RGB(blend(rgb1.red, rgb2.red, percentage), blend(rgb1.green, rgb2.green, percentage), blend(rgb1.blue, rgb2.blue, percentage));
  }
  
  public static final RGB toGrayscale(RGB rgb) {
    int luma = (int)(rgb.red * 0.3D + rgb.green * 0.59D + rgb.blue * 0.11D);
    return new RGB(luma, luma, luma);
  }
  
  public static final RGB darker(RGB rgb, int percentage) {
    return blend(rgb, BLACK, percentage);
  }
  
  public static final RGB lighter(RGB rgb, int percentage) {
    return blend(rgb, WHITE, percentage);
  }
  
  public static double perceivedBrightness(Color color) {
    double red = color.getRed() / 255.0D;
    if (!isMonochrome(color)) {
      double green = color.getGreen() / 255.0D;
      double blue = color.getBlue() / 255.0D;
      return Math.sqrt(red * red * 0.241D + green * green * 0.691D + blue * blue * 0.068D);
    } 
    return red;
  }
  
  public static boolean isMonochrome(Color color) {
    return (color.getRed() == color.getGreen() && color.getGreen() == color.getBlue());
  }
  
  public static Color getSystemSelection(boolean active) {
    Color color = getSystemListSelection();
    if (!active)
      color = COLORS.createLighter(color, 50); 
    return color;
  }
  
  public static Color getSystemDivider() {
    return getSystemWidgetNormalShadow();
  }
  
  public static Color getOutlineDivider() {
    return COLORS.create(OUTLINE_DIVIDER);
  }
  
  public static Color getOutlineHierarchy() {
    return COLORS.create(OUTLINE_HIERARCHY);
  }
  
  public static Color getNonEditableCellBackground() {
    return COLORS.create(NON_EDITABLE_CELL);
  }
  
  public static Color getTextBorder() {
    return COLORS.create(TEXT_BORDER);
  }
  
  public static Color getSystemText(boolean selectedAndActive) {
    return selectedAndActive ? getSystemListSelectionText() : getSystemListForeground();
  }
  
  public static Color getPrimaryBanding() {
    return getSystemWhite();
  }
  
  public static Color getSecondaryBanding() {
    return COLORS.create(SECONDARY_BANDING);
  }
  
  public static Color getSystemDragFill() {
    return COLORS.createLighter(getSystemSelection(true), 75);
  }
  
  public static Color getNonSelectedGraphLine() {
    return COLORS.createLighter(getSystemGray(), 50);
  }
  
  public static Color getFrozenBackground() {
    return COLORS.create(lighter(blend(BLUE, CYAN, 50), 70));
  }
  
  public static Color getProgressBarFill() {
    return COLORS.create(PROGRESS_BAR_FILL);
  }
  
  public static Color getGrid() {
    return COLORS.create(GRID);
  }
  
  public static Color getGroupTitle() {
    return COLORS.create(GROUP_TITLE);
  }
  
  public static Color getFunctionBoxFill() {
    return COLORS.create(FUNCTION_FILL_RGB);
  }
  
  public static Color getInstructionHighlight() {
    return COLORS.create(INSTRUCTION_HIGHLIGHT_RGB);
  }
  
  public static Color getColorForPercentage(double percentage) {
    Color color;
    if (percentage <= 0.9D) {
      color = COLORS.create(blend(new RGB(50.0F, 1.0F, 1.0F), WHITE, (int)Math.floor((1.0D - percentage) * 80.0D)));
    } else {
      int index = Math.min((int)Math.floor(percentage), 9);
      color = COLORS.create(new RGB(50.0F + index * -40.0F / 10.0F, 1.0F, 1.0F));
    } 
    return color;
  }
  
  public Color blend(Color color1, Color color2, int percentage) {
    if (percentage == 0)
      return color1; 
    if (percentage == 100)
      return color2; 
    return create(blend(color1.getRGB(), color2.getRGB(), percentage));
  }
  
  public Color create(RGB rgb) {
    return this.mColors.computeIfAbsent(rgb, c -> new Color((Device)Display.getCurrent(), c));
  }
  
  public Color create(int htmlColor) {
    int r = htmlColor >> 16 & 0xFF;
    int g = htmlColor >> 8 & 0xFF;
    int b = htmlColor & 0xFF;
    return create(new RGB(r, g, b));
  }
  
  public Color createDarker(Color color, int percentage) {
    return create(darker(color.getRGB(), percentage));
  }
  
  public Color createLighter(Color color, int percentage) {
    return create(lighter(color.getRGB(), percentage));
  }
  
  public void dispose() {
    for (Color color : this.mColors.values())
      color.dispose(); 
    this.mColors.clear();
  }
}
