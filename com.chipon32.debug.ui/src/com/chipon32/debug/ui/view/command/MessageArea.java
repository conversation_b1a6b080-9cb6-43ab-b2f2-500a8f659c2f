package com.chipon32.debug.ui.view.command;

import java.util.ArrayList;
import org.eclipse.jface.layout.GridLayoutFactory;
import org.eclipse.jface.resource.JFaceResources;
import org.eclipse.swt.custom.CLabel;
import org.eclipse.swt.graphics.Image;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.Layout;

public class MessageArea extends Composite {
  private Label imageLabel;
  
  private CLabel messageLabel;
  
  private ArrayList<String> messages = new ArrayList<>();
  
  private ArrayList<String> infoMessages = new ArrayList<>();
  
  private ArrayList<String> warningMessages = new ArrayList<>();
  
  private ArrayList<String> errorMessages = new ArrayList<>();
  
  public MessageArea(Composite parent, int style) {
    super(parent, style);
    super.setLayout((Layout)GridLayoutFactory.fillDefaults().numColumns(2).create());
    setLayoutData(new GridData(768));
//    setBackground(Colors.getSystemTitleInactiveBackgroundGradient());
    createControl();
  }
  
  public void setLayout(Layout layout) {}
  
  private void createControl() {
    this.imageLabel = new Label(this, 0);
    this.messageLabel = new CLabel(this, 0);
    this.messageLabel.setLayoutData(new GridData(768));
  }
  
  public void show(boolean show) {
    Object layoutData = getLayoutData();
    if (layoutData instanceof GridData) {
      ((GridData)layoutData).exclude = !show;
      getParent().layout();
    } 
  }
  
  private void updateMessage() {
    String message = null;
    int messageType = 0;
    if (this.errorMessages.size() > 0) {
      message = this.errorMessages.get(this.errorMessages.size() - 1);
      messageType = 3;
    } else if (this.warningMessages.size() > 0) {
      message = this.warningMessages.get(this.warningMessages.size() - 1);
      messageType = 2;
    } else if (this.infoMessages.size() > 0) {
      message = this.infoMessages.get(this.infoMessages.size() - 1);
      messageType = 1;
    } else if (this.messages.size() > 0) {
      message = this.messages.get(this.messages.size() - 1);
      messageType = 0;
    } 
    if (message != null) {
      Image image = null;
      switch (messageType) {
        case 1:
          image = JFaceResources.getImage("dialog_messasge_info_image");
          break;
        case 2:
          image = JFaceResources.getImage("dialog_messasge_warning_image");
          break;
        case 3:
          image = JFaceResources.getImage("dialog_message_error_image");
          break;
      } 
      this.imageLabel.setVisible((image != null));
      if (image != null)
        this.imageLabel.setImage(image); 
      this.messageLabel.setText(message);
    } 
    show((message != null));
  }
  
  public void clearAllMessages() {
    this.errorMessages.clear();
    this.warningMessages.clear();
    this.infoMessages.clear();
    this.messages.clear();
    show(false);
  }
  
  public void addMessage(String msg, int type) {
    switch (type) {
      case 1:
        addInformation(msg);
        return;
      case 2:
        addWarning(msg);
        return;
      case 3:
        addError(msg);
        return;
    } 
    addMessage(msg);
  }
  
  public void removeMessage(String msg, int type) {
    switch (type) {
      case 1:
        removeInformation(msg);
        return;
      case 2:
        removeWarning(msg);
        return;
      case 3:
        removeError(msg);
        return;
    } 
    removeMessage(msg);
  }
  
  public void addMessage(String msg) {
    if (msg == null)
      return; 
    if (this.messages.contains(msg))
      this.messages.remove(msg); 
    this.messages.add(msg);
    updateMessage();
  }
  
  public void removeMessage(String msg) {
    if (msg == null)
      return; 
    this.messages.remove(msg);
    updateMessage();
  }
  
  public void addInformation(String info) {
    if (info == null)
      return; 
    if (this.infoMessages.contains(info))
      this.infoMessages.remove(info); 
    this.infoMessages.add(info);
    updateMessage();
  }
  
  public void removeInformation(String info) {
    if (info == null)
      return; 
    this.infoMessages.remove(info);
    updateMessage();
  }
  
  public void addWarning(String warning) {
    if (warning == null)
      return; 
    if (this.warningMessages.contains(warning))
      this.warningMessages.remove(warning); 
    this.warningMessages.add(warning);
    updateMessage();
  }
  
  public void removeWarning(String warning) {
    if (warning == null)
      return; 
    this.warningMessages.remove(warning);
    updateMessage();
  }
  
  public void addError(String error) {
    if (error == null)
      return; 
    if (this.errorMessages.contains(error))
      this.errorMessages.remove(error); 
    this.errorMessages.add(error);
    updateMessage();
  }
  
  public void removeError(String error) {
    if (error == null)
      return; 
    this.errorMessages.remove(error);
    updateMessage();
  }
}
