package com.chipon32.debug.ui.view.command;

public enum STD {
	  IN, OUT, ERR, WARNING, DEBUG;
	  
	  static STD convert(IConsoleMessage.MessageCategory category) {
	    switch (category) {
	      case INPUT_USER:
	        return IN;
	      case OUTPUT_INFO:
	        return OUT;
	      case OUTPUT_ERROR:
	        return ERR;
	      case OUTPUT_WARNING:
	        return WARNING;
	      case OUTPUT_DEBUG:
	        return DEBUG;
		default:
			return OUT;
	    } 
	  }
	}
