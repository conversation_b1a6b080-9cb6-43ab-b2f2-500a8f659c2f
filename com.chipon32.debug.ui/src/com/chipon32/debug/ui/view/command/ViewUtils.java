package com.chipon32.debug.ui.view.command;

import java.util.ArrayList;

import org.eclipse.ui.IViewPart;
import org.eclipse.ui.IViewReference;
import org.eclipse.ui.IWorkbenchPage;
import org.eclipse.ui.IWorkbenchWindow;
import org.eclipse.ui.PlatformUI;

public class ViewUtils {
	
	public static ArrayList<IViewPart> getAllOpenViewsWithID(String id) {
	    ArrayList<IViewPart> result = new ArrayList<>();
	    byte b;
	    int i;
	    IWorkbenchWindow[] arrayOfIWorkbenchWindow;
	    for (i = (arrayOfIWorkbenchWindow = PlatformUI.getWorkbench().getWorkbenchWindows()).length, b = 0; b < i; ) {
	      IWorkbenchWindow window = arrayOfIWorkbenchWindow[b];
	      byte b1;
	      int j;
	      IWorkbenchPage[] arrayOfIWorkbenchPage;
	      for (j = (arrayOfIWorkbenchPage = window.getPages()).length, b1 = 0; b1 < j; ) {
	        IWorkbenchPage page = arrayOfIWorkbenchPage[b1];
	        byte b2;
	        int k;
	        IViewReference[] arrayOfIViewReference;
	        for (k = (arrayOfIViewReference = page.getViewReferences()).length, b2 = 0; b2 < k; ) {
	          IViewReference ref = arrayOfIViewReference[b2];
	          if (id.equals(ref.getId())) {
	            IViewPart view = ref.getView(false);
	            if (view != null)
	              result.add(view); 
	          } 
	          b2++;
	        } 
	        b1++;
	      } 
	      b++;
	    } 
	    return result;
	  }

}
