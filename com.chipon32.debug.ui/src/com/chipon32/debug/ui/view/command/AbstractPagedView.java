package com.chipon32.debug.ui.view.command;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.e4.ui.model.application.ui.basic.MPart;
import org.eclipse.e4.ui.workbench.modeling.EPartService;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.IViewPart;
import org.eclipse.ui.IViewSite;
import org.eclipse.ui.IWorkbenchPage;
import org.eclipse.ui.IWorkbenchPart;
import org.eclipse.ui.PartInitException;
import org.eclipse.ui.part.IPage;
import org.eclipse.ui.part.IPageBookViewPage;
import org.eclipse.ui.part.MessagePage;
import org.eclipse.ui.part.PageBook;
import org.eclipse.ui.part.PageBookView;

public abstract class AbstractPagedView extends PageBookView {
//	private Class<DebugConsolePage> mPageClass;

	private ArrayList<PageBookViewPage> mPages = new ArrayList<>();

	protected EPartService partService;

	public void init(IViewSite site) throws PartInitException {
		super.init(site);
		this.partService = (EPartService) site.getService(EPartService.class);
	}

	protected MPart getE4Model() {
		if (this.partService == null || getViewSite() == null)
			return null;
		String viewID = getViewSite().getId();
		String secondaryID = getViewSite().getSecondaryId();
		if (secondaryID != null && !secondaryID.isEmpty())
			viewID = String.valueOf(viewID) + ":" + secondaryID;
		return this.partService.findPart(viewID);
	}

	protected IPage createDefaultPage(PageBook book) {
		MessagePage page = new MessagePage();
		initPage((IPageBookViewPage) page);
		page.createControl((Composite) book);
		page.setMessage("oooooooooooo");
		return (IPage) page;
	}

//	public String getDefaultMessage() {
//		return this.mDefMsg;
//	}

	protected abstract Object getAnchorFromPart(IWorkbenchPart paramIWorkbenchPart);


	protected void doDestroyPage(IWorkbenchPart part, PageBookView.PageRec pageRecord) {
		this.mPages.remove(pageRecord.page);
		pageRecord.page.dispose();
		pageRecord.dispose();
	}

	protected IWorkbenchPart getBootstrapPart() {
		IWorkbenchPage page = getSite().getPage();
		return (page != null) ? (IWorkbenchPart) page.getActiveEditor() : null;
	}

	protected boolean isImportant(IWorkbenchPart part) {
		return part instanceof org.eclipse.ui.IEditorPart;
	}

	public void partBroughtToTop(IWorkbenchPart part) {
		partActivated(part);
	}

	public ArrayList<PageBookViewPage> getPages() {
		return this.mPages;
	}

	/*
	 * public Class<? extends PageBookViewPage> getPageClass() { return
	 * this.mPageClass; }
	 */

	public static void syncAllPages(String id) {
		for (IViewPart view : ViewUtils.getAllOpenViewsWithID(id)) {
			if (view instanceof AbstractPagedView) {
				List<PageBookViewPage> pages = ((AbstractPagedView) view).getPages();
				for (IPageBookViewPage page : pages) {
					if (page instanceof SyncablePage) {
						((SyncablePage) page).syncWithAnchor();
					}
				}
			}
		}
	}
}
