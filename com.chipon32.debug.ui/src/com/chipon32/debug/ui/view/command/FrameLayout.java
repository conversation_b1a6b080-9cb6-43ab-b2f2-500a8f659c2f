package com.chipon32.debug.ui.view.command;

import java.util.ArrayList;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.graphics.Rectangle;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Layout;

public class FrameLayout extends Layout {
  
  public static final Integer MIDDLE = Integer.valueOf(0);
  
  public static final Integer TOP = Integer.valueOf(1);
  
  public static final Integer BOTTOM = Integer.valueOf(2);
  
  public static final Integer LEFT = Integer.valueOf(3);
  
  public static final Integer RIGHT = Integer.valueOf(4);
  
  private Point mTopSize;
  
  private Point mLeftSize;
  
  private Point mMiddleSize;
  
  protected Point computeSize(Composite composite, int wHint, int hHint, boolean flushCache) {
    Control[] children = composite.getChildren();
    computeSizes(children);
    return new Point(Math.max(this.mTopSize.x, this.mLeftSize.x + this.mMiddleSize.x), Math.max(this.mLeftSize.y, this.mMiddleSize.y) + this.mTopSize.y);
  }
  
  private void computeSizes(Control[] children) {
    this.mTopSize = new Point(0, 0);
    this.mLeftSize = new Point(0, 0);
    this.mMiddleSize = new Point(0, 0);
    byte b;
    int i;
    Control[] arrayOfControl;
    for (i = (arrayOfControl = children).length, b = 0; b < i; ) {
      Control control = arrayOfControl[b];
      Point size = control.computeSize(-1, -1);
      switch (getPosition(control)) {
        case 1:
        case 2:
          this.mTopSize.y += size.y;
          this.mTopSize.x = Math.max(this.mTopSize.x, size.x);
          break;
        case 3:
        case 4:
          this.mLeftSize.x += size.x;
          this.mLeftSize.y = Math.max(this.mLeftSize.y, size.y);
          break;
        default:
          this.mMiddleSize.y += size.y;
          this.mMiddleSize.x = Math.max(this.mMiddleSize.x, size.x);
          break;
      } 
      b++;
    } 
  }
  
  private static int getPosition(Control control) {
    int position = MIDDLE.intValue();
    Object layoutData = control.getLayoutData();
    if (layoutData instanceof Integer)
      position = ((Integer)layoutData).intValue(); 
    return position;
  }
  
  protected void layout(Composite composite, boolean flushCache) {
    ArrayList<Control> top = new ArrayList<>();
    ArrayList<Control> bottom = new ArrayList<>();
    ArrayList<Control> left = new ArrayList<>();
    ArrayList<Control> right = new ArrayList<>();
    ArrayList<Control> middle = new ArrayList<>();
    Control[] children = composite.getChildren();
    byte b;
    int j;
    Control[] arrayOfControl1;
    for (j = (arrayOfControl1 = children).length, b = 0; b < j; ) {
      Control control = arrayOfControl1[b];
      switch (getPosition(control)) {
        case 1:
          top.add(control);
          break;
        case 2:
          bottom.add(control);
          break;
        case 3:
          left.add(control);
          break;
        case 4:
          right.add(control);
          break;
        default:
          middle.add(control);
          break;
      } 
      b++;
    } 
    Rectangle clientArea = composite.getClientArea();
    int x = clientArea.x, y = 0, w = clientArea.width, h = 0;
    for (int i = 0; i < top.size(); i++) {
      Control c = top.get(i);
      h = (c.computeSize(-1, -1)).y;
      c.setBounds(x, y, w, h);
      y += h;
    } 
    int middleTop = y;
    y = clientArea.height;
    for (int k = bottom.size() - 1; k >= 0; k--) {
      Control c = bottom.get(k);
      h = (c.computeSize(-1, -1)).y;
      y -= h;
      c.setBounds(x, y, w, h);
    } 
    int middleBottom = Math.max(y, middleTop);
    x = 0;
    y = middleTop;
    h = middleBottom - middleTop;
    for (int m = 0; m < left.size(); m++) {
      Control c = left.get(m);
      w = (c.computeSize(-1, -1)).x;
      c.setBounds(x, y, w, h);
      x += w;
    } 
    int middleLeft = x;
    x = clientArea.width;
    for (int n = right.size() - 1; n >= 0; n--) {
      Control c = right.get(n);
      w = (c.computeSize(-1, -1)).x;
      x -= w;
      c.setBounds(x, y, w, h);
    } 
    int middleRight = Math.max(x, middleLeft);
    x = middleLeft;
    y = middleTop;
    w = middleRight - middleLeft;
    for (int i1 = 0; i1 < middle.size(); i1++) {
      Control c = middle.get(i1);
      if (i1 == middle.size() - 1) {
        h = middleBottom - y;
      } else {
        h = (c.computeSize(-1, -1)).y;
      } 
      c.setBounds(x, y, w, h);
      y += h;
    } 
  }
  
}
