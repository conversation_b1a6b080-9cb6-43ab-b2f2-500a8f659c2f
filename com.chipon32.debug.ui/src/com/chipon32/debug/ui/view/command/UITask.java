package com.chipon32.debug.ui.view.command;

import java.util.concurrent.TimeUnit;
import org.eclipse.swt.widgets.Display;

public class UITask extends Task {
  public static void callOnUIThread(Runnable runnable) {
    Display display = Display.getDefault();
    if (display != null && !display.isDisposed())
      display.asyncExec(runnable); 
  }
  
  public static final UITask scheduleOnUIThread(Runnable runnable) {
    UITask task = new UITask(runnable, null, null, "scheduled call on UI Thread - no delay");
    task.execute();
    return task;
  }
  
  public static final UITask scheduleOnUIThread(Runnable runnable, String key, long delay, TimeUnit delayUnits) {
    UITask task = new UITask(runnable, key, null, "scheduled call on UI Thread with delay");
    task.schedule(delay, delayUnits);
    return task;
  }
  
  public static void blockingCallOnUIThread(Runnable runnable, String name) {
    try {
      UITask task = new UITask(runnable, null, name, "blocking call on UI Thread");
      if (Display.getCurrent() == null) {
        Display display = Display.getDefault();
        if (display != null && !display.isDisposed())
          display.syncExec(task); 
      } else {
        task.run();
      } 
    } catch (Throwable throwable) {
    	throwable.printStackTrace();
    } 
  }
  
  private UITask(Runnable runnable, String key, String name, String comment) {
    super(runnable, key, name, comment);
  }
  
  protected void scheduleInternal(long delay, TimeUnit delayUnits) {
    if (delay == 0L && Display.getCurrent() != null) {
      run();
    } else {
      super.scheduleInternal(delay, delayUnits);
    } 
  }
  
  public void run() {
    if (Display.getCurrent() == null) {
      callOnUIThread(this);
      return;
    } 
    super.run();
  }
}

