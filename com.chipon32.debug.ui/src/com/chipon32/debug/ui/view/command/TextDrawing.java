package com.chipon32.debug.ui.view.command;

import java.util.StringTokenizer;
import org.eclipse.swt.graphics.GC;

public class TextDrawing {
  
  public static final String LINE_ENDING = "\n";
  
  
  
  public static void drawText(GC gc, String text, int x, int y, int alignment) {
	    drawText(gc, text, x, y, alignment, true);
  }
  
  public static void drawText(GC gc, String text, int x, int y, int alignment, boolean transparent) {
	    int saved = FontInfo.prepareGCForTextDrawing(gc);
	    FontInfo fontInfo = FontInfo.get(gc.getFont());
	    int fontHeight = fontInfo.getHeight();
	    boolean lastWasReturn = true;
	    StringTokenizer tokenizer = new StringTokenizer(text, "\n", true);
	    while (tokenizer.hasMoreTokens()) {
	      String token = tokenizer.nextToken();
	      if ("\n".equals(token)) {
	        if (lastWasReturn) {
	          y += fontHeight;
	          continue;
	        } 
	        lastWasReturn = true;
	        continue;
	      } 
	      lastWasReturn = false;
	      int start = x;
	      if (alignment == 16777216) {
	        start -= fontInfo.getWidth(token) / 2;
	      } else if (alignment == 131072) {
	        start -= fontInfo.getWidth(token);
	      } 
	      gc.drawString(token, start, y, transparent);
	      y += fontHeight;
	    } 
	    FontInfo.restoreGCFromTextDrawing(gc, saved);
	  }
  
}
