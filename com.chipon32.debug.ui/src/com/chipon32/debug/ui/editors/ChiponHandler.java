/**
 * 
 */
package com.chipon32.debug.ui.editors;

import org.eclipse.core.commands.AbstractHandler;
import org.eclipse.core.commands.ExecutionEvent;
import org.eclipse.core.commands.ExecutionException;
import org.eclipse.core.commands.IHandler;
import org.eclipse.jface.text.Position;
import org.eclipse.jface.text.source.Annotation;
import org.eclipse.jface.text.source.IAnnotationModel;
import org.eclipse.ui.IEditorPart;
import org.eclipse.ui.handlers.HandlerUtil;

/**
 * <AUTHOR>
 *
 */
public class ChiponHandler extends AbstractHandler implements IHandler {

	/**
	 * 
	 */
	public ChiponHandler() {
		// TODO Auto-generated constructor stub
	}

	@Override
	public Object execute(ExecutionEvent event) throws ExecutionException {
		IEditorPart editorPart = HandlerUtil.getActiveEditor(event);
		if(editorPart instanceof ChiponEditor)
		{
			ChiponEditor editor = (ChiponEditor)editorPart;
			IAnnotationModel annModel = editor.getDocumentProvider().getAnnotationModel(editor.getEditorInput());
			annModel.addAnnotation(new Annotation(true), new Position(5));
			
		}
		return null;
	}

}
