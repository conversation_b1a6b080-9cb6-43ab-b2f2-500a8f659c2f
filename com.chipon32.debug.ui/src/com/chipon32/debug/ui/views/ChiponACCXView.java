package com.chipon32.debug.ui.views;

import java.util.List;

import org.eclipse.core.resources.IProject;
import org.eclipse.core.runtime.IProgressMonitor;
import org.eclipse.core.runtime.IStatus;
import org.eclipse.core.runtime.Status;
import org.eclipse.debug.ui.AbstractDebugView;
import org.eclipse.debug.ui.DebugUITools;
import org.eclipse.debug.ui.contexts.DebugContextEvent;
import org.eclipse.debug.ui.contexts.IDebugContextListener;
import org.eclipse.jface.action.IMenuManager;
import org.eclipse.jface.action.IToolBarManager;
import org.eclipse.jface.viewers.AbstractTreeViewer;
import org.eclipse.jface.viewers.ISelection;
import org.eclipse.jface.viewers.IStructuredSelection;
import org.eclipse.jface.viewers.TreeViewer;
import org.eclipse.jface.viewers.Viewer;
import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.FontData;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.TreeColumn;
import org.eclipse.ui.progress.UIJob;

import com.chipon32.debug.core.model.ChiponACCXData;
import com.chipon32.debug.core.model.ChiponCurrencyRegisterData;
import com.chipon32.debug.core.model.ChiponDebugTarget;
import com.chipon32.debug.core.model.ChiponStackFrame;
import com.chipon32.debug.core.model.ChiponThread;
import com.chipon32.debug.core.protocol.ChiponPrintCurrencyRegisterCommand;
import com.chipon32.debug.core.protocol.ChiponPrintCurrencyRegisterCommandResult;
import com.chipon32.util.ui.UIUtil;

public class ChiponACCXView extends AbstractDebugView implements IDebugContextListener {
	
	public static final String ID = "com.chipon32.debug.ui.view.ACCXView"; //$NON-NLS-1$

	private ChiponThread fThread;
	public ChiponACCXView() { }
	private void setInput(TreeViewer tv){
//		update(tv.getSelection());   //2017.1.11为了测试添加
		if(tv != null){
			List<ChiponCurrencyRegisterData> dataList = ChiponThread.DataList;
			if(dataList != null && dataList.size()>0){
				IProject project=null;
				if(fThread!=null)
				 project =((ChiponDebugTarget)fThread.getDebugTarget()).getfProject();
				if (project != null && dataList != null) {
					List<ChiponACCXData> accxDatas = ChiponViewTool.getViewTool().resovleAccxdatas(dataList);	
					tv.setInput(accxDatas);
					tv.refresh();
				}
			}else {
				getSite().setSelectionProvider(tv);
			}
		}
	}

	@Override
	public void debugContextChanged(final DebugContextEvent event) {
		new UIJob(getSite().getShell().getDisplay(), Messages.ChiponACCXView_1) {
			{
				setSystem(true);
			}

			@Override
			public IStatus runInUIThread(IProgressMonitor monitor) {
				if (getViewer() != null) { // runs asynchronously, view may be
											// disposed
					update(event.getContext());
				}
				return Status.OK_STATUS;
			}
		}.schedule();

	}
	
	private void update(ISelection context) {
		fThread = null;
		if (context instanceof IStructuredSelection) {
			Object element = ((IStructuredSelection) context).getFirstElement();
			if (element instanceof ChiponDebugTarget) {
				 fThread = ((ChiponDebugTarget)element).getCurrentThread();
			}
			else if (element instanceof ChiponThread) {
				 fThread = (ChiponThread)element;
			} else if (element instanceof ChiponStackFrame) {
				fThread = (ChiponThread) ((ChiponStackFrame) element)
						.getThread();
			} else
				return;
		}
		List<ChiponCurrencyRegisterData> dataList = null;
		//若通用寄存器已获取过，则直接调用结果值，否则，自己获取
		if(fThread.isUpdateCurResigter()){
			dataList = ChiponThread.DataList;
		}else {
			if (fThread != null && fThread.isSuspended() && fThread.isCanSetInputACCX()) {

				//获取通用寄存器R0~R31的值
				ChiponPrintCurrencyRegisterCommandResult currencyRegisterResult = 
						(ChiponPrintCurrencyRegisterCommandResult) fThread.sendCommand(new ChiponPrintCurrencyRegisterCommand("info registers")); //$NON-NLS-1$
				if(currencyRegisterResult != null){
					dataList = currencyRegisterResult.dataList;
					ChiponThread.DataList = currencyRegisterResult.dataList;
				}
				fThread.setCanSetInputACCX(false);
			}else {
				return;
			}
		}


		IProject project=null;
		if(fThread!=null)
		 project =((ChiponDebugTarget)fThread.getDebugTarget()).getfProject();
		if (project != null && dataList != null) {
			List<ChiponACCXData> accxDatas = ChiponViewTool.getViewTool().resovleAccxdatas(dataList);	
			
			Viewer viewer = getViewer();
			
			if(viewer instanceof AbstractTreeViewer){
				AbstractTreeViewer treeviewer = (AbstractTreeViewer)viewer;
				
				treeviewer.setInput(accxDatas);
				treeviewer.expandAll();
				treeviewer.refresh();
			}
		}
	}

	@Override
	protected Viewer createViewer(Composite parent) {
		TreeViewer tv = new TreeViewer(parent, SWT.BORDER | SWT.FULL_SELECTION);
		tv.setContentProvider(new ChiponAccContentProvider());
		tv.setLabelProvider(new ChiponAccLabelProvider());
		tv.getTree().setLayoutData(new GridData(GridData.FILL_BOTH));
		tv.getTree().setHeaderVisible(true);
		tv.getTree().setLinesVisible(true);
		
		FontData curfd[] = tv.getTree().getFont()
				.getFontData();
		int approxfontwidth = curfd[0].getHeight();
		
		TreeColumn tcName = new TreeColumn( tv.getTree(),SWT.CENTER);
		tcName.setWidth(8 * approxfontwidth);
		tcName.setResizable(true);
		tcName.setMoveable(true);
		tcName.setText(Messages.ChiponACCXView_3);
		
		TreeColumn tcValue = new TreeColumn( tv.getTree(),SWT.CENTER);
		tcValue.setWidth(12 * approxfontwidth);
		tcValue.setResizable(true);
		tcValue.setMoveable(true);
		tcValue.setText(Messages.ChiponACCXView_4);
		
		TreeColumn tdValue2 = new TreeColumn( tv.getTree(),SWT.CENTER);
		tdValue2.setWidth(12 * approxfontwidth);
		tdValue2.setResizable(true);
		tdValue2.setMoveable(true);
		tdValue2.setText(Messages.ChiponACCXView_5);
		
		TreeColumn tdValue3 = new TreeColumn( tv.getTree(),SWT.CENTER);
		tdValue3.setWidth(16 * approxfontwidth);
		tdValue3.setResizable(true);
		tdValue3.setMoveable(true);
		tdValue3.setText(Messages.ChiponACCXView_6);
		
		UIUtil.setDefaultProperties(tv);
		DebugUITools.getDebugContextManager().getContextService(getSite().getWorkbenchWindow()).addDebugContextListener(this);
		setInput(tv);
		return tv;
	}

	@Override
	protected void createActions() {
		// TODO Auto-generated method stub

	}

	@Override
	protected String getHelpContextId() {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	protected void fillContextMenu(IMenuManager menu) {
		// TODO Auto-generated method stub

	}

	@Override
	protected void configureToolBar(IToolBarManager tbm) {
		// TODO Auto-generated method stub

	}
	@Override
	public void dispose() {
		DebugUITools.getDebugContextManager()
		.getContextService(getSite().getWorkbenchWindow())
		.removeDebugContextListener(this);
		super.dispose();
	}

}
