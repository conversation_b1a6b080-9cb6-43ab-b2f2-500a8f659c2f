package com.chipon32.debug.ui.views;


import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.eclipse.core.runtime.IAdaptable;
import org.eclipse.debug.ui.DebugUITools;
import org.eclipse.jface.dialogs.MessageDialog;
import org.eclipse.jface.viewers.TableViewer;
import org.eclipse.jface.viewers.TreeViewer;
import org.eclipse.jface.viewers.Viewer;
import org.eclipse.ui.IViewPart;
import org.eclipse.ui.IViewReference;
import org.eclipse.ui.IWorkbenchPage;
import org.eclipse.ui.IWorkbenchWindow;
import org.eclipse.ui.PlatformUI;

import com.chipon32.debug.core.model.ChiponDebugTarget;
import com.chipon32.debug.core.model.ChiponGroupRomData;
import com.chipon32.debug.core.model.ChiponLabelData;
import com.chipon32.debug.core.model.ChiponRomData;
import com.chipon32.debug.core.model.ChiponRomDatas;
import com.chipon32.debug.core.model.ChiponStackFrame;
import com.chipon32.debug.core.model.ChiponThread;
import com.chipon32.debug.core.protocol.ChiponCommandResult;
import com.chipon32.debug.core.protocol.ChiponSetAndMemoryCommand;
import com.chipon32.debug.core.util.PackRomDataList;
import com.chipon32.util.ui.DoubleClickCellModifier;

/**
 * <AUTHOR> @since 
 * TableView Modify support
 */
public class ChiponMemoryCellModifier extends DoubleClickCellModifier {

	private ChiponThread    fThread;
//	private ICategory oldCategory;
	
	public String showformat="hex8";
	public ChiponMemoryCellModifier(TreeViewer treeViewer) {
		super(treeViewer);
	}
	
	public ChiponMemoryCellModifier(TableViewer tableViewer) {
		super(tableViewer);
	}

	@Override
	public boolean allowModify(Object element, int columnIndex) {
		boolean result=true;
		if(element instanceof ChiponGroupRomData)
		{
			if(((ChiponGroupRomData) element).isIs_virtual_lineGroup()==true) return false;
			
			if(columnIndex >= 1 && columnIndex <= 16 /*&& groupRomData.getAddress() >= 0*/){
				
				ChiponGroupRomData groupRomData = (ChiponGroupRomData) element;
				String columnNumAddress =groupRomData.getRowNum(); //行地址
				
				//10000000 以下行不可修改，固定的ram起始限定，无型号信息的不做可修改范围判断。
				if(columnNumAddress !=null && columnNumAddress.length()>0 && Long.parseLong(columnNumAddress, 16)<0x10000000){
					result=false; 
					return result;
				}
				 Object  value=getColumnValue(element, columnIndex);
				 String inHex = value.toString().toUpperCase();		
				 if(inHex.contains("--")||inHex.contains("??"))
					 return false;
			}
		}
		return result;
	}

	/* (non-Javadoc)
	 * @see com.chipon32.util.ui.DoubleClickCellModifier#doModify(java.lang.Object, int, java.lang.Object)
	 * doModify
	 */
	@Override
	public void doModify(Object element, int columnIndex, Object value) {
		if(element instanceof ChiponGroupRomData)
		{
			fThread=null;
			ChiponGroupRomData groupRomData = (ChiponGroupRomData)element;
			if(columnIndex >= 1 && columnIndex <= 16 /*&& groupRomData.getAddress() >= 0*/)
			{
				
				String tmp = value.toString().toUpperCase();		
				if( tmp.length()==0)
					return;
				//###########################################
				
				//###########################################				
				if(tmp.length()<2){
					StringBuffer zeroNum = new StringBuffer();
					for(int i=0;i<(2-tmp.length());i++){
						zeroNum.append("0"); //$NON-NLS-1$
					}
					tmp = zeroNum.append(tmp).toString();					
				}
				//###########################################################################
				Pattern pattern = Pattern.compile("[-]?[0-9]*[.]?[0-9a-fA-F]+(E)?-?[0-9]*"); //$NON-NLS-1$  "[0-9a-fA-F]{2}"
				Matcher match = pattern.matcher(tmp);
					
				if(!match.matches() /*|| isLegality*/){
					String errorAddress = (	groupRomData.getRomDatas().get(columnIndex-1).getAddress()	);
					String errorInfo = Messages.ChiponGroupMemoryCellModifier_2 + errorAddress +"\n\"" + value.toString() + Messages.ChiponGroupMemoryCellModifier_4; //$NON-NLS-2$
					MessageDialog.openError(null, Messages.ChiponGroupMemoryCellModifier_0, errorInfo);
					return;
				}
				//###########################################################################
				StringBuffer commandAddress = new StringBuffer("0x");				 //$NON-NLS-1$
				commandAddress.append(Long.toHexString(	Long.parseLong(((groupRomData.getRomDatas().get(columnIndex-1).getAddress())), 16)));		
				
//				String commandValue =Integer.toString(Integer.parseInt(tmp, 16)); 
				String commandValue =ChiponMemoryServiceClass.ValueStringCovSet(showformat,tmp); 
				//###########################################################################
				ChiponStackFrame frame = null;
		        IAdaptable debugContext = DebugUITools.getDebugContext();
		        if (debugContext instanceof ChiponStackFrame) {
		           frame = (ChiponStackFrame) debugContext;
		        } else if (debugContext instanceof ChiponThread) {		
		        	return;
		        } else if (debugContext instanceof ChiponDebugTarget) {
		        	return;
		        }
		        
		        //在这里通过frame发送命令
		        if(frame != null){
		        	fThread=(ChiponThread) frame.getThread();
		        }
		        if (fThread != null && fThread.isSuspended()){
		        	// 单字节的修改，而ChiponSetAndRegisterCommand为无类型默认int*的赋值
		        	String addressformat ="("+ ChiponMemoryServiceClass.getFullName(showformat)+"*)"	+commandAddress.toString();
		        	ChiponCommandResult result= fThread.sendCommand(new ChiponSetAndMemoryCommand(addressformat,commandValue));
		        	
		        	if(result!=null){
		        		
		        		List<ChiponRomData> romDataList=ChiponViewTool.getViewTool().getNewThreadList(ChiponThread.romDataList,groupRomData,columnIndex,tmp,showformat);		        		
		 		        ChiponRomDatas romDatas = new PackRomDataList().packList(romDataList);

				        tableViewer.setInput(romDatas);
						tableViewer.refresh();
						
//						boolean isregistersViewOpen=false;
						boolean isLabelViewOpen=false;
//						IViewPart registersViewPart = null;
						IViewPart labelViewPart = null;
						 IWorkbenchWindow[] windows = PlatformUI.getWorkbench().getWorkbenchWindows();
						 for(IWorkbenchWindow window : windows){
							 for(IWorkbenchPage page : window.getPages()){
								 for(IViewReference viewReference : page.getViewReferences()){
									 
									 if(ChiponLabelView.ID.equals(viewReference.getId())){
										 isLabelViewOpen = true;
										 labelViewPart = viewReference.getView(false);
									 }
								 }
							 }
						 }
								 
						 if(isLabelViewOpen && labelViewPart!=null && labelViewPart instanceof ChiponLabelView){
							 List<ChiponLabelData> newLabelData=new ArrayList<ChiponLabelData>(); 
							
							 for(ChiponLabelData data : ChiponThread.labelDataList){
								 if((data.getAddress()==null || data.getAddress().length()==0)&&(data.getName()!=null && data.getName().length()>0)){
									 newLabelData.add(new ChiponLabelData(fThread,data.getName(),"","","","")); //$NON-NLS-1$ //$NON-NLS-2$ //$NON-NLS-3$ //$NON-NLS-4$
								 }
								 for(ChiponRomData romData : romDataList){
									 if(data.getAddress()!=null && data.getAddress().length()>0 && romData.getAddress().substring(2).equals(Integer.toHexString(Integer.parseInt(data.getAddress().substring(2),16)))){
										 if(data!=null && data.getName()!=null){
											 newLabelData.add(new ChiponLabelData(fThread,data.getName(),Integer.toBinaryString(Integer.parseInt(romData.getValue().toUpperCase(), 16)),Integer.parseInt(romData.getValue().toUpperCase(), 16)+"",romData.getValue().toUpperCase(),data.getAddress())); //$NON-NLS-1$
										 }else{
											 newLabelData.add(new ChiponLabelData(fThread,"",Integer.toBinaryString(Integer.parseInt(romData.getValue().toUpperCase(), 16)),Integer.parseInt(romData.getValue().toUpperCase(), 16)+"",romData.getValue().toUpperCase(),data.getAddress())); //$NON-NLS-1$ //$NON-NLS-2$
										 }
									 }
									
								 }
							 }
							 final Viewer tableViewer =((ChiponLabelView)labelViewPart).getViewer();
			        		 tableViewer.setInput(newLabelData);
			        		 tableViewer.refresh();
			        		 ChiponThread.labelDataList=newLabelData;	
						 }
						 ChiponThread.romDataList=romDataList;
		        	}
		        }
					
			}
		}

	}
	

	/* (non-Javadoc)
	 * @see com.chipon32.util.ui.DoubleClickCellModifier#getColumnValue(java.lang.Object, int)
	 */
	@Override
	public Object getColumnValue(Object element, int columnIndex) {
		if(element instanceof ChiponGroupRomData)
		{
			ChiponGroupRomData groupRomData = (ChiponGroupRomData)element;
			if(columnIndex >= 1 && columnIndex <= 16 /*&& groupRomData.getAddress() >= 0*/)
			{
				//ChiponRomData romData = groupRomData.getRomDatas().get(columnIndex - 1);				
				//return romData.getValue().toUpperCase();
				columnIndex =columnIndex-1 ; // 0-15
				String valueOut="";
				//valueOut = groupRomData.getRomDatas().get(columnIndex).getValue().toUpperCase();
				
				int onelength =ChiponMemoryServiceClass.getFormatBytes(showformat);
				if(columnIndex%onelength ==0)
				{
					if( (columnIndex + onelength) > groupRomData.getRomDatas().size())
						return "??";
					for(int i=0; i<onelength;i++ )
					{
						valueOut = groupRomData.getRomDatas().get(columnIndex+i).getValue().toUpperCase() + valueOut;
					}
				}
				else
				{
					return "*";
				}
				
				//------------------------
				return ChiponMemoryServiceClass.ValueStringCovGet(showformat,valueOut);
			}
		}
		return null;
	}
	
	
}
