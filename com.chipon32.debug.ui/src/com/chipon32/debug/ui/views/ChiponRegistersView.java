package com.chipon32.debug.ui.views;

import java.text.Collator;
import java.util.ArrayList;  
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Locale;

import org.eclipse.core.resources.IProject;
import org.eclipse.core.runtime.IProgressMonitor;
import org.eclipse.core.runtime.IStatus;
import org.eclipse.core.runtime.Status;
import org.eclipse.debug.ui.AbstractDebugView;
import org.eclipse.debug.ui.DebugUITools;
import org.eclipse.debug.ui.contexts.DebugContextEvent;
import org.eclipse.debug.ui.contexts.IDebugContextListener;
import org.eclipse.jface.action.IMenuManager;
import org.eclipse.jface.action.IToolBarManager;
import org.eclipse.jface.dialogs.MessageDialog;
import org.eclipse.jface.viewers.CellEditor;
import org.eclipse.jface.viewers.ISelection;
import org.eclipse.jface.viewers.IStructuredSelection;
import org.eclipse.jface.viewers.TextCellEditor;
import org.eclipse.jface.viewers.TreeViewer;
import org.eclipse.jface.viewers.Viewer;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.graphics.FontData;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Button;
import org.eclipse.swt.widgets.Combo;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Label;

import org.eclipse.swt.widgets.TreeColumn;
import org.eclipse.ui.progress.UIJob;
import org.eclipse.jface.viewers.AbstractTreeViewer;
import com.chipon32.chiponide.core.chipondescription.ICategory;
import com.chipon32.chiponide.core.chipondescription.IEntry;
import com.chipon32.debug.core.model.ChiponDebugTarget;
import com.chipon32.debug.core.model.ChiponStackFrame;
import com.chipon32.debug.core.model.ChiponThread;
import com.chipon32.util.ui.UIUtil;

/**
 * Debug寄存器视图
 * <AUTHOR> @since 2013-6-20 上午9:48:13
 */
public class ChiponRegistersView extends AbstractDebugView implements IDebugContextListener{
	public ChiponRegistersView() {
	}

	//public static final String ID = "org.eclipse.debug.ui.RegisterView";//调试中寄存器部分在Eclipse中寄存器基础之上进行修改 //$NON-NLS-1$
	public static final String ID = "com.chipon32.debug.ui.view.RegisterView";//调试中寄存器部分在Eclipse中寄存器基础之上进行修改 //$NON-NLS-1$
	
	private ChiponThread fThread; //调试功能启动线程

	private Combo registerCombo;
	
	private List<String> registerNames = new ArrayList<>();  //TreeViewer中的寄存器
	private List<String> modulesNames = new ArrayList<>();
	
	private List<IEntry> modules;
	private List<IEntry> registers;
	
	private List<IEntry> currentRegister = new ArrayList<>();
	private List<IEntry> currentModule = new ArrayList<>();
	
	private TreeViewer tv;
	
	/* (non-Javadoc)
	 * @see org.eclipse.debug.ui.AbstractDebugView#createViewer(org.eclipse.swt.widgets.Composite)
	 */
	@Override
	protected Viewer createViewer(Composite parent) {
		
		parent.setLayout(new GridLayout(1, false));	
		Composite container = new Composite(parent, SWT.NONE);

		container.setLayout(new GridLayout(3, false));
		GridData gd_composite = new GridData(SWT.LEFT, SWT.CENTER, true, false, 1, 1);
		container.setLayoutData(gd_composite);
				
		final Label startAddress = new Label(container, SWT.NONE);
		startAddress.setText(Messages.ChiponRegistersView_1);
		
		registerCombo = new Combo(container, SWT.DROP_DOWN | SWT.SEARCH | SWT.FILL_WINDING);
		registerCombo.setSize(20, 5);
		setItems(); //设置registerCombo下拉框的寄存器列表 中分类模块名
		
		Button btnSearch = new Button(container, SWT.NONE);
		btnSearch.setText(Messages.ChiponRegistersView_2);
		//#######################################################
		
		//寄存器详细信息   用TreeViewer的形式显示
		Composite dataComposite = new Composite(parent, SWT.FULL_SELECTION);
		dataComposite.setLayout(new GridLayout(1, false)); 
		GridData gData = new GridData(SWT.FILL, SWT.FILL, true, true,2,8);
		dataComposite.setLayoutData(gData);
			
		tv = new TreeViewer(dataComposite, SWT.BORDER | SWT.FULL_SELECTION | SWT.MULTI);
		
		tv.setContentProvider(new ChiponRegistersContentProvider());
		tv.setLabelProvider(new ChiponRegistersLabelProvider());
		
		tv.getTree().setLayoutData(new GridData(GridData.FILL_BOTH));
		tv.getTree().setHeaderVisible(true);
		tv.getTree().setLinesVisible(true);
//		tv.getTree().addSelectionListener(new SelectionAdapter() {
//			@Override
//			public void widgetSelected(SelectionEvent e) {
//				
//			}
//		});
		FontData curfd[] = tv.getTree().getFont().getFontData();
		int approxfontwidth = curfd[0].getHeight();
		
		TreeColumn tcName = new TreeColumn(tv.getTree(),SWT.LEFT);
		tcName.setWidth(16 * approxfontwidth);
		tcName.setResizable(true);
		tcName.setMoveable(true);
		tcName.setText(Messages.ChiponRegistersView_3);
		
		TreeColumn tcValue = new TreeColumn( tv.getTree(),SWT.LEFT);
		tcValue.setWidth(20* approxfontwidth);
		tcValue.setResizable(true);
		tcValue.setMoveable(true);
		tcValue.setText(Messages.ChiponRegistersView_4);
		
		/*TreeColumn tcDescribe = new TreeColumn(tv.getTree(),SWT.LEFT);
		tcDescribe.setWidth(20 * approxfontwidth);
		tcDescribe.setResizable(true);
		tcDescribe.setMoveable(true);
		tcDescribe.setText("描述");*/
		
		TreeColumn tcAddr = new TreeColumn( tv.getTree(),SWT.LEFT);
		tcAddr.setWidth(12 * approxfontwidth);
		tcAddr.setResizable(true);
		tcAddr.setMoveable(true);
		tcAddr.setText(Messages.ChiponRegistersView_5);
		
		TreeColumn bitLength = new TreeColumn( tv.getTree(),SWT.LEFT);
		bitLength.setWidth(10 * approxfontwidth);
		bitLength.setResizable(true);
		bitLength.setMoveable(true);
		bitLength.setText(Messages.ChiponRegistersView_6);
		
		UIUtil.setDefaultProperties(tv);
		//修改寄存器数据
		CellEditor[] cellEditors = new CellEditor[4];
		cellEditors[0] = null;
		cellEditors[1] = new TextCellEditor(tv.getTree(), SWT.BORDER);
		cellEditors[2] = null;
		cellEditors[3] = null;
		//cellEditors[4] = new TextCellEditor(tv.getTree(), SWT.BORDER);
		
		tv.setCellEditors(cellEditors);
		tv.setCellModifier(new ChiponRegisterCellModifier(tv));

		DebugUITools.getDebugContextManager().getContextService(getSite().getWorkbenchWindow()).addDebugContextListener(this);
		getSite().setSelectionProvider(tv);
		
		//======================================================================================
		//***“添加” 添加寄存器信息按钮事件         添加寄存器：不能重复添加     将寄存器信息添加到TreeViewer中  
		btnSearch.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent e) {
				if(modulesNames != null){
					String ModuleName = registerCombo.getText().trim();
					if(ModuleName==null || ModuleName.length() == 0){
						MessageDialog.openWarning(Display.getCurrent().getActiveShell(), Messages.ChiponRegistersView_7, Messages.ChiponRegistersView_8);
						return;
					}
					if(!modulesNames.contains(ModuleName)){
						MessageDialog.openWarning(Display.getCurrent().getActiveShell(), Messages.ChiponRegistersView_9, Messages.ChiponRegistersView_10);
						return;
					}
					if(modules != null){
						IEntry module = modules.get(registerCombo.indexOf(ModuleName)); //registerCombo.getSelectionIndex()
						if(currentModule.contains(module)){
							//MessageDialog.openInformation(Display.getCurrent().getActiveShell(), Messages.ChiponRegistersView_11, Messages.ChiponRegistersView_12);
							return;
						}else{
							if(fThread == null){
								ISelection selection = DebugUITools.getDebugContextManager().getContextService(getViewSite().getWorkbenchWindow()).getActiveContext();
								 if (selection instanceof IStructuredSelection) {
							         Object element = ((IStructuredSelection)selection).getFirstElement();
							         if(element instanceof ChiponDebugTarget){
							         	fThread = ((ChiponDebugTarget)element).getCurrentThread();
							         }
							         if (element instanceof ChiponThread) {
							         	fThread = (ChiponThread)element;
							         } else if (element instanceof ChiponStackFrame) {
							         	fThread = (ChiponThread) ((ChiponStackFrame) element).getThread();
							         }
								 }
							}						
							if(fThread != null){  //调试功能启动                   加载寄存器的详细信息到TreeViewer中
								if(!fThread.isSuspended())
								{
									MessageDialog.openError(null, Messages.Chipon_CMD_Debug_0, Messages.Chipon_CMD_Debug_15);
									return;
								}
								ChiponViewTool.getViewTool().setSingleRegisterData1(fThread, module);
							}
							currentModule.add(module);
							ChiponThread.currentModule = currentModule;
						}
					}
				}else{
					String registerName = registerCombo.getText().trim();
					if(registerName==null || registerName.length() == 0){
						MessageDialog.openWarning(Display.getCurrent().getActiveShell(), Messages.ChiponRegistersView_13, Messages.ChiponRegistersView_14);
						return;
					}
					if(!registerNames.contains(registerName)){
						MessageDialog.openWarning(Display.getCurrent().getActiveShell(), Messages.ChiponRegistersView_15, Messages.ChiponRegistersView_16);
						return;
					}
					if(registers != null){
						IEntry register = registers.get(registerCombo.indexOf(registerName));//registerCombo.getSelectionIndex()
						if(currentRegister.contains(register)){
							MessageDialog.openInformation(Display.getCurrent().getActiveShell(), Messages.ChiponRegistersView_17, Messages.ChiponRegistersView_18);
							return;
						}else{
							if(fThread == null){
								ISelection selection = DebugUITools.getDebugContextManager().getContextService(getViewSite().getWorkbenchWindow()).getActiveContext();
								if (selection instanceof IStructuredSelection) {
								     Object element = ((IStructuredSelection)selection).getFirstElement();
								     if(element instanceof ChiponDebugTarget){
								         fThread = ((ChiponDebugTarget)element).getCurrentThread();
								     }
								     if (element instanceof ChiponThread) {
								         fThread = (ChiponThread)element;
								     } else if (element instanceof ChiponStackFrame) {
								         fThread = (ChiponThread) ((ChiponStackFrame) element).getThread();
								     }
								}
							}						
							if(fThread != null){//调试功能启动                   加载寄存器的详细信息到TreeViewer中
								if(!fThread.isSuspended())
								{
									MessageDialog.openError(null, Messages.Chipon_CMD_Debug_0, Messages.Chipon_CMD_Debug_15);
									return;
								}
								ChiponViewTool.getViewTool().setSingleRegisterData(fThread, register);
							}
							currentRegister.add(register);
							ChiponThread.currentRegister = currentRegister;
						}
					}
				}
				
				setInput(tv);
				tv.refresh();
			}
			/*public void widgetSelected(SelectionEvent e) {
				String registerName = registerCombo.getText().trim();
				if(registerName==null || registerName.length() == 0){
					MessageDialog.openWarning(Display.getCurrent().getActiveShell(), "警告", "请先选择寄存器");
					return;
				}
				if(!registerNames.contains(registerName)){
					MessageDialog.openWarning(Display.getCurrent().getActiveShell(), "警告", "请选择正确的寄存器");
					return;
				}
				if(registers != null){
					IEntry register = registers.get(registerCombo.getSelectionIndex());
					if(currentRegister.contains(register)){
						MessageDialog.openInformation(Display.getCurrent().getActiveShell(), "提示", "寄存器已在列表中");
						return;
					}else{
						if(fThread == null){
							ISelection selection = DebugUITools.getDebugContextManager().getContextService(getViewSite().getWorkbenchWindow()).getActiveContext();
							 if (selection instanceof IStructuredSelection) {
						         Object element = ((IStructuredSelection)selection).getFirstElement();
						         if(element instanceof ChiponDebugTarget){
						         	fThread = ((ChiponDebugTarget)element).getThread();
						         }
						         if (element instanceof ChiponThread) {
						         	fThread = (ChiponThread)element;
						         } else if (element instanceof ChiponStackFrame) {
						         	fThread = (ChiponThread) ((ChiponStackFrame) element).getThread();
						         }
							 }
						}						
						if(fThread != null){//调试功能启动                   加载寄存器的详细信息到TreeViewer中
							ChiponViewTool.getViewTool().setSingleRegisterData(fThread, register);
						}
						currentRegister.add(register);
						ChiponThread.currentRegister = currentRegister;
					}
				}
				tv.refresh();
			}*/
		});
		//=======================================================================================
		setInput(tv);
		return tv;
	}

	@Override
	protected void createActions() {
		
	}

	@Override
	protected String getHelpContextId() {
		return null;
	}

	@Override
	protected void fillContextMenu(IMenuManager menu) {
	}

	@Override
	protected void configureToolBar(IToolBarManager tbm) {
	}

	/* (non-Javadoc)
	 * @see org.eclipse.debug.ui.contexts.IDebugContextListener#debugContextChanged(org.eclipse.debug.ui.contexts.DebugContextEvent)
	 * 当启动调试时视图是开启状态调用，每次调试更新时调用
	 */
	@Override
	public void debugContextChanged(final DebugContextEvent event) {
		new UIJob(getSite().getShell().getDisplay(), Messages.ChiponRegistersView_19) {
			{
				setSystem(true);
			}

			@Override
			public IStatus runInUIThread(IProgressMonitor monitor) {
				if (getViewer() != null) { // runs asynchronously, view may be 
					//setItems();						
					update(event.getContext());
					setItems();		
				}
				return Status.OK_STATUS;
			}
		}.schedule();
	}

	/**
	 * Updates the view for the selected thread (if suspended)
	 */
	private void update(ISelection context) {
		fThread = null;

		if (context instanceof IStructuredSelection) {
			Object element = ((IStructuredSelection) context).getFirstElement();
			if (element instanceof ChiponDebugTarget) {
				 fThread = ((ChiponDebugTarget)element).getCurrentThread();
			}
			if (element instanceof ChiponThread) {
				 fThread = (ChiponThread)element;
			} else if (element instanceof ChiponStackFrame) {
				fThread = (ChiponThread) ((ChiponStackFrame) element).getThread();
			} else
				return;
		}
		if (fThread != null && fThread.isSuspended() && fThread.isCanSetInputRegister()) {
			fThread.setCanSetInputRegister(false);
		}else{
			return;
		}

		IProject project=null;
		if(fThread!=null)
			project =((ChiponDebugTarget)fThread.getDebugTarget()).getfProject();
		
		if (project != null && project.exists() && fThread !=null) {
			
			Viewer viewer = getViewer();
			//保留展开的寄存器不变
			if(viewer instanceof AbstractTreeViewer){
				final AbstractTreeViewer treeviewer = (AbstractTreeViewer)viewer;
				Object[] expanded =  treeviewer.getExpandedElements();
				ISelection selection = treeviewer.getSelection();
				treeviewer.getControl().setRedraw(false);
				if(fThread != null){
					Display.getDefault().syncExec(new Runnable() {
						@Override
						public void run() {	
							if(modules != null){
								//if(currentModule!=null){
									for(IEntry module : currentModule){
										ChiponViewTool.getViewTool().setSingleRegisterData1(fThread, module);
									}
									ChiponThread.currentModule = currentModule;
								//}
							}else{
								//if(currentRegister!=null){
									for(IEntry register : currentRegister){
										ChiponViewTool.getViewTool().setSingleRegisterData(fThread, register);
									}
									ChiponThread.currentRegister = currentRegister;
								//}
							}
							treeviewer.refresh();
						}
					});
				}
				treeviewer.setExpandedElements(expanded);
				treeviewer.setSelection(selection,true);
				treeviewer.getControl().setRedraw(true);
			}
		}
	}
	
	private void setItems(){
		List<ICategory> categorys = ChiponViewTool.getViewTool().getCategorys(fThread);  //categorys=[寄存器列表，芯片信息描述]
		List<String> tempRegs = new ArrayList<>();
		List<String> tempRegss = new ArrayList<>();
		List<IEntry> regs = new ArrayList<IEntry>(); 
		List<IEntry> regss=null;
		List<IEntry> regsModel = new ArrayList<IEntry>(); 
		
		if(categorys !=null){
			regs = categorys.get(0).getChildren();  //获取寄存器列表
			
			//寄存器名按字母表升序排序
			Collections.sort(regs,new Comparator<IEntry>(){
				@Override
				public int compare(IEntry reg1, IEntry reg2) {
					if(null==reg1.getName()){
						return 1;
					}
					if(null==reg2.getName()){
						return -1;
					}
					return Collator.getInstance(Locale.ENGLISH).compare(reg1.getName(), reg2.getName());
				}
			});
			//将相同模块名的寄存器合并到一个模块
			for(int j=0;j < regs.size()-1; j++){
				for(int k=j+1; ;){
					if(regs.get(j).getName().equals(regs.get(k).getName())){
						List<IEntry> tepReg = regs.get(k).getChildren();
						//regs.get(j).getChildren().addAll(tepReg);
						for(int t=0; t<tepReg.size(); t++){
							if(!regs.get(j).getChildren().contains(tepReg.get(t))){
								regs.get(j).addChild(tepReg.get(t));
							}
						}
						regs.remove(k);
					}else{
						break;
					}
				}
			}
			//regs.get(32).getChildren().addAll(regs.get(33).getChildren());
			//regs.get(32).addChild(regs.get(33).getChildren().get(0));
			//regss = regs.get(32).getChildren();
			
			for(int i=0;i<regs.size();i++){
				regsModel.add(regs.get(i));
				tempRegs.add(regs.get(i).getName());
				
				regss = regs.get(i).getChildren();
				for(int j=0;j<regss.size();j++){
					tempRegss.add(regss.get(j).getName());
				}
			}
			
			//boolean s = regss.get(0).hasChildren();
			if(regss != null && regss.size() > 0 && regss.get(0).hasChildren()){
				modules = regsModel;//regs;
				modulesNames = tempRegs;
				//registers = regss;
				registerNames = tempRegss;
				registerCombo.setItems(tempRegs.toArray(new String[tempRegs.size()]));
			}else{
				modules = null;
				modulesNames = null;
				registers = regsModel;//regs;
				registerNames = tempRegs;
				registerCombo.setItems(tempRegs.toArray(new String[tempRegs.size()]));
			}
		}
	}
	
	private void setInput(final TreeViewer tv){
		if(tv != null){
			if(modules != null){
				if(ChiponThread.currentModule != null){
					currentModule = ChiponThread.currentModule;	
				}
				tv.setInput(currentModule);
			}else{
				if(ChiponThread.currentRegister != null){
					currentRegister = ChiponThread.currentRegister;
				}
				tv.setInput(currentRegister);
			}
			tv.refresh();
		}
	}
	
	public List<IEntry> getCurrentRegister() {
		return currentRegister;
	}
	
	public List<IEntry> getCurrentModule() {
		return currentModule;
	}

	public void removeItem(Object[] objects) {
		tv.remove(objects);
	}

	@Override
	public void dispose() {
		DebugUITools.getDebugContextManager().getContextService(getSite().getWorkbenchWindow()).removeDebugContextListener(this);
		super.dispose();
	}

	public TreeViewer getTv() {
		return tv;
	}
	
}
