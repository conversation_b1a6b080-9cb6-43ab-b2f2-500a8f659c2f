package com.chipon32.debug.ui.views;

import java.util.List;

import org.eclipse.jface.viewers.IStructuredContentProvider;
import org.eclipse.jface.viewers.Viewer;

import com.chipon32.debug.core.model.ChiponGroupRomData;
import com.chipon32.debug.core.model.ChiponRomData;
import com.chipon32.debug.core.model.ChiponRomDatas;


/**
 * <AUTHOR> @since 2013-6-4 обнГ4:13:36
 */
public class ChiponMemoryContentProvider implements IStructuredContentProvider{
	public String showformat="hex8";
	@Override
	public void dispose() {
		
	}

	@Override
	public void inputChanged(Viewer viewer, Object oldObj, Object newObj) {
		if(newObj == null || oldObj == null){
			return ;
		}
		if(!(oldObj instanceof ChiponRomDatas) || !(newObj instanceof ChiponRomDatas)){
			 return ;
		}
		//+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
		List<ChiponGroupRomData> oldgroupRomDatas = ((ChiponRomDatas)oldObj).getGroupRomDatas();
		List<ChiponGroupRomData> newgroupRomDatas = ((ChiponRomDatas)newObj).getGroupRomDatas();
		
		for(int i=0,j=0;i<oldgroupRomDatas.size() && j<newgroupRomDatas.size();i++,j++){	
			
			List<ChiponRomData> oldromDatas;
			if(oldgroupRomDatas.get(i).isIs_virtual_lineGroup()) i++;
			oldromDatas= oldgroupRomDatas.get(i).getRomDatas();
			
			List<ChiponRomData> newromDatas; 
			if(newgroupRomDatas.get(j).isIs_virtual_lineGroup()) j++;
			newromDatas= newgroupRomDatas.get(j).getRomDatas();
			//
			for(int k=0;k<oldromDatas.size()&&k<newromDatas.size();k++){
				
				ChiponRomData oldRomData = oldromDatas.get(k);
				ChiponRomData newRomData = newromDatas.get(k);
				
				if(oldRomData.equals(newRomData)){
					newRomData.setColor(IChiponColor.DEFAULT_COLOR);
				}else{
					newRomData.setColor(IChiponColor.RED_COLOR);
				}				
			}
		}
		//+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
		//return newObj;
	}	

	@Override
	public Object[] getElements(Object inputElement) {
		if(inputElement instanceof ChiponRomDatas){
			ChiponRomDatas romDatas = (ChiponRomDatas) inputElement;
			return romDatas.getGroupRomDatas().toArray();
		}
		return null;
	}
}
