package com.chipon32.debug.ui.views;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.eclipse.core.runtime.IAdaptable;
import org.eclipse.debug.ui.DebugUITools;
import org.eclipse.jface.dialogs.MessageDialog;
import org.eclipse.jface.viewers.TableViewer;
import org.eclipse.jface.viewers.TreeViewer;
import org.eclipse.jface.viewers.Viewer;
import org.eclipse.ui.IViewPart;
import org.eclipse.ui.IViewReference;
import org.eclipse.ui.IWorkbenchPage;
import org.eclipse.ui.IWorkbenchWindow;
import org.eclipse.ui.PlatformUI;

import com.chipon32.chiponide.core.chipondescription.ICategory;
import com.chipon32.debug.core.model.ChiponLabelData;
import com.chipon32.debug.core.model.ChiponRomData;
import com.chipon32.debug.core.model.ChiponRomDatas;
import com.chipon32.debug.core.model.ChiponStackFrame;
import com.chipon32.debug.core.model.ChiponThread;
import com.chipon32.debug.core.protocol.ChiponCommandResult;
import com.chipon32.debug.core.protocol.ChiponSetAndRegisterCommand;
import com.chipon32.debug.core.util.PackRomDataList;
import com.chipon32.util.ui.DoubleClickCellModifier;

/**
 * <AUTHOR>
 * @since 2013-7-2 下午4:58:33
 * 内存视图窗口的鼠标双击控制类
 */
public class ChiponLabelCellModifier extends DoubleClickCellModifier {

	private ChiponThread    fThread;
	private ICategory oldCategory;
	
	
	public ChiponLabelCellModifier(TreeViewer treeViewer) {
		super(treeViewer);
	}
	
	public ChiponLabelCellModifier(TableViewer tableViewer) {
		super(tableViewer);
	}


	@Override
	public boolean allowModify(Object element, int columnIndex) {
		boolean result=false;
		
		if(element instanceof ChiponLabelData){
			ChiponStackFrame frame = null;
	        IAdaptable debugContext = DebugUITools.getDebugContext();
	        if (debugContext instanceof ChiponStackFrame) {
	           frame = (ChiponStackFrame) debugContext;
	        } 
	        
	        //在这里通过frame发送命令
	        if(frame != null){
	        	fThread=(ChiponThread) frame.getThread();
	        	result=true;
	        }
	        ChiponLabelData groupData = (ChiponLabelData)element;
	        List<ChiponLabelData> ChiponLabel=ChiponThread.labelDataList;
	        //点中第一列时，若有一行的地址没有值则不能新增行
	        if(columnIndex==0 && groupData.getName()!=null && groupData.getName().equals("+")){ //$NON-NLS-1$
	        	int addressSize=0;
	        	for(int i=0;i<ChiponLabel.size();i++){
	        		if(ChiponLabel.get(i).getAddress()!=null && ChiponLabel.get(i).getAddress().length()>0){
	        			addressSize++;
	        		}
	        	}
	        	if(ChiponLabel.size()-addressSize>1){
	        		return false;
	        	}
	        }
	        
	        
	        if ((columnIndex==2||columnIndex==3 || columnIndex==4) && (groupData.getAddress()==null || groupData.getAddress().length()==0)) {
	        	return false;
			}
	        if((columnIndex==2||columnIndex==3 || columnIndex==4) && groupData.getAddress().startsWith("0x1f")){ //$NON-NLS-1$
	        	return false;
	        }
	        String[] noAllowModify= ChiponViewTool.noAllowModify;
			for(int i=0;i<noAllowModify.length;i++){
				if(groupData.getAddress()!=null && groupData.getAddress().length()>0 && ("0x"+Integer.toHexString(Integer.parseInt(groupData.getAddress().substring(2),16))).equals(noAllowModify[i]) && (columnIndex==2||columnIndex==3 || columnIndex==4)){ //$NON-NLS-1$
					result=false; 
					break;
				}
			}
		}
		return result;
	}

	/* (non-Javadoc)
	 * @see com.chipon32.util.ui.DoubleClickCellModifier#doModify(java.lang.Object, int, java.lang.Object)
	 * doModify
	 */
	@Override
	public void doModify(Object element, int columnIndex, Object value) {
		if(element instanceof ChiponLabelData)
		{
			//校验数据是否合法
			boolean verify=false;
			String tmp=value.toString() ;
			ChiponLabelData groupData = (ChiponLabelData)element;
			ChiponLabelData data =new ChiponLabelData() ;
			
			if(tmp.length()==0){
				return;
			}
			if (fThread != null && fThread.isSuspended()){
				
			List<ICategory> categorys = ChiponViewTool.getViewTool().getCategorys(fThread); 
			List<ChiponRomData> romDataList=new ArrayList<ChiponRomData>();
			
			if(ChiponThread.romDataList==null || ChiponThread.romDataList.size()==0){
//				 ChiponPrintMemoryCommandResult ramResult = (ChiponPrintMemoryCommandResult) fThread.sendCommand(new ChiponPrintMemoryCommand());
//				 ChiponThread.romDataList = ramResult.romDataList;
				return;
			}
			
			int maxAddressLength=ChiponThread.romDataList.get(ChiponThread.romDataList.size()-1).getAddress().substring(2).length();
			switch (columnIndex) {
			case 0:
				if(groupData.getName().equals(tmp)){
					return;
				}
				data.setName(tmp);
				data.setAddress(groupData.getAddress());
				data.setValue(groupData.getValue());

				data.setBinaryValue(groupData.getBinaryValue());
				data.setHexValue(groupData.getHexValue());
				data.setfThread(fThread);
				break;
			case 1:
				tmp=tmp.toLowerCase() ;
				if(tmp.length()>2 && tmp.startsWith("0x")){    //判断0X或0x开头的地址是否合法 //$NON-NLS-1$
					Pattern pattern = Pattern.compile("[0-9a-fA-F]{1,"+tmp.substring(2).length()+"}"); //$NON-NLS-1$ //$NON-NLS-2$
					Matcher match = pattern.matcher(tmp.substring(2));
					verify=match.matches();
				}else{                     //判断没有0X或0x开头的地址是否合法
					Pattern pattern = Pattern.compile("[0-9a-fA-F]{1,"+tmp.length()+"}"); //$NON-NLS-1$ //$NON-NLS-2$
					Matcher match = pattern.matcher(tmp);
					verify=match.matches();
				}
				if(!verify){
					String errorInfo = Messages.ChiponLabelCellModifier_8 /*errorAddress +*/ + value.toString() + Messages.ChiponLabelCellModifier_9;
					MessageDialog.openError(null, Messages.ChiponLabelCellModifier_10, errorInfo);
					return;
				}
				if(tmp.length()>0 && tmp.startsWith("0x")){ //$NON-NLS-1$
					tmp=tmp.substring(2);
				}
				tmp="0x"+Integer.toHexString(Integer.parseInt(tmp, 16)).trim(); //$NON-NLS-1$
				if(groupData.getAddress()!=null && groupData.getAddress().length()>0 && Integer.toHexString(Integer.parseInt(groupData.getAddress().substring(2),16)).equals(tmp.substring(2))){
					return;
				}
				String returnString = ""; //$NON-NLS-1$
				for (ChiponLabelData newData : ChiponThread.labelDataList) {
					if(newData.getAddress()!=null && newData.getAddress().length()>0 && (Integer.parseInt(Integer.parseInt(newData.getAddress().substring(2),16)+"")+"").equals(tmp.substring(2))){ //$NON-NLS-1$ //$NON-NLS-2$
						verify=false;
						returnString=newData.getAddress();
					}
				}
				if(!verify){
					String errorInfo = Messages.ChiponLabelCellModifier_16 /*errorAddress +*/ + returnString + Messages.ChiponLabelCellModifier_17;
					MessageDialog.openError(null, Messages.ChiponLabelCellModifier_18, errorInfo);
					return;
				}
				boolean isAddrass=false;
				for(Iterator<ChiponRomData> iterator=ChiponThread.romDataList.iterator();iterator.hasNext();){
					ChiponRomData chiponRomData=iterator.next();
					if(chiponRomData.getAddress().equals(tmp)){
						String modeString="0000000000";  //$NON-NLS-1$
						tmp="0x"+modeString.substring(0, maxAddressLength-tmp.substring(2).length())+tmp.substring(2); //$NON-NLS-1$
						data.setAddress(tmp);
						data.setBinaryValue(Integer.toBinaryString(Integer.parseInt(chiponRomData.getValue(), 16)));
						data.setValue(Integer.parseInt(chiponRomData.getValue(), 16)+""); //$NON-NLS-1$
						data.setHexValue(chiponRomData.getValue().toUpperCase());
						isAddrass=true;
						
						if(groupData.getName()!=null && !groupData.getName().equals("+")){ //$NON-NLS-1$
							data.setName(groupData.getName());
						}else {
							data.setName(""); //$NON-NLS-1$
						}
						data.setfThread(fThread);
					}
				}
				if(!isAddrass){
					String errorInfo =Messages.ChiponLabelCellModifier_24+ value.toString()+Messages.ChiponLabelCellModifier_25;
					MessageDialog.openError(null, Messages.ChiponLabelCellModifier_26, errorInfo);
					return;
				}
				break;

			case 2:
				
				if(value!=null && tmp.length()>0){
					Pattern pattern = Pattern.compile("[0-1]{1,8}"); //$NON-NLS-1$
					Matcher match = pattern.matcher(tmp);
					verify=match.matches();
				}
				if(!verify){
					String errorInfo =Messages.ChiponLabelCellModifier_28+ value.toString()+Messages.ChiponLabelCellModifier_29;
					MessageDialog.openError(null, "ERROR", errorInfo); //$NON-NLS-1$
					return;
				}
				if((Integer.parseInt(groupData.getBinaryValue(),2))==((Integer.parseInt(tmp,2)))){
					return;
				}
				ChiponCommandResult binaryResult= fThread.sendCommand(new ChiponSetAndRegisterCommand(Integer.parseInt(((groupData.getAddress()).substring(2)), 16)+"",Integer.parseInt(tmp, 2)+"")); //$NON-NLS-1$ //$NON-NLS-2$
	        	if(binaryResult!=null){
	        		romDataList=ChiponViewTool.getViewTool().getNewThreadList(ChiponThread.romDataList,groupData,columnIndex,tmp);
	        		
	        		if(groupData.getName()!=null)
	        			data.setAddress(groupData.getName());
	        		if(groupData.getAddress()!=null)
	        			data.setAddress(groupData.getAddress());
	        		data.setBinaryValue(tmp);
	        		data.setValue(Integer.parseInt(tmp, 2)+""); //$NON-NLS-1$
	        		data.setHexValue(Integer.toHexString(Integer.parseInt(tmp,2)).toUpperCase());
	        	}
				break;
			case 3:
				if(value!=null && tmp.length()>0){
					Pattern pattern = Pattern.compile("[0-9]{1,3}"); //$NON-NLS-1$
					Matcher match = pattern.matcher(tmp);
					verify=match.matches();
				}
				if(!verify || Integer.parseInt(tmp)>=256){
					String errorInfo =Messages.ChiponLabelCellModifier_35+ value.toString()+Messages.ChiponLabelCellModifier_36;
					MessageDialog.openError(null, Messages.ChiponLabelCellModifier_37, errorInfo);
					return;
				}
				if((Integer.parseInt(groupData.getValue()))==((Integer.parseInt(tmp)))){
					return;
				}

				ChiponCommandResult result= fThread.sendCommand(new ChiponSetAndRegisterCommand(Integer.parseInt(((groupData.getAddress()).substring(2)), 16)+"",Integer.parseInt(tmp)+"")); //$NON-NLS-1$ //$NON-NLS-2$
	        	if(result!=null){
	        		romDataList=ChiponViewTool.getViewTool().getNewThreadList(ChiponThread.romDataList,groupData,columnIndex,tmp);
	        		
	        		if(groupData.getName()!=null)
	        			data.setAddress(groupData.getName());
	        		if(groupData.getAddress()!=null)
	        			data.setAddress(groupData.getAddress());
	        		data.setBinaryValue(Integer.toBinaryString(Integer.parseInt(tmp)));
	        		data.setValue(Integer.parseInt(tmp)+""); //$NON-NLS-1$
	        		data.setHexValue(Integer.toHexString(Integer.parseInt(tmp)).toUpperCase());
	        		
	        	}
				break;
			case 4:
				if(value!=null && tmp.length()>0){
					Pattern pattern = Pattern.compile("[0-9a-fA-F]{1,2}"); //$NON-NLS-1$
					Matcher match = pattern.matcher(tmp);
					verify=match.matches();
				}
				if(!verify){
					String errorInfo =Messages.ChiponLabelCellModifier_42+ value.toString()+Messages.ChiponLabelCellModifier_43;
					MessageDialog.openError(null, Messages.ChiponLabelCellModifier_44, errorInfo);
					return;
				}
				if((Integer.parseInt(groupData.getHexValue(),16))==((Integer.parseInt(tmp,16)))){
					return;
				}

				ChiponCommandResult hexResult= fThread.sendCommand(new ChiponSetAndRegisterCommand(Integer.parseInt(((groupData.getAddress()).substring(2)), 16)+"",Integer.parseInt(tmp, 16)+"")); //$NON-NLS-1$ //$NON-NLS-2$
	        	if(hexResult!=null){
	        		romDataList=ChiponViewTool.getViewTool().getNewThreadList(ChiponThread.romDataList,groupData,columnIndex,tmp);
	        		
	        		if(groupData.getName()!=null)
	        			data.setAddress(groupData.getName());
	        		if(groupData.getAddress()!=null)
	        			data.setAddress(groupData.getAddress());
	        		data.setBinaryValue(Integer.toBinaryString(Integer.parseInt(tmp.toUpperCase(), 16)));
	        		data.setValue(Integer.parseInt(tmp.toUpperCase(), 16)+""); //$NON-NLS-1$
	        		data.setHexValue(tmp.toUpperCase());
					
	        	}
				break;
			default:
				break;
			}
				List<ChiponLabelData> labelList=getNewLabelDataList(ChiponThread.labelDataList);
					if(groupData.getName()!=null && groupData.getName().equals("+") && (data.getAddress() !=null ||data.getName()!=null)){ //$NON-NLS-1$
						labelList.add(labelList.size()-1,data);
					}else {
						for(ChiponLabelData labelData : labelList){
							if(labelData.getName()!=null && !labelData.getName().equals("+") && groupData.getAddress()!=null && labelData.getAddress()!=null && groupData.getAddress().equals(labelData.getAddress())){ //$NON-NLS-1$
								if(data.getName()!=null&& data.getName().length()>0)
									labelData.setName(data.getName());
								labelData.setAddress(data.getAddress());
								labelData.setBinaryValue(data.getBinaryValue());
								labelData.setValue(data.getValue());
								labelData.setHexValue(data.getHexValue());
							}
//							if(groupData.getName()!=null && groupData.getName().length()>0 && groupData.getAddress()!=null){
//								if(labelData.getName()!=null && labelData.getName().length()>0 && !labelData.getName().equals("+")&& labelData.getAddress()!=null && labelData.getAddress().length()==0){
//									if(groupData.getName().equals(labelData.getName())){
//										labelData.setName(data.getName());
//										labelData.setAddress(data.getAddress());
//										labelData.setBinaryValue(data.getBinaryValue());
//										labelData.setValue(data.getValue());
//										labelData.setHexValue(data.getHexValue());
//										
//									}
//								}
//							}
							
						}
					}
		        	tableViewer.setInput(labelList);
					tableViewer.refresh();
					
					if(romDataList!=null && romDataList.size()>0){
					boolean isregistersViewOpen=false;
					boolean isMemoryViewOpen=false;
					IViewPart registersViewPart = null;
					IViewPart memoryViewPart = null;
					 IWorkbenchWindow[] windows = PlatformUI.getWorkbench().getWorkbenchWindows();
					 for(IWorkbenchWindow window : windows){
						 for(IWorkbenchPage page : window.getPages()){
							 for(IViewReference viewReference : page.getViewReferences()){
								 if(ChiponRegistersView.ID.equals(viewReference.getId())){
									 isregistersViewOpen = true;
									 registersViewPart = viewReference.getView(false);
								 }
								 if(ChiponMemoryView1.ID.equals(viewReference.getId())){
									 isMemoryViewOpen = true;
									 memoryViewPart = viewReference.getView(false);
								 }
							 }
						 }
					 }
					 if(isregistersViewOpen && registersViewPart!=null && registersViewPart instanceof ChiponRegistersView){
						
							ICategory newCategory = ChiponViewTool.getViewTool().getNewCategory(categorys.get(0),fThread);
			        		final Viewer treeViewer =((ChiponRegistersView)registersViewPart).getViewer();
			        		if(oldCategory==null){
			        			oldCategory=(ICategory) treeViewer.getInput();
			        		}
			        		
			        		treeViewer.setInput(ChiponViewTool.getViewTool().compare(oldCategory, newCategory));
			        		treeViewer.refresh();
			        		oldCategory = newCategory;
					 }
					 if(isMemoryViewOpen && memoryViewPart!=null && memoryViewPart instanceof ChiponMemoryView1){
							
						 final Viewer tableViewer =((ChiponMemoryView1)memoryViewPart).getViewer();
						 final ChiponRomDatas romDatas = new PackRomDataList().packList(romDataList);
		        		 tableViewer.setInput(romDatas);
		        		 tableViewer.refresh();
					 }
					 ChiponThread.romDataList=romDataList;
					}
					ChiponThread.labelDataList=labelList;
	        }
		}

	}
	

	/* (non-Javadoc)
	 * @see com.chipon32.util.ui.DoubleClickCellModifier#getColumnValue(java.lang.Object, int)
	 */
	@Override
	public Object getColumnValue(Object element, int columnIndex) {
		if(element instanceof ChiponLabelData)
		{
			ChiponLabelData groupData = (ChiponLabelData)element;
			switch (columnIndex) {
			case 0:
				if(groupData.getName()!=null && groupData.getName().equals("+")){ //$NON-NLS-1$
					return ""; //$NON-NLS-1$
				}
				return groupData.getName();
			case 1:
				return groupData.getAddress();
			case 2:
				return groupData.getBinaryValue();
			case 3:
				return groupData.getValue();
			case 4:
				return groupData.getHexValue();
			default:
				return null;
			}
			
		}
		return null;
	}
	private List<ChiponLabelData> getNewLabelDataList(List<ChiponLabelData> list) {
		List<ChiponLabelData> resultList=new ArrayList<ChiponLabelData>();
		for(int i=0;i<list.size();i++){
			ChiponLabelData chiponLabelData=list.get(i);
			ChiponLabelData data=new ChiponLabelData(chiponLabelData.getfThread(), chiponLabelData.getName(), chiponLabelData.getBinaryValue(), chiponLabelData.getValue(), chiponLabelData.getHexValue(), chiponLabelData.getAddress());
			resultList.add(data);
		}
		return resultList;
	}
}
