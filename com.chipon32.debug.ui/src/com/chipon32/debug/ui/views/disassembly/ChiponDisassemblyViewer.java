package com.chipon32.debug.ui.views.disassembly;

import org.eclipse.jface.text.source.IOverviewRuler;
import org.eclipse.jface.text.source.IVerticalRuler;
import org.eclipse.jface.text.source.SourceViewer;
import org.eclipse.jface.util.IPropertyChangeListener;
import org.eclipse.jface.util.PropertyChangeEvent;
import org.eclipse.swt.widgets.Composite;

public class ChiponDisassemblyViewer extends SourceViewer implements IPropertyChangeListener{


	public ChiponDisassemblyViewer(Composite parent, IVerticalRuler verticalRuler, IOverviewRuler overviewRuler,
			boolean showAnnotationsOverview, int styles) {
		super(parent, verticalRuler, overviewRuler, showAnnotationsOverview, styles);
	}

	@Override
	public void propertyChange(PropertyChangeEvent event) {
		System.err.println("ChiponDisassemblyViewer");
	}

}
