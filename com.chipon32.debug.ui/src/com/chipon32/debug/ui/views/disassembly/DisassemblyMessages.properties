##########################################################################
# Copyright (c) 2007, 2011 Wind River Systems and others.
#
# This program and the accompanying materials
# are made available under the terms of the Eclipse Public License 2.0
# which accompanies this distribution, and is available at
# https://www.eclipse.org/legal/epl-2.0/
#
# SPDX-License-Identifier: EPL-2.0
#
# Contributors:
#     Wind River Systems - initial API and implementation
#     <PERSON> (Texas Instruments) - Bug fix (326670)
#     <PERSON> (Texas Instruments) - Bug fix (329682)
##########################################################################

Disassembly_action_ShowSource_label=Show Source
Disassembly_action_ShowSymbols_label=Show Symbols
Disassembly_action_GotoPC_label=Go to Program Counter
Disassembly_action_GotoPC_tooltip=Go to Current Program Counter
Disassembly_action_GotoAddress_label=Go to Address...
Disassembly_action_Copy_label=&Copy
Disassembly_action_SelectAll_label=Select &All
Disassembly_action_Find_label=Find
Disassembly_action_BreakpointProperties_label=Breakpoint Properties...
Disassembly_action_ToggleBreakpoint_accelerator=Double Click
Disassembly_action_EnableBreakpoint_label=Enable Breakpoint
Disassembly_action_DisableBreakpoint_label=Disable Breakpoint
Disassembly_action_RefreshView_label=Re&fresh View
Disassembly_action_OpenPreferences_label=&Preferences...
Disassembly_action_Sync_label=Link with Active Debug Context
Disassembly_action_TrackExpression_label=Track Expression
Disassembly_action_AddBreakpoint_label=Add Breakpoint...
Disassembly_action_AddBreakpoint_errorTitle=Error
Disassembly_action_AddBreakpoint_errorMessage=Unable to create breakpoint
Disassembly_action_JumpToMemory_label=Jump To Memory
Disassembly_action_JumpToMemory_tooltip=Open memory view and add memory monitor for address
Disassembly_action_JumpToMemory_errorTitle=Error on jump to memory
Disassembly_action_JumpToMemory_errorMessage=Unable to jump to memory location

Disassembly_GotoAddressDialog_title=Go to Address
Disassembly_GotoAddressDialog_label=Address expression:

Disassembly_GotoLocation_initial_text=Enter location here
Disassembly_GotoLocation_warning=Specified location is invalid

Disassembly_Error_Dialog_title=Error
Disassembly_Error_Dialog_ok_button=OK

Disassembly_message_notConnected=No debug context

Disassembly_log_error_expression_eval=Expression does not evaluate to an address
Disassembly_log_error_locateFile=Unable to locate file:\u0020
Disassembly_log_error_readFile=Cannot read source file:\u0020

DisassemblyPart_showRulerColumn_label=Show {0}
DisassemblyPreferencePage_addressFormatTooltip=Use this format for the instruction address
DisassemblyPreferencePage_addressRadix=Address display format:
DisassemblyPreferencePage_showAddressRadix=Force radix prefix
DisassemblyPreferencePage_showSource=Show source
DisassemblyPreferencePage_showSourceTooltip=Show source code interleaved with instructions
DisassemblyPreferencePage_showSymbols=Show symbols
DisassemblyPreferencePage_showSymbolsTooltip=Show symbols used by the instructions
DisassemblyPreferencePage_error_not_a_number=Not a valid number format
DisassemblyPreferencePage_error_negative_number=Address cannot be negative
DisassemblyPreferencePage_radix_octal=Octal
DisassemblyPreferencePage_radix_decimal=Decimal
DisassemblyPreferencePage_radix_hexadecimal=Hexadecimal
DisassemblyPreferencePage_OpcodeFormatTooltip=Use this format for the instruction value
DisassemblyPreferencePage_OpcodeFormat=Opcode display format:
DisassemblyPreferencePage_showRadixTooltip=Show '0x' with hexadecimal addresses
DisassemblyPreferencePage_addressColor=Address color
DisassemblyPreferencePage_codeBytesColor=Code bytes color
DisassemblyPreferencePage_offsetsColor=Offsets color
DisassemblyPreferencePage_rulerBackgroundColor=Ruler background color
DisassemblyPreferencePage_errorColor=Error color
DisassemblyPreferencePage_instructionColor=Instruction color
DisassemblyPreferencePage_sourceColor=Source color
DisassemblyPreferencePage_labelColor=Label color

DisassemblyIPAnnotation_primary=Debug Current Instruction Pointer
DisassemblyIPAnnotation_secondary=Debug Call Stack

SourceReadingJob_name=Reading source file
SourceColorerJob_name=Coloring source file
EditionFinderJob_name=Finding best match for source file
EditionFinderJob_task_get_timestamp=Retrieving module timestamp
EditionFinderJob_task_search_history=Searching local history

DisassemblyBackendDsf_error_UnableToRetrieveData=Unable to retrieve disassembly data from backend.
