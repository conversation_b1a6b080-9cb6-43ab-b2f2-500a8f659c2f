package com.chipon32.debug.ui.views.disassembly;

import java.util.ArrayList;
import java.util.HashMap;

import org.eclipse.jface.preference.IPreferenceStore;
import org.eclipse.jface.text.rules.IRule;
import org.eclipse.jface.text.rules.RuleBasedScanner;
import org.eclipse.jface.text.rules.Token;
import org.eclipse.jface.util.IPropertyChangeListener;
import org.eclipse.jface.util.PropertyChangeEvent;
import org.eclipse.swt.graphics.Device;
import org.eclipse.ui.part.ViewPart;

import com.chipon32.chiponide.ui.UiActivator;
import com.chipon32.debug.ui.DebugUIActivator;


/**
 * RuleBasedScanner for the ASMEditor.
 * 
 * <AUTHOR>
 * @since 25.02.2012
 */
public class ASMCodeScanner extends RuleBasedScanner implements IPropertyChangeListener {

  private Token instructionToken;
  
  private Token pseudoInstructionToken;

  private Token segmentToken;
  
  private Token registerToken;
  
  private Token specialRegisterToken;
  
  private Token bitNameToken;
  
  private Token customWordToken;
  
  private Token labelToken;

  private ViewPart viewPart;

  /**
   * The constructor.
   * 
   * @param editor The underlying ASMEditor for the CodeScanner.
   */
  public ASMCodeScanner(ViewPart viewPart) {
    this.viewPart = viewPart;

    ArrayList<IRule> rules = new ArrayList<IRule>();
    //新建所有Token
    createTokens(viewPart.getSite().getShell().getDisplay());

    DebugUIActivator.getDefault().getPreferenceStore().addPropertyChangeListener(this);

    WordRuleCaseInsensitive wordRule = new WordRuleCaseInsensitive();
    HashMap<String, String> instructions = ASMInstructionSet.getInstructions();
    
    	//添加规则，把关键字传入
    if (instructions != null) {
      for (String instruction : instructions.keySet()) {
        wordRule.addWord(instruction, instructionToken);
      }
    }
    rules.add(wordRule);
    
    wordRule = new WordRuleCaseInsensitive();
    HashMap<String, String> pseudoInstructions = ASMInstructionSet.getPseudoInstructions();
    if (pseudoInstructions != null) {
      for (String pseudoInstruction : pseudoInstructions.keySet()) {
        wordRule.addWord(pseudoInstruction, pseudoInstructionToken);
      }
    }
    rules.add(wordRule);

    wordRule = new WordRuleCaseInsensitive();
    HashMap<String, String> segments = ASMInstructionSet.getSegments();
    if (segments != null) {
      for (String segment : segments.keySet()) {
        wordRule.addWord(segment, segmentToken);
      }
    }
    rules.add(wordRule);
    
    wordRule = new WordRuleCaseInsensitive();
    HashMap<String, String> registers = ASMInstructionSet.getRegisters();
    if (registers != null) {
      for (String register : registers.keySet()) {
        wordRule.addWord(register, registerToken);
      }
    }
    rules.add(wordRule);
    
    wordRule = new WordRuleCaseInsensitive();
    HashMap<String, String> specialRegisters = ASMInstructionSet.getSpecialRegisterMap();
    if (specialRegisters != null) {
      for (String specialRegister : specialRegisters.keySet()) {
        wordRule.addWord(specialRegister, specialRegisterToken);
      }
    }
    rules.add(wordRule);

    
    wordRule = new WordRuleCaseInsensitive();
    HashMap<String, String> bitNmes = ASMInstructionSet.getBitNameMap();
    if (bitNmes != null) {
      for (String name : bitNmes.keySet()) {
        wordRule.addWord(name, bitNameToken);
      }
    }
    rules.add(wordRule);
    
    
    wordRule = new WordRuleCaseInsensitive();
    HashMap<String, String> customWord = ASMInstructionSet.getCustomMap();
    if (customWord != null) {
      for (String name : customWord.keySet()) {
        wordRule.addWord(name, customWordToken);
      }
    }
    rules.add(wordRule);
    
    
    wordRule = new WordRuleCaseInsensitive();
    String[] lablesString = null;
    if(viewPart != null){
    	ContentProvider cp = new ContentProvider(viewPart);
    	cp.parse();
    	TreeObject labels = cp.getLabels();
		Object[] obj =labels.getChildren();
		lablesString = new String[obj.length];
		for(int j=0;j<obj.length;j++){
			String string = obj[j].toString().trim();
			lablesString[j] = string;
		}
    }	
    if (lablesString.length > 0 ) {
      for (String name : lablesString) {
        wordRule.addWord(name, labelToken);
      }
    }
    rules.add(wordRule);
    
    setRules(rules.toArray(new IRule[] {}));
  }

  /**
   * Disposes the PropertyChangeListener from the PreferenceStore.
   */
  public void dispose() {
    DebugUIActivator.getDefault().getPreferenceStore().removePropertyChangeListener(this);
  }

  /**
   * Create all Tokens.
   * 
   * @param device The device is needed for the color of the Tokens.
   */
  private void createTokens(Device device) {
	
	
    IPreferenceStore store = UiActivator.getDefault().getPreferenceStore();

    instructionToken = new Token(TextAttributeConverter
                         .preferenceDataToTextAttribute(store.getString(Constants.PREFERENCES_TEXTCOLOR_INSTRUCTION)));
    
    pseudoInstructionToken = new Token(TextAttributeConverter.preferenceDataToTextAttribute(store.getString(Constants.PREFERENCES_TEXTCOLOR_PSEUDO_INSTRUCTION)));

    segmentToken = new Token(TextAttributeConverter
                     .preferenceDataToTextAttribute(store.getString(Constants.PREFERENCES_TEXTCOLOR_SEGMENT)));
    
    registerToken = new Token(TextAttributeConverter.preferenceDataToTextAttribute(store.getString(Constants.PREFERENCES_TEXTCOLOR_REGISTER)));
    
    specialRegisterToken = new Token(TextAttributeConverter
    						.preferenceDataToTextAttribute(store.getString(Constants.PREFERENCES_TEXTCOLOR_SPECIFALREGISTER)));
    
    bitNameToken = new Token(TextAttributeConverter
    				.preferenceDataToTextAttribute(store.getString(Constants.PREFERENCES_TEXTCOLOR_BITNAME)));
    
    customWordToken = new Token(TextAttributeConverter
			.preferenceDataToTextAttribute(store.getString(Constants.PREFERENCES_TEXTCOLOR_CUSTOMWORD)));
    
    labelToken = new Token(TextAttributeConverter
			.preferenceDataToTextAttribute(store.getString(Constants.PREFERENCES_TEXTCOLOR_LABEL)));
  }

 
  @Override
public void propertyChange(PropertyChangeEvent event) {
    if (event.getProperty().equals(Constants.PREFERENCES_TEXTCOLOR_INSTRUCTION)) {
      instructionToken.setData(TextAttributeConverter.preferenceDataToTextAttribute((String) event.getNewValue()));
    } else if (event.getProperty().equals(Constants.PREFERENCES_TEXTCOLOR_SEGMENT)) {
      segmentToken.setData(TextAttributeConverter.preferenceDataToTextAttribute((String) event.getNewValue()));
    } else if (event.getProperty().equals(Constants.PREFERENCES_TEXTCOLOR_PSEUDO_INSTRUCTION))
    {
    	pseudoInstructionToken.setData(TextAttributeConverter.preferenceDataToTextAttribute((String) event.getNewValue()));
    } else if (event.getProperty().equals(Constants.PREFERENCES_TEXTCOLOR_REGISTER))
    {
    	registerToken.setData(TextAttributeConverter.preferenceDataToTextAttribute((String) event.getNewValue()));
    }else if(event.getProperty().equals(Constants.PREFERENCES_TEXTCOLOR_SPECIFALREGISTER))
    {
    	specialRegisterToken.setData(TextAttributeConverter.preferenceDataToTextAttribute((String) event.getNewValue()));
    }else if(event.getProperty().equals(Constants.PREFERENCES_TEXTCOLOR_BITNAME))
    {
    	bitNameToken.setData(TextAttributeConverter.preferenceDataToTextAttribute((String) event.getNewValue()));
    }else if(event.getProperty().equals(Constants.PREFERENCES_TEXTCOLOR_CUSTOMWORD))
    {
    	customWordToken.setData(TextAttributeConverter.preferenceDataToTextAttribute((String) event.getNewValue()));
    }else if(event.getProperty().equals(Constants.PREFERENCES_TEXTCOLOR_LABEL)){
    	labelToken.setData(TextAttributeConverter.preferenceDataToTextAttribute((String) event.getNewValue()));
    }
    if(viewPart instanceof ChiponDisassemblyView) {
    	((ChiponDisassemblyView) viewPart).refreshSourceViewer();
    }
  }
}
