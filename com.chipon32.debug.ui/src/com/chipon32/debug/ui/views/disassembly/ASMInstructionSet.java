package com.chipon32.debug.ui.views.disassembly;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileWriter;
import java.io.IOException;
import java.net.URL;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Vector;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.xml.parsers.SAXParser;
import javax.xml.parsers.SAXParserFactory;

import org.eclipse.core.runtime.FileLocator;
import org.eclipse.core.runtime.IStatus;
import org.eclipse.core.runtime.Path;
import org.eclipse.core.runtime.Status;
import org.xml.sax.Attributes;
import org.xml.sax.SAXException;
import org.xml.sax.helpers.DefaultHandler;

import com.chipon32.chiponide.core.chipondescription.ICategory;
import com.chipon32.chiponide.core.chipondescription.IDeviceDescription;
import com.chipon32.chiponide.core.chipondescription.IEntry;
import com.chipon32.chiponide.core.chipondescription.chipio.Bits;
import com.chipon32.chiponide.ui.ChipOniohDeviceDescriptionProvider;
import com.chipon32.chiponide.core.chipondescription.chipio.Register;
import com.chipon32.chiponide.ui.Messages;
import com.chipon32.chiponide.ui.UiActivator;

/**
 * Loads an manages the instructions, which are uses in the editor.
 * 一个集合
 * 一个输出的文件的内容
 * 基于型号变动的文件内容更新
 * <AUTHOR>
 * @since 
 */
public final class ASMInstructionSet {

	/*
	 * 存放汇编指令
	 */
	private static HashMap<String, String> instructionMap = null;
	/*
	 * 存放伪指令
	 */
	private static HashMap<String, String> pseudoInstructionMap = null;
	/*
	 * 存放位
	 */
	private static HashMap<String, String> segmentMap = null;
	/*
	 * 存放寄存器
	 */
	private static HashMap<String, String> registerMap = null;
	/*
	 * 存放特殊寄存器
	 */
	private static HashMap<String, String> specialRegisterMap = null;
	/*
	 * 存放# 立即数
	 */
	private static HashMap<String, String> bitNameMap = null;

	private static String preChipName = ""; // 缓存芯片信息，避免重复多次读取数据

	/*
	 * 自定义关键字
	 */
	private static HashMap<String, String> customMap = null;

	//====================== 内容数组
	private static String[][] sortedInstructionArray = null;

	private static String[][] sortedPseudoInstructionArray = null;

	private static String[][] sortedSegmentArray = null;

	private static String[][] sortedRegisterArray = null;

	private static String[][] sortedSpecialRegisterArray = null;

	private static String[][] sortedBitNameArray = null;

	private static String[][] customArray = null;

	/**
	 * Must not be instantiated.
	 */
	private ASMInstructionSet() {
		// Must not be instantiated.
	}

	/**
	 * Returns all instructions.  默认从文件中解析
	 * 
	 * @return The instructions.
	 */
	public static HashMap<String, String> getInstructions() {
		if (instructionMap == null) {
			loadXMLData();
		}

		return instructionMap;
	}

	public static HashMap<String, String> getPseudoInstructions() {
		if (pseudoInstructionMap == null) {
			loadXMLData();
		}
		return pseudoInstructionMap;
	}

	/**
	 * Returns all segments.
	 * 
	 * @return The segments.
	 */
	public static HashMap<String, String> getSegments() {
		if (segmentMap == null) {
			loadXMLData();
		}

		return segmentMap;
	}

	/**
	 * Returns all registers.
	 * 
	 * @return The registers.
	 */
	public static HashMap<String, String> getRegisters() {
		if (registerMap == null) {
			loadXMLData();
		}
		return registerMap;
	}

	public static HashMap<String, String> getSpecialRegisterMap() {
		if (specialRegisterMap == null) {
			loadXMLData();
		}
		return specialRegisterMap;
	}

	public static HashMap<String, String> getBitNameMap() {
		if (bitNameMap == null) {
			loadXMLData();
		}
		return bitNameMap;
	}

	public static HashMap<String, String> getCustomMap() {
		if (customMap == null) {
			loadXMLData();
		}
		return customMap;
	}

	/**
	 * Returns the Array with the instructions.  关键字表
	 * 
	 * @return The Instructions.
	 */
	public static String[][] getInstructionArray() {
		if (sortedInstructionArray == null) {
			loadXMLData();
		}

		return sortedInstructionArray;
	}

	/**
	 * Returns the Array with the Pseudo instructions.
	 * 
	 * @return The Pseudo Instructions.
	 */
	public static String[][] getPseudoInstructionArray() {
		if (sortedPseudoInstructionArray == null) {
			loadXMLData();
		}

		return sortedPseudoInstructionArray;
	}

	/**
	 * Returns the Array with the segments.
	 * 
	 * @return The segments.
	 */
	public static String[][] getSegmentArray() {
		if (sortedSegmentArray == null) {
			loadXMLData();
		}

		return sortedSegmentArray;
	}

	/**
	 * Returns the Array with the segments.
	 * 
	 * @return The segments.
	 */
	public static String[][] getRegisterArray() {
		if (sortedRegisterArray == null) {
			loadXMLData();
		}

		return sortedRegisterArray;
	}

	public static String[][] getSortedSpecialRegisterArray() {
		if (sortedSpecialRegisterArray == null) {
			loadXMLData();
		}
		return sortedSpecialRegisterArray;
	}

	public static String[][] getSortedBitNameArray() {
		if (sortedBitNameArray == null) {
			loadXMLData();
		}
		return sortedBitNameArray;
	}

	public static String[][] getCustomArray() {
		if (customArray == null) {
			loadXMLData();
		}
		return customArray;
	}

	/**
	 * Loads the instructions.
	 * 方法：解析配置文件 中的关键字
	 * @throws IOException
	 */
	private static void loadXMLData() {
		// 先清零
		if (instructionMap == null) {
			instructionMap = new HashMap<String, String>();
		} else {
			instructionMap.clear();
		}

		if (pseudoInstructionMap == null) {
			pseudoInstructionMap = new HashMap<String, String>();
		} else {
			pseudoInstructionMap.clear();
		}

		if (segmentMap == null) {
			segmentMap = new HashMap<String, String>();
		} else {
			segmentMap.clear();
		}

		if (registerMap == null) {
			registerMap = new HashMap<String, String>();
		} else {
			registerMap.clear();
		}

		if (specialRegisterMap == null) {
			specialRegisterMap = new HashMap<String, String>();
		} else {
			specialRegisterMap.clear();
		}

		if (bitNameMap == null) {
			bitNameMap = new HashMap<String, String>();
		} else {
			bitNameMap.clear();
		}

		if (customMap == null) {
			customMap = new HashMap<String, String>();
		} else {
			customMap.clear();
		}
		// 加载历史的输出到文件中的关键字
		String xmlfile = UiActivator.getFilePathFromPlugin("asm_instruction_set.xml");

		try {
			SAXParser parser = SAXParserFactory.newInstance().newSAXParser();
			parser.parse(new File(xmlfile), new DefaultHandler() {
				@Override
				public void startElement(String uri, String localName, String qName, Attributes attributes) throws SAXException {
					if (qName.equals("instruction")) {
						instructionMap.put(attributes.getValue("command"), attributes.getValue("description"));
					} else if (qName.equals("segment")) {
						segmentMap.put(attributes.getValue("field"), attributes.getValue("description"));
					} else if (qName.equals("pseudo_instruction")) {
						pseudoInstructionMap.put(attributes.getValue("command"), attributes.getValue("description"));
					} else if (qName.equals("register")) {
						registerMap.put(attributes.getValue("command"), attributes.getValue("description"));
					} else if (qName.equals("especialRegister")) {
						specialRegisterMap.put(attributes.getValue("command"), attributes.getValue("description"));
					} else if (qName.equals("declare")) {
						bitNameMap.put(attributes.getValue("command"), attributes.getValue("description"));
					} else if (qName.equals("custom")) {
						customMap.put(attributes.getValue("word"), attributes.getValue("description"));
					}
				}
			});
		} catch (Exception e) {
			UiActivator.getDefault().getLog().log(new Status(IStatus.ERROR, Constants.PLUGIN_ID, IStatus.OK, Messages.LOAD_ASMISET_ERROR, e));
		}
		// 基于文件解析的关键字到字符串数组中，先定义大小
		sortedInstructionArray = new String[instructionMap.size()][3];
		sortedPseudoInstructionArray = new String[pseudoInstructionMap.size()][3];
		sortedSegmentArray = new String[segmentMap.size()][3];
		sortedRegisterArray = new String[registerMap.size()][3];
		sortedSpecialRegisterArray = new String[specialRegisterMap.size()][3];
		sortedBitNameArray = new String[bitNameMap.size()][3];
		customArray = new String[bitNameMap.size()][3];
		// 开启排序和 赋值传入 1
		Vector<String> sortVector = new Vector<String>(instructionMap.keySet());
		Collections.sort(sortVector);
		int pos = 0;
		
		for (String element : sortVector) {
			sortedInstructionArray[pos][0] = new String(element);
			sortedInstructionArray[pos][1] = new String(element.toLowerCase());
			sortedInstructionArray[pos][2] = new String(instructionMap.get(element));
			pos++;
		}
		// 开启排序和 赋值传入2
		sortVector = new Vector<String>(segmentMap.keySet());
		Collections.sort(sortVector);
		pos = 0;

		for (String element : sortVector) {
			sortedSegmentArray[pos][0] = new String(element);
			sortedSegmentArray[pos][1] = new String(element.toLowerCase());
			sortedSegmentArray[pos][2] = new String(segmentMap.get(element));
			pos++;
		}
		// 开启排序和 赋值传入 3
		sortVector = new Vector<String>(pseudoInstructionMap.keySet());
		Collections.sort(sortVector);
		pos = 0;

		for (String element : sortVector) {
			sortedPseudoInstructionArray[pos][0] = new String(element);
			sortedPseudoInstructionArray[pos][1] = new String(element.toLowerCase());
			sortedPseudoInstructionArray[pos][2] = new String(pseudoInstructionMap.get(element));
			pos++;
		}
		// 开启排序和 赋值传入4
		sortVector = new Vector<String>(registerMap.keySet());
		Collections.sort(sortVector);
		pos = 0;

		for (String element : sortVector) {
			sortedRegisterArray[pos][0] = new String(element);
			sortedRegisterArray[pos][1] = new String(element.toLowerCase());
			sortedRegisterArray[pos][2] = new String(registerMap.get(element));
			pos++;
		}
		// 开启排序和 赋值传入 5
		sortVector = new Vector<String>(specialRegisterMap.keySet());
		Collections.sort(sortVector);
		pos = 0;

		for (String element : sortVector) {
			sortedSpecialRegisterArray[pos][0] = new String(element);
			sortedSpecialRegisterArray[pos][1] = new String(element.toLowerCase());
			sortedSpecialRegisterArray[pos][2] = new String(specialRegisterMap.get(element));
			pos++;
		}
		// 开启排序和 赋值传入 6
		sortVector = new Vector<String>(bitNameMap.keySet());
		Collections.sort(sortVector);
		pos = 0;

		for (String element : sortVector) {
			sortedBitNameArray[pos][0] = new String(element);
			sortedBitNameArray[pos][1] = new String(element.toLowerCase());
			sortedBitNameArray[pos][2] = new String(bitNameMap.get(element));
			pos++;
		}
		// 开启排序和 赋值传入 7
		 sortVector = new Vector<String>(customMap.keySet());
		 Collections.sort(sortVector);
		 pos = 0;
		
		 for(String element : sortVector){
		 customArray[pos][0] = new String(element);
		 customArray[pos][1] = new String(element.toLowerCase());
		 customArray[pos][2] = new String(customMap.get(element));
		 pos++;
		 }

	}

//	private static String[] c_asmRegisterKeywords1 = null;
//	private static String[] c_asmBitKeywords1 = null;
//
//	private static Set<String> bitkeywords1;

	/**
	 * 根据芯片型号实时读取寄存器的值，更新文件内容
	 * 
	 * @param chipName
	 * @throws SAXException
	 */
	public static void addXmlData(String chipName) throws SAXException {
		try {
			URL url1 = ASMInstructionSet.class.getResource("/");
			Path path1 = new Path(FileLocator.toFileURL(url1).getPath());
			String result1 = path1.makeAbsolute().toOSString();
			String result2 = new File(result1).getParent();
			File xmlFile = new File(result2 + "/asm_instruction_set.xml");
			// &&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&
			if (xmlFile.exists()) {
				// 历史文件删除，靠代码生成的
				xmlFile.delete();
			}
			// -----------------------建立新的写文件，前面被删除
			BufferedWriter bw = new BufferedWriter(new FileWriter(xmlFile));
			// 靠宏从设计指令中提前了，这里暂不根据编译器和型号进行区分  ,空格靠java过滤
//			Sub 获取指令列表()
//			'
//			' 获取指令列表 Macro
//			'
//			' 快捷键: Ctrl+Shift+M
//			'
//			strs = ""
//			For i = 1 To 500
//			    marco = Cell(i, 8)
//			    If Len(marco) > 0 Then
//			    strs = strs + ",'" + marco + "'"
//			    
//			    End If
//			    
//			Next i
//			    Cell(1, 12) = strs
//			    
//			End Sub
			
			String[] chipList = {
					"NOP","RESET","WAIT","BREAK","SYNC","SLEEP","ENI","DSI","CMN","SVC",
					"JMP","SJMP","LD","ST","MOV","ADD","SUB","CMP","LD","SET","CLR","SET",
					"CLR","JB","JNB","JB","JNB","MOV","LJMP","JMP","POP ","PUSH ","POP ",
					"PUSH ","SET ","CLR ","REV ","SXT.H ","SXT.B ","XCH ","ADD ","ADDC ",
					"SUB ","SUBC ","NOT ","ANL ","ORL ","XRL ","LSL ","LSR ","ASR ","ROR ",
					"CMP ","TST ","LD.w ","ST.w ","LD.h ","ST.h ","LD.b ","ST.b ","ASR ",
					"LSL ","LSR ","ROR ","LD.b ","LD.h ","LD.w ","ST.b ","ST.h ","ST.w ","LDS.h ","LDS.b ","LD.b ",
					"ST.b ","LD.w ","ST.w ","LD.h ","ST.h ","LDP.b ","STP.b ",
					"LDP.w ","STP.w ","LDP.h ","STP.h ",
					"MOV ","MOV ","ADD ","ADD ","ADDC ","SUB ","SUB ","SUBC ","MULS ","DIVS ","DIVU ",
					"ANL ","ORL ","XRL ","LSL ","LSR ","ASR ","LD.H ","LD.B ","ST.H ","ST.B ",
					"LD.b ","LD.h ","LD.w ","ST.b ","ST.h ","ST.w ","JZ ","JNZ ","JC ","JNC ","JMI ",
					"JPL ","JVS ","JVC ","JHI ","JLS ","JGE ","JLT ","JGT ","JLE ","MOVL ","MOVH ",
					"DMAC16LL ","DMACF16LL ","DMAC16LH ","DMACF16LH ","DMSC16LL ","DMSCF16LL ","DMSC16LH ",
					"DMSCF16LH ","MULS ","MULSF ","MADD32 ","MADDF32 ","MAC ","MACF ","MSC ","MSCF ",
					"SXTH ","SXTB ","SET ","CLR ","ADD ","SUB ","CLR ","NEG ","ABS ","ABS ","ADD ","LAC ","SAC ",
					"SACR ","DMAC16LL ","DMACF16LL ","DMAC16LH ","DMACF16LH ","DMSC16LL ","DMSCF16LL ",
					"DMSC16LH ","DMSCF16LH ","LDAC ","LAC ","SAC ","SACR ","LAC ","SAC ","SACR ","LAC ",
					"SAC ","SACR ","LAC ","SAC ","SACR ","LAC ","SAC ","SACR ","LD.W ","LD.H ","LD.B ",
					"LDS.H ","LDS.B ","ST.W ","ST.H ","ST.B ","LD.W ","LD.H ","LDS.H ","LD.B ","LDS.B ",
					"ST.W ","ST.H ","ST.B ","ADD ","ADD ","ADDC ","SUB ","SUB ","SUBC ","DIVS ","DIVU ",
					"ANL ","ORL ","XRL ","LSL ","LSR ","ASR ","LD.b ","LD.h ","LD.w ","ST.b ","ST.h ","ST.w ",
					"CMN ","CMP ","TST ","LDX.b ","LDX.h ","LDX.w ","STX.b ","STX.h ","STX.w ","ZXT.b ",
					
					"ZXT.H","QUNPACK","ZXTH","ZXTB","QSAA",	"QADD",	"QSUB",	"DO",	"DEA",	"QAVRHL",
					"QAVRHH","QAVR","ALIGN8","ALIGN16",	"ALIGN24",	"QPACK","DACL",	"DACH",
					
					"FST.W","FLD.W","FJB","FJNB","FMOV.F32","FMOV","FABS.F32","FNEG.F32","FSQRT.F32",
					"FRECIP.F32","FRSQRT.F32","FCMP.F32","FCMPE.F32","FCVTB.F32.F16","FCVTT.F32.F16",
					"FCVTB.F16.F32","FCVTT.F16.F32","FCVTR.S32.F32","FCVTR.U32.F32","FCVTR.F32.S32",
					"FCVTR.F32.U32","FCVT.S32.F32","FCVT.U32.F32","FCVT.S16.F32","FCVT.U16.F32","FCVT.F32.S32",
					"FCVT.F32.U32","FCVT.F32.S16","FCVT.F32.U16","FCVTB.F16.F64","FCVTT.F16.F64","FCVTR.S32.F64",
					"FCVTR.U32.F64","FCVTR.F32.F64","FCVTR.F64.F32","FCVTR.F64.S32","FCVTR.F64.U32","FSINPU.F32" ,
					"FCOSPU.F32","FATANPU.F32","FIEXP2.F32","FLOG2.F32","FMULPI.F32" ,"FDIVPI.F32",
					"FQUARD.F32","FMLA.F32","FMLS.F32","FMUL.F32","FNMLA.F32","FNMLS.F32","FNMUL.F32",
					"FADD.F32","FSUB.F32","FMAXNM.F32","FMINNM.F32","FSELEQ.F32" ,"FSELVS.F32","FSELGE.F32",
					"FSELGT.F32","FRINTR.F32","FRACF.F32","FPUSH.W","FPOP.W","FDIV.F32"					
					
			};
			bw.append("<?xml version=" + "\"1.0\"" + " encoding=" + "\"gb2312\"" + "?>" + "\r\n");
			bw.append("<set>" + "\r\n");
			// ####### 芯片指令集 #############
			bw.append("<!-- assemble instruction -->" + "\r\n");
			for (int i = 0; i < chipList.length; i++) {
				bw.append("<instruction command=\"" + chipList[i].trim() + "\" description=\"" + chipList[i] + "\" />" + "\r\n");
			}
			bw.append("\r\n");
			// ###### 工具链伪指令 #############
			bw.append("<!-- pseudoinstruction -->" + "\r\n");
			String[] chipWList = { ".INCLUDE", ".ORG", ".END", ".EQU", ".IFDEF", ".IFNDEF", ".IF", ".ELSE", ".ENDIF", ".MACRO", 
					".ENDM", ".EXITM", ".GLOBAL", ".TEXT", ".DATA", ".EXTERN", ".FILL", ".LONG", 
					".endfunc",".func",
					".SHORT", ".BYTE", ".ALIGN", ".WEAK", ".ABORT", ".COMM", ".FILE", ".SECTION", 
					".EXPORT", ".TYPE", ".SIZE", ".ASCII", ".INT", ".WORD", ".LIST", ".NOLIST", ".LOCAL", 
					".SET", ".import", ".literals", ".page", ".asciz", ".dc", ".dc.b", ".dc.d", ".dc.l", 
					".dc.s", ".dc.w", ".dc.x", ".double", ".float", ".hword", ".octa", ".quad", ".single", 
					".string", ".bss", ".section.s", ".sect", ".sect.s" };

			for (int i = 0; i < chipWList.length; i++) {
				bw.append("<pseudo_instruction command=\"" + chipWList[i] + "\" description=\"\" />" + "\r\n");
			}
			// ##### 芯片寄存器 ################
			bw.append("<register command=\"PSW" + "\" description=\"Register\" />" + "\r\n");
			bw.append("<!-- Register -->" + "\r\n");
			for (int i = 0; i < 32; i++) {
				bw.append("<register command=\"R" + i + "\" description=\"Register\" />" + "\r\n");
			}
			bw.append("<register command=\"LR\" description=\"Register\" />" + "\r\n");
			bw.append("<register command=\"SP\" description=\"Register\" />" + "\r\n");
			bw.append("<register command=\"PC\" description=\"Register\" />" + "\r\n");
			for (int i = 0; i < 8; i++) {
				bw.append("<register command=\"ACC" + i + "\" description=\"Combine Register ACC\" />" + "\r\n");
			}
			for (int i = 0; i < 32; i++) {
				bw.append("<register command=\"S" + i + "\" description=\"Register\" />" + "\r\n");
			}
			for (int i = 0; i < 16; i++) {
				bw.append("<register command=\"SACC" + i + "\" description=\"Combine Register SACC\" />" + "\r\n");
			}
			// ####  立即数区分的的井号 ##########
	  		bw.append("<!-- immediate -->"+"\r\n");	
	  		bw.append("<declare command=\"#\" description=\"\" />"+"\r\n");
	  		// ####  自定义  #####################
			bw.append("<!-- User KeyWord -->" + "\r\n");
			bw.append("<custom word=\"ChipON\" description=\"Provide\" />" + "\r\n"); 
			
//			bw.append("<custom word=\"uint64_t\" description=\"8byte unsigned\" />" + "\r\n");
//			bw.append("<custom word=\"uint32_t\" description=\"4byte unsigned\" />" + "\r\n");
//			bw.append("<custom word=\"uint16_t\" description=\"2byte unsigned\" />" + "\r\n");
//			bw.append("<custom word=\"uint8_t\" description=\"1byte unsigned\" />" + "\r\n");
//			
//			bw.append("<custom word=\"int64_t\" description=\"8byte signed\" />" + "\r\n");
//			bw.append("<custom word=\"int32_t\" description=\"4byte signed\" />" + "\r\n");
//			bw.append("<custom word=\"int16_t\" description=\"2byte signed\" />" + "\r\n");
//			bw.append("<custom word=\"int8_t\" description=\"1byte signed\" />" + "\r\n");
			
//			bw.append("<custom word=\"interrupt\" description=\"中断属性值\" />" + "\r\n");
			// ###  芯片的寄存器，从头文件开始追加 ###########
			bw.append("<!-- SFR -->" + "\r\n");
			List<IEntry> registers = new ArrayList<>();

			if (chipName != null && chipName.length() != 0) {
				IDeviceDescription des = ChipOniohDeviceDescriptionProvider.getDefault().getDeviceDescription(chipName);
				List<ICategory> categorys = des.getCategories();
				registers = categorys.get(0).getChildren();
				
//				IPath aaa = SystemPathsWin32.getSystemPath(ChipOnPath.CHIPON_INCLUDE);
//				String result = aaa.toOSString();
//				// 解析头文件中的关键字
//				String[] RegisterList = getC_asmRegisterKeywords1(result, chipName);
//				Set<String> rList = RegisterBitMap.getRegisterKey();
			}
			
			for (IEntry entry : registers) {
				if (entry instanceof Register) {
					Register register = (Register) entry;
					bw.append("<especialRegister command=\"" + register.getName() + "\" description=\"" + register.getDescription() + "\" />" + "\r\n");

				}
			}
			// ###   ----------------------------------------------------------
			bw.append("<!-- SFR bit -->" + "\r\n");
			for (int i = 0; i < 32; i++) {
				bw.append("<declare command=\"_b" + i + "\" description=\"\" />" + "\r\n");
			}
			// ###  
			for (IEntry entry : registers) {
				if (entry instanceof Register) {
					Register register = (Register) entry;
					List<IEntry> children = register.getChildren();
					for (IEntry child : children) {
						if (child instanceof Bits) {
							//bit位的长度
							int len = Integer.parseInt(child.getBitLength());
							Bits bit = (Bits) child;
							//汇编中的bit位全部分开成只有一位如LFCKDIV占3个bit，分解成_LFCKDIV0、_LFCKDIV1、_LFCKDIV2。
							if(len > 1){
								for(int i = 0; i < len; i++){
									bw.append("<segment field=\"_" + bit.getName()+i+ "\" description=\"" + bit.getDescription()+ i + "\" />" + "\r\n");
								}
							}else{
								bw.append("<segment field=\"_" + bit.getName()+ "\" description=\"" + bit.getDescription() + "\" />" + "\r\n");
							}

						}
					}
				}
			}
			bw.append("</set>" + "\r\n");
			// bw 结果 输出到 文件 xml中
			bw.flush();
			bw.close();

		} catch (FileNotFoundException e) {

			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		}
		// 记录 文件名字，名字不变化的不刷新关键字
		if (preChipName.length() == 0 || (chipName != null && chipName.length() != 0 && !preChipName.equals(chipName))) {
			instructionMap = null;
			preChipName = chipName;
		}
		// =======================end fun====================================
	}

	// 路径下的汇编头文件的打开 分析，过时的未使用
//	public static String[] getC_asmRegisterKeywords1(String path, String chipName) {
//		bitkeywords1 = new HashSet<String>();
//		Set<String> keywords = new HashSet<String>();
//		String result = path;
////		 List<File> ll =getFiles(new File(result),"h");
//		File file = getFile(new File(result), chipName + ".inc");
//		if (file != null) {
//			FileReader inbuffer;
//			try {
//				inbuffer = new FileReader(file);
//				BufferedReader bfreader = new BufferedReader(inbuffer);
//				String s = null;
//				while ((s = bfreader.readLine()) != null) {
//					// 寄存器名前没有"_"
//					if (s.contains(".EQU") && !s.contains("_")) {
//						String[] tempStr = s.trim().split("\\s+");
//						keywords.add(tempStr[1].replace(",", ""));
//						// bit位前有"_"
//					} else if (s.contains(".EQU") && s.contains("_")) {
//						String[] tempStr = s.trim().split("\\s+");
//						bitkeywords1.add(tempStr[1].replace(",", ""));
//					}
//				}
//			} catch (FileNotFoundException e) {
//				// TODO Auto-generated catch block
//				e.printStackTrace();
//			} catch (IOException e) {
//				// TODO Auto-generated catch block
//				e.printStackTrace();
//			}
//
//			/*
//			 * for (String file : keywords) { System.out.println(file); }
//			 */
//		}
//		c_asmRegisterKeywords1 = keywords.toArray(new String[keywords.size()]);
//		return c_asmRegisterKeywords1;
//	}

	//// 路径下的C头文件的打开 分析，过时的未使用
//	public static String[] getC_asmbitKeywords1(String path) {
//		Set<String> keywords = new HashSet<String>();
//		String result = path;
//		List<File> ll = getFiles(new File(result), "h");
//		for (File ff : ll) {
//			int m = 0;
//			FileReader inbuffer;
//			try {
//				inbuffer = new FileReader(ff);
//				BufferedReader bfreader = new BufferedReader(inbuffer);
//				String s = null;
//				while ((s = bfreader.readLine()) != null) {
//					if (s.contains("/*Address:")) {
//						m = 1;
//					}
//					if (s.contains("#define") && m > 0 && s.contains("_bits")) {
//						String sa = replace(s);
//						String ss = sa.replaceAll("\\s*", "");
//
//						keywords.add(ss);
//					}
//
//				}
//				if (bitkeywords1.size() != 0) {
//					for (String file : bitkeywords1) {
//						if (!(keywords.contains(file))) {
//							keywords.add(file);
//						}
//					}
//				}
//			} catch (FileNotFoundException e) {
//				// TODO Auto-generated catch block
//				e.printStackTrace();
//			} catch (IOException e) {
//				// TODO Auto-generated catch block
//				e.printStackTrace();
//			}
//		}
//		c_asmBitKeywords1 = keywords.toArray(new String[keywords.size()]);
//		return c_asmBitKeywords1;
//	}

	// 根据文件夹路径和文件获取满足条件的文件
	public static File getFile(File fileDir, String fileName) {
//		List<File> lfile = new ArrayList<File>();
		File[] fs = fileDir.listFiles();
		for (File f : fs) {
			if (fileName.equals(f.getName())) {
				return f;
			}
		}
		return null;
	}
	public static List<File>  getFiles(File fileDir, String filetype) {
		List<File> lfile = new ArrayList<File>();
		File[] fs = fileDir.listFiles();
		for (File f : fs) {
			if(f.getName().endsWith(filetype))
			lfile.add(f);
		}
		return null;
	}
	// 获得(#define P27 P2_bits._7)中间的值：P27
	public static String replace(String str) {
		String re = "";
		String reg = "\\s+[^\\s]+\\s+";
		Pattern p = Pattern.compile(reg);
		Matcher m = p.matcher(str);
		while (m.find()) {
			re = m.group();
		}
		return re;
	}
}
