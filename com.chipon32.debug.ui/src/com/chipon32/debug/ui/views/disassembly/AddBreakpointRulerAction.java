/*******************************************************************************
 * Copyright (c) 2008, 2014 Wind River Systems, Inc. and others.
 *
 * This program and the accompanying materials
 * are made available under the terms of the Eclipse Public License 2.0
 * which accompanies this distribution, and is available at
 * https://www.eclipse.org/legal/epl-2.0/
 *
 * SPDX-License-Identifier: EPL-2.0
 *
 * Contributors:
 *     Wind River Systems - initial API and implementation
 *******************************************************************************/
package com.chipon32.debug.ui.views.disassembly;

import org.eclipse.core.runtime.CoreException;
import org.eclipse.debug.ui.DebugUITools;
import org.eclipse.debug.ui.actions.IToggleBreakpointsTarget;
import org.eclipse.debug.ui.actions.IToggleBreakpointsTargetExtension;
import org.eclipse.debug.ui.actions.IToggleBreakpointsTargetManager;
import org.eclipse.jface.action.Action;
import org.eclipse.jface.text.BadLocationException;
import org.eclipse.jface.text.IDocument;
import org.eclipse.jface.text.IRegion;
import org.eclipse.jface.text.ITextSelection;
import org.eclipse.jface.text.TextSelection;
import org.eclipse.jface.text.source.IVerticalRulerInfo;
import org.eclipse.jface.viewers.ISelection;
import org.eclipse.ui.IWorkbenchPart;

/**
 * ChiponDisassemblyView AddBreakPoint Action
 * Ruler action to add breakpoint with a dialog properties.
 */
public class AddBreakpointRulerAction extends Action{
	
	private ChiponDisassemblyView disassemblyPart;
	private IVerticalRulerInfo rulerInfo;

	protected AddBreakpointRulerAction(ChiponDisassemblyView disassemblyPart, IVerticalRulerInfo rulerInfo) {
		this.disassemblyPart = disassemblyPart;
		this.rulerInfo = rulerInfo;
	}

	@Override
	public void run() {
		IWorkbenchPart part = disassemblyPart;
		int line = rulerInfo.getLineOfLastMouseButtonActivity();
		if(line == -1) {
			return;
		}
		IDocument document = disassemblyPart.getfDocument();
		try {
			IRegion region = document.getLineInformation(line);
			ITextSelection textSelection = new TextSelection(document, region.getOffset(), region.getLength());
			IToggleBreakpointsTargetExtension toggleTarget = getToggleTarget(textSelection);
			if (toggleTarget != null) {
				try {
					if (toggleTarget.canToggleBreakpoints(part, textSelection)) {
						toggleTarget.toggleBreakpoints(part, textSelection);
					}
				} catch (CoreException e) {
					e.printStackTrace();
				}
			}
		} catch (BadLocationException e) {
			e.printStackTrace();
		}
		super.run();
	}

	private IToggleBreakpointsTargetExtension getToggleTarget(ISelection selection) {
		IToggleBreakpointsTargetManager toggleMgr = DebugUITools.getToggleBreakpointsTargetManager();
		IToggleBreakpointsTarget toggleTarget = toggleMgr.getToggleBreakpointsTarget(disassemblyPart, selection);
		if (toggleTarget instanceof IToggleBreakpointsTargetExtension) {
			return (IToggleBreakpointsTargetExtension) toggleTarget;
		}
		return null;
	}
	
}
