package com.chipon32.debug.ui.views.disassembly;

import org.eclipse.core.runtime.ListenerList;
import org.eclipse.jface.text.ITextSelection;
import org.eclipse.jface.text.source.ISourceViewer;
import org.eclipse.jface.viewers.ISelection;
import org.eclipse.jface.viewers.ISelectionChangedListener;
import org.eclipse.jface.viewers.ISelectionProvider;
import org.eclipse.jface.viewers.SelectionChangedEvent;
import org.eclipse.jface.viewers.StructuredSelection;

/**
 * Selection provider for disassembly selections.
 * Wraps the selection provider of the underlying text viewer and provides
 *  {@link IDisassemblySelection}s instead of {@link ITextSelection}s.
 *
 * @since 2.1
 * @see IDisassemblySelection
 */
class DisassemblySelectionProvider implements ISelectionProvider {

	private final ListenerList<ISelectionChangedListener> fListenerList = new ListenerList<>(ListenerList.IDENTITY);
	private final ISelectionChangedListener fListener = event -> fireSelectionChanged(event);
	private final ChiponDisassemblyView fPart;

	DisassemblySelectionProvider(ChiponDisassemblyView disassemblyPart) {
		fPart = disassemblyPart;
		fPart.getfSourceViewer().getSelectionProvider().addSelectionChangedListener(fListener);
	}

	private void fireSelectionChanged(SelectionChangedEvent event) {
		SelectionChangedEvent newEvent = new SelectionChangedEvent(this, getSelection());
		for (ISelectionChangedListener listener : fListenerList) {
			listener.selectionChanged(newEvent);
		}
	}

	@Override
	public void addSelectionChangedListener(ISelectionChangedListener listener) {
		fListenerList.add(listener);
	}

	@Override
	public ISelection getSelection() {
		final ISourceViewer textViewer = fPart.getfSourceViewer();
		ISelectionProvider provider = textViewer.getSelectionProvider();
		if (provider != null) {
			return (ITextSelection) provider.getSelection();
		}
		return StructuredSelection.EMPTY;
	}

	@Override
	public void removeSelectionChangedListener(ISelectionChangedListener listener) {
		fListenerList.remove(listener);
	}

	@Override
	public void setSelection(ISelection selection) {
		ISelectionProvider provider = fPart.getfSourceViewer().getSelectionProvider();
		if (provider != null) {
			provider.setSelection(selection);
		}
	}
}

