package com.chipon32.debug.ui.views.disassembly;

import java.util.ArrayList;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.eclipse.core.runtime.IStatus;
import org.eclipse.core.runtime.Status;
import org.eclipse.jface.text.BadLocationException;
import org.eclipse.jface.text.IDocument;
import org.eclipse.jface.text.Position;
import org.eclipse.jface.viewers.ITreeContentProvider;
import org.eclipse.jface.viewers.Viewer;
import org.eclipse.ui.IEditorInput;
import org.eclipse.ui.part.ViewPart;

import com.chipon32.debug.ui.DebugUIActivator;


public class ContentProvider implements ITreeContentProvider {

	public ContentProvider(ViewPart viewPart) {
		this.viewPart = viewPart;
	}

	private ViewPart viewPart;

	private IEditorInput input;

	private TreeObject procedures = new TreeObject(
			"Produce_Name",
			Constants.TREEOBJECT_TYPE_ROOT_PROCEDURE);

	private TreeObject macros = new TreeObject("Macro_name",
			Constants.TREEOBJECT_TYPE_ROOT_MACRO);

	private TreeObject labels = new TreeObject("Label_name",
			Constants.TREEOBJECT_TYPE_ROOT_LABEL);

	public TreeObject getLabels() {
		return labels;
	}

	public TreeObject getSegments() {
		return segments;
	}

	private TreeObject segments = new TreeObject(
			"Segment_name",
			Constants.TREEOBJECT_TYPE_ROOT_SEGMENT);

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Object[] getChildren(Object parentElement) {
		if (parentElement instanceof TreeObject) {
			return ((TreeObject) parentElement).getChildren();
		}

		return null;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Object getParent(Object element) {
		if (element instanceof TreeObject) {
			return ((TreeObject) element).getParent();
		}

		return null;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public boolean hasChildren(Object element) {
		if (element instanceof TreeObject) {
			return (((TreeObject) element).getChildren().length > 0);
		}

		return false;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Object[] getElements(Object inputElement) {
		if (inputElement == input) {
			ArrayList<TreeObject> objects = new ArrayList<TreeObject>();

			if (procedures.getChildren().length > 0) {
				objects.add(procedures);
			}

			if (macros.getChildren().length > 0) {
				objects.add(macros);
			}

			if (labels.getChildren().length > 0) {
				objects.add(labels);
			}

			if (segments.getChildren().length > 0) {
				objects.add(segments);
			}

			return objects.toArray(new Object[0]);
		}

		return null;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public void dispose() {
		input = null;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public void inputChanged(Viewer viewer, Object oldInput, Object newInput) {
		if (oldInput instanceof IEditorInput) {
			input = null;
		}

		if (newInput instanceof IEditorInput) {
			input = (IEditorInput) newInput;
			parse();
		}
	}

	/**
	 * Finds an existing TreeObject in the tree model.
	 * 
	 * @param treeobj
	 *            The given TreeObject.
	 * 
	 * @return The TreeObject, if found. Or null if not found.
	 */
	public TreeObject findEqualTreeObject(TreeObject treeobj) {
		if (treeobj == null) {
			return null;
		}

		if (procedures.equals(treeobj)) {
			return procedures;
		}

		if (macros.equals(treeobj)) {
			return macros;
		}

		if (labels.equals(treeobj)) {
			return labels;
		}

		if (segments.equals(treeobj)) {
			return segments;
		}

		int i = 0;
		TreeObject to = null;
		Object[] o = procedures.getChildren();

		for (i = 0; i < o.length; i++) {
			if (o[i] instanceof TreeObject) {
				to = (TreeObject) o[i];

				if (to.equals(treeobj)) {
					return to;
				}
			}
		}

		o = macros.getChildren();

		for (i = 0; i < o.length; i++) {
			if (o[i] instanceof TreeObject) {
				to = (TreeObject) o[i];

				if (to.equals(treeobj)) {
					return to;
				}
			}
		}

		o = labels.getChildren();

		for (i = 0; i < o.length; i++) {
			if (o[i] instanceof TreeObject) {
				to = (TreeObject) o[i];

				if (to.equals(treeobj)) {
					return to;
				}
			}
		}

		o = segments.getChildren();

		for (i = 0; i < o.length; i++) {
			if (o[i] instanceof TreeObject) {
				to = (TreeObject) o[i];

				if (to.equals(treeobj)) {
					return to;
				}
			}
		}

		return null;
	}

	/**
	 * Parse the new input and build up the tree.
	 */
	public  void parse() {
		procedures.setChildren(null);
		macros.setChildren(null);
		labels.setChildren(null);
		segments.setChildren(null);
		
		IDocument document = null;
		if(viewPart instanceof ChiponDisassemblyView) {
			document = ((ChiponDisassemblyView) viewPart).getfDocument();
		}

		if (document != null) {
			int lines = document.getNumberOfLines();
			int lineOffset, pos, linelen, matchStart, matchEnd, startOffset, length;
			String name = "";
			String stringLine = "";
			// String stringLine = "";
			StringBuffer filterBuffer = new StringBuffer();
			Pattern pattern = null;
			Matcher matcher = null;
			TreeObject child = null;

			lineOffset = 0;
			pos = 0;
			linelen = 0;
			matchStart = 0;
			matchEnd = 0;
			startOffset = 0;
			length = 0;

			for (int line = 0; line < lines; line++) {
				try {
					lineOffset = document.getLineOffset(line);
					linelen = document.getLineLength(line);
					stringLine = document.get(lineOffset, linelen);
					filterBuffer.setLength(0);

					for (pos = 0; pos < linelen; pos++) {
						if (document.getPartition(lineOffset + pos).getType()
								.equals(IDocument.DEFAULT_CONTENT_TYPE)) {
							filterBuffer.append(stringLine.substring(pos,
									pos + 1));
						} else {
							filterBuffer.append(" ");
						}
					}

					stringLine = filterBuffer.toString();
					// stringLine = stringLine.toLowerCase();

					if (stringLine.indexOf("proc") > -1) {
						pattern = Pattern
								.compile("(\\A|\\W)\\s*proc\\s+(\\w+)");
						matcher = pattern.matcher(stringLine);

						if (matcher.find()) {
							matchStart = matcher.start(2);
							matchEnd = matcher.end(2);
							startOffset = lineOffset + matchStart;
							length = lineOffset + matchEnd - startOffset;

							child = new TreeObject(stringLine.substring(
									matchStart, matchEnd),
									Constants.TREEOBJECT_TYPE_PROCEDURE);
							child.setData(new Position(startOffset, length));

							procedures.addChild(child);
						}
					}

					if (stringLine.indexOf("macro") > -1) {
						pattern = Pattern
								.compile("(\\A|\\W)\\s*(\\w+)\\s+macro");
						matcher = pattern.matcher(stringLine);

						if (matcher.find()) {
							matchStart = matcher.start(2);
							matchEnd = matcher.end(2);
							startOffset = lineOffset + matchStart;
							length = lineOffset + matchEnd - startOffset;

							child = new TreeObject(stringLine.substring(
									matchStart, matchEnd),
									Constants.TREEOBJECT_TYPE_MACRO);
							child.setData(new Position(startOffset, length));

							macros.addChild(child);
						}
					}

					/*
					 * if (stringLine.indexOf(":") > -1) { pattern =
					 * Pattern.compile("\\A\\s*\\.*(\\w+):"); matcher =
					 * pattern.matcher(stringLine);
					 * 
					 * if (matcher.find()) { matchStart = matcher.start(1);
					 * matchEnd = matcher.end(1); startOffset = lineOffset +
					 * matchStart; length = lineOffset + matchEnd - startOffset;
					 * 
					 * child = new TreeObject(stringLine.substring(matchStart,
					 * matchEnd), Constants.TREEOBJECT_TYPE_LABEL);
					 * child.setData(new Position(startOffset, length));
					 * 
					 * labels.addChild(child); } }
					 */

					if (stringLine.trim() != null) {
						pattern = Pattern.compile("[^\\s;.]+[\\s]*");
						matcher = pattern.matcher(stringLine);

						if (matcher.matches()) {
							String string = matcher.group();

							if (!isKeyWord(string)) {
								matchStart = matcher.start();
								matchEnd = matcher.end();
								startOffset = lineOffset + matchStart;
								length = lineOffset + matchEnd - startOffset;

								child = new TreeObject(stringLine.substring(
										matchStart, matchEnd),
										Constants.TREEOBJECT_TYPE_LABEL);
								child.setData(new Position(startOffset, length));

								labels.addChild(child);
							}
						}
					}

					if (stringLine.indexOf(".") > -1) {
						pattern = Pattern.compile("(\\A|\\W)\\.(\\w+[:]*)");
						matcher = pattern.matcher(stringLine);

						if (matcher.find()) {

							matchStart = matcher.start(2);
							matchEnd = matcher.end(2);
							startOffset = lineOffset + matchStart;
							length = lineOffset + matchEnd - startOffset;

							name = stringLine.substring(matchStart, matchEnd);

							if ("include".equalsIgnoreCase(name)) {
								pattern = Pattern.compile("(.*)(\\s)(.*)");
								matcher = pattern.matcher(stringLine);
								if (matcher.find()) {
									String str = matcher.group(2);
									// System.out.println(thirdWord);
									name = name + str;
								}
							} else if ("define".equalsIgnoreCase(name)) {
								pattern = Pattern.compile("\\s\\w+");
								matcher = pattern.matcher(stringLine);

								if (matcher.find()) {
									String str = matcher.group().trim();
									/*if (str.length() > 15) {
										str = str.substring(0, 11);
										str = str + "...";
									}*/
									name = name + "   " + str;
									pattern = Pattern
											.compile("(.*)(\\s)(.*)(\\s)(.*)(\\b)");
									matcher = pattern.matcher(stringLine);
									if (matcher.find()) {
										String thirdWord = matcher.group(5);
										// System.out.println(thirdWord);
										name = name
												+ spaceNum(15 - str.length())
												+ "  " + thirdWord;
									}
								}
							} else if ("EQU".equalsIgnoreCase(name)) {
								pattern = Pattern.compile("\\w+");
								matcher = pattern.matcher(stringLine);
								if (matcher.find()) {
									String str = matcher.group().trim();
									int l = str.length();
									/*if (l > 15) {
										str = str.substring(0, 11);
										str = str + "...";
									}*/
									String string = name;
									name = str;
									str = string;
									name = str + "      " + name;
									pattern = Pattern
											.compile("(.*)(\\s)(.*)(\\s)(.*)(\\b)");
									matcher = pattern.matcher(stringLine);
									if (matcher.find()) {
										String thirdWord = matcher.group(5);
										// System.out.println(thirdWord);
										name = name + spaceNum(15 - l) + "  "
												+ thirdWord;
									}
									// System.out.println(name);
								}
							} else if ("org".equalsIgnoreCase(name)) {
								pattern = Pattern.compile("\\s+\\w*\\s");
								matcher = pattern.matcher(stringLine);

								if (matcher.find()) {
									String str = matcher.group().trim();
									if (str.length() > 15) {
										str = str.substring(0, 11);
										str = str + "...";
									}
									name = name + "      " + str;
								}
							}

							if (name.codePointAt(name.length() - 1) != ':') {
								child = new TreeObject(name,
										Constants.TREEOBJECT_TYPE_SEGMENT);
								child.setData(new Position(startOffset, length));
								segments.addChild(child);
							}
						}
					}
				} catch (BadLocationException e) {
					DebugUIActivator
							.getDefault()
							.getLog()
							.log(new Status(IStatus.ERROR, Constants.PLUGIN_ID,
									IStatus.OK, "Error", e));
				}
			}
		}
	}

	private boolean isKeyWord(String string) {
		string = string.trim().toUpperCase();
		if (ASMInstructionSet.getInstructions().containsKey(string)
				|| ASMInstructionSet.getPseudoInstructions().containsKey(string)
				|| ASMInstructionSet.getPseudoInstructions().containsKey(string)
				|| ASMInstructionSet.getRegisters().containsKey(string)
				|| ASMInstructionSet.getSpecialRegisterMap().containsKey(string)) {
			return true;
		}
		return false;

	}

	private String spaceNum(int i) {
		String str = "";

		for (int j = 0; j < i; j++) {
			str = str + " ";
		}
		return str;

	}

}
