/*****************************************************************
 * Copyright (c) 2011, 2015 Texas Instruments and others
 *
 * This program and the accompanying materials
 * are made available under the terms of the Eclipse Public License 2.0
 * which accompanies this distribution, and is available at
 * https://www.eclipse.org/legal/epl-2.0/
 *
 * SPDX-License-Identifier: EPL-2.0
 *
 * Contributors:
 *     <PERSON> (Texas Instruments) -
 *     	Update CDT ToggleBreakpointTargetFactory enablement (340177 )
 *****************************************************************/
package com.chipon32.debug.ui.views.disassembly;

import org.eclipse.core.expressions.PropertyTester;
/**
 * Disassembly toggle breakpoint factory enablement tester.
 *
 * @since 2.2
 */
public class DisassemblyToggleBreakpointTester extends PropertyTester {

	@Override
	public boolean test(Object receiver, String property, Object[] args, Object expectedValue) {
		if ("isDisassemblyViewSupportsChiponBreakpoint".equals(property) && (receiver instanceof ChiponDisassemblyView)) { //$NON-NLS-1$
			ChiponDisassemblyView view = ((ChiponDisassemblyView) receiver);
			if(view != null) {
				return true;
			}
		}
		return false;
	}
}
