package com.chipon32.debug.ui.views.disassembly;

import org.eclipse.core.runtime.NullProgressMonitor;
import org.eclipse.jface.text.IDocument;
import org.eclipse.jface.text.ITextHover;
import org.eclipse.jface.text.contentassist.IContentAssistant;
import org.eclipse.jface.text.presentation.IPresentationReconciler;
import org.eclipse.jface.text.presentation.PresentationReconciler;
import org.eclipse.jface.text.reconciler.IReconciler;
import org.eclipse.jface.text.reconciler.IReconcilingStrategy;
import org.eclipse.jface.text.reconciler.MonoReconciler;
import org.eclipse.jface.text.rules.DefaultDamagerRepairer;
import org.eclipse.jface.text.source.ISourceViewer;
import org.eclipse.ui.editors.text.TextSourceViewerConfiguration;
import org.eclipse.ui.part.ViewPart;

/**
 * Configurations for the ASMEditor. Provides sytax hightlighting.
 * 
 * <AUTHOR>
 * @since 15.11.2005
 */
public class ASMSourceViewerConfiguration extends TextSourceViewerConfiguration {

	/**
	 * The underlying ASM editor.
	 */
	private ViewPart viewPart;

	/**
	 * Rule scanner for default ASM code.
	 */
	private ASMCodeScanner asmcodescanner = null;

	/**
	 * Rule scanner for comment, multi-comment and string parts.
	 */
	private PropertyChangeRuleBaseScanner[] scanner = new PropertyChangeRuleBaseScanner[3];
	
	/**
	 * The constructor.
	 * 
	 * @param editor
	 *            The underlying ASM editor.
	 */
	public ASMSourceViewerConfiguration(ViewPart viewPart) {		
		this.viewPart = viewPart;		
	}
	
//	public ASMSourceViewerConfiguration(IPreferenceStore preferenceStore, TextEditor editor) {
//		super(preferenceStore);		
//		this.viewPart = viewPart;			
//	}
	
	/* (non-Javadoc)
	 * ×¢²áÄÚÈÝ¸¨Öú
	 * @see org.eclipse.jface.text.source.SourceViewerConfiguration#getContentAssistant(org.eclipse.jface.text.source.ISourceViewer)
	 */
	@Override
	public IContentAssistant getContentAssistant(ISourceViewer sourceViewer) {
//		ContentAssistant assistant = new ContentAssistant();
//		assistant.setContentAssistProcessor(new ASMCompletionProcessor(),
//				IDocument.DEFAULT_CONTENT_TYPE);
//		assistant.setContextInformationPopupOrientation(IContentAssistant.CONTEXT_INFO_ABOVE);
//		assistant.enableAutoActivation(true);
//		assistant.enableAutoInsert(true);
//		assistant.setAutoActivationDelay(100);

		return null;
	}

	
	/* (non-Javadoc)
	 * @see org.eclipse.jface.text.source.SourceViewerConfiguration#getPresentationReconciler(org.eclipse.jface.text.source.ISourceViewer)
	 */
	@Override
	public IPresentationReconciler getPresentationReconciler(
			ISourceViewer sourceViewer) {
		PresentationReconciler reconciler = new PresentationReconciler();

		asmcodescanner = new ASMCodeScanner(viewPart);
		DefaultDamagerRepairer dr = new DefaultDamagerRepairer(asmcodescanner);
		reconciler.setDamager(dr, IDocument.DEFAULT_CONTENT_TYPE);
		reconciler.setRepairer(dr, IDocument.DEFAULT_CONTENT_TYPE);

		scanner[0] = new PropertyChangeRuleBaseScanner(viewPart,
				Constants.PREFERENCES_TEXTCOLOR_COMMENT);
		dr = new DefaultDamagerRepairer(scanner[0]);
		reconciler.setDamager(dr, Constants.PARTITION_COMMENT_MULTI);
		reconciler.setRepairer(dr, Constants.PARTITION_COMMENT_MULTI);

		scanner[1] = new PropertyChangeRuleBaseScanner(viewPart,
				Constants.PREFERENCES_TEXTCOLOR_COMMENT);
		dr = new DefaultDamagerRepairer(scanner[1]);
		reconciler.setDamager(dr, Constants.PARTITION_COMMENT_SINGLE);
		reconciler.setRepairer(dr, Constants.PARTITION_COMMENT_SINGLE);

		scanner[2] = new PropertyChangeRuleBaseScanner(viewPart,
				Constants.PREFERENCES_TEXTCOLOR_STRING);
		dr = new DefaultDamagerRepairer(scanner[2]);
		reconciler.setDamager(dr, Constants.PARTITION_STRING);
		reconciler.setRepairer(dr, Constants.PARTITION_STRING);

		reconciler.setDocumentPartitioning(getConfiguredDocumentPartitioning(sourceViewer));

		return reconciler;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public IReconciler getReconciler(ISourceViewer sourceViewer) {
		IReconcilingStrategy reconcilingStrategy = new ASMReconcilingStategy(viewPart);

		MonoReconciler reconciler = new MonoReconciler(reconcilingStrategy,false);
		reconciler.setProgressMonitor(new NullProgressMonitor());
		reconciler.setDelay(500);

		return reconciler;
	}

	/**
	 * Remove all rule scanners from property change listener.
	 */
	public void dispose() {
		if (asmcodescanner != null) {
			asmcodescanner.dispose();
		}

		for (int i = 0; i < scanner.length; i++) {
			if (scanner[i] != null) {
				scanner[i].dispose();
			}
		}
	}

	
	/* (non-Javadoc)
	 * ×¢²áÐü¸¡ÌáÊ¾Àà
	 * @see org.eclipse.ui.editors.text.TextSourceViewerConfiguration#getTextHover(org.eclipse.jface.text.source.ISourceViewer, java.lang.String)
	 */
	@Override
	public ITextHover getTextHover(ISourceViewer sourceViewer,
			String contentType) {
//		ASMSourceHover sourceHover = new ASMSourceHover(sourceViewer);
//		sourceHover.setViewPart(viewPart);
//		return sourceHover;
		return null;
	}
	
	
	
}
