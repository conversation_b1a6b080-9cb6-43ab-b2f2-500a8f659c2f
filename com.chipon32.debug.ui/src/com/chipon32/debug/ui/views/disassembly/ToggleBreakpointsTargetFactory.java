package com.chipon32.debug.ui.views.disassembly;

import java.util.Collections;
import java.util.HashSet;
import java.util.Set;

import org.eclipse.debug.ui.DebugUITools;
import org.eclipse.debug.ui.actions.IToggleBreakpointsTarget;
import org.eclipse.debug.ui.actions.IToggleBreakpointsTargetFactory;
import org.eclipse.jface.viewers.ISelection;
import org.eclipse.jface.viewers.IStructuredSelection;
import org.eclipse.jface.viewers.StructuredSelection;
import org.eclipse.ui.IWorkbenchPart;

import com.chipon32.debug.ui.DebugUIActivator;
import com.chipon32.debug.ui.breakpoint.ChiponBreakpointAdapter;

public class ToggleBreakpointsTargetFactory implements IToggleBreakpointsTargetFactory {
	
	public static final String TOGGLE_C_BREAKPOINT_TARGET_ID = DebugUIActivator.PLUGIN_ID + ".toggleCBreakpointTarget"; //$NON-NLS-1$
	
	private static final Set<String> TOGGLE_TARGET_IDS = new HashSet<>(1);
	
	static {
		TOGGLE_TARGET_IDS.add(TOGGLE_C_BREAKPOINT_TARGET_ID);
	}

	private static final IToggleBreakpointsTarget fgDisassemblyToggleBreakpointsTarget = new ChiponBreakpointAdapter();
	
	public ToggleBreakpointsTargetFactory() {
	}

	@Override
	public Set<String> getToggleTargets(IWorkbenchPart part, ISelection selection) {
		if(part instanceof ChiponDisassemblyView) {
			return TOGGLE_TARGET_IDS;
		}
		return Collections.emptySet();
	}

	@Override
	public String getDefaultToggleTarget(IWorkbenchPart part, ISelection selection) {
		if(part instanceof ChiponDisassemblyView) {
			Object element = getDebugContext(part).getFirstElement();
//			System.err.println(element.getClass());
			return TOGGLE_C_BREAKPOINT_TARGET_ID;
		}
		return null;
	}

	@Override
	public IToggleBreakpointsTarget createToggleTarget(String targetID) {
		if (TOGGLE_C_BREAKPOINT_TARGET_ID.equals(targetID)) {
			return fgDisassemblyToggleBreakpointsTarget;
		}
		return null;
	}

	@Override
	public String getToggleTargetName(String targetID) {
		return null;
	}

	@Override
	public String getToggleTargetDescription(String targetID) {
		return null;
	}
	
	private IStructuredSelection getDebugContext(IWorkbenchPart part) {
		ISelection selection = DebugUITools.getDebugContextManager()
				.getContextService(part.getSite().getWorkbenchWindow()).getActiveContext();
		if (selection instanceof IStructuredSelection) {
			return (IStructuredSelection) selection;
		}
		return StructuredSelection.EMPTY;
	}

}
