package com.chipon32.debug.ui.views.disassembly;

import org.eclipse.core.resources.IMarker;
import org.eclipse.core.resources.IMarkerDelta;
import org.eclipse.debug.core.IBreakpointListener;
import org.eclipse.debug.core.model.IBreakpoint;
import org.eclipse.jface.text.BadLocationException;
import org.eclipse.jface.text.IRegion;
import org.eclipse.jface.text.Position;
import org.eclipse.jface.text.source.AnnotationModel;
import org.eclipse.ui.texteditor.MarkerAnnotation;

import com.chipon32.debug.core.breakpoint.ChiponAddrBreakpoint;
import com.chipon32.debug.core.model.ChiponThread;

/**
 * 
 *
 */
public class ChiponBreakpointsAnnotationModel extends AnnotationModel implements IBreakpointListener{

	@Override
	public void breakpointAdded(IBreakpoint breakpoint) {
		addBreakpointAnnotation(breakpoint,true);
	}

	@Override
	public void breakpointRemoved(IBreakpoint breakpoint, IMarkerDelta delta) {
		
	}

	@Override
	public void breakpointChanged(IBreakpoint breakpoint, IMarkerDelta delta) {
		
		
	}
	
	private void addBreakpointAnnotation(IBreakpoint breakpoint, boolean fireEvent) {
		if (fDocument == null) {
			return;
		}
		final IMarker marker = breakpoint.getMarker();
		if (marker == null) {
			return;
		}
		try {
			Position position = createPositionFromBreakpoint(breakpoint);
			if (position != null) {
				addAnnotation(new MarkerAnnotation(marker), position, fireEvent);
			}
		} catch (BadLocationException exc) {
			exc.printStackTrace();
		}
	}

	private Position createPositionFromBreakpoint(IBreakpoint breakpoint) throws BadLocationException{
//		if(breakpoint instanceof ChiponAddrBreakpoint addrBreakpoint) {//jdk16Ьиад
		if(breakpoint instanceof ChiponAddrBreakpoint) {
			ChiponAddrBreakpoint addrBreakpoint = (ChiponAddrBreakpoint) breakpoint;
			String addr = addrBreakpoint.getAddress();
			int line = ChiponThread.locationData.get(addr);
			IRegion region =  fDocument.getLineInformation(line);
			return new Position(region.getOffset(),region.getLength());
		}
		return null;
	}

}
