package com.chipon32.debug.ui.views.disassembly;

import org.eclipse.jface.preference.IPreferenceStore;
import org.eclipse.jface.text.rules.RuleBasedScanner;
import org.eclipse.jface.text.rules.Token;
import org.eclipse.jface.util.IPropertyChangeListener;
import org.eclipse.jface.util.PropertyChangeEvent;
import org.eclipse.ui.part.ViewPart;

import com.chipon32.debug.ui.DebugUIActivator;


/**
 * Default RuleBaseScanner.
 * 
 * <AUTHOR>
 * @since 13.02.2006
 */
public class PropertyChangeRuleBaseScanner extends RuleBasedScanner implements IPropertyChangeListener {

  /** Default Token for the text attributes * */
  private Token defToken;

  /** Editor need for refresh * */
  private ViewPart viewPart;

  /** Key for preference store * */
  private String preferencesKey;

  /**
   * Constructor of PropertyChangeRuleBaseScanner
   * 
   * @param editor The given Editor.
   * @param preferencesKey The preference key to be listen on.
   */
	public PropertyChangeRuleBaseScanner( final ViewPart viewPart,  final String preferencesKey) {
//    this.viewPart = viewPart;
    this.preferencesKey = preferencesKey;

    IPreferenceStore store = DebugUIActivator.getDefault().getPreferenceStore();

    defToken = new Token(TextAttributeConverter.preferenceDataToTextAttribute(store.getString(preferencesKey)));

    super.setDefaultReturnToken(defToken);

    DebugUIActivator.getDefault().getPreferenceStore().addPropertyChangeListener(this);
  }

  /**
   * Remove rule scanner from property listener.
   */
  public void dispose() {
	  DebugUIActivator.getDefault().getPreferenceStore().removePropertyChangeListener(this);
  }

  /**
   * {@inheritDoc}
   */
  @Override
public void propertyChange(PropertyChangeEvent event) {
    if (event.getProperty().equals(preferencesKey)) {
      defToken.setData(TextAttributeConverter.preferenceDataToTextAttribute((String) event.getNewValue()));
    }

    if(viewPart instanceof ChiponDisassemblyView) {
    	((ChiponDisassemblyView) viewPart).refreshSourceViewer();
    }
  }
}
