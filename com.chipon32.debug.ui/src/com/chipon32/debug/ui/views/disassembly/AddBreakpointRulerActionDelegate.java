package com.chipon32.debug.ui.views.disassembly;

import org.eclipse.jface.action.IAction;
import org.eclipse.jface.text.source.IVerticalRulerInfo;

public class AddBreakpointRulerActionDelegate extends AbstractDisassemblyRulerActionDelegate{

	public AddBreakpointRulerActionDelegate() {
	}

	@Override
	protected IAction createAction(ChiponDisassemblyView disassemblyPart, IVerticalRulerInfo rulerInfo) {
		return new AddBreakpointRulerAction(disassemblyPart,rulerInfo);
	}

}
