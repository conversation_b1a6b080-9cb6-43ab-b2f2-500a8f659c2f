package com.chipon32.debug.ui.views;

import org.eclipse.jface.viewers.BaseLabelProvider;
import org.eclipse.jface.viewers.IColorProvider;
import org.eclipse.jface.viewers.ITableLabelProvider;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.graphics.Image;

import com.chipon32.debug.core.model.ChiponACCXData;

public class ChiponAccLabelProvider extends BaseLabelProvider implements ITableLabelProvider, IColorProvider {

	@Override
	public Color getForeground(Object element) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public Color getBackground(Object element) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public Image getColumnImage(Object element, int columnIndex) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public String getColumnText(Object element, int columnIndex) {
		if(element instanceof ChiponACCXData){
			ChiponACCXData data = (ChiponACCXData) element;
			
			switch(columnIndex){
			case 0:
				return data.getaCCName();
			case 1:
				return data.getHighData();
			case 2:	
				return data.getLowData();
			case 3:	
				long v=data.getDecView();
				try{
				return Long.toString(v);
				}catch (Exception e) {
					// TODO: handle exception
					return "--";
				}
			default:
				return "--";
			}
		}
		return null;
	}

}
