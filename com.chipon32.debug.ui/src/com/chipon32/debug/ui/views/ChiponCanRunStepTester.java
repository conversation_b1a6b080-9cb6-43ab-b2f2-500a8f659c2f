package com.chipon32.debug.ui.views;

import org.eclipse.core.expressions.PropertyTester;
import org.eclipse.core.runtime.IAdaptable;
import org.eclipse.debug.core.DebugException;

import com.chipon32.debug.core.model.ChiponDebugElement;
import com.chipon32.debug.core.model.ChiponStackFrame;
import com.chipon32.debug.core.model.ChiponThread;
import com.chipon32.debug.ui.DebugUIActivator;

/**
 * 
 * <AUTHOR>
 *
 */
public class ChiponCanRunStepTester extends PropertyTester {

	private static final String CAN_RUNSTEP_PROPERTY = "canRunStep"; //$NON-NLS-1$
	public ChiponCanRunStepTester() {
	}

	@Override
	public boolean test(Object receiver, String property, Object[] args,
			Object expectedValue) {
		
		if (CAN_RUNSTEP_PROPERTY.equals(property)) {
	        if (receiver instanceof IAdaptable) {
	        
	        	ChiponDebugElement element = (ChiponDebugElement) 
	                ((IAdaptable)receiver).getAdapter(ChiponDebugElement.class);
	        	ChiponThread thread = null;
	            if (element instanceof ChiponThread) {
	                thread = (ChiponThread)element;
	            } else if (element instanceof ChiponStackFrame) {
	                thread = (ChiponThread)((ChiponStackFrame)element).getThread();
	            } 

	             if (thread != null) {
	 	            	while (thread.canRunStepData() && thread.isCanRunStepData() &&thread.isCanRunStepSuspendData() && thread.isStepRunning() /* && thread.isSuspended()&& thread.isCanRunStepSuspendData()*/) {
	 	            		
	 	            		try {
	 	            				System.out.println(Messages.ChiponCanRunStepTester_1);
	 	            				thread.setCanRunStepData(false);
	 	            				String filename = DebugUIActivator.getActiveWorkbenchWindow().getActivePage().getActiveEditor().getEditorInput().getName();
	 								Thread.sleep(thread.getChiponDebugTarget().getChiponDebugInfoModel().getAutoStepInInterval());
	 								try {
										thread.stepInto();
									} catch (DebugException e) {
										// TODO Auto-generated catch block
										e.printStackTrace();
									}
//	 								
//	 								if(null!=resetResult.resultText){
//	 									thread.setCanRunStepData(false);
//	 								}

	 							} catch (InterruptedException e) {
	 								e.printStackTrace();
	 							}
	 	            		
	 	            	}
	 	            	return thread.canRunStepData();
	    		}
	           

	            
	          /*  if (thread != null) {
	            	while (thread.canRunStepData() && thread.isCanRunStepData()) {
            			try {
							Thread.sleep(ChiponDebugPreferenceAssistent.getDebugAutoStepDelay());
							thread.sendCommand(new ChiponStepIntoCommand());
						} catch (InterruptedException e) {
							e.printStackTrace();
						}
					}
	            	return thread.canRunStepData();
	            }
*/
	        }
	    }
		return false;
	}

}
