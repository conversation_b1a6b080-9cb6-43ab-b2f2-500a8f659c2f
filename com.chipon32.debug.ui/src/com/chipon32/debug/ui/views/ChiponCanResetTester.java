package com.chipon32.debug.ui.views;

import org.eclipse.core.expressions.PropertyTester;
import org.eclipse.core.runtime.IAdaptable;
import org.eclipse.debug.core.Launch;

import com.chipon32.debug.core.model.ChiponDebugElement;
import com.chipon32.debug.core.model.ChiponDebugTarget;
import com.chipon32.debug.core.model.ChiponStackFrame;
import com.chipon32.debug.core.model.ChiponThread;

/***
 *<AUTHOR>
 *2013-6-26обнГ7:09:30
 ***/
public class ChiponCanResetTester extends PropertyTester {

	private static final String CAN_RESET_PROPERTY = "canReset";
	
	@Override
	public boolean test(Object receiver, String property, Object[] args,
			Object expectedValue) {
		if (CAN_RESET_PROPERTY.equals(property)) {
	        if (receiver instanceof IAdaptable) {
	        	ChiponDebugElement element = (ChiponDebugElement) 
	                ((IAdaptable)receiver).getAdapter(ChiponDebugElement.class);
	        	ChiponThread thread = null;
	        	
	        	if (element instanceof ChiponDebugTarget) 
	        	{	        		
	        		thread = ((ChiponDebugTarget)element).getCurrentThread();
	        	}	        	
	        	else if (element instanceof ChiponThread) {
	                thread = (ChiponThread)element;
	            } else if (element instanceof ChiponStackFrame) {
	                thread = (ChiponThread)((ChiponStackFrame)element).getThread();
	            } 
	            else
	            {
		       		 if (receiver instanceof ChiponDebugTarget) {
		       			thread =  ((ChiponDebugTarget) element).getCurrentThread();
		    		 }
		    		 else if (receiver instanceof ChiponThread) {
		    			 thread = (ChiponThread) element;
		    		 }
		    		 else if (receiver instanceof ChiponStackFrame) {
		    			 thread = (ChiponThread) ((ChiponStackFrame) element).getThread();
		    		}
		    		 else if(receiver instanceof Launch)
		    		 {
		    		     Launch lc=(Launch)receiver;		        	
		    		     ChiponDebugTarget target=(ChiponDebugTarget) lc.getDebugTargets()[0];
		    		     thread=target.getCurrentThread();      		    			
		    		 }
	            }
	        	
	        	//-------------------------------
	            if (thread != null) {
	            	return thread.canResetData();
	            }
	        }
	    }
		return false;
	}
}

