package com.chipon32.debug.ui.views;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.eclipse.jface.action.Action;
import org.eclipse.jface.action.IMenuManager;
import org.eclipse.jface.action.IToolBarManager;
import org.eclipse.jface.action.Separator;

import org.eclipse.jface.resource.ImageDescriptor;

import org.eclipse.jface.viewers.CellEditor;
import org.eclipse.jface.viewers.CheckboxCellEditor;
import org.eclipse.jface.viewers.ComboBoxCellEditor;
import org.eclipse.jface.viewers.ISelection;
import org.eclipse.jface.viewers.ISelectionChangedListener;
import org.eclipse.jface.viewers.IStructuredSelection;
import org.eclipse.jface.viewers.SelectionChangedEvent;
import org.eclipse.jface.viewers.TableLayout;
import org.eclipse.jface.viewers.TextCellEditor;
import org.eclipse.jface.viewers.<PERSON><PERSON>iewer;
import org.eclipse.jface.viewers.Viewer;

import org.eclipse.swt.SWT;

import org.eclipse.swt.events.MouseEvent;
import org.eclipse.swt.events.MouseListener;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.events.SelectionListener;
import org.eclipse.swt.graphics.GC;

import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Button;
import org.eclipse.swt.widgets.Combo;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Event;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.Listener;
import org.eclipse.swt.widgets.Text;
import org.eclipse.swt.widgets.Tree;
import org.eclipse.swt.widgets.TreeColumn;
import org.eclipse.ui.IWorkbenchActionConstants;
import org.eclipse.ui.progress.UIJob;
import org.eclipse.core.resources.IProject;
import org.eclipse.core.runtime.IProgressMonitor;
import org.eclipse.core.runtime.IStatus;
import org.eclipse.core.runtime.Status;
import org.eclipse.debug.ui.AbstractDebugView;
import org.eclipse.debug.ui.DebugUITools;
import org.eclipse.debug.ui.contexts.DebugContextEvent;
import org.eclipse.debug.ui.contexts.IDebugContextListener;


import com.chipon32.debug.core.model.ChiponDebugTarget;
import com.chipon32.debug.core.model.ChiponStackFrame;
import com.chipon32.debug.core.model.ChiponThread;
import com.chipon32.debug.core.model.ChiponWatchpointData;

import com.chipon32.debug.core.protocol.ChiponCommandResult;
import com.chipon32.debug.core.protocol.ChiponCustomCommand;

import com.chipon32.debug.core.protocol.ChiponInfoWatchpointCommand;
import com.chipon32.debug.core.protocol.ChiponInfoWatchpointCommandResult;


import com.chipon32.debug.core.protocol.ChiponWatchCommand;
import com.chipon32.debug.core.protocol.ChiponWatchCommandResult;
import com.chipon32.debug.ui.ImageKeys;
import com.chipon32.debug.ui.util.watchpointXmlIO;
import com.chipon32.util.communicate.WriteMessage;

/**
 * 资源使用率视图的最上层应用
 * <AUTHOR> @since 
 */
public class ChiponWatchpointView extends AbstractDebugView implements IDebugContextListener{
	public ChiponWatchpointView() {
	}
	//##################################################################################
	public static final String ID = "com.chipon32.debug.ui.view.WatchpointView"; //$NON-NLS-1$
	private ChiponThread  fThread;
	//##################################################################################
	private TreeViewer treeViewer;
	private Tree tree;
	//##################################################################################
	private static ChiponDeleteWatchpointAction deleteWatchpointAction;
	private static ChiponAllDeleteWatchpointAction alldeleteWatchpointAction;
	private static ChiponAddWatchpointAction addWatchpointAction;
	private static ChiponSyhWatchpointAction syhWatchpointAction;
	//##################################################################################
	private static List<ChiponWatchpointData> watchDatas = new ArrayList<>();
	public Map<String,String> watchDataNames=new HashMap<>(); // 除重名方法
	
	//##################################################################################
	Text InputVar;
	Combo typelist;
	Combo lenlist;
	Button buttontype;
	
	//监控变量和地址段在下面tableviewer的信息中显示
	public static String vartypevar = Messages.ChiponWatchpointView_1;
	public static String vartypeaddr = Messages.ChiponWatchpointView_2;
	//##################################################################################
	public static int HavaSetINMCUWatchPointCount = 0;
	
	private File xmlFile=new File("watchpoint.xml");   // 这个设计建立了xml文件记录监控了 .... //$NON-NLS-1$
	
	/**
	 * 创建视图的实现   ，不能重构，否则优先创建视图，这个仅作为普通的方法
	 */
	public void createPartControl1(Composite parent) {
		// 基于父类实例化,系统集成代码		
		parent.setLayout(new GridLayout(1, true));
		parent.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, true,1,1));
		//############################设计输入接口
		Composite InterFaceComposite=new Composite(parent, SWT.FILL);
//		InterFaceComposite.setLayout(new GridLayout(1, false));
		//###################################################################
		buttontype = new Button(InterFaceComposite, SWT.CHECK);
		buttontype.setText(Messages.ChiponWatchpointView_4);
		buttontype.setBounds(5, 5, 120, 24);
		buttontype.setSelection(true);
		buttontype.addSelectionListener(new SelectionListener() {
			@Override
			public void widgetSelected(SelectionEvent e) {
				// TODO Auto-generated method stub
				if(buttontype.getSelection())
				{
					buttontype.setText(Messages.ChiponWatchpointView_4);
					lenlist.setText(Messages.ChiponWatchpointView_6);
					lenlist.setEnabled(false);
//					InputVar.setText("");// 人会选错，重新丢失信息也不合理
					InputVar.selectAll();
				}
				else
				{
					buttontype.setText(Messages.ChiponWatchpointView_7);
					lenlist.select(0);
					lenlist.setEnabled(true);
//					InputVar.setText(""); // 人会选错，重新丢失信息也不合理
					InputVar.selectAll();
				}
			}			
			@Override
			public void widgetDefaultSelected(SelectionEvent e) {
				// TODO Auto-generated method stub				
			}
		});
		
		InputVar=new Text(InterFaceComposite, SWT.BORDER);
		InputVar.setBounds(130, 5, 150, 24);
		InputVar.setText(""); //$NON-NLS-1$
		
		Label lb2=new Label(InterFaceComposite, SWT.NONE);
		lb2.setBounds(294, 5, 320, 24);
		lb2.setText(Messages.ChiponWatchpointView_9);
		//###################################################################
		new Label(InterFaceComposite, SWT.NONE);
		//###################################################################
		Label lb3=new Label(InterFaceComposite, SWT.NONE);
		lb3.setBounds(5, 40, 50, 24);
		lb3.setText(Messages.ChiponWatchpointView_10);
		
		typelist=new Combo(InterFaceComposite,  SWT.NONE|SWT.READ_ONLY);
		typelist.setBounds(65, 38, 90, 20);
		typelist.setItems(ChiponWatchpointData.types);
		typelist.select(0);
		
		Label lb4=new Label(InterFaceComposite, SWT.NONE);
		lb4.setBounds(165, 40, 100, 24);
		lb4.setText(Messages.ChiponWatchpointView_11);
		
		lenlist=new Combo(InterFaceComposite,  SWT.NONE);
		lenlist.setBounds(275, 38, 70, 20);
		lenlist.setItems(new String[]{"1","2","4","8","16","32","64","128","256","512","1024","2048"}); // 设备通信最大2K容量，多的话会缓存失败 //$NON-NLS-1$ //$NON-NLS-2$ //$NON-NLS-3$ //$NON-NLS-4$ //$NON-NLS-5$ //$NON-NLS-6$ //$NON-NLS-7$ //$NON-NLS-8$ //$NON-NLS-9$ //$NON-NLS-10$ //$NON-NLS-11$ //$NON-NLS-12$
//		lenlist.select(0);      // 1字节的绝对地址
		lenlist.setText(Messages.ChiponWatchpointView_24);
		lenlist.setEnabled(false);
		
//		节省可用空间，靠动作进行添加和删除，保留添加，动作操作比较隐晦
		Button buttonAdd=new Button(InterFaceComposite,  SWT.NONE);
		buttonAdd.setBounds(360, 39, 50, 30);
		buttonAdd.setText(Messages.ChiponWatchpointView_25);
		buttonAdd.addMouseListener(new MouseListener() {			
			@Override
			public void mouseUp(MouseEvent e) {
				// TODO Auto-generated method stub				
			}			
			@Override
			public void mouseDown(MouseEvent e) {
				// TODO Auto-generated method stub
				RunWayAddWatchPoint();
			}			
			@Override
			public void mouseDoubleClick(MouseEvent e) {
				// TODO Auto-generated method stub				
			}
		});
//		节省可用空间，靠动作进行删除	
//		Button buttonSub=new Button(InterFaceComposite,  SWT.NONE);
//		buttonSub.setBounds(200, 60, 50, 20);
//		buttonSub.setText("删除");
//		buttonSub.addMouseListener(new MouseListener() {			
//			@Override
//			public void mouseUp(MouseEvent e) {
//				// TODO Auto-generated method stub				
//			}			
//			@Override
//			public void mouseDown(MouseEvent e) {				
//				RunWayDelWatchPoint();				
//			}			
//			@Override
//			public void mouseDoubleClick(MouseEvent e) {
//				// TODO Auto-generated method stub				
//			}
//		});
		//###################################################################
	}
	
	public ChiponWatchpointData getUIWatchpointData()
	{

		String name = InputVar.getText().trim();    //可能为变量不能大小
		name=name.replace(" ", "");
		String Type = typelist.getText().trim();
		String len = lenlist.getText().trim();
		if(name==null || name.equalsIgnoreCase("")) //$NON-NLS-1$
		{
			return null;
		}
		ChiponWatchpointData watchpointDatabuf = new ChiponWatchpointData();
		//符号监控
		if(buttontype.getSelection())
		{
			char chr=name.charAt(0);
			if(chr>='0'&& chr <='9')
			{
				WriteMessage.getDefault().writeErrMessageAndOpenErrorDialog2(Messages.ChiponWatchpointView_4+":"+Messages.ChiponWatchpointView_47);//HandleError.
				return null;
			}
			watchpointDatabuf.setName(name);
			watchpointDatabuf.setType(Type);
			watchpointDatabuf.setMessage(vartypevar + name);
		}
		//地址监控
		else
		{
			char chr=name.charAt(0);
			if(chr<'0'|| chr >'9')
			{
				WriteMessage.getDefault().writeErrMessageAndOpenErrorDialog2(Messages.ChiponWatchpointView_4+":"+Messages.ChiponWatchpointView_47);//HandleError.
				return null;
			}
			if(name.startsWith("0x")||name.startsWith("0X")) //$NON-NLS-1$ //$NON-NLS-2$
			{
				name=name.substring(2).toUpperCase();
			}						
			int addmask=Integer.parseInt(len)-1;  // 1 2 4 8 16 32 -> 0 1 3 7 15 31	
			int bitslen=0;			
			while(bitslen < addmask )			{
				bitslen=bitslen*2+1;	 // 1 3 7 15 31   align to power2		length 5 -> 8 to find 7			
			}
			//#####################
			int startaddr=Integer.parseInt(name, 16);	
			startaddr=startaddr&(~bitslen);	 // 1 2 4 8 16  32-> 0 1 3 6 F 31 
			//#####################
			watchpointDatabuf.setName(Integer.toHexString(startaddr).toUpperCase());  //  name 调试器的获取需要进行对齐
			watchpointDatabuf.setType(Type);
			
			watchpointDatabuf.setMessage(vartypeaddr+Integer.toHexString(startaddr)+"~"+Integer.toHexString(startaddr+bitslen)); //$NON-NLS-1$
		}
		return watchpointDatabuf;
	}
	
	// 调试过程结束的触发：
	@Override
	public void debugContextChanged(final DebugContextEvent event) {
//		System.out.println("调试启动更新数据");
		new UIJob(getSite().getShell().getDisplay(), "watchpointFush") { //$NON-NLS-1$
			{
				setSystem(true);
			}

			@Override
			public IStatus runInUIThread(IProgressMonitor monitor) {
				if (getViewer() != null) { // runs asynchronously, view may be 				
					update(event.getContext());
				}
				return Status.OK_STATUS;
			}
		}.schedule();
		
	}	
	/**
	 * Updates the view for the selected thread (if suspended)
	 */

	private void update(ISelection context) {
		fThread = null;
		if (context instanceof IStructuredSelection) {
			Object element = ((IStructuredSelection) context).getFirstElement();
			if (element instanceof ChiponDebugTarget) {
				 fThread = ((ChiponDebugTarget)element).getCurrentThread();
			}
			if (element instanceof ChiponThread) {
				 fThread = (ChiponThread)element;
			} else if (element instanceof ChiponStackFrame) {
				fThread = (ChiponThread) ((ChiponStackFrame) element)
						.getThread();
			} else
				return;
		}
		IProject project=null;
		if(fThread!=null)
			project =((ChiponDebugTarget)fThread.getDebugTarget()).getfProject();
				
		if (fThread != null && fThread.isSuspended()&&fThread.isCanSetInputWatchpoint()) {
				fThread.watchpointDatas=watchDatas;  // 同步给进程，使能下次启动时进行初始监控点生效实现。
				fThread.setCanSetInputWatchpoint(false);
		}
		else {
				return;
		}
		//###############
		
		if (project != null && project.exists() ) {			
			RunWaySyhWatchPoint();			
		}
	}
	@Override
	public void dispose() {
		super.dispose();
		DebugUITools.getDebugContextManager().getContextService(getSite().getWorkbenchWindow()).removeDebugContextListener(this);
		
		// 过程表修改不能同步文件的最终进行文件同步，先删除后保存	
		watchpointXmlIO.deleteALL(xmlFile);
		
		for(ChiponWatchpointData cwpd:watchDatas)
			watchpointXmlIO.add(cwpd, xmlFile);
	}
	public List<ChiponWatchpointData> getWatchDatas() {
		return watchDatas;
	}
	
    @Override
    public void setFocus() {
        // 选择下的非项目和文件激活也支持的更新，此时才会提示用户保存输入信息

    }
    /**
     * 
     * @return
     */    
	@SuppressWarnings("static-access")
	public ChiponThread getfThread() {
		fThread = null;  // 可能换调试程序
		{
			ISelection selection=DebugUITools.getDebugContextManager().
					getContextService(getViewSite().getWorkbenchWindow()).getActiveContext();
			 if (selection instanceof IStructuredSelection) {
		         Object element = ((IStructuredSelection)selection).getFirstElement();
		         if(element instanceof ChiponDebugTarget){
		         	fThread = ((ChiponDebugTarget)element).getCurrentThread();
		         }
		         if (element instanceof ChiponThread) {
		         	fThread = (ChiponThread)element;
		         } else if (element instanceof ChiponStackFrame) {
		         	fThread = (ChiponThread) ((ChiponStackFrame) element).getThread();
		         }
			 }
		}
		// 获取为工作方法，仅状态下有效
		if(fThread != null &&(!fThread.isSuspended() || fThread.isTerminated()) ) // 仅挂起后，即芯片停止运行下才可操作
			return null;
		
		return fThread;
	}

	@Override
	protected Viewer createViewer(Composite parent) {
		//==============================================================================
		// 接口部分实现     treeviewer上面的一部分视图设计
		createPartControl1(parent);
		//#############################  必须重校绘图对象，不然上面的方法的绘制被刷新无效显示了。
		
		Composite treeviesComposite=new Composite(parent,  SWT.FILL);
		treeviesComposite.setLayout(new GridLayout(1, true));
		treeviesComposite.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, true,1,1));
		
		treeViewer = new TreeViewer(treeviesComposite, SWT.BORDER | SWT.FULL_SELECTION | SWT.MULTI );
//		treeViewer = new TreeViewer(parent, SWT.BORDER | SWT.FULL_SELECTION|SWT.MULTI);
		
		tree = treeViewer.getTree();
		tree.setLinesVisible(true);
		tree.setHeaderVisible(true);
		tree.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, true, 1, 1));
		tree.setBounds(0, 0, 85, 85);
		
		TableLayout tableLayout = new TableLayout();
		tree.setLayout(tableLayout);
		//------------------基于首元素确认行高的监听使整个表格适应-----------------------
		treeViewer.getTree().addListener(SWT.MeasureItem, new Listener() {
			   @Override
			public void handleEvent(Event event) 
			   {
			      // 事件行数据对象
				  Object obj = event.item.getData();
				  if(obj instanceof ChiponWatchpointData )
				  {
					  // 行对象转为的  GroupAddrCommData 变量
					  ChiponWatchpointData rd = (ChiponWatchpointData)obj;
					  // 非内容差异之间的实际代码
					  if(rd.getName()!=null && !rd.getName().equals("")) //$NON-NLS-1$
					  {
						  // 首个元素的指令结果，根据结果调整行高
						  String command = rd.getName();
						  GC gc = new GC(Display.getDefault());
						  int y = gc.stringExtent(command).y;
						  // 基于结果更新高度 ，额外多 xx个像素
						  event.height = y + 5;
						  gc.dispose();  
					  }
				  }
			   }
			});
		
		TreeColumn tcCol0 = new TreeColumn(tree, SWT.LEFT);
		tcCol0.setWidth(75); // 因为存在前面的空
		tcCol0.setResizable(true);
		tcCol0.setMoveable(false);
		tcCol0.setText(Messages.ChiponWatchpointView_32);
		
		TreeColumn tcCol1 = new TreeColumn(tree, SWT.LEFT);
		tcCol1.setWidth(180);
		tcCol1.setResizable(true);
		tcCol1.setMoveable(false);
		tcCol1.setText(Messages.ChiponWatchpointView_33);
		
		TreeColumn tcCol2 = new TreeColumn(tree, SWT.LEFT);
		tcCol2.setWidth(95);
		tcCol2.setResizable(true);
		tcCol2.setMoveable(false);
		tcCol2.setText(Messages.ChiponWatchpointView_34);
		
		TreeColumn tcCol3 = new TreeColumn(tree, SWT.LEFT);
		tcCol3.setWidth(160);
		tcCol3.setResizable(true);
		tcCol3.setMoveable(false);
		tcCol3.setText(Messages.ChiponWatchpointView_35);
		
		int count = treeViewer.getTree().getColumnCount();
		String[] properties = new String[count];
		for (int i = 0; i < count; ++i) {
			properties[i] = "column_" + i; //$NON-NLS-1$
		}
		treeViewer.setColumnProperties(properties);
		
		final CellEditor[]	cellEditors = new CellEditor[4];
		cellEditors[0] = new CheckboxCellEditor(tree) ;
		cellEditors[1] = new TextCellEditor(tree, SWT.BORDER);
		cellEditors[2] = new ComboBoxCellEditor(tree, ChiponWatchpointData.types, SWT.READ_ONLY);;
		cellEditors[3] = null;
		treeViewer.setCellEditors(cellEditors);


		treeViewer.setCellModifier(new ChiponWachpointCellModifier(treeViewer,this));		
		treeViewer.setLabelProvider(new ChiponWatchpointLableProvider());//设置标签器
		treeViewer.setContentProvider(new ChiponWatchpointContentProvider());//设置内容器
		
		// 获取记录的监控点信息
		watchDatas=watchpointXmlIO.readWatchpointData(xmlFile);
		for(ChiponWatchpointData cwcd :watchDatas)
			watchDataNames.put(cwcd.getName(), "have"); //$NON-NLS-1$
		// 如芯片进行同步实现
		ChiponThread fThread = getfThread();
		if(fThread !=null  ) {	
			ChiponInfoWatchpointCommandResult dataResult = (ChiponInfoWatchpointCommandResult) fThread.sendCommand(	new ChiponInfoWatchpointCommand());

			if(dataResult.resultText != "success"){ //$NON-NLS-1$
				
			}
			else
			{	
				List<ChiponWatchpointData> watchDatasRead=dataResult.watchDatas;
				for(ChiponWatchpointData cwcd:watchDatasRead)
				{
					if(watchDataNames.get(cwcd.getName())!=null)
					{// 当前有效监控点
						for(ChiponWatchpointData cwcd1:watchDatas)
						{
							if(cwcd.getName().equals(cwcd1.getName()))
							{
								cwcd1.setIsNowValue(true);
								break;
							}
						}
					}
				}
			}
		}
		
		// 设计到表显示
		treeViewer.setInput(watchDatas);
		treeViewer.refresh();	
		// 建立监听对象，并将对象追加到总的系统中
		DebugUITools.getDebugContextManager().getContextService(getSite().
				getWorkbenchWindow()).addDebugContextListener(this); 
		
		
		
		return treeViewer;
	}

	
	@Override
	protected void createActions() {
		ImageDescriptor syhImage = ImageKeys.getImageDescriptor(ImageKeys.IMG_TOOL_UPDATA);
		
		ImageDescriptor deleteImage = ImageKeys.getImageDescriptor(ImageKeys.IMG_TOOL_DELETE);
		ImageDescriptor addImage = ImageKeys.getImageDescriptor(ImageKeys.IMG_TOOL_ADD);
		
		syhWatchpointAction = new ChiponSyhWatchpointAction(this, Messages.ChiponWatchpointView_39, syhImage);
		syhWatchpointAction.setDisabledImageDescriptor(ImageKeys.getImageDescriptor(ImageKeys.IMG_TOOL_DISABLEUPDATA));
		syhWatchpointAction.setEnabled(true);

		addWatchpointAction = new ChiponAddWatchpointAction(this, Messages.ChiponWatchpointView_40, addImage);
		addWatchpointAction.setDisabledImageDescriptor(ImageKeys.getImageDescriptor(ImageKeys.IMG_TOOL_DISABLEADD));
		addWatchpointAction.setEnabled(true);
		
		//添加删除按钮
		deleteWatchpointAction = new ChiponDeleteWatchpointAction(this, Messages.ChiponWatchpointView_41, deleteImage);
		deleteWatchpointAction.setDisabledImageDescriptor(ImageKeys.getImageDescriptor(ImageKeys.IMG_TOOL_DISABLEDELETE));
		
		
		alldeleteWatchpointAction = new ChiponAllDeleteWatchpointAction(this, Messages.ChiponWatchpointView_42, deleteImage);
		
	}


	@Override
	protected String getHelpContextId() {
		return null;
	}

	// 右键菜单
	@Override
	protected void fillContextMenu(IMenuManager menu) {
		menu.add(addWatchpointAction);
		menu.add(new Separator(IWorkbenchActionConstants.MB_ADDITIONS));
		menu.add(deleteWatchpointAction);
		menu.add(new Separator(IWorkbenchActionConstants.MB_ADDITIONS));
		menu.add(alldeleteWatchpointAction);
		
	}

	// 工具栏上操作
	@Override
	protected void configureToolBar(IToolBarManager tbm) {
		
		tbm.add(syhWatchpointAction);
		syhWatchpointAction.setEnabled(true);	
		
		tbm.add(addWatchpointAction);
		tbm.add(deleteWatchpointAction);

		addWatchpointAction.setEnabled(true);		
		//默认不可点击
		deleteWatchpointAction.setEnabled(false);		
		getViewer().addSelectionChangedListener(new ISelectionChangedListener() {			
			@Override
			public void selectionChanged(SelectionChangedEvent event) {
				deleteWatchpointAction.setEnabled(!event.getSelection().isEmpty());
				
			}
		});
	}
	
	//添加监控点到treeviewer中 显示
    public void RunWayAddWatchPoint()
    {
		ChiponWatchpointData watchpointData = getUIWatchpointData();
		if(watchpointData==null || watchDataNames.get(watchpointData.getName())!=null)  // 重名的否决
		{
			boolean iscanadd=false;
//			IStructuredSelection selection = 
//					(IStructuredSelection)treeViewer.getSelection();
//
//				ChiponWatchpointData[] items = 	new ChiponWatchpointData[selection.size()];
//				Iterator iter = selection.iterator();
//				int index = 0;
//				while(iter.hasNext()){
//					items[index++] = (ChiponWatchpointData)iter.next();					
//				}	
//			for(ChiponWatchpointData cwdata: items){}
			Object input = treeViewer.getInput();
			if(input!=null && input instanceof List<?> ){
				@SuppressWarnings("unchecked")
				List<ChiponWatchpointData> inputs= (List<ChiponWatchpointData>)input ;
				if(inputs!=null && inputs.size()>0)				
					for(int i=0;i< inputs.size();i++){
						ChiponWatchpointData cwdata=inputs.get(i);
							if(cwdata.getName().endsWith(watchpointData.getName()) && !cwdata.isNowValue()){	
								List<ChiponWatchpointData> watchpointdata=new ArrayList<>();
								watchpointdata.add(cwdata);
								watchDataNames.remove(cwdata.getName());
								watchpointXmlIO.delete(watchpointdata , xmlFile);
								watchDatas.remove(cwdata);		
								iscanadd=true;
							}
					}
			}
			if(iscanadd==false)
				return;
		}
		// 是否下发
		ChiponThread fThread = getfThread();
		if(fThread !=null  ) {
			//进行查看，是否存在
			String cstr = watchpointData.getName();
			ChiponCommandResult dataResult = null;
			if(watchpointData.getMessage().contains(Messages.ChiponWatchpointView_43))
			{
//				cstr= cstr;
				dataResult = (ChiponWatchCommandResult) fThread.sendCommand(
							new ChiponWatchCommand(cstr));
			}
			if(watchpointData.getMessage().contains(Messages.ChiponWatchpointView_44))
			{
				cstr = "x /1b "+Long.parseLong(cstr, 16);    //cstr; //$NON-NLS-1$
				dataResult = (ChiponCommandResult) fThread.sendCommand(
						new ChiponCustomCommand(cstr));
			}
			//#############
			try {
				if(dataResult.resultText != "success"){ //$NON-NLS-1$
					WriteMessage.getDefault().writeErrMessageAndOpenErrorDialog2(Messages.ChiponWatchpointView_47);//HandleError.getDefault().handleMessage(dataResult.errorCode);
					return;
				}
				// 存在进行下发实验，通过会更新有效状态的
				watchpointData.setWatchPoint(fThread);
				
				}catch (Exception e2) {	}
		}
		// 未下发也会添加到列表的
		{
			watchDatas.add(watchpointData);
			watchDataNames.put(watchpointData.getName(), "OK"); //$NON-NLS-1$
			watchpointXmlIO.add(watchpointData, xmlFile);
			
			treeViewer.setInput(watchDatas);
			treeViewer.refresh();
		}
    }
    
    public void RunWaySyhWatchPoint()
	{		
			ChiponThread fThread = getfThread();
			if(fThread !=null  ) {
				//有效同步
				ChiponInfoWatchpointCommandResult dataResult = (ChiponInfoWatchpointCommandResult) fThread.sendCommand(
						new ChiponInfoWatchpointCommand());
	
				if(dataResult.resultText != "success"){ //$NON-NLS-1$
				}
				else
				{
					// 同步执行
						List<ChiponWatchpointData> watchDatasRead=dataResult.watchDatas;
						List<ChiponWatchpointData> watchDatasleft=new ArrayList<>();
						
						for(ChiponWatchpointData cwcd:watchDatasRead)
						{
							watchDatasleft.add(cwcd);
						}
						for(ChiponWatchpointData cwcd:watchDatas)
						{
							cwcd.setIsNowValue(false);
						}
						// 发现记录时
						for(ChiponWatchpointData cwcd:watchDatasRead){
							if(watchDataNames.get(cwcd.getName())!=null)
							{// 当前有效监控点
								for(ChiponWatchpointData cwcd1:watchDatas)
								{									
									if(cwcd.getName().equals(cwcd1.getName()))
									{
										cwcd1.setIsNowValue(true);	
										cwcd1.setId(cwcd.getId());
										watchDatasleft.remove(cwcd);
										break;
									}
								}
							}
						}
						// 非同步的不在列表中的追加
						for(ChiponWatchpointData cwcd:watchDatasleft)
						{
							watchDatas.add(cwcd);
							watchDataNames.put(cwcd.getName(), "OK"); //$NON-NLS-1$
							watchpointXmlIO.add(cwcd, xmlFile);
						}
						// 
						treeViewer.setInput(watchDatas);
						treeViewer.refresh();
				}
			}	
	}
    
    public void RunWayDelWatchPoint()
	{
		IStructuredSelection selection = 
				(IStructuredSelection)treeViewer.getSelection();
			// 选择的对象数量
			ChiponWatchpointData[] items = 	new ChiponWatchpointData[selection.size()];
			// 基于选择的对象变量传递值
			Iterator iter = selection.iterator();
			int index = 0;
			while(iter.hasNext()){
				items[index++] = (ChiponWatchpointData)iter.next();					
			}
			// 是否下发
			ChiponThread fThread = getfThread();

			//逐个删除监控点
			for(ChiponWatchpointData cwdata: items){	
				if(fThread !=null  ) {					
					if(cwdata.isNowValue())
						cwdata.deleteWatchpoint(fThread);					
				}
				List<ChiponWatchpointData> watchpointdata=new ArrayList<>();
				watchpointdata.add(cwdata);
				watchDataNames.remove(cwdata.getName());
				watchpointXmlIO.delete(watchpointdata , xmlFile);
				watchDatas.remove(cwdata);
			}
			// 
			treeViewer.setInput(watchDatas);
			treeViewer.refresh();		
	}
    
	//清空动作
	public class ChiponAllDeleteWatchpointAction extends Action {
		private ChiponWatchpointView view;
		
		public ChiponAllDeleteWatchpointAction(ChiponWatchpointView view, String text, ImageDescriptor imageDescriptor) {
			super(text, imageDescriptor);
			this.view = view;
		}
		
		@Override
		public void run() {
				// 可解决不同步下的监控点移除
			// 是否下发
				ChiponThread fThread = getfThread();
				if(fThread !=null  ) {	
					ChiponInfoWatchpointCommandResult dataResult = (ChiponInfoWatchpointCommandResult) fThread.sendCommand(
							new ChiponInfoWatchpointCommand());
		
						if(dataResult.resultText != "success"){ //$NON-NLS-1$
						}
						else
						{
							// 解析并删除
							List<ChiponWatchpointData> watchDatasRead=dataResult.watchDatas;
							for(ChiponWatchpointData cwcd:watchDatasRead)
							{
								cwcd.deleteWatchpoint(fThread);
							}
						}
				}
				// 对象操作
				watchDatas.removeAll(watchDatas);
				watchDataNames.clear();
				watchpointXmlIO.deleteALL(xmlFile);
				view.getViewer().setInput(watchDatas);
				view.getViewer().refresh();				
		}
	}
	
	// 删除动作
	public class ChiponDeleteWatchpointAction extends Action {
		private ChiponWatchpointView view;
		
		public ChiponDeleteWatchpointAction(ChiponWatchpointView view, String text, ImageDescriptor imageDescriptor) {
			super(text, imageDescriptor);
			this.view = view;
		}
		
		@Override
		public void run() {			
			RunWayDelWatchPoint();			
		}
	}
	// 添加动作
	public class ChiponAddWatchpointAction extends Action {
		
		private ChiponWatchpointView view;
		private ChiponThread fThread;
		
		public ChiponAddWatchpointAction(ChiponWatchpointView view, String text, ImageDescriptor imageDescriptor) {
			super(text, imageDescriptor);
			this.view = view;
		}
		
		@Override
		public void run() {//			
			RunWayAddWatchPoint();
		}
	}
	
	// 同步动作，解析非事件的   与芯片一致识别
	public class ChiponSyhWatchpointAction extends Action {
		
		private ChiponWatchpointView view;
		private ChiponThread fThread;
		
		public ChiponSyhWatchpointAction(ChiponWatchpointView view, String text, ImageDescriptor imageDescriptor) {
			super(text, imageDescriptor);
			this.view = view;
		}
		
		@Override
		public void run() {//			
			RunWaySyhWatchPoint();
		}
	}
}
