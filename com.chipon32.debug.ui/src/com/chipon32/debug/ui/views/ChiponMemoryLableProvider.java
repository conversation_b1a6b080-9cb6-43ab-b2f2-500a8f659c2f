package com.chipon32.debug.ui.views;


import org.eclipse.jface.viewers.IColorProvider;
import org.eclipse.jface.viewers.ITableLabelProvider;
import org.eclipse.jface.viewers.LabelProvider;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.graphics.Image;

import com.chipon32.debug.core.model.ChiponGroupRomData;


/**
 * <AUTHOR> @since 
 */
public class ChiponMemoryLableProvider extends LabelProvider implements ITableLabelProvider,IColorProvider{
	public String showformat="hex8";
	private Color color;
	
	@Override
	public Image getColumnImage(Object element, int columnIndex) {
		return null;
	}
	@Override
	public String getColumnText(Object element, int columnIndex) {
		if(element instanceof ChiponGroupRomData){
			ChiponGroupRomData groupRomData = (ChiponGroupRomData) element;
			int size = groupRomData.getRomDatas().size();
			switch(columnIndex){
			case 0:
				color = null;
				if(groupRomData.isIs_virtual_lineGroup())
					return "" + groupRomData.getRowNum().toUpperCase();
				else
					return "0x" + groupRomData.getRowNum().toUpperCase();
			case 17:
				color = null;
				return groupRomData.getAscii();
			default:
				if(columnIndex-1 < size){
					if(groupRomData.isIs_virtual_lineGroup())
						color = null;
					else
						color = groupRomData.getRomDatas().get(columnIndex-1).getColor();
					
					if(groupRomData.isIs_virtual_lineGroup())
						return groupRomData.getRomDatas().get(columnIndex-1).getOrgValue();
					else
					{
						columnIndex =columnIndex-1 ; // 0-15
						String valueOut="";
						//valueOut = groupRomData.getRomDatas().get(columnIndex).getValue().toUpperCase();
						
						int onelength =ChiponMemoryServiceClass.getFormatBytes(showformat);
						if(columnIndex%onelength ==0)
						{
							// tail is not align to no show
							if( (columnIndex + onelength) > groupRomData.getRomDatas().size())
								return "??";
							// ok
							for(int i=0; i<onelength;i++ )
							{
								valueOut = groupRomData.getRomDatas().get(columnIndex+i).getValue().toUpperCase() + valueOut;
							}
						}
						else
						{
							return "*";
						}
						
						//------------------------
						return ChiponMemoryServiceClass.ValueStringCovGet(showformat,valueOut);
					}
				}
				return "--";
			}
		}
		return null;
	}

	@Override
	public Color getForeground(Object element) {
		
		return color;
	}
	
	int i=1;
	String oldColumnNum=null;
	
	@Override
	public Color getBackground(Object element) {
		Color color=null;
		if(element instanceof ChiponGroupRomData){
			ChiponGroupRomData groupRomData = (ChiponGroupRomData)element;
			String columnNum = groupRomData.getRowNum();
			if(oldColumnNum!=null&&oldColumnNum!=columnNum){
				i++;
			}
			if(i%2==0){
				color=IChiponColor.GRAY_COLOR;
			}
			oldColumnNum=columnNum;
		}
	//	System.out.println(i);
	//	i++;
		return color;
	}

}
