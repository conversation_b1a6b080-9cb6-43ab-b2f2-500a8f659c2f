package com.chipon32.debug.ui.views;

import org.eclipse.jface.viewers.IColorProvider;
import org.eclipse.jface.viewers.ITableLabelProvider;
import org.eclipse.jface.viewers.LabelProvider;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.graphics.Image;

import com.chipon32.debug.core.model.ChiponGroupEEpromData;

/***
 *<AUTHOR>
 *2013-6-20обнГ1:07:09
 ***/
public class ChiponEEPROMLableProvider extends LabelProvider implements
		ITableLabelProvider ,IColorProvider{

	private Color color;
	@Override
	public Image getColumnImage(Object element, int columnIndex) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public String getColumnText(Object element, int columnIndex) {
		if(element instanceof ChiponGroupEEpromData){
			ChiponGroupEEpromData groupEEData = (ChiponGroupEEpromData) element;
			int size = groupEEData.getEEpromDatas().size();
			switch(columnIndex){
			case 0:
				color = null;
				return groupEEData.getRowNum().toUpperCase();
			case 17:
				color = null;
				return groupEEData.getAscii();
			default:
				if(columnIndex-1 < size){
					color = groupEEData.getEEpromDatas().get(columnIndex-1).getColor();
					return groupEEData.getEEpromDatas().get(columnIndex-1).getValue().toUpperCase();
				}
				return "--";
			}
		}
		return null;
	}

	@Override
	public Color getForeground(Object element) {
		// TODO Auto-generated method stub
		return color;
	}
	
	int i=1;
	String oldRowNum=null;

	@Override
	public Color getBackground(Object element) {
		Color color=null;
		if(element instanceof ChiponGroupEEpromData){
			ChiponGroupEEpromData groupEEData=(ChiponGroupEEpromData)element;
			String rowNum = groupEEData.getRowNum();
			if(oldRowNum!=null&&oldRowNum!=rowNum){
				i++;
			}
			if(i%2==0){
				color=IChiponColor.GRAY_COLOR;
			}
			oldRowNum=rowNum;
			
		}
	//	System.out.println(i);
	//	i++;
		return color;
	}

}
