package com.chipon32.debug.ui.views;

import org.eclipse.jface.viewers.BaseLabelProvider;
import org.eclipse.jface.viewers.IColorProvider;
import org.eclipse.jface.viewers.ITableLabelProvider;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.graphics.Image;
import org.eclipse.swt.widgets.Display;

import com.chipon32.chiponide.core.chipondescription.IEntry;
import com.chipon32.chiponide.core.chipondescription.chipio.Module;
import com.chipon32.chiponide.core.chipondescription.chipio.Register;
import com.chipon32.debug.ui.DebugUIActivator;



/**
 * <AUTHOR> @since 2013-6-21 下午2:11:57
 */
public class ChiponRegistersLabelProvider extends BaseLabelProvider implements ITableLabelProvider,IColorProvider{
	private Color color;
	private static final Image MOD_IMAGE = new Image(Display.getDefault(), DebugUIActivator.getFilePathFromPlugin("sfr.gif"));
	private static final Image SFR_IMAGE = new Image(Display.getDefault(), DebugUIActivator.getFilePathFromPlugin("sfr.gif"));
	private static final Image BIT_IMAGE = new Image(Display.getDefault(), DebugUIActivator.getFilePathFromPlugin("bit.gif"));
	
	@Override
	public Image getColumnImage(Object element, int columnIndex) {
		if(columnIndex == 0){
			if(element instanceof Module){
				return MOD_IMAGE;
			}
			if(element instanceof Register){
				return SFR_IMAGE;
			}
			return BIT_IMAGE;
		}
		return null;
	}

	@Override
	public String getColumnText(Object element, int columnIndex) {
		if (element instanceof IEntry) {
			switch(columnIndex){
			case 0:
				color = null;
				return ((IEntry)element).getColumnData(columnIndex);//寄存器名
			case 1:
				color = ((IEntry)element).getfColor();
				if(((IEntry)element).getValue()!=null){
					return ((IEntry)element).getValue().toUpperCase();//值
				}
				return "";
			case 2:
				color = null;
				return ((IEntry)element).getColumnData(columnIndex);//地址，因为以前设计的地址在第三位，所以需要加1
			case 3 :
				color = null;
				return ((IEntry)element).getColumnData(columnIndex);
			/*case 4:
				color = ((IEntry)element).getfColor();
				if(((IEntry)element).getValue()!=null){
					return ((IEntry)element).getValue().toUpperCase();//值
				}
				return "";*/
			default:
				return "--";
			}
		}
		return null;
	}
	

	@Override
	public Color getForeground(Object element) {
		return color;
	}

	@Override
	public Color getBackground(Object element) {
		return null;
	}

}
