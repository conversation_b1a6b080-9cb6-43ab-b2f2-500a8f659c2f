package com.chipon32.debug.ui.views;


import java.util.List;

import org.eclipse.jface.viewers.ITreeContentProvider;
import org.eclipse.jface.viewers.Viewer;

import com.chipon32.debug.core.model.ChiponCurrencyRegisterData;



/**
 * <AUTHOR> @since 2013-6-4 обнГ4:13:36
 */
public class ChiponCurrencyRegisterContentProvider implements ITreeContentProvider{

	@Override
	public void dispose() {

	}

	@Override
	public void inputChanged(Viewer viewer, Object oldInput, Object newInput) {
		// TODO Auto-generated method stub
		
	}

	@Override
	public Object[] getElements(Object inputElement) {
//		System.out.println(11);
		if(inputElement instanceof List){
			return ((List) inputElement).toArray();
		}
		return null;
	}

	@Override
	public Object[] getChildren(Object parentElement) {
		
		if(parentElement instanceof ChiponCurrencyRegisterData){
			return ((ChiponCurrencyRegisterData)parentElement).getChiponDataList().toArray();
		}
		return null;
	}

	@Override
	public Object getParent(Object element) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public boolean hasChildren(Object element) {
		if(element instanceof ChiponCurrencyRegisterData){
			ChiponCurrencyRegisterData chiponData=(ChiponCurrencyRegisterData)element;
			if(chiponData.getChiponDataList()!=null && chiponData.getChiponDataList().size()>0)
				return true;
		}
		return false;
	}

	
}
