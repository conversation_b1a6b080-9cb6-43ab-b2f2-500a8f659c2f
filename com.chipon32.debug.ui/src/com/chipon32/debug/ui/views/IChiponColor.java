package com.chipon32.debug.ui.views;

import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.widgets.Display;

/**
 * 视图的颜色
 * <AUTHOR> @since 2013-6-26 上午11:10:45
 */
public interface IChiponColor {
	public static final Color DEFAULT_COLOR = new Color(Display.getDefault(), 0,0,0);//值未变时显示为黑色
	public static final Color RED_COLOR = new Color(Display.getDefault(), 255,0,0);//值改变时显示为红色
	public static final Color GRAY_COLOR = new Color(Display.getDefault(), 245,245,245);//值改变时显示为灰色
}
