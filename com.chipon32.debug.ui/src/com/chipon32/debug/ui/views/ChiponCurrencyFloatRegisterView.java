package com.chipon32.debug.ui.views;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.eclipse.core.resources.IProject;
import org.eclipse.core.runtime.IProgressMonitor;
import org.eclipse.core.runtime.IStatus;
import org.eclipse.core.runtime.Status;
import org.eclipse.debug.ui.AbstractDebugView;
import org.eclipse.debug.ui.DebugUITools;
import org.eclipse.debug.ui.contexts.DebugContextEvent;
import org.eclipse.debug.ui.contexts.IDebugContextListener;
import org.eclipse.jface.action.IMenuManager;
import org.eclipse.jface.action.IToolBarManager;
import org.eclipse.jface.viewers.AbstractTreeViewer;
import org.eclipse.jface.viewers.CellEditor;
import org.eclipse.jface.viewers.ISelection;
import org.eclipse.jface.viewers.IStructuredSelection;
import org.eclipse.jface.viewers.TextCellEditor;
import org.eclipse.jface.viewers.TreeViewer;
import org.eclipse.jface.viewers.Viewer;
import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.FontData;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.TreeColumn;
import org.eclipse.ui.progress.UIJob;

import com.chipon32.chiponide.core.properties.ChipOnProjectProperties;
import com.chipon32.chiponide.core.properties.ProjectPropertyManager;
import com.chipon32.debug.core.model.ChiponCurrencyFloatRegisterData;
import com.chipon32.debug.core.model.ChiponDebugTarget;
import com.chipon32.debug.core.model.ChiponStackFrame;
import com.chipon32.debug.core.model.ChiponThread;
import com.chipon32.debug.core.protocol.ChiponPrintCurrencyFloatRegisterCommand;
import com.chipon32.debug.core.protocol.ChiponPrintCurrencyFloatRegisterCommandResult;
import com.chipon32.hex.core.util.IConfigurationProvider;
import com.chipon32.util.provider.ConfigurationFactory;

import com.chipon32.util.ui.UIUtil;

public class ChiponCurrencyFloatRegisterView extends AbstractDebugView 
	implements IDebugContextListener{
	
	public static final String ID = "com.chipon32.debug.ui.view.currencyFloatRegisterView"; //$NON-NLS-1$

	public ChiponCurrencyFloatRegisterView() {}
	
	private ChiponThread fThread;
	
	private void setInput(TreeViewer tv){
//		update(tv.getSelection());   //2017.1.11为了测试添加
		if(tv != null){
			List<ChiponCurrencyFloatRegisterData> dataList = ChiponThread.fDataList;
			if(dataList != null && dataList.size()>0){
				IProject project=null;
				if(fThread!=null)
				 project =((ChiponDebugTarget)fThread.getDebugTarget()).getfProject();				
				if (project != null && dataList != null) {		
					// 这里没啥作用，还有一个贯彻者会执行一次，也要保证那次被转换
					dataList = ListChiponCurrencyRegisterDataReStroe(dataList, project);  //List..ReStroe中的dataLists
									
					tv.setInput(dataList);
//					tv.expandAll();
					tv.expandToLevel(2);
					tv.refresh();
				}
			}else {
				getSite().setSelectionProvider(tv);
			}
		}
	}

	/**
	 * 在调试前若视图是开启的状态，则每次暂停时调用debugContextChanged()方法更新数据。
	 */
	@Override
	public void debugContextChanged(final DebugContextEvent event) {
		new UIJob(getSite().getShell().getDisplay(), Messages.ChiponCurrencyRegisterView_1) {
			{
				setSystem(true);
			}

			@Override
			public IStatus runInUIThread(IProgressMonitor monitor) {
				if (getViewer() != null) { // runs asynchronously, view may be
											// disposed
					update(event.getContext());
				}
				return Status.OK_STATUS;
			}
		}.schedule();

	}
	/**
	 * Updates the view for the selected thread (if suspended)
	 */

	public void update(ISelection context) {
		fThread = null;
		if (context instanceof IStructuredSelection) {
			Object element = ((IStructuredSelection) context).getFirstElement();
			if (element instanceof ChiponDebugTarget) {
				 fThread = ((ChiponDebugTarget)element).getCurrentThread();
			}
			if (element instanceof ChiponThread) {
				 fThread = (ChiponThread)element;
			} else if (element instanceof ChiponStackFrame) {
				fThread = (ChiponThread) ((ChiponStackFrame) element).getThread();
			} else
				return;
		}
		IProject project = null; 
		if(fThread!=null)
		 project = ((ChiponDebugTarget)fThread.getDebugTarget()).getfProject();
		if(project!=null)		{
			ProjectPropertyManager projpropsmanager = ProjectPropertyManager.getPropertyManager(project);
			if(projpropsmanager!=null){
			ChipOnProjectProperties props = projpropsmanager.getProjectProperties();
			if(props!=null)		{
			IConfigurationProvider provider = ConfigurationFactory.getProvider(props.getChipName());
			if(provider!=null)	{
				if(Integer.parseInt(provider.getIsSupportFPU(),16)== 0)
					return ;
		}}}}		
		List<ChiponCurrencyFloatRegisterData> dataList = null;
		
		if (fThread != null && fThread.isSuspended()&&fThread.isCanSetInputCurrencyFloatRegister()) {
			//获取通用寄存器R0~R31的值
			ChiponPrintCurrencyFloatRegisterCommandResult currencyRegisterResult = 
						(ChiponPrintCurrencyFloatRegisterCommandResult) fThread.sendCommand(	new ChiponPrintCurrencyFloatRegisterCommand("info registers float")); //$NON-NLS-1$
			if(currencyRegisterResult != null){
				ChiponPrintCurrencyFloatRegisterCommandResult currencyRegisterResult2 = 
						(ChiponPrintCurrencyFloatRegisterCommandResult) fThread.sendCommand(	new ChiponPrintCurrencyFloatRegisterCommand("info registers FxPSR"));
					if(currencyRegisterResult2!=null)	{
						currencyRegisterResult.dataList.addAll(currencyRegisterResult2.dataList);
					}
				ChiponThread.fDataList = currencyRegisterResult.dataList;
				dataList=currencyRegisterResult.dataList;
				fThread.setUpdateCurFloatResigter(true);
			}
			fThread.setCanSetInputCurrencyFloatRegister(false);
		}
		else {
			return;
		}
		//###############
		
		if (project != null && dataList != null) {
			//##########################
			dataList = ListChiponCurrencyRegisterDataReStroe(dataList,project);
			//##########################
			Viewer viewer = getViewer();
			
			if(viewer instanceof AbstractTreeViewer){
				AbstractTreeViewer treeviewer = (AbstractTreeViewer)viewer;
				
				treeviewer.setInput(dataList);
//				treeviewer.expandAll();
				treeviewer.expandToLevel(2); // 3 with bit 
				treeviewer.refresh();
			}
		}
	}
	
	@Override
	protected Viewer createViewer(Composite parent) {
		
		TreeViewer tv = new TreeViewer(parent, SWT.BORDER | SWT.FULL_SELECTION);
		tv.setContentProvider(new ChiponCurrencyFloatRegisterContentProvider());
		tv.setLabelProvider(new ChiponCurrencyFloatRegisterLableProvider());
		tv.getTree().setLayoutData(new GridData(GridData.FILL_BOTH));
		tv.getTree().setHeaderVisible(true);
		tv.getTree().setLinesVisible(true);
		
		FontData curfd[] = tv.getTree().getFont()
				.getFontData();
		// 字体高度
		int approxfontwidth = curfd[0].getHeight();
		// 存在子集 ，宽点
		TreeColumn tcName = new TreeColumn( tv.getTree(),SWT.LEFT);
		tcName.setWidth(13 * approxfontwidth);
		tcName.setResizable(true);
		tcName.setMoveable(true);
		tcName.setText(Messages.ChiponCurrencyRegisterView_4);
		
		// 十六进制，最多32个字符   ACC，给出默认17个字符的空间
		TreeColumn tcValue = new TreeColumn( tv.getTree(),SWT.RIGHT);
		tcValue.setWidth(8 * approxfontwidth);
		tcValue.setResizable(true);
		tcValue.setMoveable(true);
		tcValue.setText(Messages.ChiponCurrencyRegisterView_5);
		
		// 十进制，32bit 可能宽点 靠人拉吧，空间也不能占的太多
		TreeColumn tdValue = new TreeColumn( tv.getTree(),SWT.RIGHT);
		tdValue.setWidth(12 * approxfontwidth);
		tdValue.setResizable(true);
		tdValue.setMoveable(true);
		tdValue.setText(Messages.ChiponCurrencyRegisterView_6);
		
		//数据修改
		UIUtil.setDefaultProperties(tv);
		CellEditor[] cellEditors = new CellEditor[3];
		cellEditors[0] = null;
		cellEditors[1] = new TextCellEditor(tv.getTree(), SWT.BORDER);
		cellEditors[2] = new TextCellEditor(tv.getTree(), SWT.BORDER);
		tv.setCellEditors(cellEditors);
		tv.setCellModifier(new ChiponCurrentFloatRegisterCellModifier(tv));

		DebugUITools.getDebugContextManager().getContextService(getSite().
				getWorkbenchWindow()).addDebugContextListener(this);
		setInput(tv);  //treeViewer中加载通用寄存器的值
		return tv;
	}

	@Override
	protected void createActions() {
		// TODO Auto-generated method stub
	}
	
	//？？
	public static  List<ChiponCurrencyFloatRegisterData> ListChiponCurrencyRegisterDataReStroe(List<ChiponCurrencyFloatRegisterData> dataList,IProject project)
	{
		if(dataList==null || dataList.size()==0)
			return null;
		//############################合并处理分组
		ProjectPropertyManager ppm = ProjectPropertyManager.getPropertyManager(project);
		ChipOnProjectProperties fTargetProps = ppm.getProjectProperties();  //包含：执行方式，芯片名称、电压、调试模式、加密方式等等信息
		String chipname = fTargetProps.getChipName();// 获取当前项目的芯片型号
		IConfigurationProvider provider = ConfigurationFactory.getProvider(chipname);
		if(Integer.parseInt(provider.getIsSupportFPU(),16)==0) return null;
		// 这个根的首个对象不关联这个父类，而自成父类，便于后续修改方法编写
		ChiponCurrencyFloatRegisterData coredataList = 		new ChiponCurrencyFloatRegisterData("Group", "", "");		coredataList.setFlag(true); //$NON-NLS-1$ //$NON-NLS-2$ //$NON-NLS-3$
		ChiponCurrencyFloatRegisterData accdataList = 		new ChiponCurrencyFloatRegisterData("Group3", "", "");		accdataList.setFlag(true); //$NON-NLS-1$ //$NON-NLS-2$ //$NON-NLS-3$
		ChiponCurrencyFloatRegisterData highrxdataList = 	new ChiponCurrencyFloatRegisterData("Group2", "", "");		highrxdataList.setFlag(true); //$NON-NLS-1$ //$NON-NLS-2$ //$NON-NLS-3$

		List<ChiponCurrencyFloatRegisterData> dataLists = new ArrayList<ChiponCurrencyFloatRegisterData>();
		@SuppressWarnings("rawtypes")
		Map<String ,ChiponCurrencyFloatRegisterData> MapR16TR31 = new HashMap<>();
		//##############################################################
		
		for(ChiponCurrencyFloatRegisterData ccr:dataList)
		{
			String   regName= ccr.getName();
			String[] preName = regName.split(":?[0-9]+");				 //$NON-NLS-1$
			if(preName[0].equalsIgnoreCase("s")) //$NON-NLS-1$
			{	
				int regnum=Integer.parseInt(regName.substring(1));
				MapR16TR31.put(regName.toLowerCase(), ccr);
				if(regnum>15)
					highrxdataList.AddChild(ccr);	
				else
					coredataList.AddChild(ccr);
			}
			else if(preName[0].equalsIgnoreCase("sacc")) //$NON-NLS-1$
			{
				accdataList.AddChild(ccr);
			}
			else 	if(regName.equalsIgnoreCase("fxpsr"))						 //$NON-NLS-1$
				{
					coredataList.AddChild(ccr);	//		ccr.setFlag(true);  // can not write ,showdon from sys
					// 细分
					List<ChiponCurrencyFloatRegisterData> chiponDatasChildren=new ArrayList<ChiponCurrencyFloatRegisterData>();
					long registervalue=Long.parseLong(ccr.getdValue());
					long bitValue;
					ChiponCurrencyFloatRegisterData chiponData;
					//################################
						bitValue = ParseBitFromValue(registervalue,31,1);
						 chiponData=new ChiponCurrencyFloatRegisterData("FN", Long.toHexString(bitValue), Long.toString(bitValue)); //$NON-NLS-1$					
						chiponData.setParentRegisterData(ccr);
						chiponData.setHasParent(true);chiponData.setFlag(true); // 这个继承不允许修改
						chiponData.setBitLength(1);
						chiponData.setBitLocation(Integer.toString(31));							
					chiponDatasChildren.add(chiponData);
					//################################
						bitValue = ParseBitFromValue(registervalue,30,1);
						 chiponData=new ChiponCurrencyFloatRegisterData("FZ", Long.toHexString(bitValue), Long.toString(bitValue)); //$NON-NLS-1$					
						chiponData.setParentRegisterData(ccr);
						chiponData.setHasParent(true);chiponData.setFlag(true); // 这个继承不允许修改
						chiponData.setBitLength(1);
						chiponData.setBitLocation(Integer.toString(30));							
					chiponDatasChildren.add(chiponData);
					//################################
						bitValue = ParseBitFromValue(registervalue,29,1);
						 chiponData=new ChiponCurrencyFloatRegisterData("FC", Long.toHexString(bitValue), Long.toString(bitValue)); //$NON-NLS-1$					
						chiponData.setParentRegisterData(ccr);
						chiponData.setHasParent(true);chiponData.setFlag(true); // 这个继承不允许修改
						chiponData.setBitLength(1);
						chiponData.setBitLocation(Integer.toString(29));							
					chiponDatasChildren.add(chiponData);
					//################################		
						bitValue = ParseBitFromValue(registervalue,28,1);
						 chiponData=new ChiponCurrencyFloatRegisterData("FV", Long.toHexString(bitValue), Long.toString(bitValue)); //$NON-NLS-1$					
						chiponData.setParentRegisterData(ccr);
						chiponData.setHasParent(true);chiponData.setFlag(true); // 这个继承不允许修改
						chiponData.setBitLength(1);
						chiponData.setBitLocation(Integer.toString(28));							
					chiponDatasChildren.add(chiponData);
					//################################
						bitValue = ParseBitFromValue(registervalue,25,3);
						 chiponData=new ChiponCurrencyFloatRegisterData("RMODE", Long.toHexString(bitValue), Long.toString(bitValue)); //$NON-NLS-1$					
						chiponData.setParentRegisterData(ccr);
						chiponData.setHasParent(true);chiponData.setFlag(true); // 这个继承不允许修改
						chiponData.setBitLength(3);
						chiponData.setBitLocation(Integer.toString(25));							
					chiponDatasChildren.add(chiponData);
					//################################
						bitValue = ParseBitFromValue(registervalue,24,1);
						 chiponData=new ChiponCurrencyFloatRegisterData("IXC", Long.toHexString(bitValue), Long.toString(bitValue)); //$NON-NLS-1$					
						chiponData.setParentRegisterData(ccr);
						chiponData.setHasParent(true);chiponData.setFlag(true); // 这个继承不允许修改
						chiponData.setBitLength(1);
						chiponData.setBitLocation(Integer.toString(24));							
					chiponDatasChildren.add(chiponData);
					//################################
						bitValue = ParseBitFromValue(registervalue,23,1);
						 chiponData=new ChiponCurrencyFloatRegisterData("UFC", Long.toHexString(bitValue), Long.toString(bitValue)); //$NON-NLS-1$					
						chiponData.setParentRegisterData(ccr);
						chiponData.setHasParent(true);chiponData.setFlag(true); // 这个继承不允许修改
						chiponData.setBitLength(1);
						chiponData.setBitLocation(Integer.toString(23));							
					chiponDatasChildren.add(chiponData);
					//################################
						bitValue = ParseBitFromValue(registervalue,22,1);
						 chiponData=new ChiponCurrencyFloatRegisterData("OFC", Long.toHexString(bitValue), Long.toString(bitValue)); //$NON-NLS-1$					
						chiponData.setParentRegisterData(ccr);
						chiponData.setHasParent(true);chiponData.setFlag(true); // 这个继承不允许修改
						chiponData.setBitLength(1);
						chiponData.setBitLocation(Integer.toString(22));							
					chiponDatasChildren.add(chiponData);
					//################################
						bitValue = ParseBitFromValue(registervalue,21,1);
						 chiponData=new ChiponCurrencyFloatRegisterData("DZC", Long.toHexString(bitValue), Long.toString(bitValue)); //$NON-NLS-1$					
						chiponData.setParentRegisterData(ccr);
						chiponData.setHasParent(true);chiponData.setFlag(true); // 这个继承不允许修改
						chiponData.setBitLength(1);
						chiponData.setBitLocation(Integer.toString(21));							
					chiponDatasChildren.add(chiponData);
					//################################
						bitValue = ParseBitFromValue(registervalue,20,1);
						 chiponData=new ChiponCurrencyFloatRegisterData("IOC", Long.toHexString(bitValue), Long.toString(bitValue)); //$NON-NLS-1$					
						chiponData.setParentRegisterData(ccr);
						chiponData.setHasParent(true);chiponData.setFlag(true); // 这个继承不允许修改
						chiponData.setBitLength(1);
						chiponData.setBitLocation(Integer.toString(20));							
					chiponDatasChildren.add(chiponData);
					//################################
						bitValue = ParseBitFromValue(registervalue,3,1);
						 chiponData=new ChiponCurrencyFloatRegisterData("AHFP", Long.toHexString(bitValue), Long.toString(bitValue)); //$NON-NLS-1$					
						chiponData.setParentRegisterData(ccr);
						chiponData.setHasParent(true);chiponData.setFlag(true); // 这个继承不允许修改
						chiponData.setBitLength(1);
						chiponData.setBitLocation(Integer.toString(3));							
					chiponDatasChildren.add(chiponData);
					//################################
						bitValue = ParseBitFromValue(registervalue,2,1);
						 chiponData=new ChiponCurrencyFloatRegisterData("DN", Long.toHexString(bitValue), Long.toString(bitValue)); //$NON-NLS-1$					
						chiponData.setParentRegisterData(ccr);
						chiponData.setHasParent(true);chiponData.setFlag(true); // 这个继承不允许修改
						chiponData.setBitLength(1);
						chiponData.setBitLocation(Integer.toString(2));							
					chiponDatasChildren.add(chiponData);
					//################################
						bitValue = ParseBitFromValue(registervalue,1,1);
						 chiponData=new ChiponCurrencyFloatRegisterData("FTZ", Long.toHexString(bitValue), Long.toString(bitValue)); //$NON-NLS-1$					
						chiponData.setParentRegisterData(ccr);
						chiponData.setHasParent(true);chiponData.setFlag(true); // 这个继承不允许修改
						chiponData.setBitLength(1);
						chiponData.setBitLocation(Integer.toString(1));							
					chiponDatasChildren.add(chiponData);
					//################################
						bitValue = ParseBitFromValue(registervalue,0,1);
						 chiponData=new ChiponCurrencyFloatRegisterData("IDC", Long.toHexString(bitValue), Long.toString(bitValue)); //$NON-NLS-1$					
						chiponData.setParentRegisterData(ccr);
						chiponData.setHasParent(true);chiponData.setFlag(true); // 这个继承不允许修改
						chiponData.setBitLength(1);
						chiponData.setBitLocation(Integer.toString(0));							
					chiponDatasChildren.add(chiponData);
					//+################################	
					ccr.setChiponDataList(chiponDatasChildren);
				}
				else	{
					dataLists.add(ccr);   // unknow
				}			
		}
		dataLists.add(coredataList);
		dataLists.add(highrxdataList);
		dataLists.add(accdataList);
		// 扩展
		if(accdataList!=null && accdataList.getChiponDataList()!=null)
			for(ChiponCurrencyFloatRegisterData ccrContainR:accdataList.getChiponDataList())
			{
				String   accName= ccrContainR.getName();
				int		accnum =Integer.parseInt(accName.substring(4))*2+0;
				int		accnum1 =accnum+1;
				ccrContainR.AddChild(MapR16TR31.get("s"+accnum)); //$NON-NLS-1$
				ccrContainR.AddChild(MapR16TR31.get("s"+accnum1)); //$NON-NLS-1$
			}		
		return dataLists;
	}
	
	@Override
	protected String getHelpContextId() {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	protected void fillContextMenu(IMenuManager menu) {
		// TODO Auto-generated method stub
		
	}

	@Override
	protected void configureToolBar(IToolBarManager tbm) {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void dispose() {
		DebugUITools.getDebugContextManager()
		.getContextService(getSite().getWorkbenchWindow())
		.removeDebugContextListener(this);
		super.dispose();
	}
	static long ParseBitFromValue(long registervalue,long startLocation,long bitLength){
		long num;
		num = 0;  //mask
		for(int i = 0; i < bitLength; i++){
			num += 1<<i;
		}
		return ((registervalue&(num<<startLocation))>>startLocation)&num;	
	}
}
