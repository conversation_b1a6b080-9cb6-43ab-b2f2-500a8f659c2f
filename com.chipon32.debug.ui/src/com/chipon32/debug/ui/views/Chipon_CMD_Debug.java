package com.chipon32.debug.ui.views;


import org.eclipse.jface.action.IMenuManager;
import org.eclipse.jface.action.IToolBarManager;
import org.eclipse.jface.dialogs.MessageDialog;
import org.eclipse.jface.viewers.ISelection;
import org.eclipse.jface.viewers.IStructuredSelection;
import org.eclipse.jface.viewers.Viewer;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.events.SelectionListener;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Button;
import org.eclipse.swt.widgets.Combo;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.Text;
import org.eclipse.ui.progress.UIJob;
import org.eclipse.core.resources.IProject;
import org.eclipse.core.runtime.IProgressMonitor;
import org.eclipse.core.runtime.IStatus;
import org.eclipse.core.runtime.Status;
import org.eclipse.debug.core.DebugException;
import org.eclipse.debug.ui.AbstractDebugView;
import org.eclipse.debug.ui.DebugUITools;
import org.eclipse.debug.ui.contexts.DebugContextEvent;
import org.eclipse.debug.ui.contexts.IDebugContextListener;

import com.chipon32.debug.core.model.ChiponDebugTarget;
import com.chipon32.debug.core.model.ChiponStackFrame;
import com.chipon32.debug.core.model.ChiponThread;
import com.chipon32.debug.core.protocol.ChiponStopLocationCommandResult;

/**
 * 资源使用率视图的最上层应用
 * <AUTHOR> @since 
 */
public class Chipon_CMD_Debug extends AbstractDebugView implements IDebugContextListener{
	public Chipon_CMD_Debug() {
	}
	//##################################################################################
	public static final String ID = "org.eclipse.debug.ui.CmdDebugGDB"; //$NON-NLS-1$
	
	public static boolean isViewerShow=false;
	// 无法构建lauch,,所有无法通过lauch加载目标机，从而和本身的调试系统，如文件定位功能进行关联，是否进行主动定位文件处理需要考虑实现
	
	// 因此，启动必须依靠ide自身，一些配置被集成完成
	private ChiponThread  fThread;
	private ChiponDebugTarget target;
	//##################################################################################

	
	//##################################################################################
	Combo InputVar;
	Text OutputVar;
	Button button;

	/**
	 * 创建视图的实现
	 */
	@Override
	public void createPartControl (Composite parent) {
		// 基于父类实例化,系统集成代码		
		parent.setLayout(new GridLayout(1, false));
		
		//############################设计输入接口
		Composite interFaceComposite=new Composite(parent, SWT.FILL);
		interFaceComposite.setLayout(new GridLayout(3,false));
		GridData gd_interFaceComposite = new GridData(SWT.LEFT, SWT.CENTER, true, false, 1, 1);
		interFaceComposite.setLayoutData(gd_interFaceComposite);
		//############################		
		Label cmdLabel=new Label(interFaceComposite, SWT.NONE);
		cmdLabel.setText(Messages.Chipon_CMD_Debug_1);
		//############################		
		InputVar=new Combo(interFaceComposite, SWT.BORDER);
		GridData gd_inputVar = new GridData(SWT.LEFT, SWT.CENTER, true, false, 1,1);
		gd_inputVar.widthHint = 400;
		//gd_inputVar.heightHint = 25;
		InputVar.setLayoutData(gd_inputVar);		
		InputVar.setItems(new String[]{"pause","stepi","nexti","c","finish"}); //$NON-NLS-1$ //$NON-NLS-2$
		InputVar.select(0);
		//InputVar.setBounds(56, 5, 620, 20);
		//############################		
		button=new Button(interFaceComposite,  SWT.NONE);
		//button.setBounds(700, 5, 50, 20);
		button.setText(Messages.Chipon_CMD_Debug_5);
		button.addSelectionListener(new SelectionListener() {			
			@Override
			public void widgetSelected(SelectionEvent e) {
				if(InputVar.getText()!=null && InputVar.getText().trim().length()>=1){
					if(InputVar.indexOf(InputVar.getText())<0)
						InputVar.add(InputVar.getText());
					DealCommand(InputVar.getText().trim());
					
				}else{
//					MessageDialog.openError(null, "ERROR", "请输入正确的命令！");
				}
			}			
			@Override
			public void widgetDefaultSelected(SelectionEvent e) {
			}
		});
		//############################		
		Composite outFaceComposite = new Composite(parent,SWT.BORDER);
		outFaceComposite.setLayout(new GridLayout(5,true));
		
//		final Button button1=new Button(outFaceComposite,  SWT.NONE);
//		button1.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, false, 1,1));
//		button1.setText("c");
//		button1.addSelectionListener(new SelectionListener() {			
//			@Override
//			public void widgetSelected(SelectionEvent e) {			
//				DealCommand(button1.getText());
//			}			
//			@Override
//			public void widgetDefaultSelected(SelectionEvent e) {	}
//		});
		final Button button2=new Button(outFaceComposite,  SWT.NONE);
		button2.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, false, 1,1));
		button2.setText("s"); //$NON-NLS-1$
		button2.addSelectionListener(new SelectionListener() {			
			@Override
			public void widgetSelected(SelectionEvent e) {			
				DealCommand(button2.getText());
			}			
			@Override
			public void widgetDefaultSelected(SelectionEvent e) {	}
		});
		final Button button3=new Button(outFaceComposite,  SWT.NONE);
		button3.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, false, 1,1));
		button3.setText("n");		 //$NON-NLS-1$
		button3.addSelectionListener(new SelectionListener() {			
			@Override
			public void widgetSelected(SelectionEvent e) {			
				DealCommand(button3.getText());
			}			
			@Override
			public void widgetDefaultSelected(SelectionEvent e) {	}
		});
		final Button button4=new Button(outFaceComposite,  SWT.NONE);
		button4.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, false, 1,1));
		button4.setText("si");	 //$NON-NLS-1$
		button4.addSelectionListener(new SelectionListener() {			
			@Override
			public void widgetSelected(SelectionEvent e) {			
				DealCommand(button4.getText());
			}			
			@Override
			public void widgetDefaultSelected(SelectionEvent e) {	}
		});
		final Button button5=new Button(outFaceComposite,  SWT.NONE);
		button5.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, false, 1,1));
		button5.setText("ni"); //$NON-NLS-1$
		button5.addSelectionListener(new SelectionListener() {			
			@Override
			public void widgetSelected(SelectionEvent e) {			
				DealCommand(button5.getText());
			}			
			@Override
			public void widgetDefaultSelected(SelectionEvent e) {	}
		});
		final Button button6=new Button(outFaceComposite,  SWT.NONE);
		button6.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, false, 1,1));
		button6.setText("finish"); //$NON-NLS-1$
		button6.addSelectionListener(new SelectionListener() {			
			@Override
			public void widgetSelected(SelectionEvent e) {			
				DealCommand(button6.getText());
			}			
			@Override
			public void widgetDefaultSelected(SelectionEvent e) {	}
		});
		
		OutputVar = new Text(outFaceComposite, SWT.MULTI | SWT.V_SCROLL | SWT.H_SCROLL);
		GridData gd_outputVar  = new GridData(SWT.LEFT, SWT.CENTER, true, false, 6,1);
		gd_outputVar.widthHint = 460;
		gd_outputVar.heightHint = 100;
		OutputVar.setLayoutData(gd_outputVar);
		OutputVar.setText(""); //$NON-NLS-1$
		//OutputVar.setBounds(5, 30, 760, 200);
		//############################	关联到调试，可根据调试动作获取调试对象和进程呗	
		DebugUITools.getDebugContextManager().getContextService(getSite().
				getWorkbenchWindow()).addDebugContextListener(this); 
		
		fThread = getfThread();
		if (fThread != null ) {
			target = fThread.getChiponDebugTarget();				
		}
		isViewerShow=true;
		//############################
	}
	
	@Override
	protected Viewer createViewer(Composite parent) {
		//==============================================================================
		return null;  // 上面构建方法优先的
	}
	
	public synchronized void  DealCommand(String strCommand)
	{
		if(target==null ||  fThread==null){
			OutputVar.setText(Messages.Chipon_CMD_Debug_12);
			return;
		}else{
			if(target.isTerminated()){
				OutputVar.setText(Messages.Chipon_CMD_Debug_13);
				return;
			}
			else if(!target.isSuspended() )
			{
				if(strCommand.toLowerCase().equalsIgnoreCase("pause"))				{				
				}
//				else if(strCommand.trim().toLowerCase().startsWith("handle"))				{				
//				}
				else if(strCommand.trim().toLowerCase().startsWith("shell"))				{				
				}
				else				{
				MessageDialog.openError(null, Messages.Chipon_CMD_Debug_0, Messages.Chipon_CMD_Debug_15);
				return;
				}
			}
		}
		//############################################
		ChiponStopLocationCommandResult result=new ChiponStopLocationCommandResult(null);
		boolean getCommFromThread=true;

			switch(strCommand){
				case "continue":  //$NON-NLS-1$
				case "c":  //$NON-NLS-1$
					try {
//						fThread.resume();
						OutputVar.setText(Messages.Chipon_CMD_Debug_18);
						return;
					} catch (Exception e3) {
						// TODO Auto-generated catch block
					}
					break;
				case "quit":  //$NON-NLS-1$
					try {
						fThread.terminate();
					} catch (DebugException e) {
//						e.printStackTrace();
					}
					target.debuggerTerminated();
					break;	
				case "step":  //$NON-NLS-1$
				case "s":  //$NON-NLS-1$
					try {
						fThread.stepInto();
					} catch (DebugException e2) {
//						e2.printStackTrace();
					}
					break;
				case "stepi":  //$NON-NLS-1$
				case "si":         //$NON-NLS-1$
					try {
						fThread.stepiInto();
					} catch (DebugException e2) {
//						e2.printStackTrace();
					}
					break;	
				case "next": //$NON-NLS-1$
				case "n":    //$NON-NLS-1$
					try {
						fThread.stepOver();
					} catch (DebugException e1) {
//						e1.printStackTrace();
					}
					break;
				case "nexti": //$NON-NLS-1$
				case "ni":   //$NON-NLS-1$
					try {
						fThread.stepiOver();
					} catch (DebugException e1) {
//						e1.printStackTrace();
					}
					break;	
				case "finish":  //$NON-NLS-1$
					try {
						fThread.stepReturn();
					} catch (DebugException e) {
//						e.printStackTrace();
					} 
					break;
				case "reset": //$NON-NLS-1$
					try {
						fThread.reset();
					} catch (Exception e) {
//						e.printStackTrace();
					} 
					break;
				case "pause": //$NON-NLS-1$
					try {
						fThread.suspend();
					} catch (Exception e) {
//						e.printStackTrace();
					} 
					break;
				default:
					try {
//					getCommFromThread=false;
//					result= (ChiponStopLocationCommandResult)fThread.sendCommand(new ChiponStepIntoCommand(strCommand));	// 借用ChiponStepIntoCommand而不是单步，因为参数靠传递，不是固定构建next类
					fThread.manual(strCommand);
					} catch (Exception e) {
//						e.printStackTrace();
					} 
					break;					
			}
//#####################################################################################		
			if(getCommFromThread==true)
				result = fThread.getLocationCommandResult();
//#####################################################################################				
			if (result.resultText == null || !result.resultText.equals("success")) { //$NON-NLS-1$
				String bufshowmessage = Messages.Chipon_CMD_Debug_31 + strCommand + "\r\n"+Messages.Chipon_CMD_Debug_33+"\r\n"; //$NON-NLS-2$ //$NON-NLS-4$
				for(String str : result.resultout){
					if(!str.contains("kf32command fail!")) //$NON-NLS-1$
							bufshowmessage += str +"\r\n"; //$NON-NLS-1$
				}
				OutputVar.setText(bufshowmessage);
				return;
			}else{		
				String bufshowmessage = Messages.Chipon_CMD_Debug_37 + strCommand + "\r\n"+Messages.Chipon_CMD_Debug_39 + "\r\n"; //$NON-NLS-2$ //$NON-NLS-4$
				for(String str : result.resultout){
//					if(!str.contains("kf32command success!"))
							bufshowmessage+=str +"\r\n"; //$NON-NLS-1$
				}
				OutputVar.setText(bufshowmessage);
				return;
			}
//#####################################################################################				
}
	
	// 调试过程结束的触发：
	@Override
	public void debugContextChanged(final DebugContextEvent event) {
//		System.out.println("调试启动更新数据");
		new UIJob(getSite().getShell().getDisplay(), "usercmdjob") { //$NON-NLS-1$
			{
				setSystem(true);
			}

			@Override
			public IStatus runInUIThread(IProgressMonitor monitor) {
			
					update(event.getContext());

				return Status.OK_STATUS;
			}
		}.schedule();
		
	}	
	/**
	 * Updates the view for the selected thread (if suspended)
	 */

	private void update(ISelection context) {
		//##################################################################################
		fThread = null;
		if (context instanceof IStructuredSelection) {
			Object element = ((IStructuredSelection) context).getFirstElement();
			if (element instanceof ChiponDebugTarget) {
				 fThread = ((ChiponDebugTarget)element).getCurrentThread();
			}
			if (element instanceof ChiponThread) {
				 fThread = (ChiponThread)element;
			} else if (element instanceof ChiponStackFrame) {
				fThread = (ChiponThread) ((ChiponStackFrame) element).getThread();
			} else
				return;
		}
		//##################################################################################				
		if (fThread != null ) {
			target=fThread.getChiponDebugTarget();				
		}
		else {
				return;
		}
		//##################################################################################
		IProject project=null;
		if(fThread!=null)
			project =((ChiponDebugTarget)fThread.getDebugTarget()).getfProject();
		if (project != null ) {			
				
		}
		//##################################################################################
	}
	
	@Override
	public void dispose() {
		isViewerShow=false;
		super.dispose();
		DebugUITools.getDebugContextManager().getContextService(getSite().getWorkbenchWindow()).removeDebugContextListener(this);
		
	}
	
    @Override
    public void setFocus() {
        // 选择下的非项目和文件激活也支持的更新，此时才会提示用户保存输入信息

    }
    /**
     * 
     * @return
     */    
	@SuppressWarnings("static-access")
	public ChiponThread getfThread() {
		fThread = null;  // 可能换调试程序
		{
			ISelection selection=DebugUITools.getDebugContextManager().getContextService(getViewSite().getWorkbenchWindow()).getActiveContext();
			 if (selection instanceof IStructuredSelection) {
		         Object element = ((IStructuredSelection)selection).getFirstElement();
		         if(element instanceof ChiponDebugTarget){
		         	fThread = ((ChiponDebugTarget)element).getCurrentThread();
		         }
		         if (element instanceof ChiponThread) {
		         	fThread = (ChiponThread)element;
		         } else if (element instanceof ChiponStackFrame) {
		         	fThread = (ChiponThread) ((ChiponStackFrame) element).getThread();
		         }
			 }
		}

		return fThread;
	}
	
	@Override
	protected void createActions() {
		
	}


	@Override
	protected String getHelpContextId() {
		return null;
	}

	// 右键菜单
	@Override
	protected void fillContextMenu(IMenuManager menu) {
		
	}

	// 工具栏上操作
	@Override
	protected void configureToolBar(IToolBarManager tbm) {
		
	}


}
