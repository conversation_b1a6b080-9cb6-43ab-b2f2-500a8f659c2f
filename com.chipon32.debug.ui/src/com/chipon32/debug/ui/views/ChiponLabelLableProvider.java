package com.chipon32.debug.ui.views;

import org.eclipse.jface.viewers.IColorProvider;
import org.eclipse.jface.viewers.ITableLabelProvider;
import org.eclipse.jface.viewers.LabelProvider;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.graphics.Image;
import org.eclipse.swt.widgets.Display;

import com.chipon32.debug.core.model.ChiponLabelData;
import com.chipon32.debug.ui.DebugUIActivator;

/***
 *<AUTHOR>
 ***/
public class ChiponLabelLableProvider extends LabelProvider implements
		ITableLabelProvider ,IColorProvider{

	private Color color;
	@Override
	public Image getColumnImage(Object element, int columnIndex) {
		if(element instanceof ChiponLabelData){
			Image image = null;
			if(columnIndex==0){
				ChiponLabelData groupData = (ChiponLabelData) element;
				if(groupData.getName()!=null && groupData.getName().equals("+")){ //$NON-NLS-1$
					image= new Image(Display.getCurrent(),DebugUIActivator.getFilePathFromPlugin("monitorexpression_tsk.gif")); //$NON-NLS-1$
					return image;
				}
			}
			
		}
		return null;
	}

	@Override
	public String getColumnText(Object element, int columnIndex) {
		if(element instanceof ChiponLabelData){
			ChiponLabelData groupData = (ChiponLabelData) element;
		
			switch(columnIndex){
			case 0:
				color = null;
				if(groupData.getName()!=null && groupData.getName().equals("+")){ //$NON-NLS-1$
					return Messages.ChiponLabelLableProvider_3;
				}
				return groupData.getName();
			case 1:
				color = null;
				return groupData.getAddress();
			case 2:
				color = groupData.getColor();
				return groupData.getBinaryValue();
			case 3:
				color = groupData.getColor();
				return groupData.getValue();
			case 4:
				color = groupData.getColor();
				return groupData.getHexValue();
			default:
				return "--"; //$NON-NLS-1$
			}
		}
		return null;
	}

	@Override
	public Color getForeground(Object element) {
		// TODO Auto-generated method stub
		return color;
	}

	@Override
	public Color getBackground(Object element) {
		// TODO Auto-generated method stub
		return null;
	}
	
}
