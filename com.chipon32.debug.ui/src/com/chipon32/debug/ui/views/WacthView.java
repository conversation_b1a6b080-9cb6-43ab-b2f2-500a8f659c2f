/**
 * 
 */
package com.chipon32.debug.ui.views;

import org.eclipse.core.runtime.IProgressMonitor;
import org.eclipse.core.runtime.IStatus;
import org.eclipse.core.runtime.Status;
import org.eclipse.debug.ui.AbstractDebugView;
import org.eclipse.debug.ui.DebugUITools;
import org.eclipse.debug.ui.contexts.DebugContextEvent;
import org.eclipse.debug.ui.contexts.IDebugContextListener;
import org.eclipse.jface.action.IMenuManager;
import org.eclipse.jface.action.IToolBarManager;
import org.eclipse.jface.viewers.CellEditor;
import org.eclipse.jface.viewers.IStructuredContentProvider;
import org.eclipse.jface.viewers.TableViewer;
import org.eclipse.jface.viewers.TextCellEditor;
import org.eclipse.jface.viewers.Viewer;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Button;
import org.eclipse.swt.widgets.Combo;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Table;
import org.eclipse.swt.widgets.TableColumn;
import org.eclipse.ui.progress.UIJob;

import com.chipon32.util.ui.DoubleClickCellModifier;
import com.chipon32.util.ui.UIUtil;

/**
 * <AUTHOR>
 *
 */
public class WacthView extends AbstractDebugView implements
		IDebugContextListener {
	private final static int COLUMN_WIDTH = 100 ;
	
	class WacthViewContentProvider implements IStructuredContentProvider{

		@Override
		public void dispose() {
			// TODO Auto-generated method stub
			
		}

		@Override
		public void inputChanged(Viewer viewer, Object oldInput, Object newInput) {
			// TODO Auto-generated method stub
			
		}

		@Override
		public Object[] getElements(Object inputElement) {
			// TODO Auto-generated method stub
			return null;
		}
		
	}
	
	class CellModifier extends DoubleClickCellModifier{

		public CellModifier(TableViewer tableViewer) {
			super(tableViewer);
			// TODO Auto-generated constructor stub
		}

		@Override
		public boolean allowModify(Object element, int columnIndex) {
			// TODO Auto-generated method stub
			return false;
		}

		@Override
		public void doModify(Object element, int columnIndex, Object value) {
			// TODO Auto-generated method stub
			
		}

		@Override
		public Object getColumnValue(Object element, int columnIndex) {
			// TODO Auto-generated method stub
			return null;
		}
		
	}
	
	public WacthView() {
	}

	/* (non-Javadoc)
	 * @see org.eclipse.debug.ui.contexts.IDebugContextListener#debugContextChanged(org.eclipse.debug.ui.contexts.DebugContextEvent)
	 */
	@Override
	public void debugContextChanged(DebugContextEvent event) {
		new UIJob(getSite().getShell().getDisplay(), "WacthView update") {
	        {
	            setSystem(true);
	        }
	        
	        @Override
			public IStatus runInUIThread(IProgressMonitor monitor) {
	        	if (getViewer() != null) { // runs asynchronously, view may be disposed
	        		//update(event.getContext());
	        	}
	            return Status.OK_STATUS;
	        }
	    }.schedule();
	}

	/* (non-Javadoc)
	 * @see org.eclipse.debug.ui.AbstractDebugView#createViewer(org.eclipse.swt.widgets.Composite)
	 */
	@Override
	protected Viewer createViewer(Composite parent) {
		parent.setLayout(new GridLayout(1, false));
		Composite container = new Composite(parent, SWT.NONE);
		
		container.setLayout(new GridLayout(4, false));
		
		Button btnAddSfr = new Button(container, SWT.NONE);
		btnAddSfr.setText("Add SFR");
		btnAddSfr.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent e) {
			}
		});
		
		Combo cboRegister = new Combo(container, SWT.NONE | SWT.READ_ONLY);
		cboRegister.setLayoutData(new GridData(SWT.FILL, SWT.CENTER, true, false, 1, 1));
		
		Button btnAddSymbol = new Button(container, SWT.NONE);
		btnAddSymbol.setText("Add Symbol");
		btnAddSymbol.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent e) {
			}
		});
		
		
		Combo cboSymbol = new Combo(container, SWT.NONE | SWT.READ_ONLY);
		cboSymbol.setLayoutData(new GridData(SWT.FILL, SWT.CENTER, true, false, 1, 1));
		
		TableViewer viewer = createTableViewer(container);
		
		
		DebugUITools.getDebugContextManager().getContextService(getSite().getWorkbenchWindow()).addDebugContextListener(this);
		getSite().setSelectionProvider(viewer);
		
		return viewer;
	}
	
	
	//´´½¨Table
	private TableViewer createTableViewer(Composite parent){
		TableViewer viewer = new TableViewer(parent);
		
		Table table = viewer.getTable();
		table.setLayoutData(new GridData(SWT.LEFT, SWT.CENTER, false, false, 4, 1));
		table.setLinesVisible(false);
		table.setHeaderVisible(true);
		
		viewer.setLabelProvider(DebugUITools.newDebugModelPresentation());
		viewer.setContentProvider(new WacthViewContentProvider());
		
		TableColumn tblclmnUpdate = new TableColumn(table, SWT.NONE);
		tblclmnUpdate.setWidth(COLUMN_WIDTH);
		tblclmnUpdate.setResizable(true);
		tblclmnUpdate.setText("Update");
		
		TableColumn tblclmnAddress = new TableColumn(table, SWT.NONE);
		tblclmnAddress.setWidth(COLUMN_WIDTH);
		tblclmnAddress.setResizable(true);
		tblclmnAddress.setText("Address");

		TableColumn tblclmnSymbolName = new TableColumn(table, SWT.NONE);
		tblclmnSymbolName.setWidth(COLUMN_WIDTH);
		tblclmnSymbolName.setResizable(true);
		tblclmnSymbolName.setText("Symbol Name");
		
		TableColumn tblclmnValue = new TableColumn(table, SWT.NONE);
		tblclmnValue.setWidth(COLUMN_WIDTH);
		tblclmnValue.setResizable(true);
		tblclmnValue.setText("Value");
		
		TableColumn tblclmnBinary = new TableColumn(table, SWT.NONE);
		tblclmnBinary.setWidth(COLUMN_WIDTH);
		tblclmnBinary.setResizable(true);
		tblclmnBinary.setText("Binary");
		
		UIUtil.setDefaultProperties(viewer);
		CellEditor[] cellEditors = new CellEditor[5];
		cellEditors[0] = null;
		cellEditors[1] = new TextCellEditor(table, SWT.BORDER);
		cellEditors[2] = new TextCellEditor(table, SWT.BORDER);
		cellEditors[3] = new TextCellEditor(table, SWT.BORDER);
		cellEditors[4] = new TextCellEditor(table, SWT.BORDER);
		viewer.setCellEditors(cellEditors);
		viewer.setCellModifier(new CellModifier(viewer));
		
		return viewer;
	}
	

	/* (non-Javadoc)
	 * @see org.eclipse.debug.ui.AbstractDebugView#createActions()
	 */
	@Override
	protected void createActions() {
		// TODO Auto-generated method stub

	}

	/* (non-Javadoc)
	 * @see org.eclipse.debug.ui.AbstractDebugView#getHelpContextId()
	 */
	@Override
	protected String getHelpContextId() {
		// TODO Auto-generated method stub
		return null;
	}

	/* (non-Javadoc)
	 * @see org.eclipse.debug.ui.AbstractDebugView#fillContextMenu(org.eclipse.jface.action.IMenuManager)
	 */
	@Override
	protected void fillContextMenu(IMenuManager menu) {
		// TODO Auto-generated method stub

	}

	/* (non-Javadoc)
	 * @see org.eclipse.debug.ui.AbstractDebugView#configureToolBar(org.eclipse.jface.action.IToolBarManager)
	 */
	@Override
	protected void configureToolBar(IToolBarManager tbm) {
		// TODO Auto-generated method stub

	}
	

}
