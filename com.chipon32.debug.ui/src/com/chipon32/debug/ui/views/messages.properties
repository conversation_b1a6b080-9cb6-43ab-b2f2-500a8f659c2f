Chipon_CMD_Debug_0=ERROR
Chipon_CMD_Debug_1=Command: 
Chipon_CMD_Debug_12=No Debug Threat...
Chipon_CMD_Debug_13=Debug Threat terminated ...
Chipon_CMD_Debug_15=Debug Running,Only Can Execute Command  With Pause State!
Chipon_CMD_Debug_18=Please Do This Command In Debug View
Chipon_CMD_Debug_31=Fail Command:
Chipon_CMD_Debug_33=Respond:
Chipon_CMD_Debug_37=Execute Command: 
Chipon_CMD_Debug_39=Respond:
Chipon_CMD_Debug_5=Send

ChiponACCXView_1=ACCX View Update
ChiponACCXView_3=Name
ChiponACCXView_4=High Hex
ChiponACCXView_5=Low Hex
ChiponACCXView_6=Dec Value

ChiponCanRunStepTester_1=Step In ...

ChiponCurrencyRegisterView_1=Register View Update
ChiponCurrencyRegisterView_4=Register
ChiponCurrencyRegisterView_5=Hex
ChiponCurrencyRegisterView_6=Dec

ChiponCurrentRegisterCellModifier_0=ERROR
ChiponCurrentRegisterCellModifier_1=ERROR
ChiponCurrentRegisterCellModifier_11=" Invalid Input!
ChiponCurrentRegisterCellModifier_15=Value Out Of Bit Length Range
ChiponCurrentRegisterCellModifier_16=ERROR
ChiponCurrentRegisterCellModifier_19=Assign Fail
ChiponCurrentRegisterCellModifier_20=ERROR
ChiponCurrentRegisterCellModifier_21=Assign Register Fail
ChiponCurrentRegisterCellModifier_22=ERROR
ChiponCurrentRegisterCellModifier_27=Assign Fail
ChiponCurrentRegisterCellModifier_31=Assign Fail
ChiponCurrentRegisterCellModifier_32=ERROR
ChiponCurrentRegisterCellModifier_34=Assign Fail
ChiponCurrentRegisterCellModifier_35=ERROR
ChiponCurrentRegisterCellModifier_38=Assign Fail
ChiponCurrentRegisterCellModifier_39=ERROR
ChiponCurrentRegisterCellModifier_41=Assign Fail
ChiponCurrentRegisterCellModifier_42=ERROR
ChiponCurrentRegisterCellModifier_44=Assign Fail
ChiponCurrentRegisterCellModifier_45=ERROR
ChiponCurrentRegisterCellModifier_46=Assign Fail
ChiponCurrentRegisterCellModifier_47=ERROR
ChiponCurrentRegisterCellModifier_49=Assign Fail
ChiponCurrentRegisterCellModifier_50=ERROR

ChiponEEPROMView_2=Head Address(Hex)
ChiponEEPROMView_28=Prompt
ChiponEEPROMView_29=Please Correct Input DataType 
ChiponEEPROMView_30=Prompt
ChiponEEPROMView_31=Please Correct Input Address 
ChiponEEPROMView_34=Prompt
ChiponEEPROMView_35=Error,Please Check EEprom Range
ChiponEEPROMView_4=Tail Address(Hex)
ChiponEEPROMView_5=Search

ChiponGroupMemoryCellModifier_0=ERROR
ChiponGroupMemoryCellModifier_2=\nAddress:0x
ChiponGroupMemoryCellModifier_4=" Invalid Input!

ChiponLabelCellModifier_10=ERROR
ChiponLabelCellModifier_16=\nAddress:
ChiponLabelCellModifier_17=" Address Already Being In View!
ChiponLabelCellModifier_18=ERROR
ChiponLabelCellModifier_24=\nAddress:
ChiponLabelCellModifier_25=\ Nonexistent!
ChiponLabelCellModifier_26=ERROR
ChiponLabelCellModifier_28=\nBinary\ 
ChiponLabelCellModifier_29= Invalid Input!
ChiponLabelCellModifier_35=\nDec\ 
ChiponLabelCellModifier_36= Invalid Input!
ChiponLabelCellModifier_37=ERROR
ChiponLabelCellModifier_42=\nHex\ 
ChiponLabelCellModifier_43=\  Invalid Input!
ChiponLabelCellModifier_44=ERROR
ChiponLabelCellModifier_8=\nAddress:
ChiponLabelCellModifier_9="  Invalid Input!

ChiponLabelLableProvider_3=Add new label

ChiponLabelView_1=Label
ChiponLabelView_2=Address
ChiponLabelView_3=Binary
ChiponLabelView_4=Dec
ChiponLabelView_5=Hex

ChiponMemoryView1_0=Memory View Update
ChiponMemoryView1_1=Head Address(Hex):
ChiponMemoryView1_2=Format:
ChiponMemoryView1_30=Prompt
ChiponMemoryView1_31=Please Correct Input Length 
ChiponMemoryView1_32=Prompt
ChiponMemoryView1_33=Please Correct Start Address
ChiponMemoryView1_4=8bit Count(Dec):
ChiponMemoryView1_6=Search
ChiponMemoryView1_UP=UP
ChiponMemoryView1_DOWN=DOWN
ChiponMemoryView1_Save=Save
ChiponMemoryView1_Load=Load

ChiponMemoryView2_0=Search
ChiponMemoryView2_1=Head Address(Hex):
ChiponMemoryView2_30=Prompt
ChiponMemoryView2_31=Please Correct Input Length 
ChiponMemoryView2_32=Prompt
ChiponMemoryView2_33=Please Correct Start Address
ChiponMemoryView2_35=Memory View Update
ChiponMemoryView2_4=8bit Count(Dec):

ChiponMemoryView3_1=Head Address(Hex):
ChiponMemoryView3_30=Prompt
ChiponMemoryView3_31=Please Correct Input Length 
ChiponMemoryView3_32=Prompt
ChiponMemoryView3_33=Please Correct Start Address
ChiponMemoryView3_35=Memory View Update
ChiponMemoryView3_4=8bit Count(Dec):
ChiponMemoryView3_6=Search

ChiponMemoryView4_1=Head Address(Hex):
ChiponMemoryView4_30=Prompt
ChiponMemoryView4_31=Please Correct Input Length 
ChiponMemoryView4_32=Prompt
ChiponMemoryView4_33=Please Correct Start Address
ChiponMemoryView4_35=Memory View Update
ChiponMemoryView4_4=8bit Count(Dec):
ChiponMemoryView4_6=Search

ChiponRegisterCellModifier_0=\ Value\ 
ChiponRegisterCellModifier_13=Register Assign Fail
ChiponRegisterCellModifier_3="  Invalid Input!
ChiponRegisterCellModifier_4=ERROR
ChiponRegisterCellModifier_6=\ \ Value Out Of Bit Length Range,Max:\t
ChiponRegisterCellModifier_7=ERROR

ChiponRegistersView_1=Name
ChiponRegistersView_10=Please Correct Register Input
ChiponRegistersView_11=Prompt
ChiponRegistersView_12=Register Already Being In List
ChiponRegistersView_13=Warning
ChiponRegistersView_14=Please Select Register
ChiponRegistersView_15=Warning
ChiponRegistersView_16=Please Correct Register Input
ChiponRegistersView_17=Prompt
ChiponRegistersView_18=" Register Already Being In List!
ChiponRegistersView_19=Register View Update
ChiponRegistersView_2= Add
ChiponRegistersView_3=Name
ChiponRegistersView_4=Hex
ChiponRegistersView_5=Start Address
ChiponRegistersView_6=Bit Length
ChiponRegistersView_7=Warning
ChiponRegistersView_8=Please Select Register
ChiponRegistersView_9=Warning



ChiponWachpointCellModifier_0=Address
ChiponWachpointCellModifier_3=Address_S_0x:
ChiponWachpointCellModifier_5=Watch Variable:

ChiponWatchpointView_1=Watch Variable:
ChiponWatchpointView_10=Type:
ChiponWatchpointView_11=Block Size:
ChiponWatchpointView_2=Address_S_0x:
ChiponWatchpointView_24=Auto
ChiponWatchpointView_25=Add
ChiponWatchpointView_32=State*
ChiponWatchpointView_33=Watch*
ChiponWatchpointView_34=Type*
ChiponWatchpointView_35=Message
ChiponWatchpointView_39=synchronization
ChiponWatchpointView_4= Variable
ChiponWatchpointView_40=Add
ChiponWatchpointView_41=Del
ChiponWatchpointView_42=Clean
ChiponWatchpointView_43=Watch Variable
ChiponWatchpointView_44=Address_S
ChiponWatchpointView_47=Invalid Watch Point

ChiponWatchpointView_6=Auto
ChiponWatchpointView_7=Address_S(H)
ChiponWatchpointView_9=Note:Address(H),Variable,Struct etc. 

StopWatchview_0=stopwatch
StopWatchview_1=\ total\ 
StopWatchview_10=processor speed
StopWatchview_2=\ \ \ \ synchronization
StopWatchview_3=\ \ \ \ \ \ instruction count
StopWatchview_6=\ \ \ \ Re Zero
StopWatchview_7=\ \ \ \ \ \ Time(Second)      
