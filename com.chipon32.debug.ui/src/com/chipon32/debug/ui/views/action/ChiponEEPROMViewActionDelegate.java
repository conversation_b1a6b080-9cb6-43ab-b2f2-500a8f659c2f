package com.chipon32.debug.ui.views.action;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.debug.ui.DebugUITools;
import org.eclipse.jface.action.IAction;
import org.eclipse.jface.viewers.ISelection;
import org.eclipse.jface.viewers.IStructuredSelection;
import org.eclipse.jface.viewers.Viewer;
import org.eclipse.swt.widgets.Display;
import org.eclipse.ui.IViewActionDelegate;
import org.eclipse.ui.IViewPart;

import com.chipon32.debug.core.model.ChiponDebugTarget;
import com.chipon32.debug.core.model.ChiponEEpromData;
import com.chipon32.debug.core.model.ChiponEEpromDatas;
import com.chipon32.debug.core.model.ChiponStackFrame;
import com.chipon32.debug.core.model.ChiponThread;
import com.chipon32.debug.core.protocol.ChiponPrintEEpromCommand;
import com.chipon32.debug.core.protocol.ChiponPrintEEpromCommandResult;

import com.chipon32.debug.core.util.ChiponEEpromViewParse;

import com.chipon32.debug.core.util.PackEEpromDataList;
import com.chipon32.debug.ui.views.ChiponEEPROMView;

/***
 *<AUTHOR>
 *2013-7-2上午9:41:17
 ***/
public class ChiponEEPROMViewActionDelegate implements IViewActionDelegate {

	private ChiponThread   fThread;
	@Override
	public void run(IAction action) {
		
	}

	@Override
	public void selectionChanged(IAction action, ISelection selection) {}

	
	//当程序暂停时第一次打开EEPROM视图时，查询EEPROM
	@Override
	public void init(IViewPart view) {
		fThread = null;
		
		ISelection selection=DebugUITools.getDebugContextManager().getContextService(view.getViewSite().getWorkbenchWindow()).getActiveContext();
		
	    if (selection instanceof IStructuredSelection) {
         Object element = ((IStructuredSelection)selection).getFirstElement();
         if(element instanceof ChiponDebugTarget){
         	fThread = ((ChiponDebugTarget)element).getCurrentThread();
         }
         if (element instanceof ChiponThread) {
         	fThread = (ChiponThread)element;
         } else if (element instanceof ChiponStackFrame) {
         	fThread = (ChiponThread) ((ChiponStackFrame) element).getThread();
         }else return;
     }
	    ChiponEEpromDatas eeDatas = null;
		if (fThread != null && fThread.isSuspended()&&fThread.isCanSetInputEEprom()) {
			List<ChiponEEpromData> eepromDataList = new ArrayList<>();
			ChiponEEpromViewParse eeViewParse = new ChiponEEpromViewParse(fThread);
			String startAddr = eeViewParse.getStartAddr();
			int length = eeViewParse.getLength();
			//从首地址到末地址，末地址不算
			int count = length/252;
			int remainder= length%252;   //最后一次的长度
			for(int i = 0; i < count;i++){
				ChiponPrintEEpromCommandResult eeResult = 
						(ChiponPrintEEpromCommandResult)fThread.sendCommand(
								new ChiponPrintEEpromCommand((Integer.parseInt(startAddr, 16)+252*i)+"", 63));
				eepromDataList.addAll(eeResult.eepromDataList);
			}
			
			
			ChiponPrintEEpromCommandResult eeResult = 
					(ChiponPrintEEpromCommandResult)fThread.sendCommand(
							new ChiponPrintEEpromCommand((Integer.parseInt(startAddr, 16)+252*count)+"", (remainder+3)/4));
			eepromDataList.addAll(eeResult.eepromDataList);	
			
			//重新设置EEPromData的地址
			
			eeViewParse.setRowNum(eepromDataList);
			
			ChiponThread.eepromDataList = eepromDataList;
			eeDatas = new PackEEpromDataList().packList(eepromDataList);

			fThread.setCanSetInputEEprom(false);
		}else {
			return;
		}
		final Viewer tableViewer = ((ChiponEEPROMView)view).getViewer();
		final ChiponEEpromDatas eeDatasInput = eeDatas;
		Display.getDefault().syncExec(new Runnable() {
			@Override
			public void run() {
				tableViewer.setInput(eeDatasInput);
	 			tableViewer.refresh();
			}
		});
	}

}
