package com.chipon32.debug.ui.views.action;

import org.eclipse.jface.action.IAction;
import org.eclipse.jface.viewers.ISelection;
import org.eclipse.ui.IViewActionDelegate;
import org.eclipse.ui.IViewPart;
import org.eclipse.ui.IViewReference;
import org.eclipse.ui.IViewSite;
import org.eclipse.ui.IWorkbenchPage;
import org.eclipse.ui.IWorkbenchWindow;
import org.eclipse.ui.PartInitException;

import com.chipon32.debug.ui.util.PinCloneUtils;
import com.chipon32.debug.ui.util.ViewIDCounterManager;

public class ChiponOpenNewViewActionDelegate implements IViewActionDelegate {

	private IViewPart fView;
	
	@Override
	public void run(IAction action) {
		IViewSite site = fView.getViewSite();
		String viewId = site.getId();
		IWorkbenchWindow ww = fView.getViewSite().getWorkbenchWindow();
		if (ww != null) {
			Integer secondaryId = null;
			boolean assignSecondaryId = false;
			
			// if there is a view without a secondary id, than get the next available id.
			IViewReference[] viewRefs = ww.getActivePage().getViewReferences();
			for (IViewReference viewRef : viewRefs) {
				if (viewId.equals(viewRef.getId()) && (viewRef.getSecondaryId() == null)) {
					assignSecondaryId = true;
					break;
				}					
			}
			if (assignSecondaryId)
				secondaryId = ViewIDCounterManager.getInstance().getNextCounter(viewId);
			
			try {
				ww.getActivePage().showView(viewId, 
					secondaryId != null ? PinCloneUtils.encodeClonedPartSecondaryId(secondaryId.toString()) : null,
					IWorkbenchPage.VIEW_ACTIVATE);
			} catch (PartInitException e) {
				e.printStackTrace();
			}
		}

	}

	@Override
	public void selectionChanged(IAction action, ISelection selection) {

	}

	/** Sets the view, that this action will open a new instance of */
	@Override
	public void init(IViewPart view) {
		fView = view;

	}

}
