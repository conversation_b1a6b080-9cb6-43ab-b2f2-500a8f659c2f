package com.chipon32.debug.ui.views.action;

import java.util.List;

import org.eclipse.debug.ui.DebugUITools;
import org.eclipse.jface.action.IAction;
import org.eclipse.jface.viewers.ISelection;
import org.eclipse.jface.viewers.IStructuredSelection;
import org.eclipse.jface.viewers.TreeViewer;
import org.eclipse.swt.widgets.Display;
import org.eclipse.ui.IViewActionDelegate;
import org.eclipse.ui.IViewPart;

import com.chipon32.chiponide.core.chipondescription.ICategory;
import com.chipon32.debug.core.model.ChiponDebugTarget;
import com.chipon32.debug.core.model.ChiponStackFrame;
import com.chipon32.debug.core.model.ChiponThread;
import com.chipon32.debug.ui.views.ChiponRegistersView;
import com.chipon32.debug.ui.views.ChiponViewTool;

public class ChiponRegisterViewActionDelegate implements IViewActionDelegate {
	
	private ChiponThread   fThread;
	private ICategory oldCategory;
	@Override
	public void run(IAction action) {
		// TODO Auto-generated method stub

	}

	@Override
	public void selectionChanged(IAction action, ISelection selection) {
		// TODO Auto-generated method stub

	}

	/**
	 * start debug and then call show view   init() way
	 */
	@Override
	public void init(IViewPart view) {
		
		System.out.println("Enter ChiponRegisterViewActionDelegate");
		fThread = null;

		ISelection selection=DebugUITools.getDebugContextManager().getContextService(view.getViewSite().getWorkbenchWindow()).getActiveContext();
		
	    if (selection instanceof IStructuredSelection) {
	         Object element = ((IStructuredSelection)selection).getFirstElement();
	         if(element instanceof ChiponDebugTarget){
	         	fThread = ((ChiponDebugTarget)element).getCurrentThread();
	         }
	         if (element instanceof ChiponThread) {
	         	fThread = (ChiponThread)element;
	         } else if (element instanceof ChiponStackFrame) {
	         	fThread = (ChiponThread) ((ChiponStackFrame) element).getThread();
	         }else return;
	    }
		if (fThread != null && fThread.isSuspended() && fThread.isCanSetInputRegister()) {
			fThread.setCanSetInputRegister(false);
		}else{
			return;
		}
		if (fThread !=null) {
			
//			String chip;

	    
			if(view instanceof ChiponRegistersView){
		
				final TreeViewer tableViewer = (TreeViewer) ((ChiponRegistersView)view).getViewer();
				Display.getDefault().syncExec(new Runnable() {
					@Override
					public void run() {
						List<ICategory> categorys = ChiponViewTool.getViewTool().getCategorys(fThread);
						final ICategory newCategory=ChiponViewTool.getViewTool().getNewCategory(categorys.get(0), fThread);
						tableViewer.setInput(ChiponViewTool.getViewTool().compare(oldCategory, newCategory));
//						tableViewer.expandAll();
			 			tableViewer.refresh();
			 			oldCategory = newCategory;
//			 			ChiponThread.romCategory = newCategory;
					}
				});
			}
		}
	}

}
