package com.chipon32.debug.ui.views.action;

import java.util.List;

import org.eclipse.debug.ui.DebugUITools;
import org.eclipse.jface.action.IAction;
import org.eclipse.jface.viewers.ISelection;
import org.eclipse.jface.viewers.IStructuredSelection;
import org.eclipse.jface.viewers.TreeViewer;
import org.eclipse.swt.widgets.Display;
import org.eclipse.ui.IViewActionDelegate;
import org.eclipse.ui.IViewPart;

import com.chipon32.debug.core.model.ChiponACCXData;
import com.chipon32.debug.core.model.ChiponCurrencyRegisterData;
import com.chipon32.debug.core.model.ChiponDebugTarget;
import com.chipon32.debug.core.model.ChiponStackFrame;
import com.chipon32.debug.core.model.ChiponThread;
import com.chipon32.debug.core.protocol.ChiponPrintCurrencyRegisterCommand;
import com.chipon32.debug.core.protocol.ChiponPrintCurrencyRegisterCommandResult;
import com.chipon32.debug.ui.views.ChiponACCXView;
import com.chipon32.debug.ui.views.ChiponViewTool;

public class ChiponACCXViewActionDelegate implements IViewActionDelegate {
	
	private ChiponThread   fThread;

	@Override
	public void run(IAction action) {
		

	}

	@Override
	public void selectionChanged(IAction action, ISelection selection) {
		

	}

	@Override
	public void init(IViewPart view) {
		fThread = null;
		
		ISelection selection=DebugUITools.getDebugContextManager().getContextService(view.getViewSite().getWorkbenchWindow()).getActiveContext();
		
	    if (selection instanceof IStructuredSelection) {
         Object element = ((IStructuredSelection)selection).getFirstElement();
         if(element instanceof ChiponDebugTarget){
         	fThread = ((ChiponDebugTarget)element).getCurrentThread();
         }
         if (element instanceof ChiponThread) {
         	fThread = (ChiponThread)element;
         } else if (element instanceof ChiponStackFrame) {
         	fThread = (ChiponThread) ((ChiponStackFrame) element).getThread();
         }else return;
     }
	   List<ChiponCurrencyRegisterData>  dataList = null;
		if (fThread != null && fThread.isSuspended()&&fThread.isCanSetInputACCX()) {

			ChiponPrintCurrencyRegisterCommandResult currencyRegisterResult = (ChiponPrintCurrencyRegisterCommandResult) fThread.sendCommand(new ChiponPrintCurrencyRegisterCommand("info registers"));
			
			if(currencyRegisterResult!=null && currencyRegisterResult.resultText!=null){			
				dataList = currencyRegisterResult.dataList;
				ChiponThread.DataList = currencyRegisterResult.dataList;
//				dataList = ChiponViewTool.getViewTool().getChiponDatas(currencyRegisterResult.dataList);
//				ChiponThread.DataList = dataList;
			}
			fThread.setCanSetInputACCX(false);
		}else {
			return;
		}
		if(view instanceof ChiponACCXView){
			
		
		final TreeViewer tableViewer = (TreeViewer) ((ChiponACCXView)view).getViewer();
		final List<ChiponACCXData> accxDatas = ChiponViewTool.getViewTool().resovleAccxdatas(dataList);	
		Display.getDefault().syncExec(new Runnable() {
			@Override
			public void run() {
				tableViewer.setInput(accxDatas);
				tableViewer.expandAll();
	 			tableViewer.refresh();
			}
		});
		}

	}

}
