package com.chipon32.debug.ui.views.action;

import java.util.List;

import org.eclipse.debug.ui.DebugUITools;
import org.eclipse.jface.action.IAction;
import org.eclipse.jface.viewers.ISelection;
import org.eclipse.jface.viewers.IStructuredSelection;
import org.eclipse.jface.viewers.Viewer;
import org.eclipse.swt.widgets.Display;
import org.eclipse.ui.IViewActionDelegate;
import org.eclipse.ui.IViewPart;

import com.chipon32.debug.core.model.ChiponDebugTarget;
import com.chipon32.debug.core.model.ChiponLabelData;
import com.chipon32.debug.core.model.ChiponStackFrame;
import com.chipon32.debug.core.model.ChiponThread;
import com.chipon32.debug.ui.views.ChiponLabelView;

public class ChiponLabelViewActionDelegate implements IViewActionDelegate {

	private ChiponThread   fThread;;
	@Override
	public void run(IAction action) {
		// TODO Auto-generated method stub

	}

	@Override
	public void selectionChanged(IAction action, ISelection selection) {
		// TODO Auto-generated method stub

	}

	@Override
	public void init(IViewPart view) {
		fThread = null;
		
		ISelection selection=DebugUITools.getDebugContextManager().getContextService(view.getViewSite().getWorkbenchWindow()).getActiveContext();
		
	    if (selection instanceof IStructuredSelection) {
         Object element = ((IStructuredSelection)selection).getFirstElement();
         if(element instanceof ChiponDebugTarget){
         	fThread = ((ChiponDebugTarget)element).getCurrentThread();
         }
         if (element instanceof ChiponThread) {
         	fThread = (ChiponThread)element;
         } else if (element instanceof ChiponStackFrame) {
         	fThread = (ChiponThread) ((ChiponStackFrame) element).getThread();
         }else return;
     }
	   final List<ChiponLabelData>  dataList = ChiponThread.labelDataList;
		if (fThread != null && fThread.isSuspended() && ChiponThread.labelDataList!=null && ChiponThread.labelDataList.size()==0) {
			dataList.add(new ChiponLabelData(fThread,"+","","","",""));
		}else {
			return;
		}
		if(view instanceof ChiponLabelView){
			
		
		final Viewer tableViewer = ((ChiponLabelView)view).getViewer();
		
		Display.getDefault().syncExec(new Runnable() {
			@Override
			public void run() {
				tableViewer.setInput(dataList);
				tableViewer.refresh();
			}
		});
		}
	}

}
