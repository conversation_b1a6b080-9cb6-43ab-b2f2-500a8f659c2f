package com.chipon32.debug.ui.views.action;

import java.util.List;

import org.eclipse.core.resources.IProject;
import org.eclipse.debug.ui.DebugUITools;
import org.eclipse.jface.action.IAction;
import org.eclipse.jface.viewers.ISelection;
import org.eclipse.jface.viewers.IStructuredSelection;
import org.eclipse.jface.viewers.TreeViewer;
import org.eclipse.swt.widgets.Display;
import org.eclipse.ui.IViewActionDelegate;
import org.eclipse.ui.IViewPart;

import com.chipon32.debug.core.model.ChiponCurrencyRegisterData;
import com.chipon32.debug.core.model.ChiponDebugTarget;
import com.chipon32.debug.core.model.ChiponStackFrame;
import com.chipon32.debug.core.model.ChiponThread;
import com.chipon32.debug.core.protocol.ChiponPrintCurrencyRegisterCommand;
import com.chipon32.debug.core.protocol.ChiponPrintCurrencyRegisterCommandResult;
import com.chipon32.debug.ui.views.ChiponCurrencyRegisterView;


public class ChiponCurrencyRegisterViewActionDelegate implements
		IViewActionDelegate {

	private ChiponThread   fThread;
	@Override
	public void run(IAction action) {
		// TODO Auto-generated method stub

	}

	@Override
	public void selectionChanged(IAction action, ISelection selection) {
		// TODO Auto-generated method stub

	}

	/**
	 * 启动调试后再开启视图，则调用ChiponCurrencyRegisterViewActionDelegate.java的init()方法。
	 */
	@Override
	public void init(IViewPart view) {
		fThread = null;
		
		ISelection selection=DebugUITools.getDebugContextManager().getContextService(view.getViewSite().getWorkbenchWindow()).getActiveContext();
		
	    if (selection instanceof IStructuredSelection) {
	         Object element = ((IStructuredSelection)selection).getFirstElement();
	         if(element instanceof ChiponDebugTarget){
	         	fThread = ((ChiponDebugTarget)element).getCurrentThread();
	         }
	         if (element instanceof ChiponThread) {
	         	fThread = (ChiponThread)element;
	         } else if (element instanceof ChiponStackFrame) {
	         	fThread = (ChiponThread) ((ChiponStackFrame) element).getThread();
	         }else return;
	   }
	    
	   List<ChiponCurrencyRegisterData>  dataList = null;
	   if (fThread != null && fThread.isSuspended()&&fThread.isCanSetInputCurrencyRegister()) {

			ChiponPrintCurrencyRegisterCommandResult currencyRegisterResult = 
					(ChiponPrintCurrencyRegisterCommandResult) fThread.sendCommand(
							new ChiponPrintCurrencyRegisterCommand("info registers"));
			
			if(currencyRegisterResult!=null && currencyRegisterResult.resultText!=null){			
//				ChiponCurrencyRegisterData registerData = ChiponViewTool.getViewTool().
//						getSingleChiponCurrencyRegisterData(fThread, "PSW");
//				currencyRegisterResult.dataList.add(registerData);
				ChiponThread.DataList = currencyRegisterResult.dataList;
				dataList = currencyRegisterResult.dataList;
			}
			fThread.setCanSetInputCurrencyRegister(false);
		}else {
			return;
		}
		if(view instanceof ChiponCurrencyRegisterView){
		
			final TreeViewer tableViewer = (TreeViewer) ((ChiponCurrencyRegisterView)view).getViewer();
			IProject project = null; 
			if(fThread!=null)
			 project = ((ChiponDebugTarget)fThread.getDebugTarget()).getfProject();
			final List<ChiponCurrencyRegisterData> eeDatasInput = ChiponCurrencyRegisterView.ListChiponCurrencyRegisterDataReStroe(dataList, project);;
			Display.getDefault().syncExec(new Runnable() {
				@Override
				public void run() {
					tableViewer.setInput(eeDatasInput);
					tableViewer.expandAll();
		 			tableViewer.refresh();
				}
			});
		}
	}

}
