package com.chipon32.debug.ui.views;

import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.eclipse.core.runtime.IAdaptable;
import org.eclipse.debug.core.model.IThread;
import org.eclipse.debug.ui.DebugUITools;
import org.eclipse.jface.dialogs.MessageDialog;
import org.eclipse.jface.viewers.TreeViewer;

import com.chipon32.debug.core.model.ChiponCurrencyRegisterData;
import com.chipon32.debug.core.model.ChiponDebugTarget;
import com.chipon32.debug.core.model.ChiponStackFrame;
import com.chipon32.debug.core.model.ChiponThread;
import com.chipon32.debug.core.protocol.ChiponCommandResult;
import com.chipon32.debug.core.protocol.ChiponGetCurrencyRegisterCommand;
import com.chipon32.debug.core.protocol.ChiponGetCurrencyRegisterCommandResult;
import com.chipon32.debug.core.protocol.ChiponSetCurrencyRegisterCommand;
import com.chipon32.debug.core.protocol.ChiponSetVariableCommand;
import com.chipon32.util.ui.DoubleClickCellModifier;



public class ChiponCurrentRegisterCellModifier extends DoubleClickCellModifier {
	
	private ChiponThread fThread;



	public ChiponCurrentRegisterCellModifier(TreeViewer treeViewer) {
		super(treeViewer);

	}
	
	
	/**
	 * 判断能否修改
	 * (non-Javadoc)
	 * @see com.chipon32.util.ui.DoubleClickCellModifier#allowModify(java.lang.Object, int)
	 */
	@Override
	public boolean allowModify(Object element, int columnIndex) {
		boolean result = false;

		if (element instanceof ChiponCurrencyRegisterData) {
			ChiponCurrencyRegisterData registerData = (ChiponCurrencyRegisterData)element;			
			if(registerData.isFlag())
			{
				result = false;					
			}
			else
			{
				result = true;			
			}
		}			
		return result;

	}
	
	/**
	 * 执行数据修改操作
	 */
	@Override
	public void doModify(Object element, int columnIndex, Object value) {
		// 基于现有的树级别关系，进行特征化的关联到父级和子集
		ChiponCurrencyRegisterData currencyRegisterData = null;
		boolean isMatch = false;
		if(element instanceof ChiponCurrencyRegisterData){
			currencyRegisterData = (ChiponCurrencyRegisterData)element;
		}else {
			return;
		}		
		if(columnIndex >=1){
			//######################################################
			String tmp =  (String) value;
			tmp=tmp.trim();		
			tmp=tmp.replace(" ", ""); // 考虑16进制字节断句的实现 //$NON-NLS-1$ //$NON-NLS-2$
			//比较值是否相同
			if(columnIndex == 1){	//判断16进制的值是否相等
				String hexv=currencyRegisterData.getValue().replace(" ", ""); //$NON-NLS-1$ //$NON-NLS-2$
				if(tmp.equalsIgnoreCase(hexv)){
					return;
				}
			}
			if(columnIndex ==2 ){
				if(tmp.equalsIgnoreCase(currencyRegisterData.getdValue())){
					return;
				}
			}
			//######################################################
			
			//判断输入值是否合法
			if(columnIndex == 1){
				if(tmp.startsWith("0x")||tmp.startsWith("0X")) //$NON-NLS-1$ //$NON-NLS-2$
					tmp=tmp.substring(2);
				if(tmp.endsWith("h")||tmp.endsWith("H")) //$NON-NLS-1$ //$NON-NLS-2$
					tmp=tmp.substring(0,tmp.length()-2);
				
				Pattern pattern = Pattern.compile("[0-9a-fA-F]+"); //$NON-NLS-1$
				Matcher match = pattern.matcher(tmp);
				isMatch = match.matches();
			}else if(columnIndex == 2){

				Pattern pattern = Pattern.compile("-?[0-9]+"); // 可以为负数 //$NON-NLS-1$
				Matcher match = pattern.matcher(tmp);
				isMatch = match.matches();
			}
			//######################################################
			if (!isMatch) {
				String errorInfo = "\"" + (String) value + Messages.ChiponCurrentRegisterCellModifier_11; //$NON-NLS-1$
				MessageDialog.openError(null, Messages.ChiponCurrentRegisterCellModifier_0, errorInfo);
				return;
			}			
			//######################################################接下来执行修改新值
			ChiponStackFrame frame = null;
			IAdaptable debugContext = DebugUITools.getDebugContext();
			if (debugContext instanceof ChiponStackFrame) {
				frame = (ChiponStackFrame) debugContext;
			} else if (debugContext instanceof ChiponThread) {				
				  ChiponThread thread = (ChiponThread) debugContext; 
				  try {
					  frame = (ChiponStackFrame) thread.getTopStackFrame(); 
				  }
				  catch (Exception e) { return ; }
			} else if (debugContext instanceof ChiponDebugTarget) {				
				  ChiponDebugTarget target = (ChiponDebugTarget) debugContext; 
				  try { 
						  IThread[] threads =  target.getThreads(); 
						  if (threads.length > 0) 
						  	{ frame =	(ChiponStackFrame) threads[0].getTopStackFrame(); } 
					  }
				  	catch (Exception e) { return ; }
			}

			// 在这里通过frame发送命令
			if (frame != null) {
				fThread = (ChiponThread) frame.getThread();
			}
			//######################################################
			ChiponCurrencyRegisterData currencyRegisterDataParent = currencyRegisterData.getParentRegisterData();
			List<ChiponCurrencyRegisterData> currencyRegisterDataChild = currencyRegisterData.getChiponDataList();
			if (fThread != null && fThread.isSuspended())
			{
				//############################################## 位寄存器 ,有父无子,最多32bit的long可无符号表达
				if(currencyRegisterData.getBitLength()!=0)
				{
					long ParentValue=Long.parseLong(currencyRegisterDataParent.getdValue());
					long vs=0;
					if(columnIndex == 1)
						vs=Long.parseLong(tmp,16);
					else if(columnIndex == 2)
						vs=Long.parseLong(tmp);
					long newvaules=0;
					int bitstardloacle=Integer.parseInt(currencyRegisterData.getBitLocation()); // 10进制的起始吧
					int bitlens=currencyRegisterData.getBitLength();
					String numstr="1"; //$NON-NLS-1$
					while(numstr.length()<bitlens)
					{
						numstr+="1"; //$NON-NLS-1$
					}
					if(vs>Long.parseLong(numstr, 2))
					{
						String errorInfo = Messages.ChiponCurrentRegisterCellModifier_15;
						MessageDialog.openError(null, Messages.ChiponCurrentRegisterCellModifier_16, errorInfo);
						return;  // 值不符和类型，超过了限制
					}
					newvaules=ParseBitChange2Value(bitlens,bitstardloacle,vs,ParentValue);
//					newvaules = ParentValue &((-1l)^(num<<bitstardloacle)) | (vs<<bitstardloacle);	
					ChiponCommandResult result;
					if(currencyRegisterDataParent.getAddr()==null)
					{
						result = fThread.sendCommand(new ChiponSetCurrencyRegisterCommand(currencyRegisterDataParent.getName(), Long.toString(newvaules)));
					}else
					{						
						result = fThread.sendCommand(new ChiponSetVariableCommand("*"+currencyRegisterDataParent.getAddr(), Long.toString(newvaules))); //$NON-NLS-1$
					}					
					if (result.resultText == null || !result.resultText.equals("success"))  //$NON-NLS-1$
					{
						String errorInfo = Messages.ChiponCurrentRegisterCellModifier_19;
						MessageDialog.openError(null, Messages.ChiponCurrentRegisterCellModifier_20, errorInfo);
						return;
					}
					//  更新
					ChiponGetCurrencyRegisterCommandResult commandResult ;
					if(currencyRegisterDataParent.getAddr()==null){
						commandResult=(ChiponGetCurrencyRegisterCommandResult) fThread.sendCommand(  //
																					new ChiponGetCurrencyRegisterCommand(currencyRegisterDataParent.getName()));
						currencyRegisterDataParent.setValue(commandResult.currencyRegisterData.getValue());	
						currencyRegisterDataParent.setdValue(commandResult.currencyRegisterData.getdValue());	
						// 更新子集
						ParentValue=Long.parseLong(currencyRegisterDataParent.getdValue());
						long vnew=ParseValueChange2Bit(bitlens,bitstardloacle,ParentValue);
						currencyRegisterData.setValue(Long.toHexString(vnew));	
						currencyRegisterData.setdValue(Long.toString(vnew));
					}
					else // 地址的; 不是 内核寄存器 
					{
						ChiponCurrencyRegisterData registerData = ChiponViewTool.getViewTool().  
								getSingleChiponCurrencyRegisterData(fThread, currencyRegisterDataParent.getName());
						if(registerData==null)
						{
							String errorInfo = Messages.ChiponCurrentRegisterCellModifier_21;
							MessageDialog.openError(null, Messages.ChiponCurrentRegisterCellModifier_22, errorInfo);
							return;
						}
						currencyRegisterDataParent.setValue(registerData.getValue());	
						currencyRegisterDataParent.setdValue(registerData.getdValue());	
						// 更新子集
						ParentValue=Long.parseLong(currencyRegisterDataParent.getdValue());
						long vnew=ParseValueChange2Bit(bitlens,bitstardloacle,ParentValue);
						currencyRegisterData.setValue(Long.toHexString(vnew));	
						currencyRegisterData.setdValue(Long.toString(vnew));	
					}
					//
					treeViewer.refresh();
				}
				else 
				{
					String  regName=currencyRegisterData.getName().toLowerCase();
					if(regName.contains("(")) //$NON-NLS-1$
					{
						regName=regName.substring(0,2); // 固定特征的还原
					}
					if(columnIndex == 1)
						tmp="0x"+tmp; //$NON-NLS-1$
					
					// ###############先执行自己的修改
					ChiponCommandResult result;
					if(currencyRegisterData.getAddr()==null)
					{
						result = fThread.sendCommand(new ChiponSetCurrencyRegisterCommand(regName,tmp));
					}else
					{						
						result = fThread.sendCommand(new ChiponSetVariableCommand("*"+currencyRegisterData.getAddr(), tmp)); //$NON-NLS-1$
					}					
					// TODO
					if (result.resultText == null || !result.resultText.equals("success"))  //$NON-NLS-1$
					{
						String errorInfo = Messages.ChiponCurrentRegisterCellModifier_27;
						MessageDialog.openError(null, Messages.ChiponCurrentRegisterCellModifier_1, errorInfo);
						return;
					}
					//##############################################需要识别是否有子集，子集是位信息还是不是，根据特征定向编写代码 
					// 如果是 ACC的类，需要更新子集的 R
					if(currencyRegisterData.getName().toLowerCase().contains("acc")) //$NON-NLS-1$
					{
						ChiponGetCurrencyRegisterCommandResult commandResult;
						// 更新ACC 自身
						commandResult= 	(ChiponGetCurrencyRegisterCommandResult) fThread.sendCommand(  //
								new ChiponGetCurrencyRegisterCommand(currencyRegisterData.getName()));
						if (commandResult.resultText == null || !commandResult.resultText.equals("success"))  //$NON-NLS-1$
						{
							String errorInfo = Messages.ChiponCurrentRegisterCellModifier_31;
							MessageDialog.openError(null, Messages.ChiponCurrentRegisterCellModifier_32, errorInfo);
							return;
						}
						currencyRegisterData.setValue(commandResult.currencyRegisterData.getValue());	
						currencyRegisterData.setdValue(commandResult.currencyRegisterData.getdValue());	
						// 更新子对象 Rx
						for (ChiponCurrencyRegisterData RxV: currencyRegisterDataChild)
						{
							 commandResult = 	(ChiponGetCurrencyRegisterCommandResult) fThread.sendCommand(  //
									new ChiponGetCurrencyRegisterCommand(RxV.getName()));
								if (commandResult.resultText == null || !commandResult.resultText.equals("success"))  //$NON-NLS-1$
								{
									String errorInfo = Messages.ChiponCurrentRegisterCellModifier_34;
									MessageDialog.openError(null, Messages.ChiponCurrentRegisterCellModifier_35, errorInfo);
									return;
								}
							RxV.setValue(commandResult.currencyRegisterData.getValue());	
							RxV.setdValue(commandResult.currencyRegisterData.getdValue());								
						}						
					}
					// 如果是R，父级时ACC，需要更新ACC
					else if(currencyRegisterData.isHasParent() && currencyRegisterDataParent.getName().toLowerCase().contains("acc")) //$NON-NLS-1$
					{
						ChiponGetCurrencyRegisterCommandResult commandResult;
						// 更新自身
						commandResult= 	(ChiponGetCurrencyRegisterCommandResult) fThread.sendCommand(  //
								new ChiponGetCurrencyRegisterCommand(currencyRegisterData.getName()));
						if (commandResult.resultText == null || !commandResult.resultText.equals("success"))  //$NON-NLS-1$
						{
							String errorInfo = Messages.ChiponCurrentRegisterCellModifier_38;
							MessageDialog.openError(null, Messages.ChiponCurrentRegisterCellModifier_39, errorInfo);
							return;
						}
						currencyRegisterData.setValue(commandResult.currencyRegisterData.getValue());	
						currencyRegisterData.setdValue(commandResult.currencyRegisterData.getdValue());	
						// 更新父级
						commandResult= 	(ChiponGetCurrencyRegisterCommandResult) fThread.sendCommand(  //
								new ChiponGetCurrencyRegisterCommand(currencyRegisterDataParent.getName()));
						if (commandResult.resultText == null || !commandResult.resultText.equals("success"))  //$NON-NLS-1$
						{
							String errorInfo = Messages.ChiponCurrentRegisterCellModifier_41;
							MessageDialog.openError(null, Messages.ChiponCurrentRegisterCellModifier_42, errorInfo);
							return;
						}
						currencyRegisterDataParent.setValue(commandResult.currencyRegisterData.getValue());	
						currencyRegisterDataParent.setdValue(commandResult.currencyRegisterData.getdValue());		
					}
					// 如果是普通的，识别子集存在位信息
					else if(currencyRegisterDataChild!=null && currencyRegisterDataChild.get(0).getBitLength()!=0)
					{
						// 更新自己
						ChiponGetCurrencyRegisterCommandResult commandResult ;
						if(currencyRegisterData.getAddr()==null){
							commandResult=(ChiponGetCurrencyRegisterCommandResult) fThread.sendCommand(  //
																						new ChiponGetCurrencyRegisterCommand(currencyRegisterData.getName()));
							if (commandResult.resultText == null || !commandResult.resultText.equals("success"))  //$NON-NLS-1$
							{
								String errorInfo = Messages.ChiponCurrentRegisterCellModifier_44;
								MessageDialog.openError(null, Messages.ChiponCurrentRegisterCellModifier_45, errorInfo);
								return;
							}
							currencyRegisterData.setValue(commandResult.currencyRegisterData.getValue());	
							currencyRegisterData.setdValue(commandResult.currencyRegisterData.getdValue());								
						}
						else 
						{
							ChiponCurrencyRegisterData registerData = ChiponViewTool.getViewTool().  
									getSingleChiponCurrencyRegisterData(fThread, currencyRegisterData.getName());
							if(registerData==null)
							{
								String errorInfo = Messages.ChiponCurrentRegisterCellModifier_46;
								MessageDialog.openError(null, Messages.ChiponCurrentRegisterCellModifier_47, errorInfo);
								return;
							}
							currencyRegisterData.setValue(registerData.getValue());	
							currencyRegisterData.setdValue(registerData.getdValue());	
						}
						// 更新子集
						Long ParentValue=Long.parseLong(currencyRegisterData.getdValue());
						for (ChiponCurrencyRegisterData bitR: currencyRegisterDataChild)
						{
														
							int bitstardloacle=Integer.parseInt(bitR.getBitLocation()); // 10进制的起始吧
							int bitlens=bitR.getBitLength();
							
							long vnew=ParseValueChange2Bit(bitlens,bitstardloacle,ParentValue);
							bitR.setValue(Long.toHexString(vnew));	
							bitR.setdValue(Long.toString(vnew));	
						}	
					}
					// 普通的 如 R0-R12，或  无ACC的 R16-R31   lr sp pc名字已还原的也适用   这里不设计其他地址的显示，地址显示带bit位
					else
					{
						ChiponGetCurrencyRegisterCommandResult commandResult;
						// 更新自身
						commandResult= 	(ChiponGetCurrencyRegisterCommandResult) fThread.sendCommand(  //
								new ChiponGetCurrencyRegisterCommand(regName));  // 可能还原后的名字
						// TODO
						if (commandResult.resultText == null || !commandResult.resultText.equals("success"))  //$NON-NLS-1$
						{
							String errorInfo = Messages.ChiponCurrentRegisterCellModifier_49;
							MessageDialog.openError(null, Messages.ChiponCurrentRegisterCellModifier_50, errorInfo);
							return;
						}
						currencyRegisterData.setValue(commandResult.currencyRegisterData.getValue());	
						currencyRegisterData.setdValue(commandResult.currencyRegisterData.getdValue());	
					}				
				
					treeViewer.refresh();
				}
				//############################################################
			}
		
		}
	}

	@Override
	public Object getColumnValue(Object element, int columnIndex) {
		if(element instanceof ChiponCurrencyRegisterData){
			
			ChiponCurrencyRegisterData registerData = (ChiponCurrencyRegisterData)element;
			if(columnIndex == 2){
				return registerData.getdValue().replace(" ", ""); //$NON-NLS-1$ //$NON-NLS-2$
			}else if(columnIndex == 1){
				return registerData.getValue().replace(" ", "").toUpperCase(); //$NON-NLS-1$ //$NON-NLS-2$
			}
			
		}
		return null;
	}
	

	public long ParseBitChange2Value(int bitlen,int bitstartlocal,long bitvaule,long OrgValue)
	{
		long num=0;
		
		for(int i = 0; i < bitlen; i++){
			num += 1<<i;
		}		
		return  OrgValue &((-1l)^(num<<bitstartlocal)) | (bitvaule<<bitstartlocal);			
	
	}
	public long ParseValueChange2Bit(int bitlen,int bitstartlocal,long OrgValue)
	{
		long num=0;
		
		for(int i = 0; i < bitlen; i++){
			num += 1<<i;
		}	
		
		return  ((OrgValue&(num<<bitstartlocal))>>bitstartlocal)&num;			
	}
	
}
