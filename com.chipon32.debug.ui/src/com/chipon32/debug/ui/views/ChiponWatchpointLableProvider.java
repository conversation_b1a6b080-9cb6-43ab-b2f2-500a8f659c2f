package com.chipon32.debug.ui.views;

import org.eclipse.jface.viewers.IBaseLabelProvider;
import org.eclipse.jface.viewers.IColorProvider;
import org.eclipse.jface.viewers.ILabelProviderListener;
import org.eclipse.jface.viewers.ITableLabelProvider;
import org.eclipse.jface.viewers.LabelProvider;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.graphics.Image;

import com.chipon32.debug.core.model.ChiponWatchpointData;
import com.chipon32.hex.ui.HexUiActivator;

public class ChiponWatchpointLableProvider extends LabelProvider implements IBaseLabelProvider, ITableLabelProvider, IColorProvider {
	

    @Override
    public void addListener(ILabelProviderListener listener) {
        // TODO Auto-generated method stub
        
    }
    @Override
    public void dispose() {
        // TODO Auto-generated method stub
        
    }
	@Override
	public Color getForeground(Object element) {
		// TODO Auto-generated method stub
		return null;
	}
    @Override
    public boolean isLabelProperty(Object element, String property) {
        // TODO Auto-generated method stub
        return false;
    }
    @Override
    public void removeListener(ILabelProviderListener listener) {
        // TODO Auto-generated method stub
        
    }
    
	@Override
	public Color getBackground(Object element) {
		// TODO Auto-generated method stub
		return null;
	}
	static String  PathImageFile1= HexUiActivator.getFilePathFromPlugin("true1.JPG");
	static String  PathImageFile2= HexUiActivator.getFilePathFromPlugin("false1.JPG");
	static String  PathImageFile3= HexUiActivator.getFilePathFromPlugin("show.jpg");
	private Image[]  imgs=new Image[]
			{
			 new Image(null,	PathImageFile1),
	         new Image(null,	PathImageFile2),
	         new Image(null,	PathImageFile3)	
			};
	@Override
	public Image getColumnImage(Object element, int columnIndex) {
		// TODO Auto-generated method stub
        if(element instanceof ChiponWatchpointData){
        	ChiponWatchpointData commData=(ChiponWatchpointData)element;
            switch(columnIndex)
            {           
            case 0:
            	if(commData.isNowValue())
            		return imgs[0];  
            	else
            		return imgs[1];
//            case 2:
//            	return imgs[2];
            }            
        }
		return null;
	}

	@Override
	public String getColumnText(Object element, int columnIndex) {
		if(element instanceof ChiponWatchpointData){
			ChiponWatchpointData watchpointData = (ChiponWatchpointData) element;
			switch(columnIndex){
			case 0:
				return (watchpointData.isNowValue())?"":"";
			case 1:
				return watchpointData.getName();
			case 2:
				return watchpointData.getType();
			case 3:
				return watchpointData.getMessage();

			}
		}
		return null;
	}

}
