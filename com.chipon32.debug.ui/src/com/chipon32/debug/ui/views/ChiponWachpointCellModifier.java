package com.chipon32.debug.ui.views;


import org.eclipse.jface.viewers.ICellModifier;
import org.eclipse.jface.viewers.TreeViewer;

import org.eclipse.swt.widgets.Item;


import com.chipon32.debug.core.model.ChiponWatchpointData;
import com.chipon32.util.ui.UIUtil;

public class ChiponWachpointCellModifier implements ICellModifier {
	
	protected boolean editable;
	protected TreeViewer treeViewer;
	protected Object element;
	protected String property;
	
	ChiponWatchpointView viewer;
	public ChiponWachpointCellModifier(TreeViewer treeViewer,ChiponWatchpointView view) {
		viewer=view;
		this.treeViewer=(treeViewer);
		// TODO Auto-generated constructor stub  有了这个，影响编辑器的使用
//		treeViewer.getTree().addMouseListener(new MouseAdapter() {
//			@Override
//			public void mouseDoubleClick(MouseEvent e) 
//			{
//				doTreeClick();
//			}
//		});
	}

	@Override
	public boolean canModify(Object element, String property) {
//		this.element = element;
//		this.property = property;  // 原靠鼠标监控的更新标志位，现在直接为编辑器的特性的对象识别。
//		if (editable) {
//			return true;
//		} else {
//			return false;
//		}
		int column =UIUtil.getPropertyColumnIndex(treeViewer, property);
		if (allowModify(element, column)) {
			editable = true;
		}else
		{
			editable = false;
		}
		return editable;
	}

	@Override
	public Object getValue(Object element, String property) {		
		int columnIndex = getColumnIndex(property);
		return getColumnValue(element, columnIndex);
	}

	@Override
	public void modify(Object element, String property, Object value) {
		if (element instanceof Item) {
			element = ((Item) element).getData();
		}
		int columnIndex = getColumnIndex(property);
		doModify(element, columnIndex, value);
	}
	
	private int getColumnIndex(String property) {
		int columnIndex;		
		columnIndex = UIUtil.getPropertyColumnIndex(treeViewer, property);		
		return columnIndex;
	}
	/**
	 * 
	 */
	protected void doTreeClick()
	{
		if (element == null) {
			return;
		}
		int column =UIUtil.getPropertyColumnIndex(treeViewer, property);
		if (allowModify(element, column)) {
			editable = true;
		}else
		{
			editable = false;
		}
	}
	public boolean allowModify(Object element, int columnIndex) {
		if(element instanceof ChiponWatchpointData)
		{
			ChiponWatchpointData commData = (ChiponWatchpointData)element;
			// 重设编辑器，更新当前内容的下拉存在列表
			
			// 是否允许修改
			if(commData!=null)
			{
				if(columnIndex <3)
				{
					if(columnIndex==0)
					{
						if(viewer.getfThread()==null)
						{
							return false;
						}
					}
					return true;					
				}
			}		
		}
		return false;
	}
	// 执行修改	
	public void doModify(Object element, int columnIndex, Object value){
		
		if(element instanceof ChiponWatchpointData)
		{
			ChiponWatchpointData commData = (ChiponWatchpointData)element;
			switch(columnIndex)
			{
				case 0:// 仅调试时才使能的修改的
					if((Boolean)value)		 //commData.setIsNowValue((Boolean)value);
						commData.setWatchPoint(viewer.getfThread());
					else
						commData.deleteWatchpoint(viewer.getfThread());	
					break;
				case 1:// 非调试下也可以修改的，调试下需要额外做些同步操作，即使能下需要删除并重新建立
					if(commData.getName().endsWith((String)value)) // 未改变
						return;
					if(viewer.watchDataNames.get((String)value)!=null) // 已有
						return;					

					//!!!!!!!!!!!!!!!!! 维护重要信息
					if(commData.getMessage().contains(Messages.ChiponWachpointCellModifier_0))
					{
						//
						String messbuf=commData.getMessage();
						String[] messbuf1=messbuf.split(":"); //$NON-NLS-1$
						String[] messbuf2=messbuf1[1].split("~"); //$NON-NLS-1$
						Integer  addmask=0;
						try{
							addmask=Integer.parseInt(messbuf2[1], 16)-Integer.parseInt(messbuf2[0], 16);
						}catch (Exception ex) {
							// TODO: handle exception
						}	
						try{
							int startaddr=Integer.parseInt((String)value, 16);	
							startaddr=startaddr&(~addmask);	
							value=Integer.toHexString(startaddr).toUpperCase();
							
							commData.setMessage(Messages.ChiponWachpointCellModifier_3+Integer.toHexString(startaddr)+"~"+Integer.toHexString(startaddr+addmask)); //$NON-NLS-2$
						}catch (Exception ex) {
							// TODO: handle exception
							return ;  // 类型错误
						}
					}
					else
					{
						commData.setMessage(Messages.ChiponWachpointCellModifier_5+(String)value);
					}					
					//!!!!!!!!!!!!!!!!!
					viewer.watchDataNames.remove(commData.getName());  // 排他性更新
					if(viewer.getfThread()==null)
						commData.setName((String)value);
					else
					{
						if(commData.isNowValue())
						{
							commData.deleteWatchpoint(viewer.getfThread());
							commData.setName((String)value);
							commData.setWatchPoint(viewer.getfThread());
						}else
						{
							commData.setName((String)value);
						}
					}
					viewer.watchDataNames.put(commData.getName(), "have");// 排他性更新 //$NON-NLS-1$
					break;					
				case 2:// 非调试下也可以修改的，调试下需要额外做些同步操作，即使能下需要删除并重新建立
					
					if(commData.getDefaultselid()==(int) value) // 未改变
						return;
					
					if(viewer.getfThread()==null)
					{
						commData.setSelelementtext(commData.types[(int) value]);
						commData.setDefaultselid((int) value); 
					}
					else
					{
						if(commData.isNowValue())
						{
								commData.deleteWatchpoint(viewer.getfThread());
							commData.setSelelementtext(commData.types[(int) value]);
							commData.setDefaultselid((int) value); 
								commData.setWatchPoint(viewer.getfThread());
						}else
						{
							commData.setSelelementtext(commData.types[(int) value]);
							commData.setDefaultselid((int) value); 
						}
					}
					break;
			}			
			// 结果的刷新
			treeViewer.refresh();
		}

	}
	// 获取当前行 某个下标的结果
	public Object getColumnValue(Object element, int columnIndex) {
		if(element instanceof ChiponWatchpointData)
		{
			ChiponWatchpointData watchpointData = (ChiponWatchpointData)element;
			// 重设编辑器，更新当前内容的下拉存在列表 这里修改就晚了，判断是否允许时就更新
			//================================		
			switch(columnIndex)
			{
			case 0:
				return watchpointData.isNowValue();
				
			case 1:
				return watchpointData.getName();
			case 2:
				return getSelIndex(watchpointData,watchpointData.getSelelementtext()) ;//watchpointData.getType();
			case 3:
				return watchpointData.getMessage();	
			
			}			
		}
		return null;
	}
	public int getSelIndex(ChiponWatchpointData cm,String str)
	{
		for(int i=0;i<cm.types.length;i++)
		{
			if(cm.types[i].equalsIgnoreCase(str))
			{
				return i;
			}
		}
		return -1;
	}
}
