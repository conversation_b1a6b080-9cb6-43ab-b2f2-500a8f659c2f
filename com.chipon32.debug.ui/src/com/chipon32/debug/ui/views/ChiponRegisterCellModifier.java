package com.chipon32.debug.ui.views;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.eclipse.core.runtime.IAdaptable;
import org.eclipse.debug.ui.DebugUITools;
import org.eclipse.jface.dialogs.MessageDialog;
import org.eclipse.jface.viewers.TableViewer;
import org.eclipse.jface.viewers.TreeViewer;
import com.chipon32.chiponide.core.chipondescription.IEntry;
import com.chipon32.chiponide.core.chipondescription.chipio.Bits;
import com.chipon32.chiponide.core.chipondescription.chipio.Register;
import com.chipon32.debug.core.model.ChiponDebugTarget;

import com.chipon32.debug.core.model.ChiponStackFrame;
import com.chipon32.debug.core.model.ChiponThread;
import com.chipon32.debug.core.protocol.ChiponCommandResult;
import com.chipon32.debug.core.protocol.ChiponSetAndRegisterCommand;
import com.chipon32.util.communicate.HandleError;
import com.chipon32.util.communicate.WriteMessage;
import com.chipon32.util.ui.DoubleClickCellModifier;

public class ChiponRegisterCellModifier extends DoubleClickCellModifier {

	private ChiponThread fThread;

	public ChiponRegisterCellModifier(TreeViewer treeViewer) {
		super(treeViewer);
	}

	public ChiponRegisterCellModifier(TableViewer tableViewer) {
		super(tableViewer);
	}

	@Override
	public boolean allowModify(Object element, int columnIndex) {
		boolean result = false;
		if(columnIndex!=1)
			return false;
		if (element instanceof Bits) {
			result = true;
		} else if (element instanceof Register) {
			result = true;
		}
		return result;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.chipon32.util.ui.DoubleClickCellModifier#doModify(java.lang.Object,
	 * int, java.lang.Object) doModify
	 */
	@Override
	public void doModify(Object element, int columnIndex, Object value) {
		
		Register groupRomData = null;
		String commandAddress = null;
		String commandValue = null;
		boolean isMatch = false;
		boolean isLike = false;
		String newValue = null;
		fThread = null;

		if (columnIndex == 1) {
			String tmp = (String) value;   //修改时重新输入的值（新值）
			if (element instanceof Register) {
				tmp = tmp.toUpperCase();
				Pattern pattern = Pattern.compile("[0-9a-fA-F]+"); //$NON-NLS-1$
				Matcher match = pattern.matcher(tmp);
				isMatch = match.matches();
			} else if (element instanceof Bits) {
				Pattern pattern = Pattern.compile("[0-9a-fA-F]+"); //$NON-NLS-1$
				Matcher match = pattern.matcher(tmp);
				isMatch = match.matches();
			}

			if (!isMatch) {
				// String errorAddress =
				// groupRomData.toFour(Integer.toString(groupRomData.get+columnIndex-1,
				// 16));
				String errorInfo = "\"" + tmp + Messages.ChiponRegisterCellModifier_3; //$NON-NLS-1$
				MessageDialog.openError(null, Messages.ChiponRegisterCellModifier_4, errorInfo);
				return;
			}
			if(element instanceof Bits)
			{
				int bitlen=Integer.parseInt(((Bits)element).getBitLength());
				long maxv=0;
				while((bitlen--)>0)
					maxv=maxv*2+1;
				long setv=Long.parseLong(tmp,16);
				if(setv>maxv)
				{
					String errorInfo = Messages.ChiponRegisterCellModifier_0 + tmp + Messages.ChiponRegisterCellModifier_6+maxv;
					MessageDialog.openError(null, Messages.ChiponRegisterCellModifier_7, errorInfo);
					return;
				}
			}

			if (element instanceof IEntry) {

				ChiponStackFrame frame = null;
				IAdaptable debugContext = DebugUITools.getDebugContext();
				if (debugContext instanceof ChiponStackFrame) {
					frame = (ChiponStackFrame) debugContext;
				} else if (debugContext instanceof ChiponThread) {
					/*
					 * ChiponThread thread = (ChiponThread) debugContext; try {
					 * frame = (ChiponStackFrame) thread.getTopStackFrame(); }
					 * catch (DebugException e) { return ; }
					 */
					return;
				} else if (debugContext instanceof ChiponDebugTarget) {
					/*
					 * ChiponDebugTarget target = (ChiponDebugTarget)
					 * debugContext; try { IThread[] threads =
					 * target.getThreads(); if (threads.length > 0) { frame =
					 * (ChiponStackFrame) threads[0].getTopStackFrame(); } }
					 * catch (DebugException e) { return ; }
					 */
					return;
				}

				// 在这里通过frame发送命令
				if (frame != null) {
					fThread = (ChiponThread) frame.getThread();
				}

				if (element instanceof Register) {
					groupRomData = (Register) element;
					commandAddress = groupRomData.getAddr();
					// 值本身为16进制
					commandValue = groupRomData.getValue();    //未修改前的值

					// 新值与旧值的比较
					if (tmp.equals(groupRomData.getValue().toUpperCase())) {
						isLike = true;
					}
					newValue = tmp;
				} else if (element instanceof Bits) {
					Bits bits = (Bits) element;
					commandValue = bits.getValue();   //未修改前的值

					if (tmp.equals(commandValue.toUpperCase())) {
						isLike = true;
						return;
					}
					groupRomData = (Register) bits.getParent();
					commandAddress = groupRomData.getAddr();
					// 获取位的起始地址，第几位
					int startAdr = Integer.parseInt(bits.getAddr());
					int bitLength = Integer.parseInt(bits.getBitLength());
					// 将位的寄存器值的对应位置清零，与位的值移到对应位置后或运算
					StringBuffer str = new StringBuffer();
					for (int i = 0; i < 32 - startAdr - bitLength; i++) {
						str.append("1"); //$NON-NLS-1$
					}
					for (int i = 0; i < bitLength; i++) {
						str.append("0"); //$NON-NLS-1$
					}
					for (int i = 0; i < startAdr; i++) {
						str.append("1"); //$NON-NLS-1$
					}

					long base = Long.parseLong(str.toString(), 2);
					// 位的实际值只取对应位置的几位剩余的位舍去
					long bitValue = Long.parseLong(tmp, 16) & ((int) Math.pow(2, bitLength) - 1);

					long registerValue = Long.parseLong(groupRomData.getValue(), 16);

					long newReValueLong = (base & registerValue) | (bitValue << startAdr);
					
					newValue = Long.toHexString(newReValueLong);
				}
				if (isLike) {
					return;
				}
				// List<ICategory>
				// categorys=ChiponViewTool.getViewTool().getCategorys();

				if (fThread != null && fThread.isSuspended()) {
					ChiponCommandResult result = fThread.sendCommand(new ChiponSetAndRegisterCommand(commandAddress, "0x" + newValue)); //$NON-NLS-1$
					if (result.resultText.equals("success")) { //$NON-NLS-1$
//						WriteMessage.getDefault().writeBLUEMessage("修改寄存器值成功");
						ChiponViewTool.getViewTool().setSingleRegisterData(fThread, groupRomData);
						treeViewer.refresh();
					} else {
						WriteMessage.getDefault().writeErrMessage(Messages.ChiponRegisterCellModifier_13);
						HandleError.getDefault().handleMessage(result.errorCode,null);
					}
				}

			}
		}

	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.chipon32.util.ui.DoubleClickCellModifier#getColumnValue(java.lang
	 * .Object, int)
	 */
	@Override
	public Object getColumnValue(Object element, int columnIndex) {

		if (element instanceof IEntry) {
			IEntry entry = (IEntry) element;
			// boolean bl=entry.equals(entry.getParent());

			if (columnIndex ==1 /*
								 * && columnIndex <= 16 &&
								 * groupRomData.getAddress() >= 0
								 */) {
				// ChiponRomData romData =
				// groupRomData.getRomDatas().get(columnIndex - 1);
				return entry.getValue().toUpperCase();// romData.getValue().toUpperCase();
			}
		}
		return null;
	}
	

}
