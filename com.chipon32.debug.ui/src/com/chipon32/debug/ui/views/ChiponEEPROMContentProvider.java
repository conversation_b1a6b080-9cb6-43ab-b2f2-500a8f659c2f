package com.chipon32.debug.ui.views;

import java.util.List;

import org.eclipse.jface.viewers.IStructuredContentProvider;
import org.eclipse.jface.viewers.Viewer;

import com.chipon32.debug.core.model.ChiponEEpromData;
import com.chipon32.debug.core.model.ChiponEEpromDatas;
import com.chipon32.debug.core.model.ChiponGroupEEpromData;



/***
 *<AUTHOR>
 *2013-6-20обнГ2:11:21
 ***/
public class ChiponEEPROMContentProvider implements IStructuredContentProvider {

	@Override
	public void dispose() {
		// TODO Auto-generated method stub

	}

	@Override
	public void inputChanged(Viewer viewer, Object oldInput, Object newInput) {
		if(newInput == null || oldInput == null){
			return ;
		}
		if(!(oldInput instanceof ChiponEEpromDatas) || !(newInput instanceof ChiponEEpromDatas)){
			 return ;
		}
		List<ChiponGroupEEpromData> oldgroupEEpromDatas = ((ChiponEEpromDatas)oldInput).getGroupEEpromDatas();
		List<ChiponGroupEEpromData> newgroupEEpromDatas = ((ChiponEEpromDatas)newInput).getGroupEEpromDatas();
		for(int i=0;i < oldgroupEEpromDatas.size() && i < newgroupEEpromDatas.size();i++){
			List<ChiponEEpromData> oldEEpromDatas = oldgroupEEpromDatas.get(i).getEEpromDatas();
			List<ChiponEEpromData> newEEpromDatas = newgroupEEpromDatas.get(i).getEEpromDatas();
			
			for(int j=0;j<oldEEpromDatas.size()&&j<newEEpromDatas.size() ;j++){
				ChiponEEpromData oldEEpromData = oldEEpromDatas.get(j);
				ChiponEEpromData newEEpromData = newEEpromDatas.get(j);
				if(oldEEpromData.equals(newEEpromData)){
					newEEpromData.setColor(IChiponColor.DEFAULT_COLOR);
				}else{
					newEEpromData.setColor(IChiponColor.RED_COLOR);
				}
			}
		}
	}

	@Override
	public Object[] getElements(Object inputElement) {
		if(inputElement instanceof ChiponEEpromDatas){
			ChiponEEpromDatas eeDatas = (ChiponEEpromDatas) inputElement;
			return eeDatas.getGroupEEpromDatas().toArray();
		}
		return null;
	}

}
