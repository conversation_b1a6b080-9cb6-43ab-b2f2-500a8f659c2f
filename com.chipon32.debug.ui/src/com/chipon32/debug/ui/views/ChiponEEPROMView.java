package com.chipon32.debug.ui.views;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.eclipse.core.runtime.IProgressMonitor;
import org.eclipse.core.runtime.IStatus;
import org.eclipse.core.runtime.Status;
import org.eclipse.debug.ui.AbstractDebugView;
import org.eclipse.debug.ui.DebugUITools;
import org.eclipse.debug.ui.contexts.DebugContextEvent;
import org.eclipse.debug.ui.contexts.IDebugContextListener;
import org.eclipse.jface.action.IMenuManager;
import org.eclipse.jface.action.IToolBarManager;
import org.eclipse.jface.action.Separator;
import org.eclipse.jface.dialogs.MessageDialog;
import org.eclipse.jface.viewers.CellEditor;
import org.eclipse.jface.viewers.ISelection;
import org.eclipse.jface.viewers.IStructuredSelection;
import org.eclipse.jface.viewers.TextCellEditor;
import org.eclipse.jface.viewers.Viewer;
import org.eclipse.swt.SWT;

import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.graphics.GC;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Button;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Event;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.Listener;
import org.eclipse.swt.widgets.Table;
import org.eclipse.swt.widgets.TableColumn;
import org.eclipse.swt.widgets.Text;
import org.eclipse.ui.IWorkbenchActionConstants;
import org.eclipse.ui.progress.UIJob;

import com.chipon32.debug.core.model.ChiponDebugTarget;
import com.chipon32.debug.core.model.ChiponEEpromData;
import com.chipon32.debug.core.model.ChiponEEpromDatas;
import com.chipon32.debug.core.model.ChiponGroupEEpromData;
import com.chipon32.debug.core.model.ChiponStackFrame;
import com.chipon32.debug.core.model.ChiponThread;
import com.chipon32.debug.core.protocol.ChiponPrintEEpromCommand;
import com.chipon32.debug.core.protocol.ChiponPrintEEpromCommandResult;
import com.chipon32.debug.core.util.PackEEpromDataList;
import com.chipon32.util.ui.HexTableViewer;
import com.chipon32.util.ui.UIUtil;

/***
 * <AUTHOR>
public class ChiponEEPROMView extends AbstractDebugView implements IDebugContextListener {

	private Text txtEnd;
	private Text txtStart;

	public static final String ID = "com.chipon32.debug.ui.view.EEPROMView"; //$NON-NLS-1$

	private Table table;
	private ChiponThread fThread;

	@Override
	public void debugContextChanged(final DebugContextEvent event) {
		// System.out.println("487");
		new UIJob(getSite().getShell().getDisplay(), "EEPROMView update") { //$NON-NLS-1$
			// new
			// UIJob(getSite().getShell().getDisplay(),"BlockEEView updata"){
			{
				setSystem(true);
			}

			@Override
			public IStatus runInUIThread(IProgressMonitor monitor) {
				if (getViewer() != null) { // runs asynchronously, view may be
											// disposed
					update(event.getContext());
				}
				return Status.OK_STATUS;
			}
		}.schedule();

	}

	/**
	 * Updates the view for the selected thread (if suspended)
	 */
	private void update(ISelection context) {
		fThread = null;

		if (context instanceof IStructuredSelection) {
			Object element = ((IStructuredSelection) context).getFirstElement();
			if (element instanceof ChiponDebugTarget) {
				// fThread = ((ChiponDebugTarget)element).getThread();
				return;
			}
			if (element instanceof ChiponThread) {
				// fThread = (ChiponThread)element;
				return;
			} else if (element instanceof ChiponStackFrame) {
				fThread = (ChiponThread) ((ChiponStackFrame) element).getThread();
			} else
				return;
		}
		ChiponEEpromDatas eepromDatas = null;
		if (fThread != null && fThread.isSuspended() && fThread.isCanSetInputEEprom()) {
				eepromDatas = new PackEEpromDataList().packList(getEEpromData());
				fThread.setCanSetInputEEprom(false);
		} else {
			return;
		}
		getViewer().setInput(eepromDatas);
		getViewer().refresh();
	}

	@Override
	protected Viewer createViewer(Composite parent) {

		parent.setLayout(new GridLayout(1, false));
		Composite container = new Composite(parent, SWT.NONE);

		container.setLayout(new GridLayout(6, false));
		GridData gd_composite = new GridData(SWT.LEFT, SWT.CENTER, true, false, 1, 1);
		container.setLayoutData(gd_composite);

		final Label startAddress = new Label(container, SWT.NONE);
		startAddress.setText(Messages.ChiponEEPROMView_2);
		txtStart = new Text(container, SWT.BORDER);
		Label space = new Label(container, SWT.NONE);
		space.setText("	"); //$NON-NLS-1$
		Label endAddress = new Label(container, SWT.NONE);
		endAddress.setText(Messages.ChiponEEPROMView_4);

		txtEnd = new Text(container, SWT.BORDER);
		Button btnSearch = new Button(container, SWT.NONE);
		btnSearch.setText(Messages.ChiponEEPROMView_5);



//		btnSearch.addSelectionListener(new SelectionAdapter() {
//			@Override
//			public void widgetSelected(SelectionEvent e) {
//				List<ChiponRomData> romDataList = getRomDataList();
//				ChiponRomDatas romDatas =  new PackRomDataList().packList(romDataList);
//				tableViewer.setInput(/*compare(tableViewer.getInput(),*/ romDatas);
//				tableViewer.refresh();
//			}
//		});
		Composite dataComposite = new Composite(parent, SWT.FULL_SELECTION);
		dataComposite.setLayout(new GridLayout(1, false));
		GridData gData = new GridData(SWT.FILL, SWT.FILL, true, true, 2, 7);
		dataComposite.setLayoutData(gData);

		final HexTableViewer tableViewer = new HexTableViewer(dataComposite, SWT.BORDER | SWT.FULL_SELECTION);// filteredTable.getTableViewer();
		table = tableViewer.getTable();
		table.setLinesVisible(false);
		table.setHeaderVisible(true);
		table.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, true, 1, 1));
		table.setBounds(0, 0, 85, 85);

		table.addListener(SWT.MeasureItem, new Listener() {
			@Override
			public void handleEvent(Event event) {
				// height cannot be per row so simply set
				Object obj = event.item.getData();
				if (obj instanceof ChiponGroupEEpromData) {
					ChiponGroupEEpromData rd = (ChiponGroupEEpromData) obj;
					String command = rd.getEEpromDatas().get(0).getValue();
					GC gc = new GC(Display.getDefault());
					int y = gc.stringExtent(command).y;
					event.height = y + 6;
					gc.dispose();
				}
			}
		});

		TableColumn tblclmnAddress = new TableColumn(table, SWT.CENTER);
		tblclmnAddress.setWidth(58);
		tblclmnAddress.setText("Address"); //$NON-NLS-1$

		TableColumn tblclmn00 = new TableColumn(table, SWT.CENTER);
		tblclmn00.setWidth(40);
		tblclmn00.setText("0"); //$NON-NLS-1$
		tblclmn00.setAlignment(SWT.CENTER);

		TableColumn tblclmn01 = new TableColumn(table, SWT.CENTER);
		tblclmn01.setWidth(40);
		tblclmn01.setText("1"); //$NON-NLS-1$

		TableColumn tblclmn02 = new TableColumn(table, SWT.CENTER);
		tblclmn02.setWidth(40);
		tblclmn02.setText("2"); //$NON-NLS-1$

		TableColumn tblclmn03 = new TableColumn(table, SWT.CENTER);
		tblclmn03.setWidth(40);
		tblclmn03.setText("3"); //$NON-NLS-1$

		TableColumn tblclmn04 = new TableColumn(table, SWT.CENTER);
		tblclmn04.setWidth(40);
		tblclmn04.setText("4"); //$NON-NLS-1$

		TableColumn tblclmn05 = new TableColumn(table, SWT.CENTER);
		tblclmn05.setWidth(40);
		tblclmn05.setText("5"); //$NON-NLS-1$

		TableColumn tblclmn06 = new TableColumn(table, SWT.CENTER);
		tblclmn06.setWidth(40);
		tblclmn06.setText("6"); //$NON-NLS-1$

		TableColumn tblclmn07 = new TableColumn(table, SWT.CENTER);
		tblclmn07.setWidth(40);
		tblclmn07.setText("7"); //$NON-NLS-1$

		TableColumn tblclmn08 = new TableColumn(table, SWT.CENTER);
		tblclmn08.setWidth(40);
		tblclmn08.setText("8"); //$NON-NLS-1$

		TableColumn tblclmn09 = new TableColumn(table, SWT.CENTER);
		tblclmn09.setWidth(40);
		tblclmn09.setText("9"); //$NON-NLS-1$

		TableColumn tblclmn10 = new TableColumn(table, SWT.CENTER);
		tblclmn10.setWidth(40);
		tblclmn10.setText("A"); //$NON-NLS-1$

		TableColumn tblclmn11 = new TableColumn(table, SWT.CENTER);
		tblclmn11.setWidth(40);
		tblclmn11.setText("B"); //$NON-NLS-1$

		TableColumn tblclmn12 = new TableColumn(table, SWT.CENTER);
		tblclmn12.setWidth(40);
		tblclmn12.setText("C"); //$NON-NLS-1$

		TableColumn tblclmn13 = new TableColumn(table, SWT.CENTER);
		tblclmn13.setWidth(40);
		tblclmn13.setText("D"); //$NON-NLS-1$

		TableColumn tblclmn14 = new TableColumn(table, SWT.CENTER);
		tblclmn14.setWidth(40);
		tblclmn14.setText("E"); //$NON-NLS-1$

		TableColumn tblclmn15 = new TableColumn(table, SWT.CENTER);
		tblclmn15.setWidth(40);
		tblclmn15.setText("F"); //$NON-NLS-1$

		TableColumn tblclmnASCII = new TableColumn(table, SWT.CENTER);
		tblclmnASCII.setWidth(100);
		tblclmnASCII.setText("ASCII"); //$NON-NLS-1$

		tableViewer.setLabelProvider(new ChiponEEPROMLableProvider());// 
		tableViewer.setContentProvider(new ChiponEEPROMContentProvider());// 

		UIUtil.setDefaultProperties(tableViewer);
		CellEditor[] cellEditors = new CellEditor[18];
		cellEditors[0] = null;
		cellEditors[1] = new TextCellEditor(table, SWT.BORDER);
		cellEditors[2] = new TextCellEditor(table, SWT.BORDER);
		cellEditors[3] = new TextCellEditor(table, SWT.BORDER);
		cellEditors[4] = new TextCellEditor(table, SWT.BORDER);
		cellEditors[5] = new TextCellEditor(table, SWT.BORDER);
		cellEditors[6] = new TextCellEditor(table, SWT.BORDER);
		cellEditors[7] = new TextCellEditor(table, SWT.BORDER);
		cellEditors[8] = new TextCellEditor(table, SWT.BORDER);
		cellEditors[9] = new TextCellEditor(table, SWT.BORDER);
		cellEditors[10] = new TextCellEditor(table, SWT.BORDER);
		cellEditors[11] = new TextCellEditor(table, SWT.BORDER);
		cellEditors[12] = new TextCellEditor(table, SWT.BORDER);
		cellEditors[13] = new TextCellEditor(table, SWT.BORDER);
		cellEditors[14] = new TextCellEditor(table, SWT.BORDER);
		cellEditors[15] = new TextCellEditor(table, SWT.BORDER);
		cellEditors[16] = new TextCellEditor(table, SWT.BORDER);
		cellEditors[17] = null;

		tableViewer.setCellEditors(cellEditors);
		tableViewer.setCellModifier(null);
		// tableViewer.setCellModifier(new
		// SetEEpromGroupCellModifier(tableViewer));
		btnSearch.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent e) {
				List<ChiponEEpromData> eepromDataList = getEEpromData();
				if(eepromDataList.size() != 0){
					ChiponEEpromDatas eepromDatas = new PackEEpromDataList().packList(eepromDataList);
					tableViewer.setInput(eepromDatas);
					tableViewer.refresh();
				}
			} 
		});

		DebugUITools.getDebugContextManager().getContextService(getSite().
				getWorkbenchWindow()).addDebugContextListener(this);

		return tableViewer;
	}

	protected List<ChiponEEpromData> getEEpromData() {
		
		List<ChiponEEpromData> eepromDataList = new ArrayList<>();	
		//从文本框获取首末地址的值
		String startAddr = txtStart.getText().trim().replace(" ", ""); //$NON-NLS-1$ //$NON-NLS-2$
		String endAddr = txtEnd.getText().trim().replace(" ", ""); //$NON-NLS-1$ //$NON-NLS-2$
		if (startAddr.length() == 0 || endAddr.length() == 0) {
			return eepromDataList;
		} else {
			int length = 0;
			try {
				length = Integer.parseInt(endAddr, 16) - Integer.parseInt(startAddr, 16);
			} catch (NumberFormatException e) {
				MessageDialog.openWarning(Display.getCurrent().getActiveShell(), Messages.ChiponEEPROMView_28, Messages.ChiponEEPROMView_29);
			}
			//不是正确的地址类型报错
			if(!isHex(startAddr)){
				MessageDialog.openWarning(Display.getCurrent().getActiveShell(), Messages.ChiponEEPROMView_30, Messages.ChiponEEPROMView_31);
			}
			// 从首地址到末地址，末地址不算
			if (length != 0) {
				//获取fThread的值
				if (fThread == null) {
					ISelection selection = DebugUITools.getDebugContextManager().getContextService(
							getViewSite().getWorkbenchWindow()).getActiveContext();
					if (selection instanceof IStructuredSelection) {
						Object element = ((IStructuredSelection) selection).getFirstElement();
						if (element instanceof ChiponDebugTarget) {
							fThread = ((ChiponDebugTarget) element).getCurrentThread();
						}
						if (element instanceof ChiponThread) {
							fThread = (ChiponThread) element;
						} else if (element instanceof ChiponStackFrame) {
							fThread = (ChiponThread) ((ChiponStackFrame) element).getThread();
						} else {
							return eepromDataList;
						}
					}
				}

				// 从首地址到末地址，末地址不算
				ChiponPrintEEpromCommandResult eeResult = (ChiponPrintEEpromCommandResult) fThread.sendCommand(
						new ChiponPrintEEpromCommand(
								"0x"+Integer.toHexString((Integer.parseInt(startAddr, 16) + 0X7f000000)) , //$NON-NLS-1$
								(length + 3 )/ 4));
				if(!eeResult.resultText.equals("success")){ //$NON-NLS-1$
					MessageDialog.openWarning(Display.getCurrent().getActiveShell(), Messages.ChiponEEPROMView_34, Messages.ChiponEEPROMView_35);
					return eepromDataList;
				}
				
				eepromDataList = eeResult.eepromDataList;

				
				

				// 重新设置EEPromData的地址

				ChiponThread.eepromDataList = eepromDataList;
			}
		}
		return eepromDataList;
	}

	@Override
	protected void createActions() {

	}

	@Override
	protected String getHelpContextId() {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	protected void fillContextMenu(IMenuManager menu) {
		menu.add(new Separator(IWorkbenchActionConstants.MB_ADDITIONS));

	}

	@Override
	protected void configureToolBar(IToolBarManager tbm) {

	}

	@Override
	public void dispose() {
		DebugUITools.getDebugContextManager().getContextService(getSite().
				getWorkbenchWindow()).removeDebugContextListener(this);
		super.dispose();
	}

	private boolean isHex(String input) {
		String regex = "[0-9A-Fa-f]+"; //$NON-NLS-1$
		Pattern pattern = Pattern.compile(regex);
		Matcher matcher = pattern.matcher(input);		
		return matcher.matches();
	}
	

}
