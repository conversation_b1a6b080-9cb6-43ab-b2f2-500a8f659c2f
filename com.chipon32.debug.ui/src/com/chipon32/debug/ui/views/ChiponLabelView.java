package com.chipon32.debug.ui.views;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.core.runtime.IProgressMonitor;
import org.eclipse.core.runtime.IStatus;
import org.eclipse.core.runtime.Status;
import org.eclipse.debug.ui.AbstractDebugView;
import org.eclipse.debug.ui.DebugUITools;
import org.eclipse.debug.ui.contexts.DebugContextEvent;
import org.eclipse.debug.ui.contexts.IDebugContextListener;
import org.eclipse.jface.action.IMenuManager;
import org.eclipse.jface.action.IToolBarManager;
import org.eclipse.jface.viewers.CellEditor;
import org.eclipse.jface.viewers.ISelection;
import org.eclipse.jface.viewers.IStructuredSelection;
import org.eclipse.jface.viewers.TextCellEditor;
import org.eclipse.jface.viewers.Viewer;
import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.GC;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Event;
import org.eclipse.swt.widgets.Listener;
import org.eclipse.swt.widgets.Table;
import org.eclipse.swt.widgets.TableColumn;
import org.eclipse.ui.progress.UIJob;

import com.chipon32.debug.core.model.ChiponDebugTarget;
import com.chipon32.debug.core.model.ChiponLabelData;
import com.chipon32.debug.core.model.ChiponRomData;
import com.chipon32.debug.core.model.ChiponStackFrame;
import com.chipon32.debug.core.model.ChiponThread;
import com.chipon32.util.ui.HexTableViewer;
import com.chipon32.util.ui.UIUtil;

public class ChiponLabelView extends AbstractDebugView implements IDebugContextListener{

	public static final String ID = "com.chipon32.debug.ui.view.LabelView"; //$NON-NLS-1$
	private Table table;
	private ChiponThread  fThread;
	
	public ChiponLabelView() {
	}

	@Override
	protected Viewer createViewer(Composite parent) {
		HexTableViewer tableViewer = new HexTableViewer(parent, SWT.BORDER | SWT.FULL_SELECTION);//filteredTable.getTableViewer();
		table = tableViewer.getTable();
		table.setLinesVisible(true);
		table.setHeaderVisible(true);
		table.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, true, 1, 1));
		table.setBounds(0, 0, 85, 85);
		
		table.addListener(SWT.MeasureItem, new Listener() {
			@Override
			public void handleEvent(Event event) {
			      // height cannot be per row so simply set
				  Object obj = event.item.getData();
				  if(obj instanceof ChiponLabelData)
				  {
					  ChiponLabelData rd = (ChiponLabelData)obj;
					  String command = rd.getValue();
					  GC gc = new GC(Display.getDefault());
					  int y = gc.stringExtent(command).y;
					  event.height = y + 3;
					  gc.dispose();
				  }
			   }
			});
	
		// tabel header , lie ming
		TableColumn tblclmn01 = new TableColumn(table, SWT.CENTER);
		tblclmn01.setWidth(120);
		tblclmn01.setText(Messages.ChiponLabelView_1);
		
		TableColumn tblclmn02 = new TableColumn(table, SWT.CENTER);
		tblclmn02.setWidth(60);
		tblclmn02.setText(Messages.ChiponLabelView_2);
		
		TableColumn tblclmn04 = new TableColumn(table, SWT.CENTER);
		tblclmn04.setWidth(120);
		tblclmn04.setText(Messages.ChiponLabelView_3);
		
		TableColumn tblclmn05 = new TableColumn(table, SWT.CENTER);
		tblclmn05.setWidth(120);
		tblclmn05.setText(Messages.ChiponLabelView_4);
		
		TableColumn tblclmn03 = new TableColumn(table, SWT.CENTER);
		tblclmn03.setWidth(120);
		tblclmn03.setText(Messages.ChiponLabelView_5);
		
		tableViewer.setLabelProvider(new ChiponLabelLableProvider());
		tableViewer.setContentProvider(new ChiponLabelContentProvider());
		
		UIUtil.setDefaultProperties(tableViewer);
		
		CellEditor[] cellEditors = new CellEditor[5];
		cellEditors[0] = new TextCellEditor(table, SWT.BORDER);
		cellEditors[1] = new TextCellEditor(table, SWT.BORDER);
		cellEditors[2] = new TextCellEditor(table, SWT.BORDER);
		cellEditors[3] = new TextCellEditor(table, SWT.BORDER);
		cellEditors[4] = new TextCellEditor(table, SWT.BORDER);
		
		tableViewer.setCellEditors(cellEditors);
		tableViewer.setCellModifier(new ChiponLabelCellModifier(tableViewer));//***
		
		DebugUITools.getDebugContextManager().getContextService(getSite().getWorkbenchWindow()).addDebugContextListener(this);
		
		if(ChiponThread.labelDataList==null||ChiponThread.labelDataList.size()==0){
			getSite().setSelectionProvider(tableViewer);
		}else{
			tableViewer.setInput(ChiponThread.labelDataList);
			tableViewer.refresh();
		}
		return tableViewer;
	}

	@Override
	protected void createActions() {
		
	}

	@Override
	protected String getHelpContextId() {
		return null;
	}

	@Override
	protected void fillContextMenu(IMenuManager menu) {
		
	}

	@Override
	protected void configureToolBar(IToolBarManager tbm) {
		
	}

	@Override
	public void debugContextChanged(final DebugContextEvent event) {
		 new UIJob(getSite().getShell().getDisplay(), "LabelView update") { //$NON-NLS-1$
		        {
		            setSystem(true);
		        }
		        
		        @Override
				public IStatus runInUIThread(IProgressMonitor monitor) {
		        	if (getViewer() != null) { // runs asynchronously, view may be disposed
		        		update(event.getContext());
		        	}
		            return Status.OK_STATUS;
		        }
		    }.schedule();
	}
	
	/**
     * Updates the view for the selected thread (if suspended)
     */
    private void update(ISelection context) {
    	fThread = null;
        
        if (context instanceof IStructuredSelection) {
            Object element = ((IStructuredSelection)context).getFirstElement();
            if(element instanceof ChiponDebugTarget){
            	//fThread = ((ChiponDebugTarget)element).getThread();
            	return;
            }
            if (element instanceof ChiponThread) {
            	//fThread = (ChiponThread)element;
            	return;
            } else if (element instanceof ChiponStackFrame) {
            	fThread = (ChiponThread) ((ChiponStackFrame) element).getThread();
            }else return;
        }
        
        List<ChiponLabelData> Datas = new ArrayList<ChiponLabelData>();
        Datas.add(new ChiponLabelData(fThread,"+","","","","")); //$NON-NLS-1$ //$NON-NLS-2$ //$NON-NLS-3$ //$NON-NLS-4$ //$NON-NLS-5$
		if (fThread != null && fThread.isSuspended() && ChiponThread.labelDataList!=null && fThread.isCanSetInputLabel()) {
			
			if(ChiponThread.labelDataList.size()>0){
				for(ChiponLabelData data : ChiponThread.labelDataList){
					 if((data.getAddress()==null || data.getAddress().length()==0)&&(data.getName()!=null && data.getName().length()>0 && !data.getName().equals("+"))){ //$NON-NLS-1$
						 Datas.add(Datas.size()-1,new ChiponLabelData(fThread,data.getName(),"","","","")); //$NON-NLS-1$ //$NON-NLS-2$ //$NON-NLS-3$ //$NON-NLS-4$
					 }
					 for(ChiponRomData romData : ChiponThread.romDataList){
						 if(data.getAddress()!=null && data.getAddress().length()>0 && romData.getAddress().substring(2).equals(Integer.toHexString(Integer.parseInt(data.getAddress().substring(2),16)))){
							 if(data!=null && data.getName()!=null){
								 Datas.add(Datas.size()-1,new ChiponLabelData(fThread, data.getName(),Integer.toBinaryString(Integer.parseInt(romData.getValue().toUpperCase(), 16)),Integer.parseInt(romData.getValue().toUpperCase(), 16)+"",romData.getValue().toUpperCase(),data.getAddress())); //$NON-NLS-1$
							 }else{
								 Datas.add(Datas.size()-1,new ChiponLabelData(fThread,"",Integer.toBinaryString(Integer.parseInt(romData.getValue().toUpperCase(), 16)),Integer.parseInt(romData.getValue().toUpperCase(), 16)+"",romData.getValue().toUpperCase(),data.getAddress())); //$NON-NLS-1$ //$NON-NLS-2$
							 }
						 }
					 }
				 }
			}
			
			ChiponThread.labelDataList = Datas;
			fThread.setCanSetInputLabel(false);
		}else {
			return;
		}
		getViewer().setInput(Datas);
		getViewer().refresh();
    }

}
