package com.chipon32.debug.ui.views;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.eclipse.core.resources.IProject;
import org.eclipse.core.runtime.IProgressMonitor;
import org.eclipse.core.runtime.IStatus;
import org.eclipse.core.runtime.Status;
import org.eclipse.debug.ui.AbstractDebugView;
import org.eclipse.debug.ui.DebugUITools;
import org.eclipse.debug.ui.contexts.DebugContextEvent;
import org.eclipse.debug.ui.contexts.IDebugContextListener;
import org.eclipse.jface.action.IMenuManager;
import org.eclipse.jface.action.IToolBarManager;
import org.eclipse.jface.viewers.AbstractTreeViewer;
import org.eclipse.jface.viewers.CellEditor;
import org.eclipse.jface.viewers.ISelection;
import org.eclipse.jface.viewers.IStructuredSelection;
import org.eclipse.jface.viewers.TextCellEditor;
import org.eclipse.jface.viewers.TreeViewer;
import org.eclipse.jface.viewers.Viewer;
import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.FontData;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.TreeColumn;
import org.eclipse.ui.progress.UIJob;

import com.chipon32.chiponide.core.properties.ChipOnProjectProperties;
import com.chipon32.chiponide.core.properties.ProjectPropertyManager;
import com.chipon32.debug.core.model.ChiponCurrencyRegisterData;
import com.chipon32.debug.core.model.ChiponDebugTarget;
import com.chipon32.debug.core.model.ChiponStackFrame;
import com.chipon32.debug.core.model.ChiponThread;
import com.chipon32.debug.core.protocol.ChiponPrintCurrencyRegisterCommand;
import com.chipon32.debug.core.protocol.ChiponPrintCurrencyRegisterCommandResult;
import com.chipon32.hex.core.util.IConfigurationProvider;
import com.chipon32.util.provider.ConfigurationFactory;
import com.chipon32.util.ui.UIUtil;

public class ChiponCurrencyRegisterView extends AbstractDebugView 
	implements IDebugContextListener{
	
	public static final String ID = "com.chipon32.debug.ui.view.currencyRegisterView"; //$NON-NLS-1$

	public ChiponCurrencyRegisterView() {}
	
	private ChiponThread fThread;
	
	private void setInput(TreeViewer tv){
//		update(tv.getSelection());   //2017.1.11为了测试添加
		if(tv != null){
			List<ChiponCurrencyRegisterData> dataList = ChiponThread.DataList;
			if(dataList != null && dataList.size()>0){
				IProject project=null;
				if(fThread!=null)
				 project =((ChiponDebugTarget)fThread.getDebugTarget()).getfProject();				
				if (project != null && dataList != null) {		
					// 这里没啥作用，还有一个贯彻者会执行一次，也要保证那次被转换
					dataList = ListChiponCurrencyRegisterDataReStroe(dataList, project);  //List..ReStroe中的dataLists
									
					tv.setInput(dataList);
//					tv.expandAll();
					tv.expandToLevel(2);
					tv.refresh();
				}
			}else {
				getSite().setSelectionProvider(tv);
			}
		}
	}

	/**
	 * 在调试前若视图是开启的状态，则每次暂停时调用debugContextChanged()方法更新数据。
	 */
	@Override
	public void debugContextChanged(final DebugContextEvent event) {
		new UIJob(getSite().getShell().getDisplay(), Messages.ChiponCurrencyRegisterView_1) {
			{
				setSystem(true);
			}

			@Override
			public IStatus runInUIThread(IProgressMonitor monitor) {
				if (getViewer() != null) { // runs asynchronously, view may be
											// disposed
					update(event.getContext());
				}
				return Status.OK_STATUS;
			}
		}.schedule();

	}
	/**
	 * Updates the view for the selected thread (if suspended)
	 */
	public void update(ISelection context) {
		fThread = null;
		if (context instanceof IStructuredSelection) {
			Object element = ((IStructuredSelection) context).getFirstElement();
			if (element instanceof ChiponDebugTarget) {
				 fThread = ((ChiponDebugTarget)element).getCurrentThread();
			}
			if (element instanceof ChiponThread) {
				 fThread = (ChiponThread)element;
			} else if (element instanceof ChiponStackFrame) {
				fThread = (ChiponThread) ((ChiponStackFrame) element).getThread();
			} else
				return;
		}
		IProject project = null; 
		if(fThread!=null)
		 project = ((ChiponDebugTarget)fThread.getDebugTarget()).getfProject();
		List<ChiponCurrencyRegisterData> dataList = null;
		
		if (fThread != null && fThread.isSuspended()&&fThread.isCanSetInputCurrencyRegister()) {
			//获取通用寄存器R0~R31的值
			ChiponPrintCurrencyRegisterCommandResult currencyRegisterResult = 
						(ChiponPrintCurrencyRegisterCommandResult) fThread.sendCommand(	new ChiponPrintCurrencyRegisterCommand("info registers")); //$NON-NLS-1$
			if(currencyRegisterResult != null){
					ChiponCurrencyRegisterData registerData = ChiponViewTool.getViewTool().          // 不建议加入，因为xpsr具有 NCZV的标记了，信息更多
							getSingleChiponCurrencyRegisterData(fThread, "SYS_PSW"); //$NON-NLS-1$
				if(registerData!=null)
				{
					currencyRegisterResult.dataList.add(registerData);
				}
				ChiponThread.DataList = currencyRegisterResult.dataList;
				dataList=currencyRegisterResult.dataList;
				fThread.setUpdateCurResigter(true);
			}
			fThread.setCanSetInputCurrencyRegister(false);
		}else {
			return;
		}
		//######################################################################
		
		if (project != null && project.exists() && dataList != null) {
			//##########################
			dataList = ListChiponCurrencyRegisterDataReStroe(dataList,project);
			//##########################
			Viewer viewer = getViewer();
			
			if(viewer instanceof AbstractTreeViewer){
				AbstractTreeViewer treeviewer = (AbstractTreeViewer)viewer;
				
				treeviewer.setInput(dataList);
//				treeviewer.expandAll();
				treeviewer.expandToLevel(2); // 3 with bit 
				treeviewer.refresh();
			}
		}
	}
	
	@Override
	protected Viewer createViewer(Composite parent) {
		
		TreeViewer tv = new TreeViewer(parent, SWT.BORDER | SWT.FULL_SELECTION);
		tv.setContentProvider(new ChiponCurrencyRegisterContentProvider());
		tv.setLabelProvider(new ChiponCurrencyRegisterLableProvider());
		tv.getTree().setLayoutData(new GridData(GridData.FILL_BOTH));
		tv.getTree().setHeaderVisible(true);
		tv.getTree().setLinesVisible(true);
		
		FontData curfd[] = tv.getTree().getFont()
				.getFontData();
		// 字体高度
		int approxfontwidth = curfd[0].getHeight();
		// 存在子集 ，宽点
		TreeColumn tcName = new TreeColumn( tv.getTree(),SWT.LEFT);
		tcName.setWidth(14 * approxfontwidth);
		tcName.setResizable(true);
		tcName.setMoveable(true);
		tcName.setText(Messages.ChiponCurrencyRegisterView_4);
		
		// 十六进制，最多32个字符   ACC，给出默认17个字符的空间
		TreeColumn tcValue = new TreeColumn( tv.getTree(),SWT.RIGHT);
		tcValue.setWidth(12 * approxfontwidth);
		tcValue.setResizable(true);
		tcValue.setMoveable(true);
		tcValue.setText(Messages.ChiponCurrencyRegisterView_5);
		
		// 十进制，32bit 可能宽点 靠人啦吧，空间也不能占的太多
		TreeColumn tdValue = new TreeColumn( tv.getTree(),SWT.RIGHT);
		tdValue.setWidth(12 * approxfontwidth);
		tdValue.setResizable(true);
		tdValue.setMoveable(true);
		tdValue.setText(Messages.ChiponCurrencyRegisterView_6);
		
		//数据修改
		UIUtil.setDefaultProperties(tv);
		CellEditor[] cellEditors = new CellEditor[3];
		cellEditors[0] = null;
		cellEditors[1] = new TextCellEditor(tv.getTree(), SWT.BORDER);
		cellEditors[2] = new TextCellEditor(tv.getTree(), SWT.BORDER);
		tv.setCellEditors(cellEditors);
		tv.setCellModifier(new ChiponCurrentRegisterCellModifier(tv));

		DebugUITools.getDebugContextManager().getContextService(getSite().
				getWorkbenchWindow()).addDebugContextListener(this);
		setInput(tv);  //treeViewer中加载通用寄存器的值
		return tv;
	}

	@Override
	protected void createActions() {
		// TODO Auto-generated method stub
	}
	
	/**
	 * 通用寄存器分类
	 * @param dataList
	 * @param project
	 * @return
	 */
	public static  List<ChiponCurrencyRegisterData> ListChiponCurrencyRegisterDataReStroe(List<ChiponCurrencyRegisterData> dataList,IProject project)
	{
		if(dataList==null || dataList.size()==0)
			return null;
		//############################合并处理分组
		ProjectPropertyManager ppm = ProjectPropertyManager.getPropertyManager(project);
		ChipOnProjectProperties fTargetProps = ppm.getProjectProperties();  //包含：执行方式，芯片名称、电压、调试模式、加密方式等等信息
		String chipname = fTargetProps.getChipName();// 获取当前项目的芯片型号
		// 主要看  是否 R16 和 ACC支持属性配置在哪里，属性变动麻烦，给予型号配置文件吧，模块生成遍历点
		// 如果独立型号配置文件中，需要解析属性，型号配置下代码维护遍历
		IConfigurationProvider provider = ConfigurationFactory.getProvider(chipname);
		
		// 这个根的首个对象不关联这个父类，而自成父类，便于后续修改方法编写
		ChiponCurrencyRegisterData coredataList = new ChiponCurrencyRegisterData("Group", "", "");	coredataList.setFlag(true); //$NON-NLS-1$ //$NON-NLS-2$ //$NON-NLS-3$
		ChiponCurrencyRegisterData accdataList = new ChiponCurrencyRegisterData("Group3", "", "");	accdataList.setFlag(true); //$NON-NLS-1$ //$NON-NLS-2$ //$NON-NLS-3$
		ChiponCurrencyRegisterData highrxdataList = new ChiponCurrencyRegisterData("Group2", "", "");	highrxdataList.setFlag(true); //$NON-NLS-1$ //$NON-NLS-2$ //$NON-NLS-3$
		ChiponCurrencyRegisterData bankdataList = new ChiponCurrencyRegisterData("BankSP", "", "");		bankdataList.setFlag(true); //$NON-NLS-1$ //$NON-NLS-2$ //$NON-NLS-3$
		
		List<ChiponCurrencyRegisterData> dataLists = new ArrayList<ChiponCurrencyRegisterData>();
		Map<String ,ChiponCurrencyRegisterData> MapR16TR31 = new HashMap<>();
		//##############################################################
		
		for(ChiponCurrencyRegisterData ccr:dataList)
		{
			String   regName= ccr.getName();
			String[] preName = regName.split(":?[0-9]+");				 //$NON-NLS-1$
			if(preName[0].equalsIgnoreCase("r")) //$NON-NLS-1$
			{
				int regnum=Integer.parseInt(regName.substring(1));
				if(regnum>15)
				{
					highrxdataList.AddChild(ccr);
					MapR16TR31.put(regName.toLowerCase(), ccr);  // 后续添加到ACC根据名索引工作
				}
				else					
					coredataList.AddChild(ccr);
			}
			else if(preName[0].equalsIgnoreCase("acc")) //$NON-NLS-1$
			{
				accdataList.AddChild(ccr);
			}
			else if(preName[0].equalsIgnoreCase("fxpsr")) {//$NON-NLS-1$ float skip			
			}
			else 					
			{
				if(regName.equalsIgnoreCase("xpsr"))						 //$NON-NLS-1$
				{
					coredataList.AddChild(ccr);		//	ccr.setFlag(true);  // 这个寄存器不支持写的
					// 细分
					List<ChiponCurrencyRegisterData> chiponDatasChildren=new ArrayList<ChiponCurrencyRegisterData>();
					long registervalue=Long.parseLong(ccr.getdValue());
					int bitLength;  //位宽
					int startLocation;	//位的起始位置	
					long bitValue;
					ChiponCurrencyRegisterData chiponData;

					//################################
					bitLength = 1; 
					startLocation = 28;						
					bitValue = ParseBitFromValue(registervalue,startLocation,bitLength);									
						 chiponData=new ChiponCurrencyRegisterData(
								"V", Long.toHexString(bitValue), Long.toString(bitValue)); //$NON-NLS-1$
					//给bit位设置父寄存器
					chiponData.setParentRegisterData(ccr);
					chiponData.setHasParent(true);chiponData.setFlag(true); // 这个继承不允许修改
					chiponData.setBitLength(bitLength);
					chiponData.setBitLocation(Integer.toString(startLocation));							
					chiponDatasChildren.add(chiponData);
					//################################
					bitLength = 1; 
					startLocation = 29;						
					bitValue = ParseBitFromValue(registervalue,startLocation,bitLength);									
						 chiponData=new ChiponCurrencyRegisterData(
								"C", Long.toHexString(bitValue), Long.toString(bitValue)); //$NON-NLS-1$
					//给bit位设置父寄存器
					chiponData.setParentRegisterData(ccr);
					chiponData.setHasParent(true);chiponData.setFlag(true); // 这个继承不允许修改
					chiponData.setBitLength(bitLength);
					chiponData.setBitLocation(Integer.toString(startLocation));							
					chiponDatasChildren.add(chiponData);

					//################################
					bitLength = 1; 
					startLocation = 30;						
					bitValue = ParseBitFromValue(registervalue,startLocation,bitLength);								
						 chiponData=new ChiponCurrencyRegisterData(
								"Z", Long.toHexString(bitValue), Long.toString(bitValue)); //$NON-NLS-1$
					//给bit位设置父寄存器
					chiponData.setParentRegisterData(ccr);
					chiponData.setHasParent(true);chiponData.setFlag(true); // 这个继承不允许修改
					chiponData.setBitLength(bitLength);
					chiponData.setBitLocation(Integer.toString(startLocation));							
					chiponDatasChildren.add(chiponData);
					//################################
					bitLength = 1; 
					startLocation = 31;						
					bitValue = ParseBitFromValue(registervalue,startLocation,bitLength);								
						 chiponData=new ChiponCurrencyRegisterData(
								"N", Long.toHexString(bitValue), Long.toString(bitValue)); //$NON-NLS-1$
					//给bit位设置父寄存器
					chiponData.setParentRegisterData(ccr);
					chiponData.setHasParent(true);chiponData.setFlag(true); // 这个继承不允许修改
					chiponData.setBitLength(bitLength);
					chiponData.setBitLocation(Integer.toString(startLocation));							
					chiponDatasChildren.add(chiponData);
					//################################
					bitLength = 1; 
					startLocation = 25;						
					bitValue = ParseBitFromValue(registervalue,startLocation,bitLength);									
					chiponData=new ChiponCurrencyRegisterData(
								"OVACC", Long.toHexString(bitValue), Long.toString(bitValue)); //$NON-NLS-1$
					//给bit位设置父寄存器
					chiponData.setParentRegisterData(ccr);
					chiponData.setHasParent(true);chiponData.setFlag(true); // 这个继承不允许修改
					chiponData.setBitLength(bitLength);
					chiponData.setBitLocation(Integer.toString(startLocation));							
					chiponDatasChildren.add(chiponData);
					//################################
					bitLength = 1; 
					startLocation = 24;						
					bitValue = ParseBitFromValue(registervalue,startLocation,bitLength);									
					chiponData=new ChiponCurrencyRegisterData(
								"SAT", Long.toHexString(bitValue), Long.toString(bitValue)); //$NON-NLS-1$
					//给bit位设置父寄存器
					chiponData.setParentRegisterData(ccr);
					chiponData.setHasParent(true);chiponData.setFlag(true); // 这个继承不允许修改
					chiponData.setBitLength(bitLength);
					chiponData.setBitLocation(Integer.toString(startLocation));							
					chiponDatasChildren.add(chiponData);
					//################################
					bitLength = 8; 
					startLocation = 16;						
					bitValue = ParseBitFromValue(registervalue,startLocation,bitLength);									
					chiponData=new ChiponCurrencyRegisterData(
								"SATACCx", Long.toHexString(bitValue), Long.toString(bitValue)); //$NON-NLS-1$
					//给bit位设置父寄存器
					chiponData.setParentRegisterData(ccr);
					chiponData.setHasParent(true);chiponData.setFlag(true); // 这个继承不允许修改
					chiponData.setBitLength(bitLength);
					chiponData.setBitLocation(Integer.toString(startLocation));							
					chiponDatasChildren.add(chiponData);
					//################################
					bitLength = 1; 
					startLocation = 9;						
					bitValue = ParseBitFromValue(registervalue,startLocation,bitLength);									
					chiponData=new ChiponCurrencyRegisterData(
								"STACKALIGN", Long.toHexString(bitValue), Long.toString(bitValue)); //$NON-NLS-1$
					//给bit位设置父寄存器
					chiponData.setParentRegisterData(ccr);
					chiponData.setHasParent(true);chiponData.setFlag(true); // 这个继承不允许修改
					chiponData.setBitLength(bitLength);
					chiponData.setBitLocation(Integer.toString(startLocation));							
					chiponDatasChildren.add(chiponData);
					//################################
					bitLength = 7; 
					startLocation = 0;						
					bitValue = ParseBitFromValue(registervalue,startLocation,bitLength);									
					chiponData=new ChiponCurrencyRegisterData(
								"INTACT", Long.toHexString(bitValue), Long.toString(bitValue)); //$NON-NLS-1$
					//给bit位设置父寄存器
					chiponData.setParentRegisterData(ccr);
					chiponData.setHasParent(true);chiponData.setFlag(true); // 这个继承不允许修改
					chiponData.setBitLength(bitLength);
					chiponData.setBitLocation(Integer.toString(startLocation));							
					chiponDatasChildren.add(chiponData);
					//################################	
					ccr.setChiponDataList(chiponDatasChildren);
				}
				else if(regName.equalsIgnoreCase("msp") || regName.equalsIgnoreCase("psp")) //$NON-NLS-1$ //$NON-NLS-2$
				{
					bankdataList.AddChild(ccr);
				}
				else if(regName.equalsIgnoreCase("lr")) //$NON-NLS-1$
				{
					ccr.setName(regName+"(r13)"); //$NON-NLS-1$
					coredataList.AddChild(ccr);
				}
				else if(regName.equalsIgnoreCase("sp")) //$NON-NLS-1$
				{
					ccr.setName(regName+"(r14)"); //$NON-NLS-1$
					coredataList.AddChild(ccr);
				}
				else if(regName.equalsIgnoreCase("pc")) //$NON-NLS-1$
				{
					ccr.setName(regName+"(r15)"); //$NON-NLS-1$
					coredataList.AddChild(ccr);
				}
				else						
				{
					if(regName.equalsIgnoreCase("lr(r13)") || regName.equalsIgnoreCase("sp(r14)")|| regName.equalsIgnoreCase("pc(r15)")) //$NON-NLS-1$ //$NON-NLS-2$ //$NON-NLS-3$
					{
						coredataList.AddChild(ccr);
					}
					else
					{
						dataLists.add(ccr);   // SYS_PSW
					}
				}
			}			
		}// end type
		dataLists.add(bankdataList); // MSP PSP
		dataLists.add(coredataList); // R LR PSW
		// R16-R31
		if(Integer.parseInt(provider.getIsSupportR16(),16)==1)
			dataLists.add(highrxdataList);
		//// 扩展ACC
		if(Integer.parseInt(provider.getIsSupportACC(),16)==1)		{
			dataLists.add(accdataList);			
			if(accdataList!=null && accdataList.getChiponDataList()!=null)
			for(ChiponCurrencyRegisterData ccrContainR:accdataList.getChiponDataList())			{
				String   accName= ccrContainR.getName();
				int		accnum =Integer.parseInt(accName.substring(3))*2+16;
				int		accnum1 =accnum+1;
				ccrContainR.AddChild(MapR16TR31.get("r"+accnum)); //$NON-NLS-1$
				ccrContainR.AddChild(MapR16TR31.get("r"+accnum1)); //$NON-NLS-1$
			}
		}

		
		return dataLists;
	}
	
	@Override
	protected String getHelpContextId() {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	protected void fillContextMenu(IMenuManager menu) {
		// TODO Auto-generated method stub
		
	}

	@Override
	protected void configureToolBar(IToolBarManager tbm) {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void dispose() {
		DebugUITools.getDebugContextManager()
		.getContextService(getSite().getWorkbenchWindow())
		.removeDebugContextListener(this);
		super.dispose();
	}
	static long ParseBitFromValue(long registervalue,long startLocation,long bitLength){
		long num;
		num = 0;  //mask
		for(int i = 0; i < bitLength; i++){
			num += 1<<i;
		}
		return ((registervalue&(num<<startLocation))>>startLocation)&num;	
	}
}
