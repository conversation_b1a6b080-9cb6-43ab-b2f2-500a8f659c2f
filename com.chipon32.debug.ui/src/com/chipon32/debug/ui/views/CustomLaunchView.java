package com.chipon32.debug.ui.views;

import org.eclipse.debug.core.DebugException;
import org.eclipse.debug.internal.ui.views.launch.LaunchView;
import org.eclipse.jface.viewers.IStructuredSelection;
import org.eclipse.jface.viewers.SelectionChangedEvent;
import org.eclipse.swt.widgets.Display;
import org.eclipse.ui.IViewPart;
import org.eclipse.ui.IWorkbenchWindow;
import org.eclipse.ui.PlatformUI;
import com.chipon32.debug.core.model.ChiponStackFrame;
import com.chipon32.debug.core.model.ChiponThread;
import com.chipon32.debug.core.protocol.ChiponMulCoreCommand;

@SuppressWarnings("restriction")
public class CustomLaunchView extends LaunchView {

	private int index = 0;

	@Override
	public void selectionChanged(SelectionChangedEvent event) {
		IStructuredSelection selection = event.getStructuredSelection();
		Object obj = selection.getFirstElement();
		if (obj != null) {
			if (obj instanceof ChiponStackFrame) {
				try {
					int length = ((ChiponStackFrame) obj).getChiponDebugTarget().getThreads().length;
					if(length > 1) {
						ChiponThread thread = (ChiponThread) ((ChiponStackFrame) obj).getThread();
						if (index != ((ChiponStackFrame) obj).getThreadIndex() && !thread.isVariableEmpty()) {
							index = ((ChiponStackFrame) obj).getThreadIndex();
							((ChiponStackFrame) obj).getChiponDebugTarget().setCurrentThreadIndex(index);
							((ChiponStackFrame) obj).getChiponDebugTarget()
									.sendCommand(new ChiponMulCoreCommand(((ChiponStackFrame) obj).getThreadIndex(), 0));
							((ChiponStackFrame) obj).getChiponDebugTarget().resetBreakpoints();
							try {
								((ChiponStackFrame) obj).getChiponDebugTarget().getCurrentThread().stepiOver();
							} catch (DebugException e) {
								e.printStackTrace();
							}

							((ChiponStackFrame) obj).getChiponDebugTarget().getCurrentThread().refreshDisassemblyViewer();
							((ChiponStackFrame) obj).getChiponDebugTarget().getCurrentThread().refreshInfoLocals();

							Display.getDefault().asyncExec(new Runnable() {

								@Override
								public void run() {
									((ChiponStackFrame) obj).getChiponDebugTarget().getCurrentThread()
											.setCanSetInputCurrencyRegister(true);
									IWorkbenchWindow[] workbenchWindows = PlatformUI.getWorkbench().getWorkbenchWindows();
									if (workbenchWindows.length > 0 && workbenchWindows[0].getActivePage() != null) {
										IViewPart viewpart = workbenchWindows[0].getActivePage()
												.findView("com.chipon32.debug.ui.view.currencyRegisterView");
										if (viewpart != null && viewpart instanceof ChiponCurrencyRegisterView) {
											((ChiponCurrencyRegisterView) viewpart).update(selection);
										}
									}
								}
							});

							Display.getDefault().asyncExec(new Runnable() {

								@Override
								public void run() {
									((ChiponStackFrame) obj).getChiponDebugTarget().getCurrentThread()
											.setCanSetInputCurrencyFloatRegister(true);
									IWorkbenchWindow[] workbenchWindows = PlatformUI.getWorkbench().getWorkbenchWindows();
									if (workbenchWindows.length > 0 && workbenchWindows[0].getActivePage() != null) {
										IViewPart viewpart = workbenchWindows[0].getActivePage()
												.findView("com.chipon32.debug.ui.view.currencyFloatRegisterView");
										if (viewpart != null && viewpart instanceof ChiponCurrencyFloatRegisterView) {
											((ChiponCurrencyFloatRegisterView) viewpart).update(selection);
										}
									}
								}
							});
						}
					}
				} catch (DebugException e1) {
					e1.printStackTrace();
				}
			}
		}
		super.selectionChanged(event);

	}
}