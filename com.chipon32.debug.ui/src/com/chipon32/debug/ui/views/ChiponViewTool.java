package com.chipon32.debug.ui.views;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.core.resources.IProject;
import org.eclipse.swt.graphics.Color;

import com.chipon32.chiponide.core.chipondescription.ICategory;
import com.chipon32.chiponide.core.chipondescription.IDeviceDescription;
import com.chipon32.chiponide.core.chipondescription.IEntry;
import com.chipon32.chiponide.core.chipondescription.chipio.Bits;
import com.chipon32.chiponide.core.chipondescription.chipio.Module;
import com.chipon32.chiponide.core.chipondescription.chipio.Register;
import com.chipon32.chiponide.core.properties.ChipOnProjectProperties;
import com.chipon32.chiponide.core.properties.ProjectPropertyManager;
import com.chipon32.chiponide.ui.ChipOniohDeviceDescriptionProvider;
import com.chipon32.chiponide.ui.UiActivator;
import com.chipon32.debug.core.model.ChiponACCXData;
import com.chipon32.debug.core.model.ChiponCurrencyFloatRegisterData;
import com.chipon32.debug.core.model.ChiponCurrencyRegisterData;
import com.chipon32.debug.core.model.ChiponDebugTarget;
import com.chipon32.debug.core.model.ChiponGroupRomData;
import com.chipon32.debug.core.model.ChiponLabelData;
import com.chipon32.debug.core.model.ChiponRomData;
import com.chipon32.debug.core.model.ChiponThread;
import com.chipon32.debug.core.protocol.ChiponSingleFloatRegisterCommand;
import com.chipon32.debug.core.protocol.ChiponSingleFloatRegisterCommandResult;
import com.chipon32.debug.core.protocol.ChiponSingleRegisterCommand;
import com.chipon32.debug.core.protocol.ChiponSingleRegisterCommandResult;
/***
 *<AUTHOR>
 ***/
public class ChiponViewTool {
	private static ChiponViewTool viewTool;
	public static ChiponViewTool getViewTool(){
		if(viewTool == null)
			viewTool = new ChiponViewTool();
		return viewTool;
	}

	//不能修改的地址，暂定
	public static String[] noAllowModify={"0x0","0x2","0x3","0x4","0xa","0xb","0xc","0xd","0x1c","0x2c","0x2d","0x30","0x37"}; 
	
	/*
	 * 
	 * 根据输入的数据，返回新的视图模型数据
	 * */
	public List<ChiponRomData> getNewThreadList(List<ChiponRomData> list,ChiponGroupRomData groupRomData,int columnIndex, String tmp,String valueFormat) {
		List<ChiponRomData> newList =new ArrayList<ChiponRomData>();
		int findwhere = -1;
		for(int i=0;i<list.size();i++){
			ChiponRomData oldRomData=list.get(i);
			
			String address=oldRomData.getAddress();
			String oldValue=oldRomData.getOldValue();
			
			boolean isHaveName	=oldRomData.getIsHaveName();
			String HaveName		=oldRomData.getName();
			
			if(groupRomData.getRomDatas().get(columnIndex-1).getAddress().equals(oldRomData.getAddress())){
				oldValue=tmp;
				findwhere=i;
			}
			
			ChiponRomData romData=new ChiponRomData(address, oldValue);
			romData.setIsHaveName(isHaveName);
			romData.setName(HaveName);			
			newList.add(romData);
		}
		//@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
		if(findwhere!=-1)
		{
			int bytes = ChiponMemoryServiceClass.getFormatBytes(valueFormat);
			String values= ChiponMemoryServiceClass.ValueStringCovReSet(valueFormat, tmp);
			while (values.length()< bytes*2)
				values= "0"+values;
			
			for(int i= 0; i< bytes; i++)
			{
				if((findwhere + i) < list.size()){
					
					ChiponRomData romData = newList.get(findwhere + i);
					romData.setValue(values.substring(values.length()-2));	
					
					values=values.substring(0,values.length()-2);
				}
			}
		}
		//@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
		return newList;
	}
	
	/*
	 * 
	 * 根据输入的数据，返回新的视图模型数据
	 * */
	public List<ChiponRomData> getNewThreadList(List<ChiponRomData> list,ChiponLabelData groupRomData,int columnIndex, String tmp) {
		List<ChiponRomData> newList =new ArrayList<ChiponRomData>();
		for(int i=0;i<list.size();i++){
			ChiponRomData oldRomData=list.get(i);
			String address=oldRomData.getAddress().substring(2);
			String oldValue=oldRomData.getOldValue();
			
			if(address.equals(Integer.toHexString(Integer.parseInt(groupRomData.getAddress().substring(2),16)))){
				switch (columnIndex) {
				case 2:
					oldValue=Integer.toHexString(Integer.parseInt(tmp,2));
					break;
				case 3:
					oldValue=Integer.toHexString(Integer.parseInt(tmp));
					break;
				case 4:
					oldValue=tmp;
					break;

				default:
					break;
				}
				
			}
			ChiponRomData romData=new ChiponRomData(oldRomData.getAddress(), oldValue);
			newList.add(romData);
		}
		return newList;
	}
	
	/*
	 * 根据输入的数据，返回新的寄存器视图的Thread模型数据
	 * */
	public List<ChiponRomData> getNewThreadList(List<ChiponRomData> list,Register register,int columnIndex, String tmp) {
		List<ChiponRomData> newList =new ArrayList<ChiponRomData>();
		String newAddress = register.getAddr().substring(0,register.getAddr().length()-1);
		if(newAddress.startsWith("0")){
			newAddress=newAddress.substring(1);
		}
		newAddress="0x"+newAddress.toLowerCase();
		for(int i=0;i<list.size();i++){
			ChiponRomData oldRomData=list.get(i);
			String address=oldRomData.getAddress();
			String oldValue=oldRomData.getOldValue();
			
			if(newAddress.equals(address)){
				oldValue=tmp;
			}
			ChiponRomData romData=new ChiponRomData(address,oldValue);
			newList.add(romData);
		}
		return newList;
	}
	
	/**
	 * 比较TreeViewer的上一次和这一次的input，如果不相同的话，设置为红色显示
	 * @param oldObj
	 * @param newObj
	 * @return
	 */
	public Object compare(Object oldObj,Object newObj){
		if(newObj == null || oldObj == null){
			return newObj;
		}
		if(!(oldObj instanceof ICategory) || !(newObj instanceof ICategory)){
			 return null;
		}
		ICategory oldCategory = (ICategory)oldObj;
		ICategory newCategory = (ICategory)newObj;
		List<IEntry> oldEntry = oldCategory.getChildren();
		List<IEntry> newEntry = newCategory.getChildren();
		if(newEntry.size()==oldEntry.size()){
			for(int i=0;i<newEntry.size();i++){
				IEntry entry1 = newEntry.get(i);
				IEntry entry2 = oldEntry.get(i);
				if(entry1.getValue().equalsIgnoreCase(entry2.getValue())){
					entry1.setfColor(IChiponColor.DEFAULT_COLOR);
				}else{
					entry1.setfColor(IChiponColor.RED_COLOR);
					if(entry1.hasChildren() && entry2.hasChildren()){
						List<IEntry> oldChildren = entry2.getChildren();
						List<IEntry> newChildren = entry1.getChildren();
						if(newChildren.size()==oldChildren.size()){
							for(int j=0;j<newChildren.size();j++){
								IEntry childEntry1 = newChildren.get(j);
								IEntry childEntry2 = oldChildren.get(j);
								if(childEntry1.getValue().equalsIgnoreCase(childEntry2.getValue())){
									childEntry1.setfColor(IChiponColor.DEFAULT_COLOR);
								}else{
									childEntry1.setfColor(IChiponColor.RED_COLOR);
								}
							}	
						}
						
					}
				}
				
			}
		}
		
		return newCategory;
	}
	
	/*
	 * 返回新的寄存器视图模型数据
	 * */
	public ICategory getNewCategory(final ICategory oldcategory,ChiponThread fThread){
		
		final List<IEntry> registerlist = new ArrayList<IEntry>();
		List<IEntry> entryList = oldcategory.getChildren();
		for (IEntry entry : entryList) {
			if (entry instanceof Register) {
				final Register oldRegister = (Register) entry;
				Register newRegister = new Register();
				//获取单个寄存器的值
				ChiponSingleRegisterCommandResult registerResult = 
						(ChiponSingleRegisterCommandResult)fThread.sendCommand(
								new ChiponSingleRegisterCommand("x /w "+oldRegister.getAddr()));
				
				if(null!=oldRegister.getAddr()&&!"".equals(oldRegister.getAddr()))newRegister.setAddr(oldRegister.getAddr());
				if(null!=oldRegister.getfColor())newRegister.setfColor(oldRegister.getfColor());
				if(null!=oldRegister.getName()&&!"".equals(oldRegister.getName()))newRegister.setName(oldRegister.getName());
				if(null!=oldRegister.getDescription()&&!"".equals(oldRegister.getDescription()))newRegister.setDescription(oldRegister.getDescription());
				if(null!=oldRegister.getParent())newRegister.setParent(oldRegister.getParent());
				
				newRegister.setValue(Integer.toHexString(registerResult.value));
				if(oldRegister.getColumnCount() > 0){
					for(int i = 0; i < oldRegister.getColumnCount(); i++){
						newRegister.setColumnData(i, oldRegister.getColumnData(i));
					}
				}	
				//获取寄存器内bit的值
				if(oldRegister.hasChildren()){
					for(IEntry bitEntry: oldRegister.getChildren()){
						Bits bit = new Bits();
						if(null!=bitEntry.getAddr()&&!"".equals(bitEntry.getAddr()))bit.setAddr(bitEntry.getAddr());
						if(null!=bitEntry.getfColor())bit.setfColor(bitEntry.getfColor());
						if(null!=bitEntry.getName()&&!"".equals(bitEntry.getName()))bit.setName(bitEntry.getName());
						if(null!=bitEntry.getDescription()&&!"".equals(bitEntry.getDescription()))bit.setDescription(bitEntry.getDescription());
						if(null!=bitEntry.getParent())bit.setParent(bitEntry.getParent());
						
						int bitLength = Integer.parseInt(bitEntry.getBitLength());  //位宽
						int startLocation = Integer.parseInt(bitEntry.getAddr());	//位的起始位置
						
						//解析单个bit的值
						int num = 0;  //比较位
						for(int i = 0; i < bitLength; i++){
							num += 1<<i;
						}
						int bitValue = ((registerResult.value&(num<<startLocation))>>startLocation)&num;
						bit.setValue(Integer.toHexString(bitValue));
						newRegister.addChild(bit);
					}
				}
				registerlist.add(newRegister);		
			}
		}
		
		ICategory newCategory =new ICategory() {
			@Override
			public void setfColor(Color fColor) {
				
			}
			
			@Override
			public void setValue(String value) {
				
			}
			
			@Override
			public void setParent(IEntry parent) {
				
			}
			
			@Override
			public void setName(String name) {
				
			}
			
			@Override
			public void setColumnData(int index, String data) {
				
			}
			
			@Override
			public void setAddr(String addr) {
				
			}
			
			@Override
			public boolean hasChildren() {
				return oldcategory.hasChildren();
			}
			
			@Override
			public Color getfColor() {
				return oldcategory.getfColor();
			}
			
			@Override
			public String getValue() {
				return oldcategory.getValue();
			}
			
			@Override
			public IEntry getParent() {
				return oldcategory.getParent();
			}
			
			@Override
			public String getName() {
				System.out.println("====================="+oldcategory.getName());
				return oldcategory.getName();
			}
			
			@Override
			public String getColumnData(int index) {
				return oldcategory.getColumnData(index);
			}
			
			@Override
			public List<IEntry> getChildren() {
				return registerlist;
			}
			
			@Override
			public String getAddr() {
				return oldcategory.getAddr();
			}
			
			@Override
			public void addChild(IEntry child) {
				
			}
			
			@Override
			public String[] getColumnLabels() {
				return oldcategory.getColumnLabels();
			}
			
			@Override
			public int[] getColumnDefaultWidths() {
				return oldcategory.getColumnDefaultWidths();
			}
			
			@Override
			public int getColumnCount() {
				return oldcategory.getColumnCount();
			}

			@Override
			public String getDescription() {
				return oldcategory.getDescription();
			}

			@Override
			public void setDescription(String description) {
				
			}

			@Override
			public void setBitLength(String bitLength) {
				
			}

			@Override
			public String getBitLength() {
				return null;
			}
		};
		return newCategory;
	}
	

	/**
	 * 获取当前芯片的寄存器列表
	 * */
	public List<ICategory> getCategorys(ChiponThread fThread){
		IProject project=null;
		if(fThread!=null)
			project =((ChiponDebugTarget)fThread.getDebugTarget()).getfProject();
		
		String chip;
		List<ICategory> categorys = null;  
		if (project != null && project.exists()) {
			ProjectPropertyManager ppm = ProjectPropertyManager.getPropertyManager(project);
			ChipOnProjectProperties fTargetProps = ppm.getProjectProperties();
			chip = fTargetProps.getChipName();  // 获取当前项目的芯片型号
			IDeviceDescription des = ChipOniohDeviceDescriptionProvider.getDefault().getDeviceDescription(chip);
			categorys = des.getCategories();
		}
		else
		{
			try{
			chip = UiActivator.getDefault().getCurrentchipName();  // 获取当前项目的芯片型号
			IDeviceDescription des = ChipOniohDeviceDescriptionProvider.getDefault().getDeviceDescription(chip);
			categorys = des.getCategories();
			}catch (Exception e) {			
			}
		}
		return categorys;   //[寄存器列表，芯片详细信息]
	}
	
	
	/**
	 * 2017-3-18 wanfz添加
	 * @param thread
	 * @param name 寄存器名称
	 * @return
	 */
	
	/**
	 * 获取所有的通用寄存器的值
	 */
	public List<ChiponCurrencyRegisterData> getAllChiponCurrencyRegisterData(ChiponThread fThread){
		List<ChiponCurrencyRegisterData> registerAllDatas = new ArrayList<>();
		List<ICategory> categories = getCategorys(fThread);
		List<IEntry> entryList = categories.get(0).getChildren();
		for(IEntry entry :entryList){
			if(entry instanceof Register){
				Register register = (Register)entry;
				List<ChiponCurrencyRegisterData> chiponDatasChildren = new ArrayList<>();
				ChiponSingleRegisterCommandResult registerResult = 
						(ChiponSingleRegisterCommandResult)fThread.sendCommand(
								new ChiponSingleRegisterCommand("x /w "+register.getAddr()));
				if(register.hasChildren()){
					for(IEntry chipBit : register.getChildren()){
						int bitLength = Integer.parseInt(chipBit.getBitLength());  //位宽
						int startLocation = Integer.parseInt(chipBit.getAddr());	//位的起始位置
						
						//解析单个bit的值
						int num = 0;  //比较位
						for(int i = 0; i < bitLength; i++){
							num += 1<<i;
						}
						int bitValue = ((registerResult.value&(num<<startLocation))>>startLocation)&num;									
						ChiponCurrencyRegisterData chiponData=new ChiponCurrencyRegisterData(
								chipBit.getName(), Integer.toHexString(bitValue), Integer.toString(bitValue));
					
						chiponDatasChildren.add(chiponData);
					}
				}
				ChiponCurrencyRegisterData data = new ChiponCurrencyRegisterData(
						register.getName(), Integer.toHexString(registerResult.value), Integer.toString(registerResult.value));
				data.setChiponDataList(chiponDatasChildren);
				registerAllDatas.add(data);
			}
		}
		return registerAllDatas;
	}
	
	//获取单个通用寄存器信息
	public ChiponCurrencyRegisterData getSingleChiponCurrencyRegisterData(ChiponThread fThread, String name){
//		ChiponCurrencyRegisterData data = new ChiponCurrencyRegisterData(name, "", "");
		ChiponCurrencyRegisterData data = null;
		List<ICategory> categories = getCategorys(fThread);   //从XML文件中读取寄存器的信息
		List<IEntry> entryList = categories.get(0).getChildren();
		List<ChiponCurrencyRegisterData> chiponDatasChildren=new ArrayList<ChiponCurrencyRegisterData>();
		
		for (IEntry entry : entryList) {
			if (entry instanceof Register) {
				final Register oldRegister = (Register) entry;
				if(oldRegister.getName().trim().equals(name)){
					String address = oldRegister.getAddr();
					//获取单个寄存器的值
					ChiponSingleRegisterCommandResult registerResult = 
							(ChiponSingleRegisterCommandResult)fThread.sendCommand(new ChiponSingleRegisterCommand("x /w "+address));
					if (registerResult.resultText == null || !registerResult.resultText.equals("success"))
					{
						break;
					}
					//获取SFR寄存器内部bit数据
					data = new ChiponCurrencyRegisterData(
							name, Integer.toHexString(registerResult.value), Integer.toString(registerResult.value));
					data.setAddr(address);
					for(IEntry chipBit : oldRegister.getChildren()){
						int bitLength = Integer.parseInt(chipBit.getBitLength());  //位宽
						int startLocation = Integer.parseInt(chipBit.getAddr());	//位的起始位置
						
						//解析单个bit的值
						int num = 0;  //比较位
						for(int i = 0; i < bitLength; i++){
							num += 1<<i;
						}
						int bitValue = ((registerResult.value&(num<<startLocation))>>startLocation)&num;									
						ChiponCurrencyRegisterData chiponData=new ChiponCurrencyRegisterData(
								chipBit.getName(), Integer.toHexString(bitValue), Integer.toString(bitValue));
						//给bit位设置父寄存器
						chiponData.setParentRegisterData(data);
						chiponData.setHasParent(true);
						chiponData.setBitLength(bitLength);
						chiponData.setBitLocation(chipBit.getAddr());
							
						chiponDatasChildren.add(chiponData);
					}
					data.setChiponDataList(chiponDatasChildren);
					break;
				}
			}
		}	
		return data;
	}
	
	//获取全部浮点寄存器数据
	public List<ChiponCurrencyFloatRegisterData> getAllChiponCurrencyFloatRegisterData(ChiponThread fThread){
		List<ChiponCurrencyFloatRegisterData> registerAllDatas = new ArrayList<>();
		List<ICategory> categories = getCategorys(fThread);
		List<IEntry> entryList = categories.get(0).getChildren();
		for(IEntry entry :entryList){
			if(entry instanceof Register){
				Register register = (Register)entry;
				List<ChiponCurrencyFloatRegisterData> chiponDatasChildren = new ArrayList<>();
				ChiponSingleFloatRegisterCommandResult registerResult = 
						(ChiponSingleFloatRegisterCommandResult)fThread.sendCommand(
								new ChiponSingleFloatRegisterCommand("x /w "+register.getAddr()));
				if(register.hasChildren()){
					for(IEntry chipBit : register.getChildren()){
						int bitLength = Integer.parseInt(chipBit.getBitLength());  //位宽
						int startLocation = Integer.parseInt(chipBit.getAddr());	//位的起始位置
						
						//解析单个bit的值
						int num = 0;  //比较位
						for(int i = 0; i < bitLength; i++){
							num += 1<<i;
						}
						int bitValue = ((registerResult.value&(num<<startLocation))>>startLocation)&num;									
						ChiponCurrencyFloatRegisterData chiponData=new ChiponCurrencyFloatRegisterData(
								chipBit.getName(), Integer.toHexString(bitValue), Integer.toString(bitValue));
					
						chiponDatasChildren.add(chiponData);
					}
				}
				ChiponCurrencyFloatRegisterData data = new ChiponCurrencyFloatRegisterData(
						register.getName(), Integer.toHexString(registerResult.value), Integer.toString(registerResult.value));
				data.setChiponDataList(chiponDatasChildren);
				registerAllDatas.add(data);
			}
		}
		return registerAllDatas;
	}
	
	//获取单个浮点寄存器信息
	public ChiponCurrencyFloatRegisterData getSingleChiponCurrencyFloatRegisterData(ChiponThread fThread, String name){
//		ChiponCurrencyRegisterData data = new ChiponCurrencyRegisterData(name, "", "");
		ChiponCurrencyFloatRegisterData data = null;
		List<ICategory> categories = getCategorys(fThread);   //从XML文件中读取寄存器的信息
		List<IEntry> entryList = categories.get(0).getChildren();
		List<ChiponCurrencyFloatRegisterData> chiponDatasChildren=new ArrayList<ChiponCurrencyFloatRegisterData>();
		
		for (IEntry entry : entryList) {
			if (entry instanceof Register) {
				final Register oldRegister = (Register) entry;
				if(oldRegister.getName().trim().equals(name)){
					String address = oldRegister.getAddr();
					//获取单个寄存器的值
					ChiponSingleFloatRegisterCommandResult registerResult = 
							(ChiponSingleFloatRegisterCommandResult)fThread.sendCommand(new ChiponSingleFloatRegisterCommand("x /w "+address));
					if (registerResult.resultText == null || !registerResult.resultText.equals("success"))
					{
						break;
					}
					//获取SFR寄存器内部bit数据
					data = new ChiponCurrencyFloatRegisterData(
							name, Integer.toHexString(registerResult.value), Integer.toString(registerResult.value));
					data.setAddr(address);
					for(IEntry chipBit : oldRegister.getChildren()){
						int bitLength = Integer.parseInt(chipBit.getBitLength());  //位宽
						int startLocation = Integer.parseInt(chipBit.getAddr());	//位的起始位置
						
						//解析单个bit的值
						int num = 0;  //比较位
						for(int i = 0; i < bitLength; i++){
							num += 1<<i;
						}
						int bitValue = ((registerResult.value&(num<<startLocation))>>startLocation)&num;									
						ChiponCurrencyFloatRegisterData chiponData=new ChiponCurrencyFloatRegisterData(
								chipBit.getName(), Integer.toHexString(bitValue), Integer.toString(bitValue));
						//给bit位设置父寄存器
						chiponData.setParentRegisterData(data);
						chiponData.setHasParent(true);
						chiponData.setBitLength(bitLength);
						chiponData.setBitLocation(chipBit.getAddr());
							
						chiponDatasChildren.add(chiponData);
					}
					data.setChiponDataList(chiponDatasChildren);
					break;
				}
			}
		}	
		return data;
	}
	
	/**
	 * 加载 “调试透视图下寄存器视图” 添加当前选中的  寄存器的信息
	 */
	public void setSingleRegisterData1(ChiponThread fThread, IEntry reg){
		Module dataModule = (Module) reg;
		if(dataModule.hasChildren()){
			List<IEntry> registers = dataModule.getChildren();
			for(IEntry register : registers){
				Register dataRegister = (Register) register;
				String address = dataRegister.getAddr();
				//获取单个寄存器的值
				ChiponSingleRegisterCommandResult registerResult = (ChiponSingleRegisterCommandResult)fThread.sendCommand(new ChiponSingleRegisterCommand("x /w "+address));
						
				dataRegister.setValue(Integer.toHexString(registerResult.value));	
				//获取寄存器内bit的值
				if(dataRegister.hasChildren()){
					for(IEntry bitEntry: dataRegister.getChildren()){					
						int bitLength = Integer.parseInt(bitEntry.getBitLength());  //位宽
						int startLocation = Integer.parseInt(bitEntry.getAddr());	//位的起始位置
							
						//解析单个bit的值
						int num = 0;  //比较位
						for(int i = 0; i < bitLength; i++){
							num += 1<<i;
						}
						int bitValue = ((registerResult.value & (num<<startLocation))>>startLocation) & num;
						bitEntry.setValue(Integer.toHexString(bitValue));
					}
				}
			}
		}
	}
		
	public void setSingleRegisterData(ChiponThread fThread, IEntry reg){	
		Register dataRegister = (Register) reg;
		String address = dataRegister.getAddr();
		//获取单个寄存器的值
		ChiponSingleRegisterCommandResult registerResult = 
				(ChiponSingleRegisterCommandResult)fThread.sendCommand(new ChiponSingleRegisterCommand("x /w "+address));
				
		dataRegister.setValue(Integer.toHexString(registerResult.value));	
		//获取寄存器内bit的值
		if(dataRegister.hasChildren()){
			for(IEntry bitEntry: dataRegister.getChildren()){					
				int bitLength = Integer.parseInt(bitEntry.getBitLength());  //位宽
				int startLocation = Integer.parseInt(bitEntry.getAddr());	//位的起始位置
					
				//解析单个bit的值
				int num = 0;  //比较位
				for(int i = 0; i < bitLength; i++){
					num += 1<<i;
				}
				int bitValue = ((registerResult.value & (num<<startLocation))>>startLocation) & num;
				bitEntry.setValue(Integer.toHexString(bitValue));
			}
		}
	}	
	
	
	/**
	 * 
	 * @param datas 输入通用寄存器的值
	 * @return accxDatas 从通用寄存器值中解析出ACCX的值得列表
	 */
	public List<ChiponACCXData> resovleAccxdatas(List<ChiponCurrencyRegisterData> datas){
		List<ChiponACCXData> accxDatas = new ArrayList<>();
		
		//R16为ACC0低8位， R17为ACC0高8位； R18位ACC1低8位， R19为ACC1高8位，依次类推，ACC总共有7个
		for(int i = 16; i <31; i= i+2){
			ChiponACCXData accxData = new ChiponACCXData();
			accxData.setaCCName("ACC" + (i-16)/2);
			accxData.setLowData(datas.get(i).getValue());
			accxData.setHighData(datas.get(i+1).getValue());
			String bufget=accxData.getHighData()+accxData.getLowData();
			accxData.setVauleData(Long.parseLong(bufget,16));
			accxDatas.add(accxData);
		}

		return accxDatas;
	}
 	
	

}
