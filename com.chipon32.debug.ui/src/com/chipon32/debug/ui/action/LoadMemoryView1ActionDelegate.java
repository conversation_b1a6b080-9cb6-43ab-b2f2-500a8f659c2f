package com.chipon32.debug.ui.action;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.util.ArrayList;
import java.util.List;
import java.util.Scanner;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.eclipse.core.runtime.IAdaptable;
import org.eclipse.debug.core.Launch;
import org.eclipse.debug.ui.DebugUITools;
import org.eclipse.jface.action.IAction;
import org.eclipse.jface.dialogs.MessageDialog;
import org.eclipse.jface.viewers.ISelection;
import org.eclipse.jface.viewers.TableViewer;
import org.eclipse.swt.SWT;
import org.eclipse.swt.widgets.Combo;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.FileDialog;
import org.eclipse.swt.widgets.Text;
import org.eclipse.ui.IViewActionDelegate;
import org.eclipse.ui.IViewPart;

import com.chipon32.debug.core.model.ChiponDebugTarget;
import com.chipon32.debug.core.model.ChiponRomData;
import com.chipon32.debug.core.model.ChiponRomDatas;
import com.chipon32.debug.core.model.ChiponStackFrame;
import com.chipon32.debug.core.model.ChiponThread;
import com.chipon32.debug.core.protocol.ChiponCommandResult;
import com.chipon32.debug.core.protocol.ChiponSetAndMemoryCommand;
import com.chipon32.debug.core.util.PackRomDataList;
import com.chipon32.debug.ui.views.ChiponMemoryServiceClass;
import com.chipon32.debug.ui.views.ChiponMemoryView1;

public class LoadMemoryView1ActionDelegate implements IViewActionDelegate {
	private ChiponMemoryView1 memoryView; 
	private TableViewer tableViewer;
	private Combo showDataFormat;
	private Text txtStart;
	private Text txtEnd;
	
	private ChiponThread debugthread;
	private ChiponDebugTarget target=null;
	
	@SuppressWarnings("static-access")
	@Override
	public void run(IAction action) {
		debugthread=null;
		target=null;
		try{
        IAdaptable debugContext = DebugUITools.getDebugContext();
        if (debugContext instanceof ChiponStackFrame) {
        	debugthread = (ChiponThread) ((ChiponStackFrame) debugContext).getThread();        	  
        }else if (debugContext instanceof ChiponThread) {		
        	debugthread= (ChiponThread) debugContext;        
        } else if (debugContext instanceof ChiponDebugTarget) {
        	target = (ChiponDebugTarget) debugContext;
        	debugthread=target.getCurrentThread();        
        }
        else if(debugContext instanceof Launch)
        {
        	Launch lc=(Launch)debugContext;
        	target=(ChiponDebugTarget) lc.getDebugTargets()[0];
        	debugthread=target.getCurrentThread();        	
        }
		}catch (Exception e) {
			// TODO: handle exception
		}
        //###########################################
		if(debugthread == null || !debugthread.isSuspended()){
			MessageDialog.openWarning(Display.getCurrent().getActiveShell(), Messages.LoadMemoryView1ActionDelegate_0, Messages.LoadMemoryView1ActionDelegate_1);
			return;
		}
		//-------------------------------------------------------------------------------------------------------------
		
		FileDialog openFileDialog = new FileDialog(Display.getCurrent().getActiveShell(), SWT.OPEN);
		openFileDialog.setText(Messages.LoadMemoryView1ActionDelegate_2);
		openFileDialog.setFilterExtensions(new String[]{"*.txt"}); //$NON-NLS-1$
		String openFileString = openFileDialog.open();
		if(openFileString==null){ // quit
			return;
		}
//		Display Format: hex32
//		Byte Count: 15
//
//		Address     Hex
//		10000021: EC5805D7
//		10000025: 2D5C1D28
//		10000029: E45D265D
//		1000002d: ********
//		Display Format: float
//		Byte Count: 32
//
//		Address     Hex          float
//		10000040: 4508822E  2184.1362
//		10000044: 58A05C05  1.41053665E15
//		10000048: 28E48565  2.5370936E-14
//		1000004c: 5D0D5D06  6.3664403E17
//		10000050: 00005C1D  3.3044E-41
//		10000054: 50AF0001  2.34881044E10
//		10000058: 40200100  2.500061
//		1000005c: 0000FFFE  9.1833E-41
		File openFile = new File(openFileString);
		
		
		try {
			String displayFormat = "hex8"; //$NON-NLS-1$
			String startaddress=null;
			String byteSize="1"; //$NON-NLS-1$
			
			List<ChiponRomData> romDatasHex = new ArrayList<>(); //缓存文件中记录的hex型内存数据
			
			FileInputStream fins = new FileInputStream(openFile);
			Scanner scan = new Scanner(fins);
			//###############################################################
			while(scan.hasNextLine()){
				String lineContent = scan.nextLine().trim();
				if(lineContent.isEmpty() || lineContent.contains("Address")){ //$NON-NLS-1$
					continue;
				}	
				// ----------------------------------------------------------------------------------- read and check
				if(lineContent.contains("Display Format")){ //$NON-NLS-1$
					//显示的内存数据格式
					displayFormat = lineContent.substring(lineContent.indexOf(":")+2); //$NON-NLS-1$
					if(showDataFormat.indexOf(displayFormat)<0)
					{
						MessageDialog.openWarning(Display.getCurrent().getActiveShell(), Messages.LoadMemoryView1ActionDelegate_0, Messages.LoadMemoryView1ActionDelegate_3);
						return;
					}
				
				}else if(lineContent.contains("Byte Count")){ //$NON-NLS-1$
					if(lineContent.indexOf(":")<0) //$NON-NLS-1$
					{
						MessageDialog.openWarning(Display.getCurrent().getActiveShell(), Messages.LoadMemoryView1ActionDelegate_0, Messages.LoadMemoryView1ActionDelegate_3);
						return;
					}
					byteSize = lineContent.substring(lineContent.indexOf(":")+2); //$NON-NLS-1$						
				}else{
				// -----------------------------------------------------------------------------------
					//内存数据信息
					String[] lineContentArray = lineContent.split("\\s+"); //$NON-NLS-1$
					if(lineContentArray.length<2){
						continue;
					}
					// first address  second hexvalule  [third int float ..]
					String bufaddress = lineContentArray[0].substring(0, lineContentArray[0].length()-1);
					if(bufaddress.startsWith("0x")|| bufaddress.startsWith("0X")) bufaddress=bufaddress.substring(2); //$NON-NLS-1$ //$NON-NLS-2$
					String bufvalue = lineContentArray[1];
					
					if(startaddress==null || startaddress.isEmpty())					
						startaddress=bufaddress;
					
					int displayValueByteLen = ChiponMemoryServiceClass.getFormatBytes(displayFormat);
					// //容错处理---防止导入文件被不小心弄错, Pattern.compile("[0-9a-fA-F]+")          address algned by format
					if(!verifyValidity(bufaddress,bufvalue)  || (Integer.parseInt(bufaddress,16) % displayValueByteLen)!=0	){
						MessageDialog.openWarning(Display.getCurrent().getActiveShell(), Messages.LoadMemoryView1ActionDelegate_0, Messages.LoadMemoryView1ActionDelegate_3);
						return;
					}
					//################################################## byte save  control address aligned
					int valueLen = bufvalue.length();					
					while(valueLen<displayValueByteLen*2){
						bufvalue="0"+bufvalue; //$NON-NLS-1$
						valueLen++;
					}
					romDatasHex.add(new ChiponRomData(bufaddress, bufvalue));					//##################################################
				// -----------------------------------------------------------------------------------	
				}
			}//end while
			//###############################################################			
			String message =Messages.LoadMemoryView1ActionDelegate_10 + Messages.LoadMemoryView1ActionDelegate_11 + displayFormat + Messages.LoadMemoryView1ActionDelegate_12+startaddress +Messages.LoadMemoryView1ActionDelegate_13 + byteSize ;
			boolean isLoad=MessageDialog.openConfirm(Display.getCurrent().getActiveShell(), Messages.LoadMemoryView1ActionDelegate_0, message);
			if(isLoad==false) return;
			//###############################################################
			//将数据写入内存中	
				txtStart.setText(startaddress);
				txtEnd.setText(byteSize);	
				showDataFormat.select(showDataFormat.indexOf(displayFormat));
				
				for(int i=0; i<romDatasHex.size(); i++){
					String romValueInFormat = ChiponMemoryServiceClass.ValueStringCovGet(displayFormat,romDatasHex.get(i).getValue());
					String commandValue = ChiponMemoryServiceClass.ValueStringCovSet(displayFormat,romValueInFormat);
					String address = "0x"+romDatasHex.get(i).getAddress(); //$NON-NLS-1$
					String commandAddress = "("+ChiponMemoryServiceClass.getFullName(displayFormat)+"*)"+address; //$NON-NLS-1$ //$NON-NLS-2$
					
					ChiponCommandResult result = debugthread.sendCommand(new ChiponSetAndMemoryCommand(commandAddress,commandValue));
					
					if(result.resultText.equals("failed")){ //$NON-NLS-1$		
						String Failmessage =  address +  commandValue ;
						boolean isContinue=MessageDialog.openConfirm(Display.getCurrent().getActiveShell(),Messages.LoadMemoryView1ActionDelegate_0,Failmessage);		
						if(!isContinue)
							break;
					}
				}			
				//刷新当前内存视图
				List<ChiponRomData> romDataList = memoryView.getRomDataList(0);
			    ChiponRomDatas romviewDatas = new PackRomDataList().packList(romDataList);
		        tableViewer.setInput(romviewDatas);
				tableViewer.refresh();			
		} catch (FileNotFoundException e) {
			// TODO Auto-generated catch block
			//e.printStackTrace();
			MessageDialog.openWarning(Display.getCurrent().getActiveShell(), Messages.LoadMemoryView1ActionDelegate_0, Messages.LoadMemoryView1ActionDelegate_4);
		}
	}

	private boolean verifyValidity(String address, String hexValue) {
		Pattern pattern = Pattern.compile("[0-9a-fA-F]+"); //$NON-NLS-1$
		Matcher match1 = pattern.matcher(address);
		Matcher match2 = pattern.matcher(hexValue);
		
		if(match1.matches() && match2.matches() && address.length()==8){
			return true;
		}
		return false;
	}

	@Override
	public void selectionChanged(IAction action, ISelection selection) {
		// TODO Auto-generated method stub

	}

	@Override
	public void init(IViewPart view) {
		memoryView = (ChiponMemoryView1) view;
		tableViewer = memoryView.getTableViewer();
		showDataFormat = memoryView.getShowDataFormat();
		txtStart = memoryView.getTxtStart();
		txtEnd = memoryView.getTxtEnd();
	}

}
