 package com.chipon32.debug.ui.action;

import java.util.Set;

import org.eclipse.core.resources.IMarker;
import org.eclipse.core.runtime.CoreException;
import org.eclipse.debug.core.DebugPlugin;
import org.eclipse.debug.core.model.Breakpoint;
import org.eclipse.debug.core.model.IBreakpoint;
import org.eclipse.debug.core.model.ILineBreakpoint;
import org.eclipse.jface.action.IAction;
import org.eclipse.jface.viewers.ISelection;
import org.eclipse.jface.window.Window;
import org.eclipse.ui.IViewActionDelegate;
import org.eclipse.ui.IViewPart;
import org.eclipse.ui.PlatformUI;

import com.chipon32.debug.core.DebugCoreActivator;
import com.chipon32.debug.core.breakpoint.ChiponAddrBreakpoint;
import com.chipon32.debug.core.breakpoint.ChiponLineBreakpoint;
import com.chipon32.debug.core.model.ChiponThread;
import com.chipon32.debug.ui.dilaogs.AddBreakpointDialog;
import com.chipon32.util.communicate.WriteMessage;

/**
 * 
 * <AUTHOR>
 */
public class AddBreakpointViewActionDelegate implements IViewActionDelegate {

	@Override
	public void run(IAction action) {
		AddBreakpointDialog breakpointDialog = new AddBreakpointDialog(
				PlatformUI.getWorkbench().getActiveWorkbenchWindow().getShell());
		//从弹窗中获取watchData的值
		if(breakpointDialog.open() != Window.OK)
			return;
		
		
		IBreakpoint[] breakpoints = DebugPlugin.getDefault().getBreakpointManager().getBreakpoints(DebugCoreActivator.ID_CHIP_DEBUG_MODEL);
		Breakpoint newBreakpoint = breakpointDialog.getBreakpoint();
		if(newBreakpoint instanceof ChiponLineBreakpoint){//添加行断点
			ChiponLineBreakpoint lineBreakpoint = (ChiponLineBreakpoint)newBreakpoint;
			if(breakpoints.length == 0){
				try {
					DebugPlugin.getDefault().getBreakpointManager().addBreakpoint(lineBreakpoint);//唤醒breakpointAdded(IBreakpoint)-> install(DebugTarget)方法
				
					//设置不成功，删除断点
					if(!lineBreakpoint.isSuccess()){
						lineBreakpoint.delete();
					}
				} catch (CoreException e1) {
					e1.printStackTrace();
				}
				
			}else {
				for (int i = 0; i < breakpoints.length; i++) {
					IBreakpoint breakpoint = breakpoints[i];
					if (breakpoint instanceof ILineBreakpoint /*&& resource.equals(breakpoint.getMarker().getResource())*/) {
						int pointLine;
						try {
							pointLine = (int) ((ILineBreakpoint)breakpoint).getMarker().getAttribute(IMarker.LINE_NUMBER);
							if (pointLine == (lineBreakpoint.getLineNumber())) {//判断当前双击鼠标的行数是否有断点，如果有则不做任何改动，否则添加断点。
								WriteMessage.getDefault().writeBLUEMessage(Messages.AddBreakpointViewActionDelegate_0);
								lineBreakpoint.delete();
								return;
							}
						} catch (CoreException e) {
							e.printStackTrace();
						}

					}	
				}
				try {
					DebugPlugin.getDefault().getBreakpointManager().addBreakpoint(lineBreakpoint);//唤醒breakpointAdded(IBreakpoint)-> install(DebugTarget)方法
					
					if(!lineBreakpoint.isSuccess()){
						lineBreakpoint.delete();
					}
				} catch (CoreException e) {
					e.printStackTrace();
				}	
			}
		}
		else if(newBreakpoint instanceof ChiponAddrBreakpoint){ //添加地址断点
			ChiponAddrBreakpoint addrBreakpoint = (ChiponAddrBreakpoint)newBreakpoint;
			Set<String> addrSet = ChiponThread.locationData.keySet();
			
			if(!addrSet.contains(addrBreakpoint.getAddress().toLowerCase())){
				try {				
					WriteMessage.getDefault().writeErrMessage(addrBreakpoint.getAddress()+Messages.AddBreakpointViewActionDelegate_1);
					addrBreakpoint.delete();
					return;
				} catch (CoreException e) {
					e.printStackTrace();
				}
			}
			
			if(breakpoints.length == 0){
				try {
					DebugPlugin.getDefault().getBreakpointManager().addBreakpoint(addrBreakpoint);
					
					if(!addrBreakpoint.isSuccess()){
						addrBreakpoint.delete();
					}
				} catch (CoreException e) {
					e.printStackTrace();
				}
				
			}else {
				for (int i = 0; i < breakpoints.length; i++) {
					
					IBreakpoint breakpoint = breakpoints[i];
					if (breakpoint instanceof ChiponAddrBreakpoint /*&& resource.equals(breakpoint.getMarker().getResource())*/) {
						try {
							String addr = (String) ((ChiponAddrBreakpoint)breakpoint).getMarker().getAttribute(IMarker.LOCATION);
							if (addr.equals(addrBreakpoint.getAddress())) {//判断是否有断点，如果有则不做任何操作，否则添加断点。
								WriteMessage.getDefault().writeBLUEMessage(Messages.AddBreakpointViewActionDelegate_0);
								addrBreakpoint.delete();
								return;
							}
						} catch (CoreException e) {
							e.printStackTrace();
						}
					}	
				}
				try {
					DebugPlugin.getDefault().getBreakpointManager().addBreakpoint(addrBreakpoint);
					if(!addrBreakpoint.isSuccess()){
						addrBreakpoint.delete();
					}
				} catch (CoreException e) {
					e.printStackTrace();
				}	
			}
		}
	}

	
	


	@Override
	public void selectionChanged(IAction action, ISelection selection) {
		// TODO Auto-generated method stub

	}

	@Override
	public void init(IViewPart view) {

		
	}


	
}
