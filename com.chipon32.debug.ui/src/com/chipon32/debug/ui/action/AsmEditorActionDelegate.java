/**
 * 
 */
package com.chipon32.debug.ui.action;

import org.eclipse.jface.action.IAction;
import org.eclipse.jface.text.source.IVerticalRulerInfo;
import org.eclipse.swt.widgets.Event;
import org.eclipse.ui.IActionDelegate2;
import org.eclipse.ui.IEditorPart;
import org.eclipse.ui.texteditor.AbstractRulerActionDelegate;
import org.eclipse.ui.texteditor.ITextEditor;

import com.chipon32.debug.ui.breakpoint.ChiponToggleBreakpointAction;



/**
 * <AUTHOR>
 *
 */
public class AsmEditorActionDelegate extends AbstractRulerActionDelegate
		implements IActionDelegate2 {
	private IEditorPart fEditor = null;
	private ChiponToggleBreakpointAction fDelegate = null;

	/**
	 * 
	 */
	public AsmEditorActionDelegate() {
		// TODO Auto-generated constructor stub
	}

	/* (non-Javadoc)
	 * @see org.eclipse.ui.texteditor.AbstractRulerActionDelegate#createAction(org.eclipse.ui.texteditor.ITextEditor, org.eclipse.jface.text.source.IVerticalRulerInfo)
	 */
	@Override
	protected IAction createAction(ITextEditor editor,
			IVerticalRulerInfo rulerInfo) {
		fDelegate = new ChiponToggleBreakpointAction(editor, rulerInfo);
		return fDelegate;
	}
	
	@Override
	public void setActiveEditor(IAction action, IEditorPart targetEditor) {
		if (fEditor != null) {
			if (fDelegate != null) {
				fDelegate = null;
			}
		}
		fEditor = targetEditor;
		super.setActiveEditor(action, targetEditor);

	}
	
	@Override
	public void init(IAction action) {
	}
	
	@Override
	public void dispose() {
	
		fDelegate = null;
		fEditor = null;
	}

	@Override
	public void runWithEvent(IAction action, Event event) {
		run(action);
	}


}
