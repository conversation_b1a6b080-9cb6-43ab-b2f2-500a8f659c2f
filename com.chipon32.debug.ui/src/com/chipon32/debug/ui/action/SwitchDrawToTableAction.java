package com.chipon32.debug.ui.action;
import org.eclipse.jface.action.Action;

import com.chipon32.debug.ui.view.trace.TraceVariableView;



public class SwitchDrawToTableAction extends Action{
	//private TableShowRunnable tableshowRunnable;
	public SwitchDrawToTableAction (String text, int style) {
		super(text,style);
		
	}
	
	public void run() {
		/*view.stacklayout.topControl = view.traceComposite;
		view.homeComposite.layout();*/
		
		if(TraceVariableView.currentComposit == TraceVariableView.traceComposite) {
			
			TraceVariableView.stacklayout.topControl = TraceVariableView.tableshowComposite;
			TraceVariableView.currentComposit = TraceVariableView.tableshowComposite;
			
		}else if(TraceVariableView.currentComposit == TraceVariableView.tableshowComposite) {
			TraceVariableView.stacklayout.topControl = TraceVariableView.traceComposite;
			TraceVariableView.currentComposit = TraceVariableView.traceComposite;
			//Display.getCurrent().asyncExec(TraceVariableView.updater);
		}
		
		TraceVariableView.homeComposite.layout(); //╦бл┬
	}
	
}
