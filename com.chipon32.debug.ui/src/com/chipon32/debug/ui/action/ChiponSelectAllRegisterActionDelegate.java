package com.chipon32.debug.ui.action;

import org.eclipse.debug.internal.ui.actions.SelectAllAction;
import org.eclipse.debug.ui.IDebugView;

import com.chipon32.debug.core.model.ChiponThread;

public class ChiponSelectAllRegisterActionDelegate extends SelectAllAction {

	public ChiponSelectAllRegisterActionDelegate() {
		// TODO Auto-generated constructor stub
	}

	@Override
	protected String getActionId() {
		return IDebugView.SELECT_ALL_ACTION + ".Variables";
	}

	@Override
	protected void initialize() {
		// TODO Auto-generated method stub

	}

	@Override
	protected boolean isEnabled() {
		return ChiponThread.currentRegister !=null && ChiponThread.currentRegister.size() != 0;

	}

}
