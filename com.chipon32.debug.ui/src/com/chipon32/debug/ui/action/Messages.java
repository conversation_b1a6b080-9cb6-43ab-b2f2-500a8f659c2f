package com.chipon32.debug.ui.action;

import org.eclipse.osgi.util.NLS;

public class Messages extends NLS {
	private static final String BUNDLE_NAME = "com.chipon32.debug.ui.action.messages"; //$NON-NLS-1$
	public static String AddBreakpointViewActionDelegate_0;
	public static String AddBreakpointViewActionDelegate_1;
	public static String LoadMemoryView1ActionDelegate_0;
	public static String LoadMemoryView1ActionDelegate_1;
	public static String LoadMemoryView1ActionDelegate_10;
	public static String LoadMemoryView1ActionDelegate_11;
	public static String LoadMemoryView1ActionDelegate_12;
	public static String LoadMemoryView1ActionDelegate_13;
	public static String LoadMemoryView1ActionDelegate_2;
	public static String LoadMemoryView1ActionDelegate_3;
	public static String LoadMemoryView1ActionDelegate_4;
	
	
	public static String RemoveAllRegistersAction_0;
	public static String RemoveAllRegistersAction_1;
	public static String RemoveAllRegistersAction_2;
	
	
	public static String SaveMemoryView1ActionDelegate_0;
	public static String SaveMemoryView1ActionDelegate_1;
	public static String SaveMemoryView1ActionDelegate_10;
	public static String SaveMemoryView1ActionDelegate_12;
	public static String SaveMemoryView1ActionDelegate_13;
	public static String SaveMemoryView1ActionDelegate_14;
	public static String SaveMemoryView1ActionDelegate_3;
	public static String SaveMemoryView1ActionDelegate_4;
	static {
		// initialize resource bundle
		NLS.initializeMessages(BUNDLE_NAME, Messages.class);
	}

	private Messages() {
	}
}
