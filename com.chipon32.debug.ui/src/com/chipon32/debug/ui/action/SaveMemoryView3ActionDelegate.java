package com.chipon32.debug.ui.action;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.util.ArrayList;
import java.util.List;

import org.eclipse.jface.action.IAction;
import org.eclipse.jface.dialogs.MessageDialog;
import org.eclipse.jface.viewers.ISelection;
import org.eclipse.jface.viewers.TableViewer;
import org.eclipse.swt.SWT;
import org.eclipse.swt.widgets.Combo;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.FileDialog;
import org.eclipse.swt.widgets.Text;
import org.eclipse.ui.IViewActionDelegate;
import org.eclipse.ui.IViewPart;


import com.chipon32.debug.core.model.ChiponGroupRomData;
import com.chipon32.debug.core.model.ChiponRomData;
import com.chipon32.debug.core.model.ChiponRomDatas;
import com.chipon32.debug.ui.views.ChiponMemoryServiceClass;
import com.chipon32.debug.ui.views.ChiponMemoryView3;

public class SaveMemoryView3ActionDelegate implements IViewActionDelegate {

	private ChiponMemoryView3 memoryView; 
	private TableViewer tableViewer;
	
	private Combo showDataFormat;
	private Text txtStart;
	private Text txtEnd;
	

	@Override
	public void run(IAction action) {
		//###########################################################################
		String viewformat =  showDataFormat.getText().trim();
		int formatByteLen = ChiponMemoryServiceClass.getFormatBytes(viewformat);
		String startaddress = txtStart.getText().trim().replace(" ", ""); //$NON-NLS-1$ //$NON-NLS-2$
		String viewLength = txtEnd.getText().trim().replace(" ", ""); //$NON-NLS-1$ //$NON-NLS-2$
		if(startaddress.startsWith("0x")|| startaddress.startsWith("0X")) startaddress=startaddress.substring(2); //$NON-NLS-1$ //$NON-NLS-2$
		int startaddr =Integer.parseInt(startaddress,16);		
		int addlength =Integer.parseInt(viewLength,10);	
		int startaddrto=startaddr;
		int lengthto=addlength;
		while((startaddr+addlength)%4>0 ) // 命令对其读取芯片存在内容的尾部4对其......
		{
			addlength++;
			lengthto=addlength;
		}
		//###########################################################################
		// 需要保存的当前内存视图信息
		ChiponRomDatas romDatasIntable = (ChiponRomDatas) tableViewer.getInput();
		if(romDatasIntable==null){
			MessageDialog.openWarning(Display.getCurrent().getActiveShell(), Messages.SaveMemoryView1ActionDelegate_0, Messages.SaveMemoryView1ActionDelegate_1);
			return;
		}
		//###########################################################################
		boolean isNeedAlignen=false;
		String  AligMessage=Messages.SaveMemoryView1ActionDelegate_10+viewformat+"\""; //$NON-NLS-2$
		if(startaddr%formatByteLen!=0)
		{
			isNeedAlignen=true;
			startaddrto=startaddr + formatByteLen-(startaddr%formatByteLen);
			AligMessage+=Messages.SaveMemoryView1ActionDelegate_12+Integer.toHexString(startaddrto);
			
		}
		if( (startaddr+addlength )%formatByteLen !=0)
		{
			isNeedAlignen=true;		
			
			lengthto=(startaddr+addlength ) - (startaddr+addlength )%formatByteLen -startaddrto;
			AligMessage+=Messages.SaveMemoryView1ActionDelegate_13+Integer.toString(lengthto) + Messages.SaveMemoryView1ActionDelegate_14 +Integer.toHexString(startaddrto+lengthto-1);
		}			
		if(isNeedAlignen)
		{
			boolean iscontinue=MessageDialog.openConfirm(Display.getCurrent().getActiveShell(), Messages.SaveMemoryView1ActionDelegate_0, AligMessage);	
			if(!iscontinue)
				return;
		}
		if(lengthto<=0)
		{
			MessageDialog.openWarning(Display.getCurrent().getActiveShell(), Messages.SaveMemoryView1ActionDelegate_0, Messages.SaveMemoryView1ActionDelegate_1);
			return;
		}
		//###########################################################################
		List<ChiponGroupRomData> groupRomDatas = romDatasIntable.getGroupRomDatas();
		//System.out.println(groupRomDatas.size());
		List<String> fileContent = new ArrayList<String>();//存放将要保存到本地文件中的字符串...
		
		for(int i=0; i<groupRomDatas.size(); i++){			
			ChiponGroupRomData groupRomData = groupRomDatas.get(i);		
			
			if(groupRomData.isIs_virtual_lineGroup())
			continue;
			//			
			List<ChiponRomData> bufRomDatas = groupRomData.getRomDatas();
			for(int j=0; j<bufRomDatas.size(); j++){
			// align with -- to linestartaddr
				String itsaddress=bufRomDatas.get(j).getAddress();
				int inAddress=Integer.parseInt(itsaddress,16);
				if(inAddress <startaddrto ) continue;
				if(inAddress >=(startaddrto+lengthto) ) break;
				if(inAddress%formatByteLen==0)
				{
					String valueOut=""; //$NON-NLS-1$
					if( (j + formatByteLen) > groupRomData.getRomDatas().size())
						continue;
					int loopbytes=formatByteLen;
					while(loopbytes-->0)
					{
						valueOut = groupRomData.getRomDatas().get(j++).getValue().toUpperCase() + valueOut;
					}	
					j--;
					;					
					fileContent.add("0x"+ChiponMemoryServiceClass.toLength(Integer.toHexString(inAddress).toUpperCase(),4)+":\t" //$NON-NLS-1$ //$NON-NLS-2$
							+valueOut+"\t"+ChiponMemoryServiceClass.ValueStringCovGet(viewformat,valueOut)); //$NON-NLS-1$
				}
				else {
					continue;	
				}
			}
		}
		//###########################################################################
		FileDialog saveFileDialog = new FileDialog(Display.getCurrent().getActiveShell(), SWT.SAVE);
		saveFileDialog.setText(Messages.SaveMemoryView1ActionDelegate_3);
		saveFileDialog.setFilterExtensions(new String[]{"*.txt"}); //$NON-NLS-1$
		String selectedFile = saveFileDialog.open();
		if(selectedFile == null){  // quit
			return;
		}
		//###########################################################################
		try {
			//向选择的文件中写入当前的内存视图内容
			File file = new File(selectedFile);
			if(file.exists()){
				file.delete();
			}	
			FileOutputStream fos = new FileOutputStream(file);
			OutputStreamWriter out = new OutputStreamWriter(fos);
			BufferedWriter bufferedOut = new BufferedWriter(out);
			
			bufferedOut.write("Display Format: "+viewformat); //$NON-NLS-1$
			bufferedOut.newLine();
			bufferedOut.write("Byte Count: "+lengthto); //$NON-NLS-1$
			bufferedOut.newLine();
			bufferedOut.newLine();
			switch(formatByteLen)
			{
			case 1:
				bufferedOut.write("Address\t\tHex\t"+viewformat); //$NON-NLS-1$
				break;
			case 2:
				bufferedOut.write("Address\t\tHex\t"+viewformat); //$NON-NLS-1$]
				break;
			case 4:
				bufferedOut.write("Address\t\tHex\t\t"+viewformat); //$NON-NLS-1$]
				break;
			case 8:
				bufferedOut.write("Address\t\tHex\t\t\t"+viewformat); //$NON-NLS-1$]
				break;
			default:
				bufferedOut.write("Address\t\tHex\t"+viewformat); //$NON-NLS-1$]
				break;
			}
			bufferedOut.newLine();				

			for(int i=0;i<fileContent.size();i++){
				bufferedOut.write(fileContent.get(i) + System.lineSeparator() );			 //$NON-NLS-1$
			}
			bufferedOut.flush();
			bufferedOut.close();
			out.close();
			fos.close();
			
			
		} catch (IOException e1) {
			
			e1.printStackTrace();
			MessageDialog.openWarning(Display.getCurrent().getActiveShell(), Messages.LoadMemoryView1ActionDelegate_0, Messages.SaveMemoryView1ActionDelegate_4);
		}
		//###########################################################################

	}

	@Override
	public void selectionChanged(IAction action, ISelection selection) {
//		ChiponStackFrame frame = null;
//        IAdaptable debugContext = DebugUITools.getDebugContext();
//        if (debugContext instanceof ChiponStackFrame) {
//           frame = (ChiponStackFrame) debugContext;
//        } /*else if (debugContext instanceof ChiponThread) {		
//        	return;
//        } else if (debugContext instanceof ChiponDebugTarget) {
//        	return;
//        }*/
//        if(frame != null){
//        	debugthread=(ChiponThread) frame.getThread();
//        }
//        
//		if(debugthread != null ){
//			action.setEnabled(debugthread.canStepInto());
//		}else{
//			action.setEnabled(false);
//		}
//		
	}

	@Override
	public void init(IViewPart view) {
		// TODO Auto-generated method stub
		memoryView = (ChiponMemoryView3) view;
		tableViewer = memoryView.getTableViewer();
		showDataFormat = memoryView.getShowDataFormat();
		txtStart=memoryView.getTxtStart();
		txtEnd = memoryView.getTxtEnd();
		
	}

}
