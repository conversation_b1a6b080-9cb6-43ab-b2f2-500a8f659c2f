package com.chipon32.debug.ui.action;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import org.eclipse.core.runtime.IAdaptable;
import org.eclipse.jface.action.IAction;
import org.eclipse.jface.viewers.ISelection;
import org.eclipse.jface.viewers.IStructuredSelection;
import org.eclipse.jface.viewers.TreePath;
import org.eclipse.jface.viewers.TreeSelection;
import org.eclipse.ui.IViewActionDelegate;
import org.eclipse.ui.IViewPart;
import com.chipon32.chiponide.core.chipondescription.IEntry;
import com.chipon32.chiponide.core.chipondescription.chipio.Bits;
import com.chipon32.chiponide.core.chipondescription.chipio.Module;
import com.chipon32.chiponide.core.chipondescription.chipio.Register;
import com.chipon32.debug.core.model.ChiponThread;
import com.chipon32.debug.ui.views.ChiponRegistersView;

public class RemoveRegistersAction implements IViewActionDelegate {
	
	private ChiponRegistersView fView;
	
	@Override
	public void run(IAction action) {
//		IStructuredSelection selection = 
//				(IStructuredSelection)fView.getViewer().getSelection();
//		Register register = null; 
//		if(selection.getFirstElement() instanceof Bits){
//			return;
//		}
//		if(selection.getFirstElement() instanceof Register){
//			register = (Register)selection.getFirstElement();
//		}
//		if(register != null){
//			fView.getCurrentRegister().remove(register);
//			ChiponThread.currentRegister = fView.getCurrentRegister();
//		}	
//		fView.getViewer().refresh();
		
		TreeSelection selection = (TreeSelection)fView.getViewer().getSelection();
		List<IEntry> registers = new ArrayList<>();
		List<IEntry> modules = new ArrayList<>();
		TreePath[] paths = selection.getPaths();
		for (int i = paths.length-1; i >=0; i--) {
			TreePath path = paths[i];
			//int segmentCount = path.getSegmentCount();
			Object segment;
			segment = path.getFirstSegment();
			/*if(segmentCount==1){
				segment = path.getFirstSegment();
			}else if(segmentCount==2){
				segment = path.getSegment(1);
				if(segment instanceof Bits){
					segment = path.getFirstSegment();
				}else{
					segment = null;
					fView.getTv().remove(path);
				}
			}else{
				//segment = path.getSegment(1); 
				segment = null;
				path = path.getParentPath();
				fView.getTv().remove(path);
			}*/
			
			if (segment instanceof Module) {
				modules.add((IEntry) segment);
			}else if(segment instanceof Register){
				registers.add((IEntry) segment);
			}else if (segment instanceof IAdaptable) {
			    IEntry register = (IEntry)((IAdaptable)segment).getAdapter(IEntry.class);
			    if (register != null) {
			    	registers.add(register);
			    }
			}
		}
		if(modules.size()!=0){
			fView.getCurrentModule().removeAll(modules);
			ChiponThread.currentModule = fView.getCurrentModule();	
			fView.getViewer().refresh();		
		}
		if(registers.size()!=0){
			fView.getCurrentRegister().removeAll(registers);
			ChiponThread.currentRegister = fView.getCurrentRegister();	
			fView.getViewer().refresh();
		}
	}

	@Override
	public void selectionChanged(IAction action, ISelection selection) {
		if (selection instanceof IStructuredSelection) {
			IStructuredSelection ss = (IStructuredSelection) selection;
			action.setEnabled(getEnableStateForSelection(ss));
			
		} else {
			action.setEnabled(false);

		}
	}

	@Override
	public void init(IViewPart view) {
		fView = (ChiponRegistersView)view;
		
	}

	protected boolean getEnableStateForSelection(IStructuredSelection selection) {
		if (selection.size() == 0) {
			return false;
		}
		Iterator itr = selection.iterator();
		while (itr.hasNext()) {
			Object element = itr.next();
			if (!isEnabledFor(element)) {
				return false;
			}
		}
		return true;
	}
	
	
	protected boolean isEnabledFor(Object element) {
		return true;
	}



}
