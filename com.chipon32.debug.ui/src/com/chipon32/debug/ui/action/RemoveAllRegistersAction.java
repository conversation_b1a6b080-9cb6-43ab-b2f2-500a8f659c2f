package com.chipon32.debug.ui.action;

import java.util.Iterator; 

import org.eclipse.debug.internal.ui.DebugUIPlugin;
import org.eclipse.debug.internal.ui.preferences.IDebugPreferenceConstants;
import org.eclipse.jface.action.IAction;
import org.eclipse.jface.dialogs.IDialogConstants;
import org.eclipse.jface.dialogs.MessageDialogWithToggle;
import org.eclipse.jface.preference.IPreferenceStore;
import org.eclipse.jface.viewers.ISelection;
import org.eclipse.jface.viewers.IStructuredSelection;
import org.eclipse.ui.IViewActionDelegate;
import org.eclipse.ui.IViewPart;
import org.eclipse.ui.IWorkbenchWindow;

import com.chipon32.debug.core.model.ChiponThread;
import com.chipon32.debug.ui.views.ChiponRegistersView;
/**
 * 
 * <AUTHOR>
 */
@SuppressWarnings("restriction")
public class RemoveAllRegistersAction implements IViewActionDelegate {

	private ChiponRegistersView fView;
	
	@Override
	public void run(IAction action) {
		//DebugUIPlugin.getDefault();
		IWorkbenchWindow window = DebugUIPlugin.getActiveWorkbenchWindow();
		if(window != null){
			IPreferenceStore store = DebugUIPlugin.getDefault().getPreferenceStore();
			boolean prompt = store.getBoolean(IDebugPreferenceConstants.PREF_PROMPT_REMOVE_ALL_EXPRESSIONS);
			//boolean prompt = true;
			boolean proceed = true;
			if(prompt){
				MessageDialogWithToggle mdwt =  MessageDialogWithToggle.openYesNoQuestion(
						window.getShell(), 
						Messages.RemoveAllRegistersAction_0,
						Messages.RemoveAllRegistersAction_1, 
						Messages.RemoveAllRegistersAction_2, 
						!prompt, 
						null,
						null);
				if(mdwt.getReturnCode() !=  IDialogConstants.YES_ID){
					proceed = false;
				}
				else {
					store.setValue(IDebugPreferenceConstants.PREF_PROMPT_REMOVE_ALL_EXPRESSIONS, !mdwt.getToggleState());
				}
			}
			if (proceed) {
				if(ChiponThread.currentModule != null){
					ChiponThread.currentModule.clear();
					fView.getViewer().refresh();
				}
				if(ChiponThread.currentRegister != null){
					ChiponThread.currentRegister.clear();
					fView.getViewer().refresh();
				}
			}
		}
		

	}

	@Override
	public void selectionChanged(IAction action, ISelection selection) {
		if (selection instanceof IStructuredSelection) {
			IStructuredSelection ss = (IStructuredSelection) selection;
			action.setEnabled(getEnableStateForSelection(ss));
		} else {
			action.setEnabled(false);

		}

	}

	@Override
	public void init(IViewPart view) {
		fView = (ChiponRegistersView) view;

	}

	protected boolean getEnableStateForSelection(IStructuredSelection selection) {
		if (selection.size() == 0) {
			return false;
		}
		Iterator itr = selection.iterator();
		while (itr.hasNext()) {
			Object element = itr.next();
			if (!isEnabledFor(element)) {
				return false;
			}
		}
		return true;
	}
	
	protected boolean isEnabledFor(Object element) {
		return true;
	}
	
}
