package com.chipon32.debug.ui.action;

import org.eclipse.core.runtime.IAdaptable;
import org.eclipse.debug.core.DebugPlugin;
import org.eclipse.debug.core.model.IDebugElement;
import org.eclipse.debug.core.model.IWatchExpression;
import org.eclipse.debug.ui.DebugUITools;
import org.eclipse.debug.ui.IDebugUIConstants;
import org.eclipse.jface.action.IAction;
import org.eclipse.jface.text.ITextSelection;
import org.eclipse.jface.viewers.ISelection;
import org.eclipse.jface.viewers.ISelectionProvider;
import org.eclipse.swt.widgets.Shell;
import org.eclipse.ui.IEditorActionDelegate;
import org.eclipse.ui.IEditorPart;
import org.eclipse.ui.IWorkbenchPage;
import org.eclipse.ui.IWorkbenchPart;
import org.eclipse.ui.IWorkbenchWindow;
import org.eclipse.ui.PartInitException;
import org.eclipse.ui.PlatformUI;
import org.eclipse.ui.actions.ActionDelegate;

import com.chipon32.debug.ui.DebugUIActivator;

public class ChiponAddExpressionCEditorActionDelegate extends ActionDelegate implements IEditorActionDelegate{

private IEditorPart fEditorPart;
	
	/* (non-Javadoc)
	 * @see org.eclipse.ui.IEditorActionDelegate#setActiveEditor(org.eclipse.jface.action.IAction, org.eclipse.ui.IEditorPart)
	 */
	@Override
	public void setActiveEditor( IAction action, IEditorPart targetEditor ) {
		setEditorPart( targetEditor );
	}

	private IEditorPart getEditorPart() {
		return fEditorPart;
	}

	private void setEditorPart( IEditorPart editorPart ) {
		fEditorPart = editorPart;
	}

	@Override
	public void run( IAction action ) {
//		String text = getSelectedText();
//		ExpressionDialog dlg = new ExpressionDialog( getShell(), text );
//		if ( dlg.open() != Window.OK )
//			return;
		createExpression(getSelectedText());
		activateExpressionView();
	}

	private String getSelectedText() {
		ISelection selection = getTargetSelection();
		if ( selection != null && selection instanceof ITextSelection ) {
			return ((ITextSelection)selection).getText().replaceAll("(\\r\\n|\\n|\\t| )+", " ").trim(); //$NON-NLS-1$ //$NON-NLS-2$;
		}
		return ""; //$NON-NLS-1$
	}

	protected ISelection getTargetSelection() {
		IWorkbenchPart part = getEditorPart();
		if ( part != null ) {
			ISelectionProvider provider = part.getSite().getSelectionProvider();
			if ( provider != null ) {
				return provider.getSelection();
			}
		}
		return null;
	}

	private void createExpression(String text) {
		if(text.isEmpty()) {//¿Õ×Ö·û²»Ìí¼Ó¼à¿Ø
			return;
		}
		IWatchExpression watchExpression= DebugPlugin.getDefault().getExpressionManager().newWatchExpression(text);
		DebugPlugin.getDefault().getExpressionManager().addExpression( watchExpression );
		IAdaptable context = DebugUITools.getDebugContext();
		if (context instanceof IDebugElement)
			watchExpression.setExpressionContext((IDebugElement)context);
	}

	protected Shell getShell() {
		return ( getEditorPart() != null ) ? getEditorPart().getSite().getShell() : DebugUIActivator.getActiveWorkbenchShell();
	}

	private void activateExpressionView() {
		IWorkbenchWindow window = PlatformUI.getWorkbench().getActiveWorkbenchWindow();
		if ( window != null ) {
			IWorkbenchPage page = window.getActivePage();
			if ( page != null ) {
				try {
					page.showView( IDebugUIConstants.ID_EXPRESSION_VIEW );
				}
				catch( PartInitException e ) {
				}
			}
		}
	}

}
