package com.chipon32.debug.ui.util;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;

import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import com.chipon32.debug.core.model.ChiponWatchpointData;


/**
 * 文件的属性外的 设计信息记录体
 *
 */
public class watchpointXmlIO {
	
	/**
	 * 保存
	 */	
	public static void  add(ChiponWatchpointData watchpointdata, File xmlFile){
//		File xmlFile = new File("./XX.xml");
		DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
		try {
			DocumentBuilder db = dbf.newDocumentBuilder();
			Document doc = null;
			Element root = null;
			if(xmlFile.exists()){	
				doc = db.parse(xmlFile);
				root = doc.getDocumentElement();				
			}else{
				doc = db.newDocument();
				root = doc.createElement("WATCHPOINT");
				doc.appendChild(root);
			}	
			//########################
			Element son = doc.createElement("watchpoint");
			son.setAttribute("message", watchpointdata.getMessage());
			son.setAttribute("defaultid", Integer.toString(watchpointdata.getDefaultselid()));
			son.setAttribute("defaulttext", watchpointdata.getSelelementtext());
			son.setAttribute("enable",Boolean.toString(watchpointdata.isNowValue()));
			son.setAttribute("id", watchpointdata.getId());
			son.setTextContent(watchpointdata.getName());
			//########################
			root.appendChild(son);
			//########################保存
			TransformerFactory factory = TransformerFactory.newInstance();
			Transformer former  = factory.newTransformer();
			former.transform(new DOMSource(doc), new StreamResult(xmlFile));		
		
		}catch (Exception e) {			
			e.printStackTrace();
		}		
	}
	
	
	/**
	 * 读取
	 * @return
	 */
	public static List<ChiponWatchpointData> readWatchpointData(File xmlFile){
		List<ChiponWatchpointData> watchpoint = new ArrayList<>();
//		File xmlFile = new File("./userException.xml");
		if(xmlFile.exists()){
			DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
			dbf.setIgnoringElementContentWhitespace(false);
			
			try {
				DocumentBuilder db = dbf.newDocumentBuilder();
				Document doc = db.parse(xmlFile);
				
				NodeList sonliList = doc.getElementsByTagName("WATCHPOINT"); // 所有的watchpoint
				
				for(int i = 0; i < sonliList.getLength(); i++){
					
					Element son = (Element)sonliList.item(i);
					
					for(Node node = son.getFirstChild(); node != null; node = node.getNextSibling()){
						if(node.getNodeType() == Node.ELEMENT_NODE){
							
							ChiponWatchpointData cwpd=new ChiponWatchpointData();
							//获取属性的值 对应添加的方法
//							son.setAttribute("message", watchpointdata.getMessage());
//							son.setAttribute("defaultid", Integer.toString(watchpointdata.getDefaultselid()));
//							son.setAttribute("defaulttext", watchpointdata.getSelelementtext());
//							son.setAttribute("enable",Boolean.toString(watchpointdata.isNowValue()));
//							son.setAttribute("id", watchpointdata.getId());
//							son.setTextContent(watchpointdata.getName());
							
							Node curNode ;
							curNode= node.getAttributes().getNamedItem("message");					
							cwpd.setMessage( curNode.getFirstChild().getNodeValue());
							
							curNode= node.getAttributes().getNamedItem("defaultid");					
							cwpd.setDefaultselid(Integer.parseInt( curNode.getFirstChild().getNodeValue()));
							curNode= node.getAttributes().getNamedItem("defaulttext");					
							cwpd.setSelelementtext( curNode.getFirstChild().getNodeValue());
							
							cwpd.setIsNowValue(false); // 后续处理有效							
							cwpd.setId("-1");   // id只有交互才后期有的
							
							String nameget = node.getFirstChild().getNodeValue();							
							cwpd.setName(nameget);
							
							watchpoint.add(cwpd);
						}
					}
				}
				
				
			} catch (Exception e) {
				e.printStackTrace();
			} 
		}
		
		return watchpoint;
	}
	
	/**
	 * 删除
	 * @param expressions
	 */
	public static void delete(List<ChiponWatchpointData> watchpointdata, File xmlFile){
//		File xmlFile = new File("userException.xml");
		if(xmlFile.exists()){

			DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
			dbf.setIgnoringElementContentWhitespace(false);
			try {
				DocumentBuilder db = dbf.newDocumentBuilder();
				Document doc = db.parse(xmlFile);
				NodeList sonliList = doc.getElementsByTagName("WATCHPOINT");
//				Node root = sonliList.item(0);
				
								
				for(ChiponWatchpointData cwpd : watchpointdata){
					
					boolean isfind = false;
					for(int i = 0; i < sonliList.getLength()&&!isfind; i++){
						
						Element son = (Element)sonliList.item(i);		
						
						for(Node node = son.getFirstChild(); node != null; node = node.getNextSibling()){
							
							if(node.getNodeType() == Node.ELEMENT_NODE){
								//id属性的值
//								String name = node.getAttributes().getNamedItem("message").getFirstChild().getNodeValue();
								String name = node.getFirstChild().getNodeValue();
								
									if(name.equals(cwpd.getName())){
										son.removeChild(node);
										isfind = true;
										break;
									}
								}	
							}
					} // endfor
					
				}
				TransformerFactory factory = TransformerFactory.newInstance();
				Transformer former  = factory.newTransformer();
				former.transform(new DOMSource(doc), new StreamResult(xmlFile));
				
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
	}
	
	public static void deleteALL(File xmlFile){
//		File xmlFile = new File("userException.xml");
		if(xmlFile.exists()){
			xmlFile.delete();
		}
	}
	
	

	
}
