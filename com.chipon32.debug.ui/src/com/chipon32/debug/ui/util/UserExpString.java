package com.chipon32.debug.ui.util;

/**
 * 用户自定义的观察点类
 * <AUTHOR>
 *
 */
public class UserExpString {
	
	/**	观察点的值 */
	private String expression;
	
	/** 用户标记 */
	private String id;
	
	

	public UserExpString() {
		super();
	}

	public UserExpString(String expression, String id) {
		super();
		this.expression = expression;
		this.id = id;
	}

	public String getExpression() {
		return expression;
	}

	public void setExpression(String expression) {
		this.expression = expression;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	} 
	
	
}
