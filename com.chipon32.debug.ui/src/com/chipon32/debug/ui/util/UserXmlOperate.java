package com.chipon32.debug.ui.util;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;

import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;


/**
 * 观察点数据的添加和删除
 * <AUTHOR>
 *
 */
public class UserXmlOperate {
	
	private static UserXmlOperate userXmlOperate;
	
	private UserXmlOperate(){}
	
	public static UserXmlOperate newInstance(){
		if(userXmlOperate == null){
			userXmlOperate = new UserXmlOperate();
		}
		return userXmlOperate;
	}
	
	/**
	 * 保存观察点数据
	 */	
	public  void  save(UserExpString exception, File xmlFile){
//		File xmlFile = new File("./userException.xml");
		DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
		try {
			DocumentBuilder db = dbf.newDocumentBuilder();
			Document doc = null;
			Element root = null;
			if(xmlFile.exists()){	
				doc = db.parse(xmlFile);
				root = doc.getDocumentElement();				
			}else{
				doc = db.newDocument();
				root = doc.createElement("EXPRESSIONS");
				doc.appendChild(root);
			}	
			Element son = doc.createElement("expression");
			son.setAttribute("id", exception.getId());
			son.setTextContent(exception.getExpression());
//				son.setNodeValue(s);
			root.appendChild(son);

			

			//保存
			TransformerFactory factory = TransformerFactory.newInstance();
			Transformer former  = factory.newTransformer();
			former.transform(new DOMSource(doc), new StreamResult(xmlFile));
			
		
		}catch (Exception e) {
			
			e.printStackTrace();
		}
		
	}
	
	
	/**
	 * 读取保存的观察点
	 * @return
	 */
	public  List<UserExpString> readExceptions(File xmlFile){
		List<UserExpString> exceptions = new ArrayList<>();
//		File xmlFile = new File("./userException.xml");
		if(xmlFile.exists()){

			DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
			dbf.setIgnoringElementContentWhitespace(false);
			try {
				DocumentBuilder db = dbf.newDocumentBuilder();
				Document doc = db.parse(xmlFile);
				
				NodeList sonliList = doc.getElementsByTagName("EXPRESSIONS");
				for(int i = 0; i < sonliList.getLength(); i++){
					Element son = (Element)sonliList.item(i);
					for(Node node = son.getFirstChild(); node != null; node = node.getNextSibling()){
						if(node.getNodeType() == Node.ELEMENT_NODE){
							//获取属性id的值
							Node curNode = node.getAttributes().getNamedItem("id");				
							String id = curNode.getFirstChild().getNodeValue();
							String expression = node.getFirstChild().getNodeValue();
							
							System.out.println( id + " : " + expression);
							exceptions.add(new UserExpString(expression, id));
						}
					}
				}
				
				
			} catch (Exception e) {
				e.printStackTrace();
			} 
		}
		
		return exceptions;
	}
	
	/**
	 * 删除观察点
	 * @param expressions
	 */
	public void delete(List<UserExpString> expressions, File xmlFile){
//		File xmlFile = new File("userException.xml");
		if(xmlFile.exists()){

			DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
			dbf.setIgnoringElementContentWhitespace(false);
			try {
				DocumentBuilder db = dbf.newDocumentBuilder();
				Document doc = db.parse(xmlFile);
				NodeList sonliList = doc.getElementsByTagName("EXPRESSIONS");
//				Node root = sonliList.item(0);
				
								
				for(UserExpString s : expressions){
					boolean isfind = false;
					for(int i = 0; i < sonliList.getLength()&&!isfind; i++){
						Element son = (Element)sonliList.item(i);										
						for(Node node = son.getFirstChild(); node != null; node = node.getNextSibling()){
							if(node.getNodeType() == Node.ELEMENT_NODE){
								//id属性的值
								String id = node.getAttributes().getNamedItem("id").getFirstChild().getNodeValue();
	//							String value = node.getFirstChild().getNodeValue();
								
									if(id.equals(s.getId())){
										son.removeChild(node);
										isfind = true;
										break;
									}
								}	
							}
						}
				}
				TransformerFactory factory = TransformerFactory.newInstance();
				Transformer former  = factory.newTransformer();
				former.transform(new DOMSource(doc), new StreamResult(xmlFile));
				
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
	}
	
	public void deleteALL(File xmlFile){
//		File xmlFile = new File("userException.xml");
		if(xmlFile.exists()){
			xmlFile.delete();
		}
	}
	
	

	
}
