package com.chipon32.debug.ui.util;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.eclipse.swt.events.VerifyEvent;
import org.eclipse.swt.events.VerifyListener;

public class ConditionVerifyListener implements VerifyListener {

	@Override
	public void verifyText(VerifyEvent e) {
		Pattern pattern = Pattern.compile("[=<>!.~0-9a-zA-Z]*");
        Matcher match = pattern.matcher(e.text);
		if(match.matches()){
			e.text = e.text.toUpperCase();
			e.doit = true;
		}else if(e.text.length()>0){
			e.doit = false;
		}else
			e.doit = true;

	}

}
