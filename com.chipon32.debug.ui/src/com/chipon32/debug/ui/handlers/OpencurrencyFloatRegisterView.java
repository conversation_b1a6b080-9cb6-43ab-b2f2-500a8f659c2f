package com.chipon32.debug.ui.handlers;

import org.eclipse.core.commands.AbstractHandler;
import org.eclipse.core.commands.ExecutionEvent;
import org.eclipse.core.commands.ExecutionException;

import org.eclipse.ui.IWorkbenchPage;
import org.eclipse.ui.IWorkbenchWindow;
import org.eclipse.ui.PartInitException;
import org.eclipse.ui.handlers.HandlerUtil;

import com.chipon32.debug.ui.views.ChiponCurrencyFloatRegisterView;

public class OpencurrencyFloatRegisterView extends AbstractHandler {



	@Override
	public Object execute(ExecutionEvent event) throws ExecutionException {
		//获取活动的window
		IWorkbenchWindow window = HandlerUtil.getActiveWorkbenchWindowChecked(event);
		if(window == null){
			return null;
		}
		//获取活动的page
		IWorkbenchPage page = window.getActivePage();
		if(page == null){
			return null;
		}
		
		try {
			page.showView(ChiponCurrencyFloatRegisterView.ID);
		} catch (PartInitException e) {
			
		}
		return null;
	}



}
