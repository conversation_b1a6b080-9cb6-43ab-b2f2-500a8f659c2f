package com.chipon32.debug.ui.handlers;

import org.eclipse.core.commands.AbstractHandler;
import org.eclipse.core.commands.ExecutionEvent;
import org.eclipse.core.commands.ExecutionException;
import org.eclipse.debug.core.Launch;
import org.eclipse.debug.ui.DebugUITools;
import org.eclipse.jface.viewers.ISelection;
import org.eclipse.jface.viewers.IStructuredSelection;
import org.eclipse.ui.handlers.HandlerUtil;

import com.chipon32.debug.core.model.ChiponDebugTarget;
import com.chipon32.debug.core.model.ChiponStackFrame;
import com.chipon32.debug.core.model.ChiponThread;

/***
 *<AUTHOR>
 ***/
public class ResetDeviceDiaplasisHandler extends AbstractHandler{

	  
	private ChiponThread   fThread;

	@Override
	public Object execute(ExecutionEvent event) throws ExecutionException {
		fThread = null;

		ISelection selection=HandlerUtil.getActiveWorkbenchWindow(event).getActivePage().getSelection();
		
		 Object element = ((IStructuredSelection)selection).getFirstElement();
		 if (element instanceof ChiponDebugTarget) {
			 fThread =  ((ChiponDebugTarget) element).getCurrentThread();
		 }
		 else  
		 if (element instanceof ChiponThread) {
			 fThread = (ChiponThread) element;
		 }
		 else  if (element instanceof ChiponStackFrame) {
        	fThread = (ChiponThread) ((ChiponStackFrame) element).getThread();
        }
		 else
		 {
			 element = DebugUITools.getDebugContext();
			 if(element instanceof Launch)
			 {
		        	Launch lc=(Launch)element;		        	
		        	ChiponDebugTarget target=(ChiponDebugTarget) lc.getDebugTargets()[0];
		        	fThread=target.getCurrentThread();      
			 }
			 else if (element instanceof ChiponDebugTarget) {
				 fThread =  ((ChiponDebugTarget) element).getCurrentThread();
			 }
			 else if (element instanceof ChiponThread) {
				 fThread = (ChiponThread) element;
			 }
			 else if (element instanceof ChiponStackFrame) {
	        	fThread = (ChiponThread) ((ChiponStackFrame) element).getThread();
	        }
		 }
		//need list with tree  or  can't find thread
		if(fThread!=null)
			fThread.reset();
		// 
			
		return null;
	}
	

}
