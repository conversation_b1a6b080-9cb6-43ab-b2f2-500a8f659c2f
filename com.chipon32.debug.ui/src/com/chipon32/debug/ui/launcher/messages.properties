ChiponLaunchShortcut_0=Prompt
ChiponLaunchShortcut_1=Debug Limit:Please Select File Or Open File From Editor View,And Try Again...

ChiponLaunchShortcutSoft_0=Prompt
ChiponLaunchShortcutSoft_1=Debug Limit:Please Select File Or Open File From Editor View,And Try Again...

ChipONLaunchShortcut_2=The selected project is inconsistent with the currently open file.
ChipONLaunchShortcut_3=Please open the file under the selected project.

ChiponMainTab_0=&Program:
ChiponMainTab_1=&Browse...
ChiponMainTab_2=ChipON Program
ChiponMainTab_3=Select ChipON Program
ChiponMainTab_4=Main
ChiponMainTab_5=Specified program does not exist
ChiponMainTab_6=Specify a program
ChiponMainTab_7=First program breakpoint line:
ChiponMainTab_8=Default:0(Determine the number of rows based on the main function)
ChiponMainTab_9=Set $pc Address(Example:0xB0060000):
ChiponMainTab_10=Initial break method for program:
ChiponMainTab_11=Instruction Set Type:
ChiponMainTab_12=Debug Mode
ChiponMainTab_13=Single Core Debug
ChiponMainTab_14=Multi-Core Debug

ChiponDebugConfigPage_0=Download Setting Before Debug
ChiponDebugConfigPage_1=Auto Download
ChiponDebugConfigPage_2=Manual Download
ChiponDebugConfigPage_3=Auto StepIn Interval:
ChiponDebugConfigPage_6=" Invalid Value!
ChiponDebugConfigPage_7=ERROR
ChiponDebugConfigPage_8=" Invalid Value!
ChiponDebugConfigPage_9=ERROR

ChiponConfigTab_0=Config

