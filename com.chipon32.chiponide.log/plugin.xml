<?xml version="1.0" encoding="UTF-8"?>
<?eclipse version="3.4"?>
<plugin>
   <extension
         point="org.eclipse.ui.views">
      <view
            category="com.chipon32.chiponide.ui.view.chipontype"
            class="com.chipon32.chiponide.log.view.HistoryView"
            icon="icons/history.png"
            id="com.chipon32.chiponide.ui.view.history"
            name="%view_kungfu32_message_history"
            restorable="true">
      </view>
   </extension>
   <extension
         point="org.eclipse.ui.perspectiveExtensions">
      <perspectiveExtension
            targetID="com.chipon32.chiponide.ui.CPerspective">
         <view
               id="com.chipon32.chiponide.ui.view.history"
               minimized="false"
               relationship="stack"
               relative="org.eclipse.ui.console.ConsoleView">
         </view>
         <viewShortcut
               id="com.chipon32.chiponide.ui.view.history">
         </viewShortcut>
      </perspectiveExtension>
      <perspectiveExtension
            targetID="org.eclipse.debug.ui.DebugPerspective">
         <view
               id="com.chipon32.chiponide.ui.view.history"
               minimized="false"
               relationship="stack"
               relative="org.eclipse.ui.console.ConsoleView">
         </view>
         <viewShortcut
               id="com.chipon32.chiponide.ui.view.history">
         </viewShortcut>
      </perspectiveExtension>
   </extension>
   <extension
         point="org.eclipse.core.runtime.preferences">
      <initializer
            class="com.chipon32.chiponide.log.preference.FilterHistoryPreferenceInitializer">
      </initializer>
   </extension>

</plugin>
