package com.chipon32.chiponide.log.dialog;

import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.events.SelectionListener;
import org.eclipse.swt.graphics.Image;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Button;
import org.eclipse.swt.widgets.Group;
import org.eclipse.swt.widgets.Shell;

import com.chipon32.chiponide.log.Activator;
import com.chipon32.chiponide.log.Messages;

public class ExportHistoryDialog extends AbstractButtonBarDialog {
	
	private int selectIndex = 0;

	public ExportHistoryDialog(Shell shell) {
		super(shell);
	}
	
	@Override
	protected Image getTitleImage() {
		return Activator.getImageDescriptor("icons/input.png").createImage();
	}
	
	@Override
	protected String getTitle() {
		return Messages.EXPORT_HISTORY;
	}
	
	@Override
	protected void createContent(Shell shell) {
		Group exportGroup = new Group(shell, SWT.NONE);
		exportGroup.setText(Messages.EXPORT_TYPE);
		GridLayout exporttGroupGL = new GridLayout(1, false);
		exporttGroupGL.marginRight = 10;
		exporttGroupGL.marginLeft = 10;
		exporttGroupGL.marginTop = 10;
		exporttGroupGL.marginBottom = 10;
		exporttGroupGL.verticalSpacing = 10;

		exportGroup.setLayout(exporttGroupGL);
		exportGroup.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
		
		Button allExportBtn = new Button(exportGroup, SWT.RADIO);
		allExportBtn.setText(Messages.EXPORT_ALL);
		allExportBtn.addSelectionListener(new SelectionListener() {
			
			@Override
			public void widgetSelected(SelectionEvent e) {
				selectIndex = 0;				
			}
			
			@Override
			public void widgetDefaultSelected(SelectionEvent e) {
				
			}
		});
		
		Button inputExportBtn = new Button(exportGroup, SWT.RADIO);
		inputExportBtn.setText(Messages.EXPORT_INPUT);
		inputExportBtn.addSelectionListener(new SelectionListener() {
			
			@Override
			public void widgetSelected(SelectionEvent e) {
				selectIndex = 1;								
			}
			
			@Override
			public void widgetDefaultSelected(SelectionEvent e) {
				
			}
		});
		
		Button outputExportBtn = new Button(exportGroup, SWT.RADIO);
		outputExportBtn.setText(Messages.EXPORT_OUTPUT);
		outputExportBtn.addSelectionListener(new SelectionListener() {
			
			@Override
			public void widgetSelected(SelectionEvent e) {
				selectIndex = 2;								
			}
			
			@Override
			public void widgetDefaultSelected(SelectionEvent e) {
				
			}
		});

	}

	@Override
	public Object getResult() {
		return selectIndex;
	}

}
