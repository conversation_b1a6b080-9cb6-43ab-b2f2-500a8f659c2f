
package com.chipon32.chiponide.log.dialog;

import org.eclipse.core.runtime.Platform;
import org.eclipse.jface.dialogs.IDialogConstants;
import org.eclipse.jface.resource.JFaceResources;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.graphics.FontMetrics;
import org.eclipse.swt.graphics.GC;
import org.eclipse.swt.graphics.Image;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.graphics.Rectangle;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Button;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Dialog;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Shell;

import com.chipon32.chiponide.log.Messages;

/**
 * 
 * <AUTHOR>
 *
 */
public abstract class AbstractButtonBarDialog extends Dialog {
	protected boolean isOk = false;

	/** Number of vertical dialog units per character, value <code>8</code>. */
	private static final int VERTICAL_DIALOG_UNITS_PER_CHAR = 8;
	protected Button okBtn = null;
	protected Button cancelBtn = null;

	public AbstractButtonBarDialog(Shell shell) {
		super(shell, SWT.DIALOG_TRIM | SWT.SYSTEM_MODAL | SWT.RESIZE);
	}

	public AbstractButtonBarDialog(Shell shell, int style) {
		super(shell, style);
	}

	protected void buttonPressed(int buttonId, Composite parent) {
		if (IDialogConstants.OK_ID == buttonId) {
			okPressed(parent);
		} else if (IDialogConstants.CANCEL_ID == buttonId) {
			cancelPressed(parent);
		}
	}

	protected void cancelPressed(Composite parent) {
		parent.getParent().dispose();
		isOk = false;
	}

	public static int convertVerticalDLUsToPixels(FontMetrics fontMetrics, int dlus) {
		// round to the nearest pixel
		return (fontMetrics.getHeight() * dlus + VERTICAL_DIALOG_UNITS_PER_CHAR / 2) / VERTICAL_DIALOG_UNITS_PER_CHAR;
	}

	protected int convertVerticalDLUsToPixels(int dlus, Composite composite) {
		GC gc = new GC(composite);
		gc.setFont(JFaceResources.getDialogFont());
		FontMetrics fontMetrics = gc.getFontMetrics();
		if (fontMetrics == null) {
			return 0;
		}
		gc.dispose();
		return convertVerticalDLUsToPixels(fontMetrics, dlus);
	}

	protected Button createButton(final Composite parent, int id, String label, boolean defaultButton) {
		// increment the number of columns in the button bar
		((GridLayout) parent.getLayout()).numColumns++;
		Button button = new Button(parent, SWT.PUSH);
		button.setText(label);
		button.setFont(JFaceResources.getDialogFont());
		button.setData(Integer.valueOf(id));
		button.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				buttonPressed(((Integer) event.widget.getData()).intValue(), parent.getParent());
			}
		});
		if (defaultButton) {
			Shell shell = parent.getShell();
			if (shell != null) {
				shell.setDefaultButton(button);
			}
		}
		return button;
	}

	protected Control createButtonBar(Composite parent) {

		Composite composite = new Composite(parent, SWT.NONE);
		// create a layout with spacing and margins appropriate for the font
		// size.
		GridLayout layout = new GridLayout();
		layout.numColumns = 3; // this is incremented by createButton
		layout.makeColumnsEqualWidth = false;
		composite.setLayout(layout);
		GridData data = new GridData(getButtonAlignLocation() | GridData.VERTICAL_ALIGN_CENTER);
		composite.setLayoutData(data);
		if (parent != null && parent.getFont() != null) {
			composite.setFont(parent.getFont());
		}

		// Add the buttons to the button bar.
		createButtonsForButtonBar(composite);

		Shell shell = (Shell) parent;
		if (shell != null) {
			if (shell.getDisplay().getDismissalAlignment() == SWT.RIGHT) {
				// make the default button the right-most button
				Button defaultButton = shell.getDefaultButton();
				if (defaultButton != null) {
					defaultButton.moveBelow(null);
					defaultButton.getParent().layout();
				}
			}
		}
		return composite;
	}

	protected void createButtonsForButtonBar(Composite parent) {
		Composite leftComposite = new Composite(parent, SWT.None);
		GridData leftGd = new GridData(GridData.HORIZONTAL_ALIGN_BEGINNING);
		leftGd.grabExcessHorizontalSpace = true;
		leftComposite.setLayoutData(leftGd);
		leftComposite.setLayout(new GridLayout());
		createLeftButtons(leftComposite);

		Composite centerComposite = new Composite(parent, SWT.None);
		GridData centerGd = new GridData(GridData.HORIZONTAL_ALIGN_CENTER);
		centerGd.grabExcessHorizontalSpace = true;
		centerComposite.setLayoutData(centerGd);
		centerComposite.setLayout(new GridLayout());
		createCenterButtons(centerComposite);

		Composite rightComposite = new Composite(parent, SWT.None);
		GridData rightGd = new GridData(GridData.HORIZONTAL_ALIGN_END);
		rightGd.grabExcessHorizontalSpace = true;
		rightComposite.setLayoutData(rightGd);
		rightComposite.setLayout(new GridLayout());
		createRightButtons(rightComposite);
	}

	protected void createCenterButtons(Composite parent) {

	}

	protected void createContent(Shell shell) {

	}

	protected void createLeftButtons(Composite parent) {
	}

	protected void createRightButtons(Composite parent) {
		okBtn = createButton(parent, IDialogConstants.OK_ID, getOkButtonLabel(), true);
		cancelBtn = createButton(parent, IDialogConstants.CANCEL_ID, getCancelButtonLabel(), false);
	}

	public void dispose() {
		if (okBtn != null && !okBtn.isDisposed()) {
			okBtn.dispose();
		}
		if (cancelBtn != null && !cancelBtn.isDisposed()) {
			cancelBtn.dispose();
		}
	}

	protected int getButtonAlignLocation() {
		return GridData.FILL_HORIZONTAL;
	}

	protected int getButtonBarHorizontalSpace(Composite parent) {
		return convertVerticalDLUsToPixels(IDialogConstants.HORIZONTAL_SPACING, parent);
	}

	protected String getCancelButtonLabel() {
		return Messages.CANCEL;
	}

	protected String getOkButtonLabel() {
		return Messages.OK;
	}

	public Object getResult() {
		return null;
	}

	protected String getTitle() {
		return "";
	}
	
	protected Image getTitleImage() {
		return null;
	}

	protected boolean isAlignCenter() {
		return true;
	}

	protected void okPressed(Composite parent) {
		parent.getParent().dispose();
		isOk = true;
	}

	protected Point getShellDefaultSize() {
		return null;
	}

	public boolean open() {
		isOk = false;

		final Shell shell = new Shell(getParent(), getStyle());
		GridLayout glShell = new GridLayout(1, false);
		glShell.verticalSpacing = 2;
		glShell.marginLeft = 5;
		glShell.marginRight = 5;

		shell.setText(getTitle());
		shell.setLayout(glShell);
		shell.setImage(getTitleImage());

		createContent(shell);
		createButtonBar(shell);

		if (getShellDefaultSize() == null) {
			shell.pack();
		} else {
			shell.setSize(getShellDefaultSize());
		}

		Display display = getParent().getDisplay();

		if (isAlignCenter()) {
			Rectangle bounds = getParent().getMonitor().getBounds();
			Rectangle rect = shell.getBounds();
			int x = bounds.x + (bounds.width - rect.width) / 2;
			int y = bounds.y + (bounds.height - rect.height) / 2;
			shell.setLocation(x, y);
		}
		shell.open();
		while (!shell.isDisposed()) {
			if (!display.readAndDispatch()) {
				display.sleep();
			}
		}
		dispose();
		return isOk;
	}

	public void setCancelButtonStatus(boolean status) {
		if (cancelBtn != null) {
			cancelBtn.setEnabled(status);
		}
	}

	protected void setMinButtonLayoutData(Button button) {
		GridData data = new GridData(GridData.HORIZONTAL_ALIGN_FILL);
		Point minSize = button.computeSize(SWT.DEFAULT, SWT.DEFAULT, true);
		data.widthHint = minSize.x;
		button.setLayoutData(data);
	}

	public void setOkButtonStatus(boolean status) {
		if (okBtn != null) {
			okBtn.setEnabled(status);
		}
	}

	public boolean getOkButtonStatus() {
		if (okBtn != null) {
			return okBtn.getEnabled();
		}
		return true;
	}

	public String getPlatformInstallPath() {
		return checkPathByOs(Platform.getInstallLocation().getURL().getPath().toString()) + "/";
	}

	public String checkPathByOs(String path) {
		path = path.replaceAll("//", "/");

		if (System.getProperty("os.name").startsWith("Window")) {
			path = path.replaceAll("\\\\", "/");
			if (path.startsWith("/")) {
				path = path.substring(1, path.length());
			}

			if (path.startsWith("\\")) {
				path = path.substring(2, path.length());
			}

			if (path.endsWith("/")) {
				path = path.substring(0, path.length() - 1);
			}

			if (path.endsWith("\\")) {
				path = path.substring(0, path.length() - 2);
			}

			return path;
		}
		return path;
	}



}
