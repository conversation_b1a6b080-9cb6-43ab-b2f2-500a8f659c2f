package com.chipon32.chiponide.log.preference;

import org.eclipse.core.runtime.preferences.AbstractPreferenceInitializer;
import org.eclipse.jface.preference.IPreferenceStore;

import com.chipon32.chiponide.log.Activator;
import com.chipon32.chiponide.log.constant.FilterConstants;

public class FilterHistoryPreferenceInitializer extends AbstractPreferenceInitializer {

	public FilterHistoryPreferenceInitializer() {
	}

	@Override
	public void initializeDefaultPreferences() {
		IPreferenceStore store = Activator.getDefault().getPreferenceStore();
		store.setDefault(FilterConstants.PREFERENCES_FILTER_DOWNLOAD_BOOLEAN, false);
		store.setDefault(FilterConstants.PREFERENCES_FILTER_DEBUG_REGISTER_BOOLEAN, true);
		store.setDefault(FilterConstants.PREFERENCES_FILTER_DEBUG_WATCH_BOOLEAN, true);
	}

}
