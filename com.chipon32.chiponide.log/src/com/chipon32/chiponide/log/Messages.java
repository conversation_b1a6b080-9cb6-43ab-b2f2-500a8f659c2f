package com.chipon32.chiponide.log;

import org.eclipse.osgi.util.NLS;

public class Messages extends NLS {

  /**
   * Name of the message bundle.
   */
  private static final String BUNDLE_NAME = "com.chipon32.chiponide.log.messages";

  public static String CLEAR_HISTORY;
  public static String EXPORT_INPUT;
  public static String EXPORT_OUTPUT;
  public static String EXPORT_ALL;
  public static String EXPORT_HISTORY;
  public static String EXPORT_TYPE;
  public static String FILTER;
  public static String FILTER_APPLY;
  public static String FILTER_CONFIG_DOWNLOAD;
  public static String FILTER_CONFIG_DEBUG_REGISTER;
  public static String FILTER_CONFIG_DEBUG_WATCH;
  public static String OK;
  public static String CANCEL;

  // Initialize the constants.
  static {
    NLS.initializeMessages(BUNDLE_NAME, Messages.class);
  }
}
