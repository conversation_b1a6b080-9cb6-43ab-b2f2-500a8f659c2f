package com.chipon32.chiponide.ui.util;

import org.eclipse.jface.viewers.TableViewer;
import org.eclipse.jface.viewers.TreeViewer;
import org.eclipse.swt.widgets.Item;

public abstract class DoubleClickCellModifier extends AbstractMouseCellModifier {
	
//	private boolean editable;
//	private Object element;
//	private String property;
//	
//	protected TableViewer tableViewer;
//	protected TreeViewer treeViewer;
	
	public DoubleClickCellModifier(final TableViewer tableViewer) {
		super(tableViewer);
	}
	
	public DoubleClickCellModifier(final TreeViewer treeViewer) {
		super(treeViewer);
	}
	
	@Override
	public Object getValue(Object element, String property) {
		int columnIndex = getColumnIndex(property);
		return getColumnValue(element, columnIndex);
	}
	
	@Override
	public boolean canModify(Object element, String property) {
		this.element = element;
		this.property = property;
		if (editable) {
			return true;
		} else {
			return false;
		}
	}
	
	@Override
	public void modify(Object element, String property, Object value) {
		if (element instanceof Item) {
			element = ((Item) element).getData();
		}
		int columnIndex = getColumnIndex(property);
		doModify(element, columnIndex, value);
	}
	
	private int getColumnIndex(String property) {
		int columnIndex;
		if (tableViewer != null) {
			columnIndex = UIUtil.getPropertyColumnIndex(tableViewer, property);
		} else  { // treeViewer != null
			columnIndex = UIUtil.getPropertyColumnIndex(treeViewer, property);
		}
		return columnIndex;
	}
	
	@Override
	protected void doTableDoubleClick()
	{
		if (element == null) {
			return;
		}
		int column = UIUtil.getPropertyColumnIndex(tableViewer, property);
		if (allowModify(element, column)) {
			editable = true;
			try {
				tableViewer.editElement(element, column);
			} finally {
				editable = false;
			}
		}
	}

	@Override
	protected void doTreeDoubleClick()
	{
		if (element == null) {
			return;
		}
		int column =UIUtil.getPropertyColumnIndex(treeViewer, property);
		if (allowModify(element, column)) {
			editable = true;
			try {
				treeViewer.editElement(element, column);
			} finally {
				editable = false;
			}
		}
	}
	
	@Override
	public abstract boolean allowModify(Object element, int columnIndex);
	
	@Override
	public abstract void doModify(Object element, int columnIndex, Object value);
	
	@Override
	public abstract Object getColumnValue(Object element, int columnIndex);
}
