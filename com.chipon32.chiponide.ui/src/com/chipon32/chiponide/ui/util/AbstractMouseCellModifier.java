package com.chipon32.chiponide.ui.util;

import org.eclipse.jface.viewers.ICellModifier;
import org.eclipse.jface.viewers.TableViewer;
import org.eclipse.jface.viewers.TreeViewer;
import org.eclipse.swt.events.MouseAdapter;
import org.eclipse.swt.events.MouseEvent;
import org.eclipse.swt.widgets.Item;

public abstract class AbstractMouseCellModifier implements ICellModifier
{

	protected boolean editable;
	protected Object element;
	protected String property;
	
	protected TableViewer tableViewer;
	protected TreeViewer treeViewer;
	
	public AbstractMouseCellModifier(final TableViewer tableViewer) {
		this.tableViewer = tableViewer;
		
		tableViewer.getTable().addMouseListener(new MouseAdapter() {
			@Override
			public void mouseDoubleClick(MouseEvent e) {
				doTableDoubleClick();
			}
		});
		
		
	}
	
	public AbstractMouseCellModifier(final TreeViewer treeViewer) {
		this.treeViewer = treeViewer;
		
		treeViewer.getTree().addMouseListener(new MouseAdapter() {
			@Override
			public void mouseDoubleClick(MouseEvent e) 
			{
				doTreeDoubleClick();
			}
		});
		
	}
	
	@Override
	public Object getValue(Object element, String property) {
		int columnIndex = getColumnIndex(property);
		return getColumnValue(element, columnIndex);
	}
	
	@Override
	public boolean canModify(Object element, String property) {
		this.element = element;
		this.property = property;
		if (editable) {
			return true;
		} else {
			return false;
		}
	}
	
	@Override
	public void modify(Object element, String property, Object value) {
		if (element instanceof Item) {
			element = ((Item) element).getData();
		}
		int columnIndex = getColumnIndex(property);
		doModify(element, columnIndex, value);
	}
	
	private int getColumnIndex(String property) {
		int columnIndex;
		if (tableViewer != null) {
			columnIndex = UIUtil.getPropertyColumnIndex(tableViewer, property);
		} else  { // treeViewer != null
			columnIndex = UIUtil.getPropertyColumnIndex(treeViewer, property);
		}
		return columnIndex;
	}
	
	public abstract boolean allowModify(Object element, int columnIndex);
	
	public abstract void doModify(Object element, int columnIndex, Object value);
	
	public abstract Object getColumnValue(Object element, int columnIndex);

	protected abstract void doTableDoubleClick();

	protected abstract void doTreeDoubleClick();

}
