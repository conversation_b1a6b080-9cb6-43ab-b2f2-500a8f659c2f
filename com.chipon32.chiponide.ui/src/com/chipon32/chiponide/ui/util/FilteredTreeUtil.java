package com.chipon32.chiponide.ui.util;

import java.util.List;

import org.eclipse.jface.viewers.IBaseLabelProvider;
import org.eclipse.jface.viewers.ILabelProvider;
import org.eclipse.jface.viewers.ITableLabelProvider;
import org.eclipse.jface.viewers.TableViewer;
import org.eclipse.jface.viewers.TreeViewer;
import org.eclipse.jface.viewers.Viewer;

import com.chipon32.util.ui.StringMatcher;

class FilteredTreeUtil {
	
	public static boolean isMatch(Viewer viewer, Object element, StringMatcher matcher) {
		if (matcher == null) {
			return true;
		}
		
		IBaseLabelProvider lp = null;
		int length = 0;
		if (viewer instanceof TableViewer) {
			TableViewer tv = (TableViewer) viewer;
			lp = tv.getLabelProvider();
			length = tv.getTable().getColumnCount();
		} else if (viewer instanceof TreeViewer) {
			TreeViewer tv = (TreeViewer) viewer;
			lp = tv.getLabelProvider();
			length = tv.getTree().getColumnCount();
		} else {
			throw new RuntimeException();
		}
		//#####################################################
		if (lp instanceof ITableLabelProvider) {
			for (int i = 0; i < length; ++i) {
				String t = ((ITableLabelProvider) lp).getColumnText(element, i);
				if (matcher.match(t)) {
					return true;
				}
				
				if(element instanceof ChipNameModel){
				    ChipNameModel model=(ChipNameModel)element;
				    if(model!=null && model.isHasChildren()){
				        List<ChipNameModel> list=model.getChildList();
				        for(int j=0;j<list.size();j++){
				            String text=list.get(j).getName();
				            if(matcher.match(text)){
				                return true;
				            }
				        }
				        
				    }
				}
			}
		} else if (lp instanceof ILabelProvider) {
			String t = ((ILabelProvider) lp).getText(element);
			return matcher.match(t);
		}
		
		return false;
	}
	
}
