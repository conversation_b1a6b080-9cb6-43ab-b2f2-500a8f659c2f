package com.chipon32.chiponide.ui.util;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.List;
import java.util.StringTokenizer;

import org.eclipse.core.runtime.IProgressMonitor;
import org.eclipse.jface.dialogs.MessageDialog;
import org.eclipse.jface.dialogs.ProgressMonitorDialog;
import org.eclipse.jface.operation.IRunnableWithProgress;

import com.chipon32.chiponide.core.paths.ChipOnPath;
import com.chipon32.chiponide.core.paths.win32.SystemPathsWin32;
import com.chipon32.chiponide.ui.UiActivator;


//not design for kf32,old code
public class ChiponUpdateFirmware {
	private static ChiponUpdateFirmware instant;
	
	public static ChiponUpdateFirmware getInstant(){
		if(instant==null)
			instant=new ChiponUpdateFirmware();
		return instant;
	}

	public void updateFirmware(String selectFile){
		final String file = selectFile;
		
		ProgressMonitorDialog dialog = new ProgressMonitorDialog(null);
		dialog.setCancelable(false);
		try {
			dialog.run(true, false, new IRunnableWithProgress() {
				@Override
				public void run(IProgressMonitor monitor) throws InvocationTargetException,InterruptedException {
					List<String> list;
					List<String> msgList = null;
					if(file != null){
						try {
							int totalWork ;

							Process process = Runtime.getRuntime().exec(SystemPathsWin32.getSystemPath(ChipOnPath.CHIPON_COMMON).toOSString()+
									File.separator+"UpLoader.exe"+" --update"+" \""+file+"\""); //$NON-NLS-1$ //$NON-NLS-2$ //$NON-NLS-3$ //$NON-NLS-4$
							InputStream is = process.getInputStream();
							BufferedReader br = new BufferedReader(new InputStreamReader(is)); 
							String line;
							while((line = br.readLine()) != null){
								if(line.startsWith("i")){ //$NON-NLS-1$
									msgList = parseMessage(line);
									if(msgList.size() > 1){
										monitor.setTaskName(msgList.get(1));
									}
								}else if(line.startsWith("p")){ //$NON-NLS-1$
									list = parseMessage(line);
									if(list.size() > 1){
										String taskName;
										totalWork = Integer.parseInt(list.get(1));
										if(msgList != null && msgList.size()>1){
											taskName = msgList.get(1);
										}else{
											taskName = Messages.ChiponUpdateFirmware_6;
										}
										monitor.beginTask(taskName, totalWork);
									}
								}else if(line.startsWith("s")){ //$NON-NLS-1$
									monitor.worked(1);
								}else if(line.startsWith("e")){ //$NON-NLS-1$
									list = parseMessage(line);
									if(list != null && list.size() > 1){
										final String errmsg = list.get(1);
										UiActivator.getActiveDisplay().asyncExec(new Runnable() {
											
											@Override
											public void run() {
												MessageDialog.openError(UiActivator.getActiveDisplay().getActiveShell(), Messages.ChiponUpdateFirmware_9, errmsg);
											}
										});
										break;
									}
								
								}else if(line.startsWith("n")){ //$NON-NLS-1$
									list = parseMessage(line);
									if(list != null && list.size()>1){
										final String errmsg = list.get(1);
										UiActivator.getActiveDisplay().asyncExec(new Runnable() {
											
											@Override
											public void run() {
												MessageDialog.openError(UiActivator.getActiveDisplay().getActiveShell(), Messages.ChiponUpdateFirmware_11, errmsg);
											}
										});
									break;
									}
								}else if(line.startsWith("o")){ //$NON-NLS-1$
									UiActivator.getActiveDisplay().asyncExec(new Runnable() {
										
										@Override
										public void run() {
											MessageDialog.openConfirm(UiActivator.getActiveDisplay().getActiveShell(), Messages.ChiponUpdateFirmware_13, Messages.ChiponUpdateFirmware_14);
										}
									});
									break;
								}
							}
								
							br.close();
							is.close();
							process.destroy();
						} catch (IOException e) {
							e.printStackTrace();
						}
					}
					
					
				}
			});
		} catch (InvocationTargetException e) {
			e.printStackTrace();
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
		dialog.close();
	}
	private List<String> parseMessage(String msg){
		List<String> list = new ArrayList<String>();
		StringTokenizer tokenizer = new StringTokenizer(msg, "|"); //$NON-NLS-1$
		while(tokenizer.hasMoreTokens()){
			list.add(tokenizer.nextToken());
		}
		return list;
	}
}
