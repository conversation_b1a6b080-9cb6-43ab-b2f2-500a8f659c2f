package com.chipon32.chiponide.ui.util;

import java.util.List;

/**
 *  芯片信息，类型，类型下型号列表
 *  <AUTHOR>
public class ChipNameModel {
	// 名字
    private String name;
    // 归属类
    private String type;
    private List<ChipNameModel> childList;
    private String parent;
    //#################################################选择编译器
	private int 		defaultselid;		// 默认配置项
	private String      selelementtext;		// 选择的内容
	//#################################################
    private String[] tools;
    public String[] getElement() {
        return tools;
    }
    public void setElement(String[] tool,String defaultTool) {
        this.tools = tool;
        if(tool!=null && tool.length>0)
        {
        	defaultselid=tool.length-1;
        	selelementtext=tool[defaultselid];
        	for(int i=0;i<tool.length;i++)
        	{
        		if(defaultTool.equalsIgnoreCase(tool[i]))
        		{
                	defaultselid=i;
                	selelementtext=tool[defaultselid];
        			break;
        		}        		
        	}
        }
        else
        {
        	defaultselid=0;
        	selelementtext=" ";
        }
    }

	public int getDefaultselid() {
		return defaultselid;
	}
	public void setDefaultselid(int defaultselid) {
		this.defaultselid = defaultselid;
	}
	//########## show get toolname
	public String getSelelementtext() {
		return selelementtext;
	}
	public void setSelelementtext(String selelementtext) {
		this.selelementtext = selelementtext;
	}
	//#################################################
    public String getName() {
        return name;
    }
    public void setName(String name) {
        this.name = name;
    }
    public String getType() {
        return type;
    }
    public void setType(String type) {
        this.type = type;
    }
    //#################################################
    public List<ChipNameModel> getChildList() {
        return childList;
    }
    public void setChildList(List<ChipNameModel> childList) {
        this.childList = childList;
    }
    //#################################################
    public String getParent() {
        return parent;
    }
    public void setParent(String parent) {
        this.parent = parent;
    }
    //#################################################
    public boolean isHasChildren() {
        if(childList!=null) return childList.size()>0;
        return false;
    }
    //#################################################
	String  ramSizeString;
	String  flashSizeString;
    
    public String getRamString() {
        return ramSizeString;
    }
    public void setRamString(String str) {
        this.ramSizeString = str;
    }
    
    public String getFlashString() {
        return flashSizeString;
    }
    public void setFlashString(String str) {
        this.flashSizeString = str;
    }
}
