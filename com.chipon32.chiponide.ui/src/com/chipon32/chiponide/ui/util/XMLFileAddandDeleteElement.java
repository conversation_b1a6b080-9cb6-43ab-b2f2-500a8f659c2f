package com.chipon32.chiponide.ui.util;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.io.UnsupportedEncodingException;
import java.util.List;

import com.chipon32.chiponide.ui.editors.ASMInstructionSet;

/**
 * 此类与首选项中自定义关键字的页面配合使用，用于控制关键字的添加和删除操作
 * <AUTHOR>
 */

public class XMLFileAddandDeleteElement {
	
	
	/**
	 * 向XML文件中添加自定义关键字
	 * @param file
	 * @param str
	 * @param sbu
	 * @param br
	 * @return
	 * @throws FileNotFoundException 
	 */
	public StringBuffer addElement(File file,List<String> strList,StringBuffer sbu) throws FileNotFoundException{
		
		if(file == null || file.isFile() == false){
			return null;
		}
		
		BufferedReader br = null;
		try {
			br = new BufferedReader(new InputStreamReader(new FileInputStream(file),"UTF-8"));
		} catch (UnsupportedEncodingException e1) {
			// TODO Auto-generated catch block
			e1.printStackTrace();
		}
		try {
			
			String line = br.readLine();
			sbu = new StringBuffer();
			while(line != null){
				//sbu.append(line.trim()).append(System.getProperty("line.separator"));
				if(line.contains("<!-- Custom KeyWords -->")){
					sbu.append(line.trim()).append(System.getProperty("line.separator"));
					
					for(int i = 0;i<strList.size();i++){
						sbu.append("<custom word=\""+strList.get(i)+"\" description=\"\"/>").append(System.getProperty("line.separator"));
						ASMInstructionSet.getCustomMap().put(strList.get(i), null);
					}
					
				}else if(line.contains("<custom word=")){
					
				}else{
					sbu.append(line.trim()).append(System.getProperty("line.separator"));
				}
				line = br.readLine();
			}
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}finally{
			if(br!=null){
				try {
					br.close();
				} catch (IOException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
			}
		}
		return sbu;
		
	}
	
	/*
	public StringBuffer DelElement(File file,List<String> str,StringBuffer sbu) throws FileNotFoundException{
		if(file == null || file.isFile() ==false){
			return null;
		}
		BufferedReader br = new BufferedReader(new FileReader(file));
		Collections.sort(str);
		try {
			
			String line = br.readLine();
			while(line != null){
				sbu.append(line.trim()).append(System.getProperty("line.separator"));
				line = br.readLine();
			}
			for(Object obj : str){
				ASMInstructionSet.getCustomMap().remove(obj);
				String deleteLine = "<custom word=\""+obj+"\" description=\"\"/>";
				int j = sbu.lastIndexOf(deleteLine);
				sbu.delete(j, j+deleteLine.length()+2);
			}
			
			System.out.println(sbu.toString());
		} catch (IOException e) {
			
			e.printStackTrace();
		}finally{
			if(br!=null){
				try {
					br.close();
				} catch (IOException e) {
				
					e.printStackTrace();
				}
			}
		}
		return sbu;
		
	}*/
	
	/**
	 * 将字符串重写回文件
	 * @param file
	 * @param sbu
	 * @param bw
	 */
	
	public  void write(File file,StringBuffer sbu,BufferedWriter bw){
		
		if(file == null || file.isFile() == false){
			return;
		}
		
		try {
			bw = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(file),"UTF-8"));
			file.delete();
			bw.flush();
			bw.write(sbu.toString());
			bw.flush();
			
		} catch (IOException e) {
			e.printStackTrace();
		}finally{
			if(bw != null){
				try {
					bw.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
		
	}
	
}
