package com.chipon32.chiponide.ui.util;

import java.util.List;

import org.eclipse.jface.viewers.ILabelProviderListener;
import org.eclipse.jface.viewers.ITableLabelProvider;
import org.eclipse.swt.graphics.Image;
import org.eclipse.wb.swt.ResourceManager;

import com.chipon32.hex.ui.HexUiActivator;

/**
 *
 *<AUTHOR>
public class ChiponNameLabelProvider implements ITableLabelProvider{

    @Override
    public void addListener(ILabelProviderListener listener) {
        // TODO Auto-generated method stub
        
    }

    @Override
    public void dispose() {
        // TODO Auto-generated method stub
        
    }

    @Override
    public boolean isLabelProperty(Object element, String property) {
        // TODO Auto-generated method stub
        return false;
    }

    @Override
    public void removeListener(ILabelProviderListener listener) {
        // TODO Auto-generated method stub
        
    }

	private Image[]  imgs=new Image[]
			{
			 ResourceManager.getPluginImage(HexUiActivator.PLUGIN_ID, "/icons/mouse.jpg"),
			 ResourceManager.getPluginImage(HexUiActivator.PLUGIN_ID, "/icons/mouse1.jpg"),
			 ResourceManager.getPluginImage(HexUiActivator.PLUGIN_ID, "/icons/mouse2.jpg")			
			};
    @Override
    public Image getColumnImage(Object element, int columnIndex) {
        // TODO Auto-generated method stub
        if(element instanceof ChipNameModel){
            ChipNameModel commData=(ChipNameModel)element;
            switch(columnIndex)
            {           
            case 1:
            	if(commData.getElement()==null)
            		return null;
            	return imgs[0];           
            }            
        }
        return null;
    }

    @Override
    public String getColumnText(Object element, int columnIndex) {
    	String namestr;
    	char c;
    	List<ChipNameModel> childLists;
        // TODO Auto-generated method stub
        if(element instanceof ChipNameModel){
            ChipNameModel commData = (ChipNameModel)element;
            switch(columnIndex)
            {
            case 0:
            	return commData.getName();
            case 1:
            	return commData.getSelelementtext();
            case 2:
            	return commData.getFlashString();
            case 3:
            	return commData.getRamString();
            case 4:  // 名字规则时的解析，否则从软id反向解析，芯片型号命名依赖规则
//            	namestr=commData.getName();
//            	if(namestr.contains("-"))
//            		namestr=namestr.substring(0,namestr.indexOf("-"));
//            	
//            	childLists=commData.getChildList();
//            	if(childLists!=null)
//            		return "";
//            	if(namestr.length()<2)return "";
//            	namestr = namestr.substring(namestr.length()-2,namestr.length()).toUpperCase();
//            	c = namestr.charAt(0);
            	c = getPackagePins(commData, 1);
            	if(c==' ') {
            		return "";
            	}
            	switch(c)
            	{
	            	case 'S': return "SOP/SOT";
	            	case 'M': return "MSOP";
	            	case 'N': return "QFN";
	            	case 'Q': return "LQFP";
	            	case 'D': return "DIP";
	            	case 'K': return "SKDIP";
	            	case 'O': return "SSOP";
	            	case 'T': return "TSSOP";
	            	case 'U': return "UQFN";
	            	case 'B': return "BGA";
	            	default: return "----";
            	}
            case 5:  // 名字规则时的解析，否则从软id反向解析，依赖芯片命名规则
//            	namestr=commData.getName();
//            	if(namestr.contains("-"))
//            		namestr=namestr.substring(0,namestr.indexOf("-"));
//            	
//            	childLists=commData.getChildList();
//            	if(childLists!=null)
//            		return "";
//            	if(namestr.length()<2)return "";
//            	namestr=namestr.substring(namestr.length()-2,namestr.length()).toUpperCase();
//            	c =namestr.charAt(1);
            	
            	c = getPackagePins(commData, 2);
            	if(c==' ') {
            		return "";
            	}
            	switch(c)
            	{
	            	case 'A': return "6";
	            	case 'B': return "8";
	            	case 'C': return "10";
	            	case 'D': return "14";
	            	case 'E': return "16";
	            	case 'F': return "18";
	            	case 'G': return "20";
	            	case 'M': return "24";
	            	case 'N': return "28";
	            	case 'P': return "32";
	            	case 'R': return "44";
	            	case 'S': return "48";
	            	case 'K': return "52";
	            	case 'T': return "64";
	            	case 'U': return "80";
	            	case 'V': return "100";
	            	case 'W': return "144";
	            	case 'X': return "176";
	            	case 'Y': return "56";
	            	default: return "----";
            	}	
            default:
            	return "NULL";
            }            
        }
        return null;
    }

	private char getPackagePins(ChipNameModel commData, int index) {
		List<ChipNameModel>  childLists = commData.getChildList();
    	if(childLists!=null) {
    		return ' ';
    	}
    	String namestr = commData.getName();
    	if(namestr.contains("-")) {
    		namestr=namestr.substring(0,namestr.indexOf("-"));
    	}
    	if(namestr.length()<3) {
    		return ' ';
    	}
    	String bufLetter = "";
    	for(int i=namestr.length()-1 ; i>=0; i--) {
    		char c1 = namestr.charAt(i);
    		if(!Character.isDigit(c1)) {
    			bufLetter = c1 + bufLetter;
    		}else {
    			break;
    		}
    	}
    	if(bufLetter.length()<3) {
    		return ' ';
    	}
    	
    	return  bufLetter.charAt(index);
	}

}
