package com.chipon32.chiponide.ui.util;

import org.eclipse.jface.viewers.IBaseLabelProvider;
import org.eclipse.jface.viewers.ILabelProvider;
import org.eclipse.jface.viewers.ITableLabelProvider;
import org.eclipse.jface.viewers.TableViewer;
import org.eclipse.jface.viewers.TreeViewer;
import org.eclipse.jface.viewers.Viewer;

class FilteredUtil {
	
	public static boolean isMatch(Viewer viewer, Object element, StringMatcher matcher) {
		if (matcher == null) {
			return true;
		}
		
		IBaseLabelProvider lp = null;
		int length = 0;
		if (viewer instanceof TableViewer) {
			TableViewer tv = (TableViewer) viewer;
			lp = tv.getLabelProvider();
			length = tv.getTable().getColumnCount();
		} else if (viewer instanceof TreeViewer) {
			TreeViewer tv = (TreeViewer) viewer;
			lp = tv.getLabelProvider();
			length = tv.getTree().getColumnCount();
		} else {
			throw new RuntimeException();
		}
		
		if (lp instanceof ITableLabelProvider) {
			for (int i = 0; i < length; ++i) {
				String t = ((ITableLabelProvider) lp).getColumnText(element, i);
				if (matcher.match(t)) {
					return true;
				}
			}
		} else if (lp instanceof ILabelProvider) {
			String t = ((ILabelProvider) lp).getText(element);
			return matcher.match(t);
		}
		
		return false;
	}
	
}
