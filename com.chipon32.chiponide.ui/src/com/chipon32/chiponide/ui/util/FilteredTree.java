package com.chipon32.chiponide.ui.util;

import org.eclipse.core.runtime.IProgressMonitor;
import org.eclipse.core.runtime.IStatus;
import org.eclipse.core.runtime.Status;
import org.eclipse.core.runtime.jobs.Job;
import org.eclipse.jface.viewers.TreeViewer;
import org.eclipse.jface.viewers.Viewer;
import org.eclipse.jface.viewers.ViewerFilter;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.ModifyEvent;
import org.eclipse.swt.events.ModifyListener;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.Text;
import org.eclipse.swt.widgets.Tree;
import org.eclipse.ui.progress.WorkbenchJob;

import com.chipon32.util.ui.StringMatcher;

public class FilteredTree extends Composite {

	private TreeViewer tableViewer;
	private Tree table;
	private Text text;
	
	public Text getText() {
		return text;
	}

	public void setText(Text text) {
		this.text = text;
	}

	private Job refreshJob;
	
	/**
	 * Create the composite
	 * @param parent
	 * @param style
	 */
	public FilteredTree(Composite parent, int style) {
		super(parent, SWT.NONE);
		
		final GridLayout gridLayout = new GridLayout();
		gridLayout.marginHeight = 3;
		setLayout(gridLayout);
		Label label = new Label(this,SWT.NONE);
		label.setText(Messages.FilteredTree_0);
		text = new Text(this, SWT.BORDER);
		text.setLayoutData(new GridData(SWT.FILL, SWT.BEGINNING, true, false));

		tableViewer = new TreeViewer(this, style);
		table = tableViewer.getTree();
		table.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, true));
		//
		initialize();
	}
	
	public TreeViewer getTableViewer() {
		return tableViewer;
	}
	
	public Tree geTable() {
		return table;
	}
	
	private void initialize() {
		tableViewer.addFilter(new MyViewerFilter());
		refreshJob = new WorkbenchJob("Refresh") { //$NON-NLS-1$
			@Override
			public IStatus runInUIThread(IProgressMonitor monitor) {
				if(table.isDisposed()) {
					return Status.CANCEL_STATUS;
				}
				tableViewer.expandAll();
				tableViewer.refresh();
				 return Status.OK_STATUS;
			}
		};
		refreshJob.setSystem(true);
	}
	
	private class MyViewerFilter extends ViewerFilter {
		private StringMatcher matcher;
		
		public MyViewerFilter() {
			text.addModifyListener(new ModifyListener() {
				@Override
				public void modifyText(ModifyEvent e) {
					String str = text.getText();
					if (str.length() == 0) {
						matcher = null;
					} else {
						matcher = new StringMatcher("*" + str + "*", true, false); //$NON-NLS-1$ //$NON-NLS-2$
					}
					refreshJob.cancel();
			    	refreshJob.schedule(400);
				}
			});
		}
		
		@Override
		public boolean select(Viewer viewer, Object parentElement, Object element) {
		   return FilteredTreeUtil.isMatch(viewer, element, matcher);
		}
	}

}
