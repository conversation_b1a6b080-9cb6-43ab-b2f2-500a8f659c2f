package com.chipon32.chiponide.ui.util;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.chipon32.hex.core.util.IConfigurationProvider;
import com.chipon32.util.provider.ConfigurationFactory;

public class ChipManager {
	
	public  String Default_compiler_Tool="";
	
	public  String getDefault_compiler_Tool() {
		return Default_compiler_Tool;
	}

	public  void setDefault_compiler_Tool(String default_compiler_Tool) {
		Default_compiler_Tool = default_compiler_Tool.trim();
	}
//##################################################################
	private static ChipManager singleton = null;
	

	private ChipManager()
	{
		
	}

	// 自身的初始化 
	public static ChipManager getInstance()
	{
		if(singleton == null)
			singleton = new ChipManager();
		
		return singleton;
	}
	
	// 获取芯片类型和类型下内容
	public List<?> getchipTypeAndNameListModel()
	{  	 
		//################# 先获取所有型号的类别列表
		List<String> typelist= ConfigurationFactory.getchipTypeList();
		//################# 再获取类型下的型号列表
		Map<String, List<String>> namemaps= ConfigurationFactory.getTypeAndNameMap();
		//################# 再获取每个型号对应的工具
		Map<String, String> nametool=ConfigurationFactory.getNameAndToolMap();
		
		//建立树结果的空集合，集合对象设计为chipNameModel		
		 List<ChipNameModel> resultList =new ArrayList<>();
		 
		 // 基于每个类型建立树的节点数据
		 for(int i=0;i<typelist.size();i++)
		 {
			 // 类型节点的元素，无父节点
			 ChipNameModel model=new ChipNameModel();			 
			 model.setName(typelist.get(i));  // 名字和类型，此时都设为一样，就是根节点树对象
			 model.setType(typelist.get(i));
			 model.setElement(null,Default_compiler_Tool);
			 model.setRamString(null);
			 model.setFlashString(null);
 			 // 子节点对象添加
			 List<String> listnams=(namemaps.get(typelist.get(i)));
			 List<ChipNameModel> childList =new ArrayList<>();
			 
			 for(int j=0;j<listnams.size();j++)
			 {				
				 ChipNameModel childmodel=new ChipNameModel();
                 childmodel.setName(listnams.get(j));
                 childmodel.setType(listnams.get(j));  // 名字和类型，此时都设为一样，就是根节点树对象,即此时类型无所谓了
                 childmodel.setElement(nametool.get(listnams.get(j)).split(","),Default_compiler_Tool);
                 childmodel.setParent(typelist.get(i));
                 
                 IConfigurationProvider co =ConfigurationFactory.getProvider(listnams.get(j)); 
                 String SizeGet="";
				 int buff=0;
				 try{
					 SizeGet=co.getRams().get(0).getSize();
				 buff=Integer.parseInt(SizeGet,16);
				 if(buff>1024)
				 {
					 childmodel.setRamString(Integer.toString(buff/1024)+"K");
				 }
				 else {
					 childmodel.setRamString(Integer.toString(buff)+"B");
				 }	
				 }catch (Exception e) {
					// TODO: handle exception
					//System.out.print("ramsize:"+SizeGet+"\r\n");
					childmodel.setRamString("--K");
				 }
				 try{
					SizeGet=co.getFlashs().get(0).getSize();
					buff=Integer.parseInt(co.getFlashs().get(0).getSize(),16);
					if(buff>1024)
					{
						childmodel.setFlashString(Integer.toString(buff/1024)+"K");
					}
					else {
						childmodel.setFlashString(Integer.toString(buff)+"B");
					}	
				 }catch (Exception e) {
					// TODO: handle exception
					//System.out.print("flashsize:"+SizeGet+"\r\n");
					childmodel.setFlashString("--K");
				 }
    			 model.setRamString(null);
    			 model.setFlashString(null);
                 // 一个型号节点加入
                 childList.add(childmodel);
			 }
			// 根节点添加具体型号子节点
			 model.setChildList(childList);
			 // 遍历的一个根节点的对象加入
			 resultList.add(model);
		 }
		 // 结果返回
		 return resultList;
	}
	
}
