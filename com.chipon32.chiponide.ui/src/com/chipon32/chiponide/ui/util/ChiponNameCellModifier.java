package com.chipon32.chiponide.ui.util;


import org.eclipse.jface.viewers.CellEditor;
import org.eclipse.jface.viewers.ComboBoxCellEditor;
import org.eclipse.jface.viewers.ICellModifier;
import org.eclipse.jface.viewers.TreeViewer;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.MouseAdapter;
import org.eclipse.swt.events.MouseEvent;
import org.eclipse.swt.widgets.Item;

import com.chipon32.util.ui.UIUtil;


// table view  modif way
public class ChiponNameCellModifier implements ICellModifier {
	protected boolean editable;
	protected TreeViewer treeViewer;
	protected Object element;
	protected String property;
	// 

	public ChiponNameCellModifier(TreeViewer treeViewer) {
		this.treeViewer=(treeViewer);
		// TODO Auto-generated constructor stub
		treeViewer.getTree().addMouseListener(new MouseAdapter() {
			@Override
			public void mouseDoubleClick(MouseEvent e) 
			{
				doTreeClick();
			}
		});
	}
	
	@Override
	public boolean canModify(Object element, String property) {
		this.element = element;
		this.property = property;
		if (editable) {
			return true;
		} else {
			return false;
		}
	}
	
	@Override
	public Object getValue(Object element, String property) {
		int columnIndex = getColumnIndex(property);
		return getColumnValue(element, columnIndex);
	}
	
	@Override
	public void modify(Object element, String property, Object value) {
		if (element instanceof Item) {
			element = ((Item) element).getData();
		}
		int columnIndex = getColumnIndex(property);
		doModify(element, columnIndex, value);
	}
	
	private int getColumnIndex(String property) {
		int columnIndex;		
		columnIndex = UIUtil.getPropertyColumnIndex(treeViewer, property);		
		return columnIndex;
	}
	/**
	 * 
	 */
	protected void doTreeClick()
	{
		if (element == null) {
			return;
		}
		int column =UIUtil.getPropertyColumnIndex(treeViewer, property);
		if(column==0) {//展开或收缩节点
			ChipNameModel chipType = (ChipNameModel) element;
			if(chipType.isHasChildren()) {
				if(treeViewer.getExpandedState(element))
					treeViewer.collapseToLevel(element, 1);
				else 
					treeViewer.expandToLevel(element, 1); 
			}
		}
		
		if (allowModify(element, column)) {
			editable = true;
			try {
				treeViewer.editElement(element, column);
			} finally {
				editable = false;
			}
		}
	}
	
	
	public boolean allowModify(Object element, int columnIndex) {
		if(element instanceof ChipNameModel)
		{
			ChipNameModel commData = (ChipNameModel)element;
			// if can modif
			if(commData!=null)
			{
				if(columnIndex==1 && !commData.getSelelementtext().trim().isEmpty()) {
					// reset editer, to flush list element
					CellEditor[] cellEditorsconfig = new CellEditor[3];
					cellEditorsconfig[0] = null;
					cellEditorsconfig[1] = new ComboBoxCellEditor(treeViewer.getTree(),commData.getElement(),SWT.READ_ONLY);
					cellEditorsconfig[2] = null;
					treeViewer.setCellEditors(cellEditorsconfig);
					
					return true;
				}
			}		
		}
		
		return false;
	}
	
	// do modif
	public void doModify(Object element, int columnIndex, Object value){
		
		if(element instanceof ChipNameModel)
		{
			ChipNameModel commData = (ChipNameModel)element;
			switch(columnIndex)
			{
				case 1:
					commData.setSelelementtext(commData.getElement()[(int) value]);
					commData.setDefaultselid((int) value);					
					break;
			}			
			// reflush
			treeViewer.refresh();
		}

	}
	
	//contex under line and  order 
	public Object getColumnValue(Object element, int columnIndex) {
		if(element instanceof ChipNameModel)
		{
			ChipNameModel commData = (ChipNameModel)element;
			// not reset editer,to later in here,do in if allow way 
			//================================
			String restr; 
			switch(columnIndex)
			{
			case 0:
				restr=commData.getName();
				return restr;
			case 1:
				return getSelIndex(commData,commData.getSelelementtext());
			default :
				return null;			
			
			}			
		}
		return null;
	}
	
	
	public int getSelIndex(ChipNameModel cm, String str)
	{
		for(int i=0;i<cm.getElement().length;i++)
		{
			if(cm.getElement()[i].equalsIgnoreCase(str))
			{
				return i;
			}
		}
		return -1;
	}

}
