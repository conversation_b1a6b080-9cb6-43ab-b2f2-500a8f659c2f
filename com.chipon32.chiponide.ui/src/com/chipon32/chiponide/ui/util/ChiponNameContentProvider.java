package com.chipon32.chiponide.ui.util;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.jface.viewers.ITreeContentProvider;
import org.eclipse.jface.viewers.Viewer;

/**
 *
 *<AUTHOR>
public class ChiponNameContentProvider implements ITreeContentProvider{

    @Override
    public void dispose() {
        // TODO Auto-generated method stub
        
    }

    @Override
    public void inputChanged(Viewer viewer, Object oldInput, Object newInput) {
        // TODO Auto-generated method stub
        
    }

    @SuppressWarnings("rawtypes")
	@Override
    public Object[] getElements(Object inputElement) {
        // TODO Auto-generated method stub
       
        if(inputElement instanceof ArrayList<?>){
            List list=(ArrayList)inputElement;
            return list.toArray();
        }
        return null;
    }

    @Override
    public Object[] getChildren(Object parentElement) {
        // TODO Auto-generated method stub
        if(parentElement instanceof ChipNameModel){
            ChipNameModel chipElement=(ChipNameModel)parentElement;
            return chipElement.getChildList().toArray();
        }
        
        return null;
    }

    @Override
    public Object getParent(Object element) {
        // TODO Auto-generated method stub
        return null;
    }

    @Override
    public boolean hasChildren(Object element) {
        // TODO Auto-generated method stub
        if(element instanceof ChipNameModel){
            ChipNameModel chipElement=(ChipNameModel)element;
            List<ChipNameModel> list= chipElement.getChildList();
            if(list==null || list.size()==0)return false;
            return (list.size()>0);
        }
        return false;
    }

}
