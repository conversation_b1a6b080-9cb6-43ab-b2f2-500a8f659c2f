package com.chipon32.chiponide.ui.util;

public class ByteConvertor 
{
	/**
	 * 
	 * @param hex 十六进制字符串，代表指令码，例如"C0".
	 * 		在Java中byte类型为有符号，因此大于127的十六进制值，调用Byte.parseByte()方法会产生转换错误。
	 * @return
	 */
	public static byte hexToByte(String hex)
	{
		int intValue = Integer.parseInt(hex, 16);
		byte byteValue=0;   
		int temp = intValue % 256;   
		if ( intValue < 0) {   
		  byteValue =  (byte)(temp < -128 ? 256 + temp : temp);   
		}   
		else {   
		  byteValue =  (byte)(temp > 127 ? temp - 256 : temp);   
		}
		
		return byteValue;
	}
	
	public static String toHex(byte b) 
	{
		 return ("" + "0123456789ABCDEF".charAt(0xf & b >> 4) + "0123456789ABCDEF".charAt(b & 0xf));
	}
	
	public static String toHex(byte[] bytes)
	{
		StringBuffer buffer = new StringBuffer();
		for(byte b : bytes)
		{
			buffer.append(toHex(b));
		}
		
		return buffer.toString();
	}
	
	/**
	 * 和校验 从第0位开始
	 * @param bytes
	 * @return 校验值 为改数组的最后一位
	 */
	public static byte format(byte[] bytes){
		int j;
		int k = 0;
		for(int i=0;i<(bytes.length-1);i++){
			j=bytes[i]&0xFF;
			k += j;
		}
		return (byte) (k%256);
	}
	
	/**
	 * 和校验   bytes的最后一位为反码，从第0位开始
	 * @param bytes
	 * @return 校验值 为改数组的最后一位
	 */
	public static byte formatFrom1(byte[] bytes){
		int j;
		int k = 0;
		for(int i=0;i<(bytes.length-2);i++){
			j=bytes[i]&0xFF;
			k += j;
		}
		return (byte) (k%256);
	}
	
	/**
	 * 和校验    bytes的最后一位为反码，从第2位开始
	 * @param bytes
	 * @return 校验值
	 */
	public static byte byteSum(byte[] bytes){
		int j;
		int k = 0;
		for(int i=1;i<(bytes.length-2);i++){
			j=bytes[i]&0xFF;
			k += j;
		}
		return (byte) (k%256);
	}
	
	/**
	 * 帧校验 
	 * @param buffer
	 * @param i
	 * @return
	 */
	public static byte frameSum(byte[] buffer){
		if(buffer.length<0){
			return 0;
		}
		
		int j;
		int k = 0;
		for(int i=0;i<(buffer.length-3);i++){
			j=buffer[i]&0xFF;
			k += j;
		}
		return (byte) (k%256);
		
		
	}

	
	public static String format(byte[] bytes, int offset, int numBytes) {

		StringBuilder buffer = new StringBuilder();

		for (int i = offset; i < numBytes; i++) {
			buffer.append(String.format("%02X ", bytes[i] & 0xFF)); //$NON-NLS-1$        
		}

		return buffer.toString(); 
	}
	
	public static String toFour(String input)
	{
		String tmp = input;
		if(input.length() == 2)
		{
			tmp = "00" + input;
		}
		else if(input.length() == 1)
		{
			tmp = "000" + input;
		}else if(input.length() == 3)
		{
			tmp = "0" + input;
		}
		
		return tmp.toUpperCase();
	}
}
