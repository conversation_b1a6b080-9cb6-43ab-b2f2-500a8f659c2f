package com.chipon32.chiponide.ui.action;

import java.io.File;

import org.eclipse.core.runtime.Platform;
import org.eclipse.jface.action.IAction;
import org.eclipse.jface.viewers.ISelection;
import org.eclipse.swt.program.Program;
import org.eclipse.ui.IWorkbenchWindow;
import org.eclipse.ui.IWorkbenchWindowActionDelegate;

/**
 * open help document file
 * <AUTHOR>
 *
 */
public class OpenCDocAction implements IWorkbenchWindowActionDelegate {

	@Override
	public void run(IAction action) {
		String pathwhere="doc\\";
		if(Platform.getOS().equals(Platform.OS_LINUX))
			pathwhere="doc/";
		
    	String preFixRootPath="";
    	if(Platform.getOS().equals(Platform.OS_LINUX))  
    		 preFixRootPath=System.getProperty("file.separator");    	// get like home/gjz/eclipse/eclipseL/,but  need /home/<USER>/eclipse/eclipseL/ 
    	
    	pathwhere = preFixRootPath+Platform.getInstallLocation().getURL().getPath().substring(1) + pathwhere; //+ separator
    	
    	String prefixCGuideFileName = "ChipON KF32 C Language_Users_Guide_cn";
    	if(Messages.TestLanguage.equalsIgnoreCase("Default_CN"))
		{
    		prefixCGuideFileName="ChipON KF32 C Language_Users_Guide_cn";
		}
		if(Messages.TestLanguage.equalsIgnoreCase("Default_EN"))
		{
			prefixCGuideFileName="ChipON KF32 C Language_Users_Guide_cn"; //暂时未提供英文版
		}
		
    	File CGuideFile = null;
    	File dirFile = new File(pathwhere);
    	if(dirFile.exists() && dirFile.isDirectory()) {
    		File[] docFiles = dirFile.listFiles();
    		for(File docFile:docFiles) {//遍历查找C语言指导手册文件，存在带版本号的使用说明文件情况
    			String docFileName = docFile.getName();
    			if(docFileName.contains(prefixCGuideFileName)) {
    				CGuideFile = docFile;
    				break;
    			}
    		}
    	}
    	
    	if(CGuideFile!=null) {
    		try {
				String paths = CGuideFile.getCanonicalPath();				
				Program.launch(paths);
    		} catch (Exception e) {
    			// TODO: handle exception
    		}
    	}else {//未找到C语言指导文件，则打开说明文档文件夹
    		File f2=new File(pathwhere); //$NON-NLS-1$
			if (f2.exists()) 
			{
				try {
					String paths=f2.getCanonicalPath();				
					Program.launch(paths);
	    		} catch (Exception e) {
	    			// TODO: handle exception
	    		}
			}
			else
			{
				//MessageDialog.openInformation(null, "info", "not add...");	
			}
    	}

		/*String name="ChipON KF32 C Language_Users_Guide_cn.pdf";
		if(Messages.TestLanguage.equalsIgnoreCase("Default_CN"))
		{
			name="ChipON KF32 C Language_Users_Guide_cn.pdf";
		}
		if(Messages.TestLanguage.equalsIgnoreCase("Default_EN"))
		{
			name="ChipON KF32 C Language_Users_Guide_cn.pdf";
		}
		try {
			File f=new File(pathwhere+name);
			if (f.exists()) 
			{
	//			String paths=f.getAbsolutePath();
				String paths=f.getCanonicalPath();				
				Program.launch(paths);
			}
			else {						
				//MessageDialog.openInformation(null, "info", "not add...");				
			}
		} catch (Exception e) {
			// TODO: handle exception
		}*/
	}

	@Override
	public void selectionChanged(IAction action, ISelection selection) {
		// TODO Auto-generated method stub

	}

	@Override
	public void dispose() {
		// TODO Auto-generated method stub

	}

	@Override
	public void init(IWorkbenchWindow window) {
		// TODO Auto-generated method stub

	}

}
