package com.chipon32.chiponide.ui.action;

import java.io.File;

import org.eclipse.core.runtime.Platform;
import org.eclipse.jface.action.IAction;
import org.eclipse.jface.viewers.ISelection;
import org.eclipse.swt.program.Program;
import org.eclipse.ui.IWorkbenchWindow;
import org.eclipse.ui.IWorkbenchWindowActionDelegate;

/**
 * open help document file
 * 
 * <AUTHOR>
 *
 */
public class OpenMoreDocAction implements IWorkbenchWindowActionDelegate {

	@Override
	public void run(IAction action) {

		String pathwhere = "doc\\";
		if (Platform.getOS().equals(Platform.OS_LINUX))
			pathwhere = "doc/";

		String preFixRootPath = "";
		if (Platform.getOS().equals(Platform.OS_LINUX))
			preFixRootPath = System.getProperty("file.separator"); // get like home/gjz/eclipse/eclipseL/,but need /home/<USER>/eclipse/eclipseL/

		pathwhere = preFixRootPath + Platform.getInstallLocation().getURL().getPath().substring(1) + pathwhere; // +  separator

		try {
			File f = new File(pathwhere);
			if (f.exists()) {
				// String paths=f.getAbsolutePath();
				String paths = f.getCanonicalPath();
				Program.launch(paths);
			} else {

				File f2 = new File(preFixRootPath + Platform.getInstallLocation().getURL().getPath().substring(1) + "doc"); //$NON-NLS-1$
				if (f2.exists()) {
					// String paths=f2.getAbsolutePath();
					String paths = f2.getCanonicalPath();
					Program.launch(paths);
				} else {
					// MessageDialog.openInformation(null, "info", "not add...");
				}
			}
		} catch (Exception e) {
			// TODO: handle exception
		}
	}

	@Override
	public void selectionChanged(IAction action, ISelection selection) {
		// TODO Auto-generated method stub

	}

	@Override
	public void dispose() {
		// TODO Auto-generated method stub

	}

	@Override
	public void init(IWorkbenchWindow window) {
		// TODO Auto-generated method stub

	}

}
