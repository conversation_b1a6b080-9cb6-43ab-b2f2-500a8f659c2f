package com.chipon32.chiponide.ui.action;

import java.io.File;

import org.eclipse.core.runtime.Platform;
import org.eclipse.jface.action.IAction;
import org.eclipse.jface.viewers.ISelection;
import org.eclipse.swt.program.Program;
import org.eclipse.ui.IWorkbenchWindow;
import org.eclipse.ui.IWorkbenchWindowActionDelegate;

/**
 * open ChangeLogIDE file
 * <AUTHOR>
 *
 */
public class OpenChangeLogAction implements IWorkbenchWindowActionDelegate {

	@Override
	public void run(IAction action) {
		String pathwhere="\\";
		if(Platform.getOS().equals(Platform.OS_LINUX)) {
			pathwhere="/";
		}
		
    	String preFixRootPath="";
    	if(Platform.getOS().equals(Platform.OS_LINUX)) { 
    		 preFixRootPath=System.getProperty("file.separator"); 
    	}
    	
    	pathwhere = preFixRootPath + Platform.getInstallLocation().getURL().getPath().substring(1) + pathwhere; //+ separator

		String name="ChangeLogIDE.txt";
		if(Messages.TestLanguage.equalsIgnoreCase("Default_CN")) {
			name="ChangeLogIDE.txt";
		}
		if(Messages.TestLanguage.equalsIgnoreCase("Default_EN")){
			name="ChangeLogIDE.txt";
		}
		
		try {
			File f=new File(pathwhere+name);
			if (f.exists()) {
	//			String paths = f.getAbsolutePath();
				String paths = f.getCanonicalPath();				
				Program.launch(paths);
			}else {						
				//MessageDialog.openInformation(null, "info", "not add...");				
			}
		} catch (Exception e) {
			// TODO: handle exception
		}
	}

	@Override
	public void selectionChanged(IAction action, ISelection selection) {
		// TODO Auto-generated method stub

	}

	@Override
	public void dispose() {
		// TODO Auto-generated method stub

	}

	@Override
	public void init(IWorkbenchWindow window) {
		// TODO Auto-generated method stub

	}

}
