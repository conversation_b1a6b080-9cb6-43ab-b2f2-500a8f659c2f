package com.chipon32.chiponide.ui.action;

import org.eclipse.jface.action.IMenuManager;
import org.eclipse.ui.navigator.CommonActionProvider;
import org.eclipse.ui.navigator.ICommonActionExtensionSite;

public class NewKFProjectWizardActionProvider extends CommonActionProvider {

	public NewKFProjectWizardActionProvider() {
		
	}
	
	

	@Override
	public void init(ICommonActionExtensionSite aSite) {
		// TODO Auto-generated method stub
		super.init(aSite);
	}



	@Override
	public void fillContextMenu(IMenuManager menu) {
		menu.insertBefore("group.new", new NewKFProjectWizardAction()); //Ìí¼Ó²Ëµ¥Ïî
		menu.add(new NewKFProjectWizardAction());
		
	}
	
	

}
