package com.chipon32.chiponide.ui.action;

import java.io.File;
import java.io.IOException;
import org.eclipse.core.resources.IResource;
import org.eclipse.core.runtime.Platform;
import org.eclipse.jface.viewers.ISelection;
import org.eclipse.jface.viewers.IStructuredSelection;
import org.eclipse.swt.program.Program;

public class OpenFileUtil {
  public static void open(ISelection currentSelection) {
	  
    if (currentSelection != null && currentSelection instanceof IStructuredSelection) {
    	
      IStructuredSelection treeSelection = (IStructuredSelection)currentSelection;
      Object obj = treeSelection.getFirstElement();
      
      if (obj instanceof IResource) {
    	  
        IResource resource = (IResource)obj;
        String location = resource.getLocation().toOSString();
        openInBrowser(location);
        
      } 
    } 
  }
  private static void openInBrowser(String location) {
    try {    	
    	if(Platform.getOS().equals(Platform.OS_WIN32))
    		Runtime.getRuntime().exec("explorer /select, \"" + location + "\"");// only windows    	 
    	else {	// windows can ,but may use the old ,use explore will with section
    		File f=new File(location);
    		if(f.isDirectory()){
        		String paths=f.getCanonicalPath();						
    			Program.launch(paths);
    		}
    		else
    		{
    			String paths=f.getParent();
    			Program.launch(paths);
    		}
    	}    	
    } catch (IOException e) {
      e.printStackTrace();
    } 
  }
}
