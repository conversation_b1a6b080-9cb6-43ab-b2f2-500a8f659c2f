package com.chipon32.chiponide.ui.action;

import org.eclipse.jface.action.IAction;
import org.eclipse.jface.viewers.ISelection;
import org.eclipse.ui.IEditorActionDelegate;
import org.eclipse.ui.IEditorPart;
import org.eclipse.ui.internal.ObjectPluginAction;

@SuppressWarnings("restriction")
public class OpenFilePopupAction implements IEditorActionDelegate {
	public void run(IAction action) {
		ObjectPluginAction action1 = (ObjectPluginAction) action;
		OpenFileUtil.open(action1.getSelection());
	}

	public void selectionChanged(IAction action, ISelection selection) {
	}

	public void setActiveEditor(IAction action, IEditorPart editorPart) {
	}
}
