package com.chipon32.chiponide.ui.action;

import java.io.File;
import java.io.IOException;

import org.eclipse.jface.action.IAction;
import org.eclipse.jface.dialogs.MessageDialog;
import org.eclipse.jface.viewers.ISelection;
import org.eclipse.swt.program.Program;
import org.eclipse.ui.IWorkbenchWindow;
import org.eclipse.ui.IWorkbenchWindowActionDelegate;
/**
 * setup driver
 * <AUTHOR>
 *
 */
public class OpenDriveSetupAction implements IWorkbenchWindowActionDelegate {

	@Override
	public void run(IAction action) {

		try {	
			// relativity from run workdir			
	
			File f1=new File("KFLink\\USBDriver\\1 Install Certificate(Admin).bat");   // open path //$NON-NLS-1$
			if (f1.exists()) 
			{
//				String paths=f1.getAbsolutePath();
				String paths=f1.getCanonicalPath();				
				Program.launch(paths,new File("KFLink\\USBDriver").getAbsolutePath());
			}
			else {						
				//MessageDialog.openInformation(null, "info", "not add...");				
			}
			File f2=new File("KFLink\\USBDriver\\2 dp-Installer.exe");   // open path //$NON-NLS-1$
			if (f2.exists()) 
			{
//				String paths=f2.getAbsolutePath();
				String paths=f2.getCanonicalPath();				
				Program.launch(paths,new File("KFLink\\USBDriver").getAbsolutePath());
			}
			else 
			{						
				try {
					File f3=new File("KFLink\\USBDriver");   // open path //$NON-NLS-1$
					if (f3.exists()) 
					{
		//				String paths= f3.getAbsolutePath();
						String paths= f3.getCanonicalPath();
									
						Program.launch(paths);
					}
					else {						
						MessageDialog.openInformation(null, Messages.OpenDriveSetupAction_2, Messages.OpenDriveSetupAction_3);				
					}
				} catch (IOException e1) {
					// TODO Auto-generated catch block
					e1.printStackTrace();
				}		
			}		
		} catch (Exception e) {
			// TODO: handle exception
			try {
				File f3=new File("KFLink\\USBDriver");   // open path //$NON-NLS-1$
				if (f3.exists()) 
				{
	//				String paths=f.getAbsolutePath();
					String paths= f3.getCanonicalPath();
								
					Program.launch(paths);
				}
				else {						
					MessageDialog.openInformation(null, Messages.OpenDriveSetupAction_5, Messages.OpenDriveSetupAction_6);				
				}
			} catch (IOException e1) {
				// TODO Auto-generated catch block
				MessageDialog.openInformation(null, Messages.OpenDriveSetupAction_7, Messages.OpenDriveSetupAction_8);				
			}	
		}
	}

	@Override
	public void selectionChanged(IAction action, ISelection selection) {
		// TODO Auto-generated method stub

	}

	@Override
	public void dispose() {
		// TODO Auto-generated method stub

	}

	@Override
	public void init(IWorkbenchWindow window) {
		// TODO Auto-generated method stub

	}

}
