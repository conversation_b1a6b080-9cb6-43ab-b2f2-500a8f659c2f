package com.chipon32.chiponide.ui.action;

import org.eclipse.core.runtime.IStatus;
import org.eclipse.core.runtime.Status;
import org.eclipse.jface.action.IAction;
import org.eclipse.jface.text.BadLocationException;
import org.eclipse.jface.text.IDocument;
import org.eclipse.jface.text.IRegion;
import org.eclipse.jface.text.ITextSelection;
import org.eclipse.jface.text.Position;
import org.eclipse.jface.text.TextSelection;
import org.eclipse.jface.viewers.ISelection;
import org.eclipse.ui.IEditorActionDelegate;
import org.eclipse.ui.IEditorPart;
import org.eclipse.ui.IWorkbenchSite;
import org.eclipse.ui.editors.text.TextEditor;

import com.chipon32.chiponide.ui.UiActivator;
import com.chipon32.chiponide.ui.editors.ASMEditor;
import com.chipon32.chiponide.ui.editors.ASMInstructionSet;
import com.chipon32.chiponide.ui.editors.TreeObject;
import com.chipon32.chiponide.ui.util.ASMWordFinder;
import com.chipon32.chiponide.ui.util.ContentProvider;

/**
 *
 *
 */
public class OpenDeclarationInASMEditorAction implements IEditorActionDelegate {

	private ITextSelection fTextSelection;

	protected TextEditor fEditor;

	protected IWorkbenchSite fSite;

	public OpenDeclarationInASMEditorAction() {
	}

	@Override
	public void run(IAction action) {
		String str = computeSelectedWord();
		if (str != null) {
			jumpTo(str);
		}
	}

	public void jumpTo(String keyword) {
		if (!isKeyWord(keyword.toUpperCase())) {
			if (fEditor instanceof ASMEditor) {
				IDocument document = fEditor.getDocumentProvider().getDocument(fEditor.getEditorInput());
				ContentProvider cp = new ContentProvider((ASMEditor) fEditor);
				cp.parse();

				TreeObject labels = cp.getLabels();
				TreeObject segments = cp.getSegments();
				
				boolean isLabels = false;
				Object[] childs = labels.getChildren();
				for (Object child : childs) {
					if (child instanceof TreeObject) {
						TreeObject node = (TreeObject) child;
						if (keyword.equals(node.toString().trim())) {
							Position p = (Position) node.getData();
							try {
								int line = document.getLineOfOffset(p.getOffset());
								int length = document.getLineLength(line);
								fEditor.getSelectionProvider().setSelection(new TextSelection(document.getLineOffset(line),length - 1));
								isLabels = true;
								break;
							} catch (BadLocationException e) {
								e.printStackTrace();
							}
						}
					}
				}
				
				if(!isLabels){
					childs = segments.getChildren();
					for (Object child : childs) {
						if (child instanceof TreeObject) {
							TreeObject node = (TreeObject) child;
							String source = node.toString().trim();
							String[] strs = source.split("\\s+"); //$NON-NLS-1$
							if (strs.length>1&&strs[1].equals(keyword)) {
								Position p = (Position) node.getData();
								try {
									int line = document.getLineOfOffset(p.getOffset());
									int length = document.getLineLength(line);
									fEditor.getSelectionProvider().setSelection(new TextSelection(document.getLineOffset(line),length - 1));
									break;
								} catch (BadLocationException e) {
									e.printStackTrace();
								}
							}						
						}
					}
				}				
			}
		}
	}

	private boolean isKeyWord(String string) {
		if (ASMInstructionSet.getInstructions().containsKey(string)
				|| ASMInstructionSet.getPseudoInstructions().containsKey(string)
				|| ASMInstructionSet.getBitNameMap().containsKey(string)
				|| ASMInstructionSet.getRegisters().containsKey(string)
				|| ASMInstructionSet.getSpecialRegisterMap().containsKey(string)
				|| ASMInstructionSet.getSegments().containsKey(string)) {
			return true;
		}
		return false;

	}

	@Override
	public void selectionChanged(IAction action, ISelection selection) {
	}

	@Override
	public void setActiveEditor(IAction action, IEditorPart targetEditor) {
		if (targetEditor != null) {
			fEditor = (TextEditor) targetEditor;
			fSite = fEditor.getSite();
		} 
	}

	private String computeSelectedWord() {
		fTextSelection = getSelectedStringFromEditor();
		String text = null;
		if (fTextSelection != null) {
			IDocument document = fEditor.getDocumentProvider().getDocument(
					fEditor.getEditorInput());
			IRegion reg = ASMWordFinder.findWord(document,
					fTextSelection.getOffset());
			if (reg != null && reg.getLength() > 0) {
				try {
					text = document.get(reg.getOffset(), reg.getLength());
				} catch (BadLocationException e) {
					UiActivator.getDefault().getLog().log(new Status(IStatus.ERROR,UiActivator.PLUGIN_ID, IStatus.ERROR,Messages.OpenDeclarationInASMEditorAction_1, e));
				}
			}
		}
		return text;
	}

	protected ITextSelection getSelectedStringFromEditor() {
		ISelection selection = getSelection();
		if (!(selection instanceof ITextSelection))
			return null;

		return (ITextSelection) selection;
	}

	protected ISelection getSelection() {
		ISelection sel = null;
		if (fSite != null && fSite.getSelectionProvider() != null) {
			sel = fSite.getSelectionProvider().getSelection();
		}
		return sel;
	}

}
