package com.chipon32.chiponide.ui.action;

import java.io.File;
import java.io.IOException;

import org.eclipse.jface.action.IAction;
import org.eclipse.jface.dialogs.MessageDialog;
import org.eclipse.jface.viewers.ISelection;
import org.eclipse.swt.program.Program;
import org.eclipse.ui.IWorkbenchWindow;
import org.eclipse.ui.IWorkbenchWindowActionDelegate;

/**
 * <AUTHOR> firmware undata
 *
 */
public class ChiponUpdateFirmwareAction implements IWorkbenchWindowActionDelegate{

	@Override
	public void run(IAction action) {		
		
		String name="FW3 UpLoaderCN.exe";
		if(Messages.TestLanguage.equalsIgnoreCase("Default_CN"))
		{
			name="FW3 UpLoaderCN.exe";
		}
		if(Messages.TestLanguage.equalsIgnoreCase("Default_EN"))
		{
			name="FW3 UpLoaderEN.exe";
		}
		try {	
			// relativity path under run workdir
			File f=new File("KFLink\\"+name); //$NON-NLS-1$
			if (f.exists()) 
			{
//				String paths=f.getAbsolutePath();
				String paths=f.getCanonicalPath();				
				Program.launch(paths);
			}
			else {						
				File f2=new File("KFLink"); //$NON-NLS-1$
				if (f2.exists()) 
				{
//					String paths=f2.getAbsolutePath();
					String paths=f2.getCanonicalPath();				
					Program.launch(paths);
				}
				else
				{
					MessageDialog.openInformation(null, Messages.ChiponUpdateFirmwareAction_2, Messages.ChiponUpdateFirmwareAction_3);	
				}
			}
		} catch (Exception e) {
			// TODO: handle exception
			try {
				File f3=new File("KFLink");   // open path //$NON-NLS-1$
				if (f3.exists()) 
				{
	//				String paths=f.getAbsolutePath();
					String paths= f3.getCanonicalPath();
								
					Program.launch(paths);
				}
				else {						
					MessageDialog.openInformation(null, Messages.ChiponUpdateFirmwareAction_5, Messages.ChiponUpdateFirmwareAction_6);			
				}
			} catch (IOException e1) {
				// TODO Auto-generated catch block
				MessageDialog.openInformation(null, Messages.ChiponUpdateFirmwareAction_7, Messages.ChiponUpdateFirmwareAction_8);			
			}		
			
		}
	}
	
	

	@Override
	public void selectionChanged(IAction action, ISelection selection) {
		
	}

	@Override
	public void dispose() {
		
	}

	@Override
	public void init(IWorkbenchWindow window) {
		
	}

}
