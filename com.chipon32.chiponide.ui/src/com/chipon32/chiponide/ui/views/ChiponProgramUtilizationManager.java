package com.chipon32.chiponide.ui.views;

import com.chipon32.hex.core.Memory;
import com.chipon32.hex.core.util.IConfigurationProvider;
import com.chipon32.util.provider.ConfigurationFactory;
import com.chipon32.util.ui.drawChart.RectangleData;
import com.chipon32.util.ui.drawChart.RectanglePaintTool;

import java.io.BufferedReader;
import java.io.File;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.text.NumberFormat;

import org.eclipse.cdt.managedbuilder.core.IConfiguration;
import org.eclipse.cdt.managedbuilder.core.IManagedBuildInfo;
import org.eclipse.cdt.managedbuilder.core.ManagedBuildManager;
import org.eclipse.cdt.managedbuilder.macros.BuildMacroException;
import org.eclipse.cdt.managedbuilder.macros.IBuildMacroProvider;
import org.eclipse.core.resources.IFile;
import org.eclipse.core.resources.IProject;
import org.eclipse.core.resources.ResourcesPlugin;
import org.eclipse.core.runtime.Platform;
import org.eclipse.swt.widgets.Display;

import com.chipon32.chiponide.core.config.ChiponMessageConfigModel;
import com.chipon32.chiponide.core.config.ConfigFactory;
import com.chipon32.chiponide.core.paths.ChipOnPath;
import com.chipon32.chiponide.core.paths.SystemPathHelper;
import com.chipon32.chiponide.core.properties.ChipOnProjectProperties;
import com.chipon32.chiponide.core.properties.ProjectPropertyManager;
import com.chipon32.chiponide.mbs.ChiponBuilder;

/**
 * <AUTHOR> @since
 * @description 资源使用率控制器的具体实现，整个视图结果
 */

public class ChiponProgramUtilizationManager {

	private static ChiponProgramUtilizationManager manager;

	private ChiponProgramUtilizationManager() {
	}

	public synchronized static ChiponProgramUtilizationManager getManager() {
		if (manager == null) {
			manager = new ChiponProgramUtilizationManager();
		}
		return manager;
	}

	private Memory memory;

	/**
	 * 当前传入的模型
	 */
	private static ChiponMessageConfigModel model;

	/**
	 * 获取编译结果hex的memory的校验和
	 * 
	 * @return
	 */
	public String gethexsumvaule() {
		// TODO Auto-generated method stub
		String sum;
		try {
			sum = memory.getHexFlashSumVaule();
		} catch (NullPointerException e) {
			// e.printStackTrace();
			sum = "--------------------------------";// "--------------------------------";不对其字体D占较宽便于完整显示
		}
		return sum;
	}

	public synchronized ChiponMessageConfigModel getModel() {
		if (model == null) {
			model = ConfigFactory.eINSTANCE.createChiponMessageConfigModel();
		}
		return model;
	}

	public RectangleData[] getFlashProgresChartValue() {
		// 获取程序使用率
		int flashSize = 0; // flash的总大小
		double flashUtil = 0; // 百分率
		String flashUsed = model.getFlashValue();
		if(flashUsed == null) {
			flashUsed = "-1";
		}else if(model.getCheckSumValue1().startsWith("---")) {
			flashUsed = "-1";
		}
		try {
			if (getConfigurationProvider().getFlashs().get(0).getSize() != null && !getConfigurationProvider().getFlashs().get(0).getSize().equals("")) { //$NON-NLS-1$
				flashSize = Integer.parseInt(getConfigurationProvider().getFlashs().get(0).getSize(), 16);
			} else {
				return null;
			}
		} catch (NullPointerException e) {
			// e.printStackTrace();
			return null;
		}
		
		flashUtil = (double) Integer.valueOf(flashUsed) / flashSize;
		RectangleData[] oils = new RectanglePaintTool().drawnRectangChart( //
				String.valueOf(flashSize), //
				flashUsed, //
				instanceNumberFormat().format(flashUtil), //
				(int) (flashUtil * 120));
		return oils;
	}
	
	/*
	 * public String getFlashUsed() { String flashUsed = ""; if
	 * (isDebugOrRelease().equalsIgnoreCase("Release")) { flashUsed =
	 * getCOPP().getfrFlashUsed(); } else if
	 * (isDebugOrRelease().equalsIgnoreCase("Debug")) { flashUsed =
	 * getCOPP().getfdFlashUsed(); } else { // do nothing } return flashUsed; }
	 */
	
	/*
	 * public String getRamUsed() { String ramUsed = ""; if
	 * (isDebugOrRelease().equalsIgnoreCase("Release")) { ramUsed =
	 * getCOPP().getfrRamUsed(); } else if
	 * (isDebugOrRelease().equalsIgnoreCase("Debug")) { ramUsed =
	 * getCOPP().getfdRamUsed(); } else { // do nothing } return ramUsed; }
	 */

	/**
	 * 基于获取型号名方法的获取型号的信息提供者
	 */
	public IConfigurationProvider getConfigurationProvider() {
		return ConfigurationFactory.getProvider(model.getChipType());
	}

	public RectangleData[] getRamProgresChartValue() {
		int RAMSize = 0; // RAM的总大小
		String ramUsed = model.getRamValue();
		if(ramUsed == null) {
			ramUsed = "-1";
		}else if(model.getCheckSumValue1().startsWith("---")) {
			ramUsed = "-1";
		}
		try {
			if (getConfigurationProvider().getRams().get(0).getSize() != null && !getConfigurationProvider().getRams().get(0).getSize().equals("")) { //$NON-NLS-1$
				RAMSize = Integer.parseInt(getConfigurationProvider().getRams().get(0).getSize(), 16);
			} else {
				return null;
			}
		} catch (NullPointerException e) {
			// e.printStackTrace();
			return null;
		}
		
		// 使用率的百分分数值
		double ramUtil = 0;
		if (RAMSize > 0) {
			ramUtil = (double) Integer.valueOf(ramUsed) / RAMSize;
		}
		// 格式： 总空间：使用空间：百分率数值：进度条总的200下的进度值
		RectangleData[] oils = new RectanglePaintTool().drawnRectangChart( //
				String.valueOf(RAMSize), //
				ramUsed, //
				instanceNumberFormat().format(ramUtil), //
				(int) (ramUtil * 120));
		
		return oils;
	}

	/**
	 * 判断当前项目的编译状态是Debug还是Release
	 * 
	 * @return
	 */
	public String isDebugOrRelease() {
		String buildMode = "";
		IProject currentProject = getCurrentProject();
		if (currentProject.exists()) {
			IManagedBuildInfo mbinfo = ManagedBuildManager.getBuildInfoLegacy(currentProject);
			if (mbinfo != null) {
				buildMode = mbinfo.getConfigurationName();
			}
		}

		return buildMode;
	}

	public void refurbishFlashRamProgresChartValueOrg() {
		// #######################################
		// 构建管理类
		IFile Fileelf = null;
		IManagedBuildInfo bi = ManagedBuildManager.getBuildInfo(getCurrentProject());
		if (bi == null) {
			return;
		}
		IConfiguration buildcfg = bi.getDefaultConfiguration();
		String cfgName = buildcfg.getName();
		IBuildMacroProvider provider = ManagedBuildManager.getBuildMacroProvider();
		try {
			// String buildArtifactName =
			// provider.resolveValue(buildcfg.getArtifactName(),
			// "", " ", IBuildMacroProvider.CONTEXT_CONFIGURATION, buildcfg);
			String buildArtifactName = provider.getMacro("BuildArtifactFileBaseName", IBuildMacroProvider.CONTEXT_CONFIGURATION, buildcfg, true).getStringValue();
			buildArtifactName = provider.resolveValue(buildArtifactName, "", " ", IBuildMacroProvider.CONTEXT_CONFIGURATION, buildcfg);

			String fileName = buildArtifactName + "." + "elf";// buildcfg.getArtifactExtension();
			IFile tmpFile = getCurrentProject().getFile(cfgName + "/" + fileName);
			if (tmpFile != null && tmpFile.exists()) {
				Fileelf = tmpFile;
			} else {
				return;
			}
		} catch (BuildMacroException e) {
			e.printStackTrace();
			return;
		}
		// ########################################统计使用率
		if (Fileelf.exists()) {
			//获取ram/flash使用情况的超时进程避免kf32-size.exe程序执行异常
			TimeoutThread tot = new TimeoutThread(System.currentTimeMillis(), 3000);
			tot.start();
			
			final String commands = "\"" + SystemPathHelper.getPath(ChipOnPath.CHIPONCC, false, getCurrentProject()).toOSString() + File.separator + //$NON-NLS-1$
					"kf32-size.exe" + "\"    \"" + Fileelf.getLocation().toOSString() + "\""; //$NON-NLS-1$
			// 运行线程完成资源使用率的统计
			new Thread(new Runnable() {
				@Override
				public void run() {
					try {
						String[] command = null;
						if (Platform.getOS().equals(Platform.OS_WIN32)) {
							command = new String[] { commands }; // command=new
																	// String
																	// []{"cmd.exe","/c",commands};
						} else if (Platform.getOS().equals(Platform.OS_LINUX)) {
							command = new String[] { "/bin/sh", "-c", commands };
						} else {
							command = new String[] { commands };
						}
						Process process = Runtime.getRuntime().exec(command);


						InputStream is = process.getInputStream();
						BufferedReader br = new BufferedReader(new InputStreamReader(is));
						// 单行结果输出，并解析
						/*
						 * flash = text + data + bss ram = data + bss 数据类型 顺序
						 * text data bss eep dec hex filename
						 */
						UtilizationViewerSizeGetParse iVSP = new UtilizationViewerSizeGetParse();
						String line = br.readLine();
						// 为过滤掉warnning
						while (line != null) { // while(line != null && line.length()>1){
							line = line.trim();
							if (line.length() > 1){ //exist empty line  
								System.out.println(Messages.UtilizationViewerConfigurationFactory_15 + line);//普通信息打印费时，暂不打印
							}	

							iVSP.ParseInputString(line);
							// ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
							if (line.startsWith("text")) { //$NON-NLS-1$
								break;  //跳出循环
							} else {
								 //System.out.println(Messages.UtilizationViewerConfigurationFactory_15+ line);
							}
							// ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
							try {
								line = br.readLine();
							} catch (Exception e) {
								// TODO: handle exception
								line = null;
							}
						}//end while
						// ++++++++++++++++++++++++++++++++++++++++++++++
						// 下一行为内容
						String ramUsed="-1",flashUsed = "-1";
						if (iVSP.getRamUsed() == -1) {
							line = br.readLine();
							if(line!=null) {
								line = line.trim();
								String[] datas = line.split("\\W+"); //$NON-NLS-1$
								System.out.println(Messages.UtilizationViewerConfigurationFactory_14 + line);
								ramUsed = Integer.toString(Integer.parseInt(datas[1]) + Integer.parseInt(datas[2]));
								flashUsed = Integer.toString(Integer.parseInt(datas[0]) + Integer.parseInt(datas[1]));
							}else {
								ramUsed = ""+iVSP.getRamUsed();
								flashUsed = ""+iVSP.getFlashUsed();
							}
						} else {
							ramUsed = ""+iVSP.getRamUsed();
							flashUsed = ""+iVSP.getFlashUsed();
						}
						
						model.setRamValue(ramUsed);
						model.setFlashValue(flashUsed);
						
						// 关闭输入流，读取流
						br.close();
						is.close();
						
					} catch (Exception e1) {
						e1.printStackTrace();
					}
				}
			}).start();
			
			tot.stop=true;	
			System.out.print(Messages.UtilizationViewerConfigurationFactory_17+model.getFlashValue() +"$"+model.getRamValue()+"\r\n"); //$NON-NLS-2$ //$NON-NLS-3$
			if(tot.isWatchTimeout)
			{				
				System.out.print(Messages.UtilizationViewerConfigurationFactory_20+commands); // 可打开控制台看到的
			}
			
		}
		
	}

	public IProject getCurrentProject() {
		IProject project = null;
		String projectName = model.getProjectName();
		if (projectName != null) {
			project = ResourcesPlugin.getWorkspace().getRoot().getProject(projectName);
		}
		return project;
	}

	public ChipOnProjectProperties getCOPP() {
		IProject project = getCurrentProject();
		if (project == null) {
			return null;
		} else {
			return ProjectPropertyManager.getPropertyManager(project).getProjectProperties();
		}
	}

	/**
	 * 设置百分率的格式
	 * @return
	 */
	private NumberFormat instanceNumberFormat() {
		NumberFormat numFormat = NumberFormat.getPercentInstance();
		numFormat.setMaximumFractionDigits(1);
		numFormat.setMinimumFractionDigits(1);
		return numFormat;
	}
	
	/**
	 * 程序执行超时守护
	 * <AUTHOR>
	 */
	class TimeoutThread extends Thread{
		long startTime;
		long timeOut;
		boolean stop;
		boolean isWatchTimeout;
		
		public TimeoutThread(long starttime, long timeout) {
			startTime = starttime;
			timeOut = timeout;
			stop = false;
			isWatchTimeout = false;
		}
		
		@Override
		public void run() {
			while(!stop) {
				try {
					Thread.sleep(100);//延迟
					
					if(System.currentTimeMillis()-startTime>timeOut) {
						
						if(Platform.getOS().equals(Platform.OS_WIN32)){
							
						}else if (Platform.getOS().equals(Platform.OS_LINUX)) {
							// linux 主动中止程序
							Runtime.getRuntime().exec(new String[]{"/bin/sh","-c","killall -s 9 kf32-size"}); 
						}
						
						isWatchTimeout = true;
						break;
					}
					
				} catch (Exception e) {
//					e.printStackTrace();
					isWatchTimeout = true;
					stop = true;
				}
				
			}
			
		}//end TimeoutThread class
		
		
	}
	

}
