package com.chipon32.chiponide.ui.views;

import org.eclipse.osgi.util.NLS;

public class Messages extends NLS {
	private static final String BUNDLE_NAME = "com.chipon32.chiponide.ui.views.messages"; //$NON-NLS-1$
	public static String ChiponProgramUtilizationComponent_11;
	public static String ChiponProgramUtilizationComponent_13;
	public static String ChiponProgramUtilizationComponent_15;
	public static String ChiponProgramUtilizationComponent_17;
	public static String ChiponProgramUtilizationComponent_18;
	public static String ChiponProgramUtilizationComponent_19;
	public static String ChiponProgramUtilizationComponent_23;
	public static String ChiponProgramUtilizationComponent_26;
	public static String ChiponProgramUtilizationComponent_31;
	public static String ChiponProgramUtilizationComponent_33;
	public static String ChiponProgramUtilizationComponent_34;
	public static String ChiponProgramUtilizationComponent_35;
	public static String ChiponProgramUtilizationComponent_36;
	public static String ChiponProgramUtilizationComponent_37;
	public static String ChiponProgramUtilizationComponent_38;
	public static String ChiponProgramUtilizationComponent_41;
	public static String ChiponProgramUtilizationComponent_50;
	public static String ChiponProgramUtilizationComponent_8;
	public static String ChiponProgramUtilizationComponent_9;
	public static String ChiponView_0;
	public static String ChiponView_4;
	public static String McuConfigDialogIDE_11;
	public static String McuConfigDialogIDE_12;
	public static String McuConfigDialogIDE_13;
	public static String McuConfigDialogIDE_14;
	public static String McuConfigDialogIDE_15;
	public static String McuConfigDialogIDE_17;
	public static String McuConfigDialogIDE_18;
	public static String McuConfigDialogIDE_19;
	public static String McuConfigDialogIDE_20;
	public static String McuConfigDialogIDE_21;
	public static String McuConfigDialogIDE_22;
	public static String McuConfigDialogIDE_23;
	public static String McuConfigDialogIDE_24;
	public static String McuConfigDialogIDE_25;
	public static String McuConfigDialogIDE_8;
	public static String McuConfigDialogIDE_9;
	public static String SetWhichWorkDialog_12;
	public static String SetWhichWorkDialog_13;
	public static String SetWhichWorkDialog_14;
	public static String SetWhichWorkDialog_18;
	public static String SetWhichWorkDialog_2;
	public static String SetWhichWorkDialog_21;
	public static String SetWhichWorkDialog_22;
	public static String SetWhichWorkDialog_23;
	public static String SetWhichWorkDialog_24;
	public static String SetWhichWorkDialog_25;
	public static String SetWhichWorkDialog_26;
	public static String SetWhichWorkDialog_29;
	public static String SetWhichWorkDialog_30;
	public static String SetWhichWorkDialog_32;
	public static String SetWhichWorkDialog_33;
	public static String SetWhichWorkDialog_4;
	public static String SetWhichWorkDialog_6;
	public static String SetWhichWorkDialog_7;
	public static String UtilizationViewerConfigurationFactory_14;
	public static String UtilizationViewerConfigurationFactory_15;
	public static String UtilizationViewerConfigurationFactory_17;
	public static String UtilizationViewerConfigurationFactory_20;
	public static String SetWhichWorkDialog_btnIcsp_text;

	static {
		// initialize resource bundle
		NLS.initializeMessages(BUNDLE_NAME, Messages.class);
	}

	private Messages() {
	}
}
