#Eclipse modern messages class
#Tue Feb 15 10:56:58 CST 2022
ChiponProgramUtilizationComponent_11=Project Config\:
ChiponProgramUtilizationComponent_13=ChipType\:
ChiponProgramUtilizationComponent_15=ChipTool \:
ChiponProgramUtilizationComponent_17=RAM Percent
ChiponProgramUtilizationComponent_18=Flash Percent
ChiponProgramUtilizationComponent_19=Check Code\:
ChiponProgramUtilizationComponent_23=Config
ChiponProgramUtilizationComponent_26=Mode\:
ChiponProgramUtilizationComponent_31=press button inure to save
ChiponProgramUtilizationComponent_33=Protect\:
ChiponProgramUtilizationComponent_34=Protect A
ChiponProgramUtilizationComponent_35=Protect C
ChiponProgramUtilizationComponent_36=Protect D
ChiponProgramUtilizationComponent_37=NO Protect
ChiponProgramUtilizationComponent_38=press button inure to save
ChiponProgramUtilizationComponent_41=inure
ChiponProgramUtilizationComponent_50=Save Fail
ChiponProgramUtilizationComponent_8=---***Update By Active File Or Project***---
ChiponProgramUtilizationComponent_9=Project Name\:
ChiponView_0=Chip Message Description
ChiponView_4=ChipON Type Message
McuConfigDialogIDE_11=Config Content
McuConfigDialogIDE_12=Config 
McuConfigDialogIDE_13=Config Explain
McuConfigDialogIDE_14=Reset Config
McuConfigDialogIDE_15=Sector Protect Config
McuConfigDialogIDE_17=Page
McuConfigDialogIDE_18=Start Address
McuConfigDialogIDE_19=Size
McuConfigDialogIDE_20=Read Protect
McuConfigDialogIDE_21=Write Protect
McuConfigDialogIDE_22=No Select
McuConfigDialogIDE_23=Select all
McuConfigDialogIDE_24=Apply
McuConfigDialogIDE_25=Cancel
McuConfigDialogIDE_8=No Design But Only UI Form
McuConfigDialogIDE_9=User Configuration
SetWhichWorkDialog_12=Ensure
SetWhichWorkDialog_13=Select Set 
SetWhichWorkDialog_14=\   Mode
SetWhichWorkDialog_18=Cancel
SetWhichWorkDialog_2=Select Program Set 
SetWhichWorkDialog_21=\:Mode DPI|ISP
SetWhichWorkDialog_22=\:Mode ISP
SetWhichWorkDialog_23=refurbish
SetWhichWorkDialog_24=waiting
SetWhichWorkDialog_25=\:Mode DPI\\ISP...
SetWhichWorkDialog_26=\:Mode ISP...
SetWhichWorkDialog_29=info
SetWhichWorkDialog_30=Force Skip Chip Identify Check...
SetWhichWorkDialog_32=info
SetWhichWorkDialog_33=Force Skip Chip Identify Check...
SetWhichWorkDialog_4=Set List
SetWhichWorkDialog_6=\:Mode DPI\\ISP...
SetWhichWorkDialog_7=\:Mode ISP...

SetWhichWorkDialog_btnIcsp_text=ICSP
UtilizationViewerConfigurationFactory_14=Statistical Message\:
UtilizationViewerConfigurationFactory_15=Commonly Message\:
UtilizationViewerConfigurationFactory_17=Get Resource Used Message\:
UtilizationViewerConfigurationFactory_20=Process Wrong, Get Resource Used Fail\:
