/*******************************************************************************
 * 
 * Copyright (c) 2007 <PERSON> (<EMAIL>) and others
 * 
 * This program and the accompanying materials are made
 * available under the terms of the GNU Public License v3
 * which accompanies this distribution, and is available at
 * http://www.gnu.org/licenses/gpl.html
 * 
 * Contributors:
 *     Thomas Holland - initial API and implementation
 *     
 * $Id: EntryLabelProvider.java,v 1.1 2013/11/21 06:41:09 zhangji Exp $
 *     
 *******************************************************************************/
package com.chipon32.chiponide.ui.views;

import org.eclipse.jface.viewers.BaseLabelProvider;
import org.eclipse.jface.viewers.IColorProvider;
import org.eclipse.jface.viewers.ITableLabelProvider;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.graphics.Image;
import org.eclipse.swt.widgets.Display;

import com.chipon32.chiponide.core.chipondescription.IEntry;
import com.chipon32.chiponide.core.chipondescription.chipio.Register;

public 	class EntryLabelProvider extends BaseLabelProvider implements ITableLabelProvider ,IColorProvider{
	private Color color;
	private static final Color REGISTER_COLOR = new Color(Display.getDefault(), 128,0,255);
	private static final Color BIT_COLOR = new Color(Display.getDefault(), 128,0,64);
	
	
	@Override
	public Image getColumnImage(Object element, int columnIndex) {
	    
		return null;
	}

	@Override
	public String getColumnText(Object element, int columnIndex) {
		if (element instanceof IEntry) {
			if(element instanceof Register){
				color = REGISTER_COLOR;
			}else {
				color = BIT_COLOR;
			}
			return ((IEntry)element).getColumnData(columnIndex);
		}
		return null;
	}

	@Override
	public Color getForeground(Object element) {
		// TODO Auto-generated method stub
		return color;
	}

	@Override
	public Color getBackground(Object element) {
		// TODO Auto-generated method stub
		return null;
	}

}
