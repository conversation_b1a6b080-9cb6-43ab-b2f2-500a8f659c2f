package com.chipon32.chiponide.ui.views;


//import org.eclipse.cdt.managedbuilder.core.IConfiguration;
//import org.eclipse.cdt.managedbuilder.core.IManagedBuildInfo;
//import org.eclipse.cdt.managedbuilder.core.ManagedBuildManager;
//import org.eclipse.cdt.managedbuilder.macros.IBuildMacroProvider;
//import org.eclipse.core.resources.IFile;
//import org.eclipse.core.resources.IProject;
//import org.eclipse.core.resources.IResource;
import org.eclipse.emf.common.notify.Adapter;
import org.eclipse.emf.common.notify.Notification;
import org.eclipse.emf.common.notify.Notifier;
//import org.eclipse.jface.dialogs.MessageDialog;
import org.eclipse.swt.SWT;
import org.eclipse.swt.custom.ScrolledComposite;
//import org.eclipse.swt.events.ModifyEvent;
//import org.eclipse.swt.events.ModifyListener;
import org.eclipse.swt.events.PaintEvent;
import org.eclipse.swt.events.PaintListener;
//import org.eclipse.swt.events.SelectionAdapter;
//import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Button;
import org.eclipse.swt.widgets.Combo;
import org.eclipse.swt.widgets.Composite;
//import org.eclipse.swt.widgets.Group;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.Text;
//import org.eclipse.wb.swt.SWTResourceManager;

import com.chipon32.chiponide.core.config.ChiponMessageConfigModel;
//import com.chipon32.chiponide.core.properties.ChipOnProjectProperties;
//import com.chipon32.chiponide.core.properties.ProjectPropertyManager;
//import com.chipon32.chiponide.mbs.ChiponNotBuilderHexDo;
//import com.chipon32.chiponide.mbs.WriteFile;
//import com.chipon32.chiponide.ui.UiActivator;
import com.chipon32.util.ui.drawChart.RectangleChart;
import com.chipon32.util.ui.drawChart.RectanglePaintTool;

/**
 * 资源使用率的实现
 * 
 * <AUTHOR> @since
 * @description 资源使用率视图view层
 */

public class ChiponProgramUtilizationComponent implements Adapter {
	

	private ChiponMessageConfigModel model;
	// 载入的绘图对象
	private Composite composite; // 父类添加滚动条后的绘图容器
	// 滚动条
	private ScrolledComposite scrolledComposite;
	// 是否存在焦点
	public boolean isHavaFocus = false;

	// ######################################################## 显示内容1 基本信息
	private Label chipProjectNameLabel; // project name
	private Label chipProjectModeLabel; // Debug or Release
	private Label chipNameLabel; // 芯片型号显示label
	private Label chipToolLabel; // 型号工具选择
	// ######################################################## 显示内容2 编译结果信息

	/**
	 * RAM使用率label
	 */
	private Label ramlabel;
	/**
	 * Flash使用率label
	 */
	private Label flashLabel;

	/**
	 * 校验和label
	 */
	private Label checkSumLabel;
	// 校验和值label 128bit拆分为2行的显示
//	private Label checkSumValue;
//	private Label checkSumValue1;
	private Text checkSumValue;
	private Text checkSumValue1;
	// falsh进度条 综合绘图组件
	private RectangleChart flashProgresChart;

	/**
	 * ram进度条 综合绘图组件
	 */
	private RectangleChart ramProgressChart;

	// ######################################################## 显示内容3 选项类UI
	private Label configMessage;

	public Label getConfigMessage() {
		return configMessage;
	}

	// 倒叙写
	public String NO_ISP = "21436587"; // 8765 4321 //$NON-NLS-1$
	public String ODMOD = "78563412"; // 1234 5678 //$NON-NLS-1$
	public String ODMOD_NO_ISP = "65872143"; // 4321 8765 //$NON-NLS-1$

	// 倒叙
	public String Protect_NO = "5A5AA5A5"; // A5A5 5A5A //$NON-NLS-1$
	public String Protect_A = "5A0EA5F1"; // F1A5 0E5A //$NON-NLS-1$
	public String Protect_C = "FFFFFFFF"; // 非 A 非D 非 //$NON-NLS-1$
	public String Protect_C2 = "F1F2F3F4"; // 写的指定，非不加密，非A 非 D的 //$NON-NLS-1$
											// C，非FFFFFFFF的支持ICSP
	public String Protect_D = "DEBC2143"; // 4321 BCDE //$NON-NLS-1$

	private Button protectSetButtonSys;

	// 加密模式的控件
	private Combo ConfigProtectModeCombo;

	public Combo getConfigProtectModeCombo() {
		return ConfigProtectModeCombo;
	}

	public void ReFlushMode(String vString) {
		if (ConfigProtectModeCombo != null) {
			int whichfind;
			if (vString.equalsIgnoreCase(Protect_A)) {
				whichfind = 0;
			} else if (vString.equalsIgnoreCase(Protect_C)) {
				whichfind = 1;
			} else if (vString.equalsIgnoreCase(Protect_D)) {
				whichfind = 2;
			} else if (vString.equalsIgnoreCase(Protect_NO)) {
				whichfind = 3;
			} else {
				whichfind = 1;
			}

			ConfigProtectModeCombo.select(whichfind);
			protectSetButtonSys.setEnabled(true);
		}
	}

	public void setConfigProtectModeCombo(Combo configProtectModeCombo) {
		ConfigProtectModeCombo = configProtectModeCombo;
	}

	// 配置模式，是否isp，是否swd控件
	private Combo ConfigProtectValueCombo;

	public Combo getConfigProtectVauleCombo() {
		return ConfigProtectValueCombo;
	}

	public void ReFlushValue(String vString) {
		if (ConfigProtectValueCombo != null) {
			int whichfind;
			if (vString.equalsIgnoreCase(NO_ISP)) {
				whichfind = 0;
			} else if (vString.equalsIgnoreCase(ODMOD)) {
				whichfind = 1;
			} else if (vString.equalsIgnoreCase(ODMOD_NO_ISP)) {
				whichfind = 2;
			} else {
				whichfind = 3;
			}

			ConfigProtectValueCombo.select(whichfind);
			protectSetButtonSys.setEnabled(true);
		}

	}

	public void setConfigProtectVauleCombo(Combo configProtectVauleCombo) {
		ConfigProtectValueCombo = configProtectVauleCombo;
	}

	// ######################################################## 对象实现方法
	// 样式 ，父绘图对象 @@@@@@@@@@@@@@@@ 构建@@@@@@@@@@@@@@@@@@
	public ChiponProgramUtilizationComponent(Composite parent, int style, ChiponMessageConfigModel model) {
		// 1列
		parent.setLayout(new GridLayout(1, false));
		// 数据填充自适应
		parent.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, true, 1, 1));
		// 滚动条
		scrolledComposite = new ScrolledComposite(parent, SWT.H_SCROLL | SWT.V_SCROLL);
		scrolledComposite.setLayout(new GridLayout(1, false));
		scrolledComposite.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, true, 1, 1));
		scrolledComposite.setExpandHorizontal(true);
		scrolledComposite.setExpandVertical(true);
		
		composite = new Composite(scrolledComposite, SWT.NONE);
		composite.setLayout(new GridLayout());

		scrolledComposite.setContent(composite);
		// 确定大小
		scrolledComposite.setMinSize(260, 540);
		
		this.model = model;
	}

	// 绘制内容
	public void createPartControl() {
		// 基于1列新建绘图对象
		// 背景色 （白色）
		composite.setBackground(new Color(null, 255, 255, 255));
		composite.setBackgroundMode(SWT.INHERIT_FORCE);
		// 分3列,第3列使用空的label，形成名与结果的 1:2 比例
		composite.setLayout(new GridLayout(3, false));
		// 绘图的布局信息设置
		GridData gd_composite = new GridData(SWT.FILL, SWT.FILL, true, true);
		composite.setLayoutData(gd_composite);
		//占空
		new Label(composite, SWT.NONE).setLayoutData(new GridData(SWT.FILL, SWT.CENTER, false, false, 3, 4)); // 3列4行占空

		Label labelflag = new Label(composite, SWT.NONE);
		labelflag.setLayoutData(new GridData(SWT.BEGINNING, SWT.CENTER, true, false, 3, 1));
		labelflag.setText(Messages.ChiponProgramUtilizationComponent_8);
		//占空
		new Label(composite, SWT.NONE).setLayoutData(new GridData(SWT.FILL, SWT.CENTER, true, false, 3, 1));

		// ##################################################### 显示信息1
		Label Chipprojectname = new Label(composite, SWT.NONE);
		Chipprojectname.setLayoutData(new GridData(SWT.BEGINNING, SWT.CENTER, false, false, 1, 1));// GridData(水平对齐,
		Chipprojectname.setText(Messages.ChiponProgramUtilizationComponent_9);

		chipProjectNameLabel = new Label(composite, SWT.NONE);
		chipProjectNameLabel.setLayoutData(new GridData(SWT.FILL, SWT.CENTER, true, false, 2, 1));
		chipProjectNameLabel.setText(model.getProjectName()); // 初始的长度，决定了可显示的最大长

		Label ChipprojectMode = new Label(composite, SWT.NONE);
		ChipprojectMode.setLayoutData(new GridData(SWT.BEGINNING, SWT.CENTER, false, false, 1, 1));// GridData(水平对齐,
		ChipprojectMode.setText(Messages.ChiponProgramUtilizationComponent_11);
		
		chipProjectModeLabel = new Label(composite, SWT.NONE);
		chipProjectModeLabel.setLayoutData(new GridData(SWT.FILL,SWT.CENTER,true,false,2,1));
		chipProjectModeLabel.setText(model.getBuildMode()); //
		 
		GridData labelGridData = new GridData(SWT.NONE, SWT.NONE, false, false);
		Label ChipModel = new Label(composite, SWT.NONE);
		ChipModel.setLayoutData(labelGridData);// GridData(水平对齐, 垂直对齐, false,
		ChipModel.setText(Messages.ChiponProgramUtilizationComponent_13);

		chipNameLabel = new Label(composite, SWT.NONE);
		chipNameLabel.setLayoutData(new GridData(SWT.FILL, SWT.CENTER, true, false, 2, 1));
		chipNameLabel.setText(model.getChipType()); // 初始的长度，决定了可显示的最大长

		Label ChipToolName = new Label(composite, SWT.NONE);
		ChipToolName.setLayoutData(new GridData(SWT.BEGINNING, SWT.CENTER, false, false, 1, 1));// GridData(水平对齐,
		ChipToolName.setText(Messages.ChiponProgramUtilizationComponent_15);

		chipToolLabel = new Label(composite, SWT.NONE);
		chipToolLabel.setLayoutData(new GridData(SWT.FILL, SWT.CENTER, true, false, 2, 1));
		chipToolLabel.setText(model.getChipTool()); // 初始的长度，决定了可显示的最大长
													// //$NON-NLS-1$
		// 空行占位形成间隔
		new Label(composite, SWT.NONE).setLayoutData(new GridData(SWT.FILL, SWT.CENTER, true, false, 3, 4)); // 3列4行占空

		// ##################################################### 显示信息2
		GridData moresytleGridData = new GridData(120, 25); // 进度条信息样式，设定了最小尺寸
		moresytleGridData.horizontalSpan = 2;
		moresytleGridData.grabExcessHorizontalSpace = true;
		// RAM 使用率标签
		ramlabel = new Label(composite, SWT.NONE);
		ramlabel.setLayoutData(new GridData(SWT.FILL, SWT.CENTER, false, false, 1, 1));
		ramlabel.setText(Messages.ChiponProgramUtilizationComponent_17);
		// 进度条RAM ***
		ramProgressChart = new RectangleChart(composite, SWT.NONE); // SWT.CENTER
																	// linux
																	// wrong
		ramProgressChart.setLayoutData(moresytleGridData);
		ramProgressChart.setData(RectangleChart.KEY, ChiponProgramUtilizationManager.getManager().getRamProgresChartValue());
		ramProgressChart.addPaintListener(new PaintListener() {
			@Override
			public void paintControl(PaintEvent e) {
				new RectanglePaintTool().paintRectangleControl(e);
			}
		});
		// 进度条flash
		flashLabel = new Label(composite, SWT.NONE);
		flashLabel.setLayoutData(new GridData(SWT.FILL, SWT.CENTER, false, false, 1, 1));
		flashLabel.setText(Messages.ChiponProgramUtilizationComponent_18);

		flashProgresChart = new RectangleChart(composite, SWT.NONE); // SWT.CENTER
																		// linux
																		// wrong
		flashProgresChart.setLayoutData(moresytleGridData);
		flashProgresChart.setData(RectangleChart.KEY, ChiponProgramUtilizationManager.getManager().getFlashProgresChartValue());
		flashProgresChart.addPaintListener(new PaintListener() {
			@Override
			public void paintControl(PaintEvent e) {
				new RectanglePaintTool().paintRectangleControl(e); // **
			}
		});
		
		// hex校验和 拆分为2行
		checkSumLabel = new Label(composite, SWT.NONE);
		checkSumLabel.setLayoutData(new GridData(SWT.BEGINNING, SWT.CENTER, false, false, 1, 1));
		checkSumLabel.setText(Messages.ChiponProgramUtilizationComponent_19);

		checkSumValue = new Text(composite, SWT.NONE);
		checkSumValue.setEditable(false);
		checkSumValue.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, false, 2, 1));
		checkSumValue.setText(model.getCheckSumValue1()); // $NON-NLS-1$

		new Label(composite, SWT.NONE).setLayoutData(new GridData(SWT.FILL, SWT.CENTER, false, false, 1, 1));

		checkSumValue1 = new Text(composite, SWT.NONE);
		checkSumValue1.setEditable(false);
		checkSumValue1.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, false, 2, 1));
		checkSumValue1.setText(model.getCheckSumValue2()); // $NON-NLS-1$
		// 空行占位
		new Label(composite, SWT.NONE).setLayoutData(new GridData(SWT.CENTER, SWT.CENTER, false, false, 3, 4)); // 3列4行占空

		// ##################################################### 显示信息3   该部分设计对11KA02适配后调整到项目属性页
		/*Group configInfoGroup = new Group(composite, SWT.NONE);
		configInfoGroup.setFont(SWTResourceManager.getFont("System", 11, SWT.ITALIC)); //$NON-NLS-1$
		configInfoGroup.setText(Messages.ChiponProgramUtilizationComponent_23);
		configInfoGroup.setLayout(new GridLayout(3, true));
		configInfoGroup.setLayoutData(new GridData(SWT.FILL, SWT.FILL, false, false, 3, 1));
		
		Label bufconfigMessage = new Label(configInfoGroup, SWT.NONE);
		bufconfigMessage.setLayoutData(new GridData(SWT.CENTER, SWT.CENTER, false, false, 3, 1)); // 分割线
		bufconfigMessage.setText("-----------------------"); //$NON-NLS-1$
		//
		Label model1show = new Label(configInfoGroup, SWT.NONE);
		model1show.setFont(SWTResourceManager.getFont("System", 9, SWT.NORMAL)); //$NON-NLS-1$
		model1show.setLayoutData(new GridData(SWT.LEFT, SWT.CENTER, false, false, 1, 1));
		model1show.setText(Messages.ChiponProgramUtilizationComponent_26);

		ConfigProtectValueCombo = new Combo(configInfoGroup, SWT.READ_ONLY);
		ConfigProtectValueCombo.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, true, 2, 1));
		String[] ispmodset = { "NO_ISP", "ODMOD", "ODMOD+NO_ISP", "NO SET" }; //$NON-NLS-1$ //$NON-NLS-2$ //$NON-NLS-3$ //$NON-NLS-4$
		ConfigProtectValueCombo.setItems(ispmodset);
		ConfigProtectValueCombo.select(0);
		ConfigProtectValueCombo.addModifyListener(new ModifyListener() {
			@Override
			public void modifyText(ModifyEvent e) {
				if (isHavaFocus == true) {
					protectSetButtonSys.setEnabled(true);
					configMessage.setText(Messages.ChiponProgramUtilizationComponent_31);
				}
			}
		});
		// ==================================================安全配置的加密选择
		Label model2show = new Label(configInfoGroup, SWT.NONE);
		model2show.setFont(SWTResourceManager.getFont("System", 9, SWT.NORMAL)); //$NON-NLS-1$
		model2show.setText(Messages.ChiponProgramUtilizationComponent_33);
		model2show.setLayoutData(new GridData(SWT.LEFT, SWT.CENTER, false, false, 1, 1));

		ConfigProtectModeCombo = new Combo(configInfoGroup, SWT.READ_ONLY);
		ConfigProtectModeCombo.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, true, 2, 1));
		String[] protectmodset = { Messages.ChiponProgramUtilizationComponent_34, Messages.ChiponProgramUtilizationComponent_35, Messages.ChiponProgramUtilizationComponent_36, Messages.ChiponProgramUtilizationComponent_37 };
		ConfigProtectModeCombo.setItems(protectmodset);
		ConfigProtectModeCombo.select(3);
		ConfigProtectModeCombo.addModifyListener(new ModifyListener() {
			@Override
			public void modifyText(ModifyEvent e) {
				if (isHavaFocus == true) {
					protectSetButtonSys.setEnabled(true);
					configMessage.setText(Messages.ChiponProgramUtilizationComponent_38);
				}
			}
		});
		// =============================================
		configMessage = new Label(configInfoGroup, SWT.NONE);
		configMessage.setForeground(SWTResourceManager.getColor(SWT.COLOR_RED));
		configMessage.setLayoutData(new GridData(SWT.LEFT, SWT.FILL, false, false, 2, 1));
		configMessage.setText("                                    "); // 要有宽度，不然要被优化占宽为0的不可见 //$NON-NLS-1$

		protectSetButtonSys = new Button(configInfoGroup, SWT.NONE);
		protectSetButtonSys.setLayoutData(new GridData(SWT.RIGHT, SWT.FILL, false, false, 1, 1));
		protectSetButtonSys.setFont(org.eclipse.wb.swt.SWTResourceManager.getFont("System", 9, SWT.NORMAL)); //$NON-NLS-1$
		protectSetButtonSys.setText(Messages.ChiponProgramUtilizationComponent_41);// 设置，这先生成到文件中，基于编译动作结果将内容往hex文件中添加
		protectSetButtonSys.setEnabled(false);
		protectSetButtonSys.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent e) {
				
				String valueString;
				switch (ConfigProtectValueCombo.getSelectionIndex()) {
				case 0:
					valueString = NO_ISP;
					break;
				case 1:
					valueString = ODMOD;
					break;
				case 2:
					valueString = ODMOD_NO_ISP;
					break;
				default:
					valueString = "FFFFFFFF"; //$NON-NLS-1$
					break;
				}
//				ChiponProgramUtilizationManager.getManager().getCOPP().setfDebugIspMode(valueString);
				String modeValueString = valueString;
				// #########################################################
				switch (ConfigProtectModeCombo.getSelectionIndex()) {
				case 0:
					valueString = Protect_A;
					break;
				case 1:
					valueString = Protect_C2;
					break;
				case 2:
					valueString = Protect_D;
					break;
				default:
					valueString = Protect_NO;
					break;
				}
//				ChiponProgramUtilizationManager.getManager().getCOPP().setfProtectSet(valueString);
				String protectValueString = valueString;
				// ################################################ 生成文件方案
				//将模式和加密配置写入文件中
				IProject project = UiActivator.getDefault().getCurrentProject();
				if(project==null) {
					MessageDialog.openWarning(null, "警告", "没有选择有效项目!");
					return;
				}
				WriteFile.writeFile(modeValueString, protectValueString, project);
				//=================================================================================
				
				// 生效完成，update to ChipOnProjectProperties and syc to hex file
				configMessage.setText(""); //$NON-NLS-1$
				try {
//					ChiponProgramUtilizationManager.getManager().getCOPP().save();
					ChipOnProjectProperties cpp = ProjectPropertyManager.getPropertyManager(project).getProjectProperties();
					cpp.setfDebugIspMode(modeValueString);
					cpp.setfProtectSet(protectValueString);
					cpp.save();
					// 如果项目有了hex文件，同步到hex中的不影响增量编译功能
					IManagedBuildInfo bi = ManagedBuildManager.getBuildInfo(project);
					IConfiguration[] configs = bi.getManagedProject().getConfigurations();
					// String configsNames[]=bi.getConfigurationNames();
					for (int i = 0; i < configs.length; i++) {
						IConfiguration buildcfg = configs[i];
						String cfgName = buildcfg.getName();
						IBuildMacroProvider provider = ManagedBuildManager.getBuildMacroProvider();
						String buildArtifactName = provider.getMacro("BuildArtifactFileBaseName", IBuildMacroProvider.CONTEXT_CONFIGURATION, buildcfg, true).getStringValue();
						buildArtifactName = provider.resolveValue(buildArtifactName, "", " ", IBuildMacroProvider.CONTEXT_CONFIGURATION, buildcfg);
						// String fileName = buildArtifactName + "." + buildcfg.getArtifactExtension();
						String fileName = buildArtifactName + "." + "hex";
						IFile hexfileget = project.getFile(cfgName + "/" + fileName);
						if (hexfileget.exists())
							ChiponNotBuilderHexDo.addHexMessage(project, hexfileget.getLocation().toString(), true);
					}
					// ######################################################################################################
					project.refreshLocal(IResource.DEPTH_INFINITE, null);

				} catch (Exception e1) {
					configMessage.setText(Messages.ChiponProgramUtilizationComponent_50);
					e1.printStackTrace();
				}
				protectSetButtonSys.setEnabled(false);
			}
		});*/
		
	}

	/**
	 * 刷新视图上的信息
	 */
	public void refreshProjectInfo() {
		if(!chipProjectNameLabel.isDisposed()) {//视图打开才可进行项目信息的更新

			chipProjectNameLabel.setText(model.getProjectName()); //$NON-NLS-1$
			chipProjectModeLabel.setText(model.getBuildMode()); //$NON-NLS-1$
			chipNameLabel.setText(model.getChipType()); //$NON-NLS-1$
			chipToolLabel.setText(model.getChipTool()); //$NON-NLS-1$
			checkSumValue.setText(model.getCheckSumValue1()); //$NON-NLS-1$
			checkSumValue1.setText(model.getCheckSumValue2()); //$NON-NLS-1$
			
			ramProgressChart.setData(RectangleChart.KEY, ChiponProgramUtilizationManager.getManager().getRamProgresChartValue());
			flashProgresChart.setData(RectangleChart.KEY, ChiponProgramUtilizationManager.getManager().getFlashProgresChartValue());
		}
	}
	
	/**
	 * 项目信息视图是否打开
	 * @return
	 */
	public boolean isDispose() {
		if(chipProjectNameLabel.isDisposed()) {
			return true;
		}
		return false;
	}

	
	/**
	 * 当模型改变时，会调用此方法
	 * 调用次数太多了，影响性能
	 */
	@Override
	public void notifyChanged(Notification notification) {
//		int featureID = notification.getFeatureID(getClass());
		
//		if(!chipProjectNameLabel.isDisposed()) {
//			switch (featureID) {
//		        case ConfigPackage.CHIPON_MESSAGE_CONFIG_MODEL__CHIP_TYPE:
//		        	chipNameLabel.setText(model.getChipType()); //$NON-NLS-1$
//		        	break;
//		        case ConfigPackage.CHIPON_MESSAGE_CONFIG_MODEL__CHIP_TOOL:
//		        	chipToolLabel.setText(model.getChipTool()); //$NON-NLS-1$
//		        	break;
//		        case ConfigPackage.CHIPON_MESSAGE_CONFIG_MODEL__RAM_VALUE:
//		        	ramProgressChart.setData(RectangleChart.KEY, ChiponProgramUtilizationManager.getManager().getRamProgresChartValue());
//		        	break;
//		        case ConfigPackage.CHIPON_MESSAGE_CONFIG_MODEL__FLASH_VALUE:
//		        	flashProgresChart.setData(RectangleChart.KEY, ChiponProgramUtilizationManager.getManager().getFlashProgresChartValue());
//		        	break;
//		        case ConfigPackage.CHIPON_MESSAGE_CONFIG_MODEL__CHECK_SUM_VALUE1:
//		        	checkSumValue.setText(model.getCheckSumValue1()); //$NON-NLS-1$
//		        	break;
//		        case ConfigPackage.CHIPON_MESSAGE_CONFIG_MODEL__PROJECT_NAME:
//		        	chipProjectNameLabel.setText(model.getProjectName()); //$NON-NLS-1$
//		        	break;
//		        case ConfigPackage.CHIPON_MESSAGE_CONFIG_MODEL__CHECK_SUM_VALUE2:
//		        	checkSumValue1.setText(model.getCheckSumValue2()); //$NON-NLS-1$
//		        	break;
//		        case ConfigPackage.CHIPON_MESSAGE_CONFIG_MODEL__BUILD_MODE:
//		        	chipProjectModeLabel.setText(model.getBuildMode()); //$NON-NLS-1$
//		        	break;
//			}
			
//			chipProjectNameLabel.setText(model.getProjectName()); //$NON-NLS-1$
//			chipProjectModeLabel.setText(model.getBuildMode()); //$NON-NLS-1$
//			chipNameLabel.setText(model.getChipType()); //$NON-NLS-1$
//			chipToolLabel.setText(model.getChipTool()); //$NON-NLS-1$
//			checkSumValue.setText(model.getCheckSumValue1()); //$NON-NLS-1$
//			checkSumValue1.setText(model.getCheckSumValue2()); //$NON-NLS-1$
//			
//			ramProgressChart.setData(RectangleChart.KEY, ChiponProgramUtilizationManager.getManager().getRamProgresChartValue());
//			flashProgresChart.setData(RectangleChart.KEY, ChiponProgramUtilizationManager.getManager().getFlashProgresChartValue());
//		}
	}

	@Override
	public Notifier getTarget() {
		return null;
	}

	@Override
	public void setTarget(Notifier newTarget) {

	}

	@Override
	public boolean isAdapterForType(Object type) {
		return false;
	}
}
