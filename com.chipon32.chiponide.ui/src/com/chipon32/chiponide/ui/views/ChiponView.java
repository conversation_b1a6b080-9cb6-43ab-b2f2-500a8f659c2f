package com.chipon32.chiponide.ui.views;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileReader;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.eclipse.jface.dialogs.MessageDialog;
import org.eclipse.jface.text.BadLocationException;
import org.eclipse.jface.text.IDocument;
import org.eclipse.jface.text.ITextSelection;
import org.eclipse.jface.text.TextSelection;
import org.eclipse.jface.viewers.ComboViewer;
import org.eclipse.jface.viewers.DoubleClickEvent;
import org.eclipse.jface.viewers.IDoubleClickListener;
import org.eclipse.jface.viewers.ISelection;
import org.eclipse.jface.viewers.ISelectionChangedListener;
import org.eclipse.jface.viewers.SelectionChangedEvent;
import org.eclipse.jface.viewers.StructuredSelection;
import org.eclipse.jface.viewers.TreeViewer;
import org.eclipse.swt.SWT;
import org.eclipse.swt.custom.CTabFolder;
import org.eclipse.swt.custom.CTabItem;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.events.SelectionListener;
import org.eclipse.swt.graphics.FontData;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Text;
import org.eclipse.swt.widgets.TreeColumn;
import org.eclipse.ui.IEditorPart;
import org.eclipse.ui.IMemento;
import org.eclipse.ui.IViewSite;
import org.eclipse.ui.PartInitException;
import org.eclipse.ui.PlatformUI;
import org.eclipse.ui.editors.text.TextEditor;
import org.eclipse.ui.part.ViewPart;

import com.chipon32.chiponide.core.CoreActivator;
import com.chipon32.chiponide.core.chipondescription.ICategory;
import com.chipon32.chiponide.core.chipondescription.IDeviceDescription;
import com.chipon32.chiponide.core.chipondescription.IDeviceDescriptionProvider;
import com.chipon32.chiponide.core.chipondescription.IProviderChangeListener;
import com.chipon32.chiponide.ui.ChipOniohDeviceDescriptionProvider;
import com.chipon32.chiponide.core.utils.ChipNameContentProvider;
import com.chipon32.chiponide.core.utils.ChipNameLabelProvider;

import org.eclipse.swt.widgets.Label;

/*
 * 芯片信息视图
 * 
 */
public class ChiponView extends ViewPart {

	public static final String ID = "com.chipon32.chiponide.ui.view.chiponview"; //$NON-NLS-1$

	private static final String INFORMATION = Messages.ChiponView_0;

	private Composite fViewParent;
	private Composite fTop;
	
	private ComboViewer fCombo;
	
	// 文件夹的视图表
	private CTabFolder fTabFolder;
	private final List<CTabItem> fTabs = new ArrayList<CTabItem>(0);
	private Map<String, List<TreeColumn>> fTreeColumns;
	
	private IMemento fMemento;   //?  是什么？

	//芯片信息描述
	private IDeviceDescriptionProvider dmprovider = null;
	
	private IProviderChangeListener fProviderChangeListener;
	
	// 寄存器获取的文件所在路径————更换实现方法
	//private final IPathProvider fPathProvider = new ChipOnPathProvider(ChipOnPath.CHIPON_REGISTER);

	public ChiponView() {
	}

	/*
	 * (non-Javadoc) Method declared on IViewPart.
	 */
	@Override
	public void init(IViewSite site, IMemento memento) throws PartInitException {
		// Initialize the SuperClass and store the passed memento for use by
		// the individual methods.
		super.init(site, memento);
		fMemento = memento;
	}

	@Override
	public void saveState(IMemento memento) {
		// Save the current state of the viewer
		super.saveState(memento);
	}

	/**
	 * Create contents of the view part.
	 * 
	 * @param parent
	 */
	@Override
	public void createPartControl(Composite parent) {
		// 总的绘图对象
		fViewParent = parent;
		// 型号变化监听者
		fProviderChangeListener = new ProviderChangeListener();
		// 监听属性
		// fWorkbenchSelectionListener = new WorkbenchSelectionListener();
		dmprovider = ChipOniohDeviceDescriptionProvider.getDefault();//

		if (dmprovider != null) {
			// setup ourself as a change listener for the
			// DeviceDescriptionProvider
			dmprovider.addProviderChangeListener(fProviderChangeListener); // ProviderChangeListener
		}
		// 重新绘图
		fViewParent.setLayout(new GridLayout());
		fTreeColumns = new HashMap<String, List<TreeColumn>>();

		GridLayout gl = new GridLayout(3, true);
		gl.marginHeight = 0;
		gl.marginWidth = 0;
		
		// 顶部的型号绘图
		fTop = new Composite(fViewParent, SWT.NONE);
		fTop.setLayout(gl);
		fTop.setLayoutData(new GridData(SWT.FILL, SWT.NONE, true, false));
		// 型号选择框
		fCombo = new ComboViewer(fTop, SWT.READ_ONLY | SWT.DROP_DOWN);
		fCombo.getControl().setLayoutData(new GridData(SWT.FILL, SWT.NONE, true, false));
		// 型号内容来源
		fCombo.setContentProvider(new ChipNameContentProvider());
		fCombo.setLabelProvider(new ChipNameLabelProvider());

		// register the combo as a Selection Provider
		getSite().setSelectionProvider(fCombo);
		new Label(fTop, SWT.NONE);
		new Label(fTop, SWT.NONE);
		
		//#############################################
		// 创建寄存内容表页
		fTabFolder = new CTabFolder(fViewParent, SWT.BORDER);
		fTabFolder.setTabPosition(SWT.BOTTOM);
		fTabFolder.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, true));
		
		//** 监听代码源
		fCombo.addSelectionChangedListener(new ComboSelectionChangedListener());
		fTabFolder.addSelectionListener(new TabFolderSelectionListener());
		
		// 内容刷新
		providerChanged();

		// getSite().getWorkbenchWindow().getSelectionService().addPostSelectionListener(fWorkbenchSelectionListener);
	}

	private void providerChanged() {
		// pass the current ChipOniohDeviceDescriptionProvider to the
		// DeviceListContentProvider
		fCombo.setInput(dmprovider);

		String show = null;
		if (fMemento != null)
			show = fMemento.getString("combovalue"); //$NON-NLS-1$

		if (show == null || "".equals(show)) { //$NON-NLS-1$
			show = (String) fCombo.getElementAt(0);
		}
		
		if (show != null) {
			// This next step will cause a SelectionChangeEvent which in turn
			// will load the sources, tabs and treeviewers
			fCombo.setSelection(new StructuredSelection(show), true);
		}

	}

	// 下拉框触发事件
	private class ComboSelectionChangedListener implements ISelectionChangedListener {

		@Override
		public void selectionChanged(SelectionChangedEvent event) {
			// 
			String devicename = (String) ((StructuredSelection) event.getSelection()).getFirstElement();

			if (fMemento != null) {
				// persist the selected mcu
				fMemento.putString("combovalue", devicename); //$NON-NLS-1$
			}
			// 获取描述
			IDeviceDescription device = dmprovider.getDeviceDescription(devicename);
			if (device == null) {
				MessageDialog.openError(fViewParent.getShell(), Messages.ChiponView_4, dmprovider.getErrorMessage());
			} else {
				updateTabs(fTabFolder, device, devicename); //**
				// System.out.println(device);
				// System.out.println(devicename);
				// setFocus();
			}

		}

	}

	// 选项卡触发事件
	private static class TabFolderSelectionListener implements SelectionListener {

		public TabFolderSelectionListener() {
		}

		@Override
		public void widgetDefaultSelected(SelectionEvent e) {
			// not called for a CTabFolder
		}

		@Override
		public void widgetSelected(SelectionEvent e) {
			CTabItem ti = (CTabItem) e.item;
			if (!ti.getText().equals(INFORMATION)) {

				((TreeViewer) ti.getData()).refresh();  //**
				((TreeViewer) ti.getData()).getControl().setFocus();
				
			}

		}
	}

	@Override
	public void dispose() {
		// remove the listeners from their objects
		dmprovider.removeProviderChangeListener(fProviderChangeListener);
		// getSite().getWorkbenchWindow().getSelectionService().removePostSelectionListener(fWorkbenchSelectionListener);
		super.dispose();
	}

	private void updateTabs(CTabFolder parent, IDeviceDescription device, String devicename) {
		TreeViewer tv = null;
		CTabItem cti = null;
		String activetabname = null;
		// StyledText syText=null;
		Text syText = null;

		// StyledText syText = createText(fTabFolder, devicename);
		// Text syText = createText(fTabFolder, devicename);
		// fTabItem.setData(syText.getText());
		//
		// fTabItem.setControl(syText);

		// Remember the name of the active tab
		if (parent.getSelection() != null) {
			activetabname = parent.getSelection().getText();
		}

		//** 获取  芯片信息描述和寄存器列表 两种分类  =》RegisterCategory()和InformationCategory()
		List<ICategory> categories = device.getCategories(); 

		// Strategy: iterate over all elements of the category list, and try to
		// get a CTabItem control from the existing CTabItems. If this fails
		// with an Exception a new CTabItem control is created and added to the
		// internal list fTabs. Also a TreeViewer is created and linked to the
		// tab.
		// After the list has been iterated, any CTabItems that
		// are left over in the list will be disposed.
		int cticounter = 0;
		for (ICategory cat : categories) {
			try {
				cti = fTabs.get(cticounter);
			} catch (IndexOutOfBoundsException ioobe) {
				// Tab did not exist: create a new CTabItem and an associated
				// TreeViewer
				cti = new CTabItem(parent, SWT.NONE);
				if (cat.getName().equals(INFORMATION)) {
					syText = createText(parent, devicename);  //创建加载芯片信息视图
					cti.setData(syText);
					cti.setControl(syText);

				} else {
					tv = createTreeView(parent, cat);  //创建加载寄存器列表信息的视图
					cti.setData(tv);
					cti.setControl(tv.getControl());
				}
				fTabs.add(cti);
			}
			cti.setText(cat.getName());
			if (!cat.getName().equals(INFORMATION)) {

				tv = (TreeViewer) cti.getData();
				updateTreeView(tv, cat);          //

				tv.setInput(cat);
			} else {
				syText = createText(parent, devicename);     //
				cti.setData(syText);
				cti.setControl(syText);
			}
			// Check if this tab should be made active
			if (cat.getName().equals(activetabname)) {
				parent.setSelection(cti);
			}
			cticounter++;
		}

		// dispose and remove any remaining CTabItems and associated TreeViewers
		// The loop will end with an Exception once the last list element has
		// been removed and we try to read the same index again.
		try {
			while (true) {
				cti = fTabs.get(cticounter);
				cti.getControl().dispose();
				cti.dispose();
				fTabs.remove(cticounter); // all remaining items are moved up
			}
		} catch (IndexOutOfBoundsException ioobe) {
			// do nothing, as this exception is expected
		}

		// If no Tab is active then activate the first one
		if (parent.getSelectionIndex() == -1) {
			parent.setSelection(0);
		}

	}

	// 创建text加载芯片KF32DL52xxx.info（芯片描述）的信息
	private Text createText(Composite parent, String devicename) {
//		String filePath = fPathProvider.getPath().toOSString() + "\\" + devicename + ".info"; //ChionCC32文件中  //$NON-NLS-1$ //$NON-NLS-2$
		String filePath = CoreActivator.getFilePathFromPlugin("chipregister") + "\\" + devicename + ".info";	
	    
		Text tv = new Text(parent, SWT.NONE | SWT.WRAP);
		tv.setBackground(Display.getDefault().getSystemColor(SWT.COLOR_WHITE));
		tv.setEditable(false);
		BufferedReader in = null; //从字符输入流中读取文本，缓冲各个字符
		try {
			File file = new File(filePath);
			if (file.exists()) {
				in = new BufferedReader(new FileReader(filePath));
				String line;
				StringBuffer buffer = new StringBuffer();
				while ((line = in.readLine()) != null) {
					if (line != null) {
						buffer.append("\t" + line); //$NON-NLS-1$
						buffer.append("\n"); //$NON-NLS-1$
					}
				}
				tv.setText(buffer.toString());
				in.close();
			} else {
				// MessageDialog.openError(tv.getShell(), "ChipON类别 ", filePath+" 文件不存在！");
				return null;
			}
		} catch (FileNotFoundException fnfe) {
			fnfe.printStackTrace();
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return tv;
	}

	//创建TreeViewer加载KF32DL52xxxx.info寄存器列表的信息
	private TreeViewer createTreeView(Composite parent, ICategory category) {
		TreeViewer tv = new TreeViewer(parent, SWT.BORDER | SWT.FULL_SELECTION);
		
		tv.setContentProvider(new DeviceModelContentProvider());
		tv.setLabelProvider(new EntryLabelProvider());
		
		tv.getTree().setLayoutData(new GridData(GridData.FILL_BOTH));
		tv.getTree().setHeaderVisible(true);
		tv.getTree().setLinesVisible(true);

		/*
		 * 添加双击监听器 实现向编辑器光标处添加相应指令的功能
		 */
		tv.addDoubleClickListener(new IDoubleClickListener() {
			@Override
			public void doubleClick(DoubleClickEvent event) {
				String str = event.getSelection().toString();
				str = str.substring(1, str.length() - 1);
				IEditorPart editorPart = PlatformUI.getWorkbench().getActiveWorkbenchWindow().getActivePage().getActiveEditor();
				if (editorPart != null && editorPart instanceof TextEditor) {
					ISelection selection = editorPart.getEditorSite().getSelectionProvider().getSelection();
					if (selection instanceof ITextSelection) {
						ITextSelection sel = (ITextSelection) selection;
						IDocument doc = ((TextEditor) editorPart).getDocumentProvider().getDocument(editorPart.getEditorInput());
						try {
							doc.replace(sel.getOffset(), 0, str);

						} catch (BadLocationException e) {
							e.printStackTrace();
						}
						editorPart.getEditorSite().getSelectionProvider().setSelection(new TextSelection(sel.getOffset() + str.length(), 0));
					}
					editorPart.setFocus();
				}
				// System.out.println("tv:str : "+str);
			}
		});

		tv.setInput(category);//刷新treeviewer

		return tv;
	}

	private void updateTreeView(TreeViewer parent, ICategory cat) {
		List<TreeColumn> columnlist;
		// 获取tabItem的名称
		String catname = cat.getName();
		// have a somewhat reasonable layout regardless of font size.
		// Yes, I know that height is not the same as width, but with
		// proportional fonts its the next best thing.
		FontData curfd[] = parent.getTree().getFont().getFontData();
		int approxfontwidth = curfd[0].getHeight();

		// See if a column layout for this Category already exists
		columnlist = fTreeColumns.get(catname);
		if (columnlist == null) {
			// No: create the columns for this category name
			columnlist = new ArrayList<TreeColumn>(cat.getColumnCount());

			// TODO: pass more information from the Category, like alignment,
			// icon etc.
			String[] labels = cat.getColumnLabels();
			int[] widths = cat.getColumnDefaultWidths();

			for (int i = 0; i < labels.length; i++) {
				TreeColumn tc = new TreeColumn(parent.getTree(), SWT.LEFT);
				tc.setWidth(widths[i] * approxfontwidth);
				tc.setResizable(true);
				tc.setMoveable(true);
				tc.setText(labels[i]);
				columnlist.add(tc);
			}
			fTreeColumns.put(catname, columnlist);
		}
	}

	private class ProviderChangeListener implements IProviderChangeListener {
		@Override
		public void providerChange() {
			// We don't know which Threat this comes from.
			// Assume its not the SWT Threat and act accordingly
			fViewParent.getDisplay().asyncExec(new Runnable() {
				@Override
				public void run() {
					providerChanged();
				}
			}); // Runnable
		}
	}

	@Override
	public void setFocus() {
		// Set the focus
		// Passing the focus request to the treeviewer of the selected tab
		CTabItem item = fTabFolder.getSelection();
		if (item != null) {
			if (!item.getText().equals(INFORMATION)) {
				TreeViewer tv = (TreeViewer) item.getData();
				tv.getControl().setFocus();
			}
		}

	}
}
