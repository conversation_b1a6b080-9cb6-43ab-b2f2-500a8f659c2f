package com.chipon32.chiponide.ui.views;

import org.eclipse.jface.viewers.ISelection;
import org.eclipse.jface.viewers.IStructuredSelection;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.IEditorPart;
import org.eclipse.ui.IPerspectiveDescriptor;
import org.eclipse.ui.ISelectionListener;
import org.eclipse.ui.ISelectionService;
import org.eclipse.ui.IWorkbenchPage;
import org.eclipse.ui.IWorkbenchPart;
import org.eclipse.ui.IWorkbenchWindow;
import org.eclipse.ui.PlatformUI;
import org.eclipse.ui.navigator.resources.ProjectExplorer;
import org.eclipse.ui.part.ViewPart;
import org.eclipse.ui.texteditor.ITextEditor;
import org.osgi.service.prefs.BackingStoreException;

import java.io.IOException;
import java.io.InputStream;

import org.eclipse.cdt.core.model.ICElement;
import org.eclipse.cdt.core.model.ICProject;
import org.eclipse.cdt.managedbuilder.core.ManagedBuildManager;
import org.eclipse.core.resources.IFile;
import org.eclipse.core.resources.IProject;
import org.eclipse.core.resources.IResource;
import org.eclipse.core.runtime.CoreException;
import org.eclipse.core.runtime.IAdaptable;

import com.chipon32.chiponide.core.config.ChiponMessageConfigModel;
import com.chipon32.chiponide.core.properties.ChipOnProjectProperties;
import com.chipon32.chiponide.core.properties.ProjectPropertyManager;
import com.chipon32.chiponide.mbs.ChiponBuilder;
import com.chipon32.chiponide.ui.UiActivator;
import com.chipon32.chiponide.ui.util.TargetHexFileMonitorUtil;
import com.chipon32.hex.core.HexFileParser;
import com.chipon32.hex.core.Memory;
import com.chipon32.hex.core.util.IConfigurationProvider;
import com.chipon32.util.provider.ConfigurationFactory;


/**
 * 资源使用率视图的最上层应用
 * 视图界面
 * <AUTHOR> @since 
 */
public class ProgramUtilizationView extends ViewPart{
	public static final String ID = "com.chipon32.chiponide.ui.views.ProgramUtilization"; //$NON-NLS-1$
	
	public ProgramUtilizationView() {
	}
	
	// 芯片自身信息绘图，包括型号，id号  使用率  和  校验和配置字的视图实现类的前半部分,被管理器实现，这里获取出来执行操作作用
	private ChiponProgramUtilizationComponent  component;
	private ChiponMessageConfigModel model;
	private Memory memory;
	// 选择监听类
	private ISelectionListener	fWorkbenchSelectionListener;
	
	private String activeBuildMode = "";
	//##################################################################################
	/**
	 * 创建视图的实现
	 */
	@Override
	public void createPartControl(Composite parent) {
		model = ChiponProgramUtilizationManager.getManager().getModel();
		IProject currentProject = UiActivator.getDefault().getCurrentProject();
		if(currentProject != null && currentProject.exists()) {
			this.refreshModel(currentProject);
		}
		component = new ChiponProgramUtilizationComponent(parent, PROP_TITLE, model);
		// 添加视图内容，包括芯片型号，id号，校准字，资源使用进度条，是否代码实现配置字的功能按钮
		component.createPartControl();
		// 视图内容默认值方法实现，型号，id，校验和
//		component.setDefaultValue();
		model.eAdapters().add(component);//component上添加了唤醒机制notifyChanged(xx)
		// 管理者 的项目设入，可以追踪项目，获取项目下的资源信息
//		manager.setModel(ChiponProgramUtilizationManager.model);
		// 建立监听对象，并将对象追加到总的系统中
		fWorkbenchSelectionListener = new WorkbenchSelectionListener();		
		getSite().getWorkbenchWindow().getSelectionService().addPostSelectionListener(fWorkbenchSelectionListener);		
	}
	
	/**
	 * 监听类的监听对象事件
	 * <AUTHOR>
	 *
	 */
	private class WorkbenchSelectionListener implements ISelectionListener{

		@Override
		public void selectionChanged(IWorkbenchPart part, ISelection selection) {
			if (!component.isDispose()){//判断“项目信息”视图是否打开，视图打开状态下才进行内容更新
				/** 如果事件没有发生在资源管理器视图 和编辑器中,则不做任何处理 **/
				if(!(part instanceof ProjectExplorer || part instanceof ITextEditor) ){
					return;
				}
				
				//如果当前是Debug透视图，则选中“项目信息”视图才执行更新信息动作
				if(isDebugPerspective()) {
					if(!component.isHavaFocus) {
						return;
					}
				}
				
				// 从事件中获取当前被激活的项目
				IProject currentProject = getProject(part);
				if(currentProject == null || !currentProject.isOpen()){
					return;
				}
				
				//项目相同且未进行构建则不更新项目视图
				ChipOnProjectProperties cpp = ProjectPropertyManager.getPropertyManager(currentProject).getProjectProperties();
				activeBuildMode = ManagedBuildManager.getBuildInfoLegacy(currentProject).getConfigurationName();
				if(currentProject.getName().equals(model.getProjectName()) && model.getBuildMode().equals(activeBuildMode) 
						&& cpp.getChipName().equals(model.getChipType())) {
					if(!ChiponBuilder.canRefresh) {
						return;
					}
				}
				
				//刷新视图 **
				refreshModel(currentProject);
				component.refreshProjectInfo();
				//更新项目属性信息
				
				
				ChiponBuilder.canRefresh = false;
				component.isHavaFocus=false;
			}
		}
	}
	
	
	/**
	 * 根据项目更新模型数据
	 * @param project
	 */
	public void refreshModel(IProject project) {
		ChipOnProjectProperties cpp = ProjectPropertyManager.getPropertyManager(project).getProjectProperties();
		model.setProjectName(project.getName());
		model.setChipType(cpp.getChipName());
		model.setChipTool(cpp.getCompilerType());
		
		memory = this.getMemory(project);
		String strbuf = gethexsumvaule(memory);
		model.setCheckSumValue1(strbuf.substring(0,16));
		model.setCheckSumValue2(strbuf.substring(16,32));
		
		model.setBuildMode(ManagedBuildManager.getBuildInfoLegacy(project).getConfigurationName());
		ChiponProgramUtilizationManager.getManager().refurbishFlashRamProgresChartValueOrg();
		//更新项目属性里的Flash与ram使用率
		try {
			if(model.getRamValue()!=null && model.getFlashValue()!=null) {
				if(model.getBuildMode().equalsIgnoreCase("release")) {
					cpp.setfrRamUsed(model.getRamValue());
					cpp.setfrFlashUsed(model.getFlashValue());
					cpp.save();
				}else if(model.getBuildMode().equalsIgnoreCase("debug")) {
					cpp.setfrRamUsed(model.getRamValue());
					cpp.setfrFlashUsed(model.getFlashValue());
					cpp.save();
				}
			}
		}catch(BackingStoreException e) {
			e.printStackTrace();
		}
	}


    @Override
    public void setFocus() {
        // 选择下的非项目和文件激活也支持的更新，此时才会提示用户保存输入信息
    	component.isHavaFocus=true;
    }

    /**
     * 判断当前是否为调试视图
     * @return
     */
    private boolean isDebugPerspective() {
        IWorkbenchWindow window = PlatformUI.getWorkbench().getActiveWorkbenchWindow();
        if (window != null) {
            IWorkbenchPage page = window.getActivePage();
            if (page != null) {
                IPerspectiveDescriptor perspective = page.getPerspective();
                if (perspective != null) {
                    String perspectiveId = perspective.getId();
                    return "org.eclipse.debug.ui.DebugPerspective".equals(perspectiveId);
                }
            }
        }
        
        return false;
    }

	/**
	 * 基于视图或编辑器的获取关联的项目的方法
	 * @param part
	 * @return
	 */
	private IProject getProject(IWorkbenchPart part){
		IProject project = null;
		// 如果是项目资源管理器视图
		if(part instanceof ProjectExplorer){
			ISelectionService service = PlatformUI.getWorkbench().getActiveWorkbenchWindow().getSelectionService();
			ISelection selection = service.getSelection();
			// 如果是选择容器类，从上面的方法的选择项进行项目获取并返回
			if(selection instanceof IStructuredSelection){
				project = getProject((IStructuredSelection) selection);
				return project;
			}
			//如果触发的不是有效的选中，还是使用编辑器的反向获取项目
			if(project == null){
				// 如果激活了其编辑器，获取文件类的文件获取项目
				IEditorPart editorpart = PlatformUI.getWorkbench().getActiveWorkbenchWindow().getActivePage().getActiveEditor();
				if(editorpart != null){
					Object obj =  editorpart.getEditorInput().getAdapter(IFile.class);
					if(obj != null){
						project = ((IFile)obj).getProject();
						return project;
					}
				}
			}
		}
		
		// 如果是编辑器视图，从编辑器的文件类获取项目
		if(part instanceof ITextEditor){
			IWorkbenchPage workbenchPage = PlatformUI.getWorkbench().getActiveWorkbenchWindow().getActivePage();
			if(workbenchPage==null)return null;
			IEditorPart editorpart = workbenchPage.getActiveEditor();
			if(editorpart != null){
				Object obj =  editorpart.getEditorInput().getAdapter(IFile.class);
				if(obj != null){
					project = ((IFile)obj).getProject();
					return project;
				}
			}
		}
		
		
		return null;
	}
	
	/**
	 * 项目选中类的获取项目对象方法
	 * @param selection
	 * @return
	 */
	private IProject getProject(IStructuredSelection selection){
		// 选中对象
		Object item = selection.getFirstElement();
		if (item == null) {
			return null;
		}
		// 
		IProject project = null;
		// 选中的是项目
		if (item instanceof IProject) {
			project = (IProject) item;
			return project;
		}
		// 选中的是资源
		else if (item instanceof IResource) {
			project = ((IResource) item).getProject();
			return project;
		} 
		// 选中的是适配器对象
		else if (item instanceof IAdaptable) {
			IAdaptable adaptable = (IAdaptable) item;
			// 项目类尝试
			project = (IProject) adaptable.getAdapter(IProject.class);
			if (project == null) {
				//C项目类尝试   Try ICProject -> IProject
				ICProject cproject = (ICProject) adaptable.getAdapter(ICProject.class);
				if (cproject == null) {
					//C元素类尝试   Try ICElement -> ICProject -> IProject
					ICElement celement = (ICElement) adaptable.getAdapter(ICElement.class);
					if (celement != null) {						
						cproject = celement.getCProject();
					}
				}
				if (cproject != null) {
					project = cproject.getProject();
				}
			}
		}
		
		// 结果返回
		return project;
	}
	
	/**
	 * @return 获得基于项目，并项目的活跃配置下的hex文件
	 */
	public IFile getBuildFile(IProject project){
		IFile file  = TargetHexFileMonitorUtil.getBuildTargetFileFromProject(project);
		if(file==null)return null;
		if(file.exists()){
			return file;
		}
		return null;
	}
	
	/**
	 *   基于获取项目的获取配置下的hex文件，进而解析，获取 memory内存化对象
	 * @return
	 */
	private Memory getMemory(IProject project ){
		InputStream is = null;
		try {
			if(getBuildFile(project)==null)
				return null;
			is = getBuildFile(project).getContents();
		} catch (CoreException e) {
			e.printStackTrace();
			return null;
		}
		HexFileParser parser = new HexFileParser(is, getConfigurationProvider());
		parser.parse();
		try {
			is.close();
		} catch (IOException e) {
			e.printStackTrace();
		}
		return parser.getMemory();
	}
	
	/**
	 * 基于获取型号名方法的获取型号的信息提供者
	 */
	private IConfigurationProvider getConfigurationProvider(){
		return ConfigurationFactory.getProvider(this.model.getChipType());
	}
	
	private String gethexsumvaule(Memory memory) {
		// TODO Auto-generated method stub
		String sum;
		try {
			sum = this.memory.getHexFlashSumVaule();
		} catch (NullPointerException e) {
			// e.printStackTrace();
			sum ="--------------------------------";
		}
		return sum;
	}

}
