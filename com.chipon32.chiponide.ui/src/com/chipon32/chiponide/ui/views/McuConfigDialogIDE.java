package com.chipon32.chiponide.ui.views;



import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.GC;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.widgets.Dialog;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Shell;
import org.eclipse.wb.swt.ResourceManager;
import org.eclipse.wb.swt.SWTResourceManager;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;



import org.eclipse.swt.widgets.Button;
import org.eclipse.swt.widgets.Event;
import org.eclipse.swt.widgets.Group;
import org.eclipse.swt.widgets.Listener;
import org.eclipse.swt.widgets.Table;
import org.eclipse.swt.widgets.TableColumn;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;

import com.chipon32.chiponide.ui.UiActivator;
import com.chipon32.hex.core.ConfigMessage;
import com.chipon32.hex.core.Memory;
import com.chipon32.hex.core.SectorElementVaule;
import com.chipon32.hex.core.util.IConfigurationProvider;
import com.chipon32.hex.ui.editorsconfig.ConfigCellModifier;
import com.chipon32.hex.ui.editorsconfig.ConfigContentProvider;

import com.chipon32.hex.ui.editorsconfig.ConfigTableLabelProvider;
import com.chipon32.hex.ui.editorssector.SectorCellModifier;
import com.chipon32.hex.ui.editorssector.SectorContentProvider;
import com.chipon32.hex.ui.editorssector.SectorTableLabelProvider;
import com.chipon32.util.ui.UIUtil;

import org.eclipse.jface.viewers.CellEditor;
import org.eclipse.jface.viewers.CheckboxCellEditor;
import org.eclipse.jface.viewers.ComboBoxCellEditor;
import org.eclipse.jface.viewers.TableViewer;

/**
 * 
 * 芯片的配置ide视图
 * <AUTHOR>
 *
 */
public class McuConfigDialogIDE extends Dialog {


	protected Shell 					containShell;
	protected Object 					result;
	private	 Point point;

	private IConfigurationProvider provider;
	private SectorElementVaule[] sector;
	private Memory memory;	
	
	private TableViewer tableViewer;
	private Table table;
	
	private ConfigMessage[] cms;
	private TableViewer ConfigViewTable;
	private Table configtable;
	
	private String[] abc={"A","B","C","D","E"};  // 初始值，激活时会替换combo的下拉存在内容 //$NON-NLS-1$ //$NON-NLS-2$ //$NON-NLS-3$ //$NON-NLS-4$ //$NON-NLS-5$

	public McuConfigDialogIDE(Shell parent,IConfigurationProvider prd) {
		super(parent);
		if(prd!=null )
		{
			try {
				String name=prd.getChipName();
				if(!name.contains("KF")) //$NON-NLS-1$
				{
					provider=null;
					return;
				}
			} catch (Exception e) {
				// TODO: handle exception
				provider=null;
				return;				
			}
			provider=prd;
		
			// 这里的memory在ide里面只有henx文件，这里只是借用空内容的结构
			memory=new Memory(provider,null);
			sector=memory.getFlashElement();
			//==============================================
			//如果文件存在，从文件中更新已有内容
			
			//==============================================
			int h=memory.configs.size();
			cms=new ConfigMessage[h];		
			for(int g=0;g<h;g++)
			{			
				cms[g]=memory.configs.get(g);
			}
		//==============================
		}
	}
	
	/**
	 * Open the dialog.
	 * @return the result
	 */
	public Object open() {
		createContents();
		setDialogLocation();	
		if(provider==null)
		{
			containShell.close();
			return null;
		}
		containShell.open();
		containShell.layout();
		Display display = getParent().getDisplay();
		while (!containShell.isDisposed()) {
			if (!display.readAndDispatch()) {
				display.sleep();
			}
		}
		return result;
	}
	
	
	public void setDialogLocation(){
		int height = getParent().getBounds().height;
		int width = getParent().getBounds().width;
		Point parentPoint = getParent().getLocation();
		int x = parentPoint.x + (width-containShell.getBounds().width)/2;
		int y = parentPoint.y + (height-containShell.getBounds().height)/2;
		point = new Point(x,y);
		containShell.setLocation(point);
	}
	/**
	 * Create contents of the dialog.
	 */
	private void createContents() {
		//================================================窗口创建		
		containShell = new Shell(getParent(), SWT.CLOSE | SWT.TITLE | SWT.SYSTEM_MODAL);
		containShell.setImage(ResourceManager.getPluginImage(UiActivator.PLUGIN_ID, "/icons/app64.gif")); //$NON-NLS-1$ //$NON-NLS-2$
		containShell.setModified(true);
		containShell.setMinimumSize(new Point(230, 150));
		containShell.setSize(545, 618);
		containShell.setText(Messages.McuConfigDialogIDE_8);				
		containShell.setLayout(new GridLayout(2, true));
		//==================================================用户配置字组
		Group ConfigBytes = new Group(containShell, SWT.NONE);
		ConfigBytes.setText(Messages.McuConfigDialogIDE_9);
		GridData gd_ConfigBytes = new GridData(SWT.FILL, SWT.FILL, true, true,2,1);
		gd_ConfigBytes.heightHint = 238;
		ConfigBytes.setLayoutData(gd_ConfigBytes);

		
							ConfigViewTable = new TableViewer(ConfigBytes, SWT.BORDER | SWT.FULL_SELECTION);
							configtable = ConfigViewTable.getTable();
							configtable.setBounds(10, 10, 466, 170);
						
						
							// 表格的字体定义
							configtable.setFont(SWTResourceManager.getFont("System", 10, SWT.NORMAL)); //$NON-NLS-1$
							// 不显示表格分割线
							configtable.setLinesVisible(true);
							// 显示表头
							configtable.setHeaderVisible(true);
							// 网格高度
							//------------------基于首元素确认行高的监听使整个表格适应-----------------------
							configtable.addListener(SWT.MeasureItem, new Listener() {
								   @Override
								public void handleEvent(Event event) 
								   {
								      // 事件行数据对象
									  Object obj = event.item.getData();
									  if(obj instanceof SectorElementVaule )
									  {
										  // 行对象转为的  GroupAddrCommData 变量
										  SectorElementVaule rd = (SectorElementVaule)obj;
										  // 非内容差异之间的实际代码
										  if(rd.getAddress() >= 0)
										  {
											  // 首个元素的指令结果，根据结果调整行高
											  String command = rd.getAddressHexValue();
											  GC gc = new GC(Display.getDefault());
											  int y = gc.stringExtent(command).y;
											  // 基于结果更新高度 ，额外多 13个像素
											  event.height = y + 13;
											  gc.dispose();  
										  }
									  }
								   }
								});
							// 表格第一列 的内容 为 地址的结果
							TableColumn tblclmnAddressconfig = new TableColumn(configtable, SWT.NONE);
							tblclmnAddressconfig.setWidth(99);
							tblclmnAddressconfig.setText(Messages.McuConfigDialogIDE_11);
							tblclmnAddressconfig.setAlignment(SWT.CENTER);
							tblclmnAddressconfig.setResizable(true);
							// 表格第二列的内容为
							TableColumn tblclmnCommand0config = new TableColumn(configtable, SWT.NONE);
							tblclmnCommand0config.setWidth(192);
							tblclmnCommand0config.setText(Messages.McuConfigDialogIDE_12);
							tblclmnCommand0config.setResizable(true);
							
							TableColumn tblclmnCommand1config = new TableColumn(configtable, SWT.NONE);
							tblclmnCommand1config.setWidth(400);
							tblclmnCommand1config.setText(Messages.McuConfigDialogIDE_13);
							tblclmnCommand1config.setResizable(true);		
							
							ConfigViewTable.setLabelProvider(new ConfigTableLabelProvider());
							ConfigViewTable.setContentProvider(new ConfigContentProvider());
							//  列数量的设入
							UIUtil.setDefaultProperties(ConfigViewTable);	
							
							// 单元格编辑器，对应8个地址的结果，根据GroupAddrCommData的修改方法进行结果进行处理
							CellEditor[] cellEditorsconfig = new CellEditor[3];
							cellEditorsconfig[0] = null;
							cellEditorsconfig[1] = new ComboBoxCellEditor(configtable,abc,SWT.READ_ONLY);  // 临时元素 ，修改获取内容时，替换编辑器，元素为配置信息的存在元素
							cellEditorsconfig[2] = null;
					
							ConfigViewTable.setCellEditors(cellEditorsconfig);
							// 单元格行编辑器，检测修改事件监听
							ConfigViewTable.setCellModifier(new ConfigCellModifier(ConfigViewTable));
							
			Button ReSetConfigVauleButton = new Button(ConfigBytes, SWT.NONE);
			ReSetConfigVauleButton.addSelectionListener(new SelectionAdapter() {
				@Override
				public void widgetSelected(SelectionEvent e) {
					//  cms和memory.configs对应同一个实体，因此界面直接修改了memory.configs的选择文本,机制未研究			
					memory.synchronizationConfigFromHex();		
					int h=memory.configs.size();
					cms=new ConfigMessage[h];		
					for(int g=0;g<h;g++)
					{			
						cms[g]=memory.configs.get(g);
					}	
					ConfigViewTable.setInput(cms);	
				}
			});
			ReSetConfigVauleButton.setBounds(401, 196, 72, 24);
			ReSetConfigVauleButton.setText(Messages.McuConfigDialogIDE_14);
			// 输入内容
			ConfigViewTable.setInput(cms);			
			//=========================================================
			//==================================================配置框闪存扇区保护配置
			Group pageConfigShell = new Group(containShell, SWT.NONE);
			pageConfigShell.setText(Messages.McuConfigDialogIDE_15);
			GridData gd_pageConfigShell = new GridData(SWT.FILL, SWT.FILL, true, true,2,1);
			gd_pageConfigShell.heightHint = 283;
			pageConfigShell.setLayoutData(gd_pageConfigShell);			
							//=================================================表视图对象
							tableViewer = new TableViewer(pageConfigShell, SWT.BORDER | SWT.FULL_SELECTION);
							table = tableViewer.getTable();
							table.setBounds(10, 10, 495, 214);
					
							// 表格的字体定义
							table.setFont(SWTResourceManager.getFont("System", 10, SWT.NORMAL)); //$NON-NLS-1$
							// 不显示表格分割线
							table.setLinesVisible(true);
							// 显示表头
							table.setHeaderVisible(true);
							// 网格高度
							//------------------基于首元素确认行高的监听使整个表格适应-----------------------
							table.addListener(SWT.MeasureItem, new Listener() {
								   @Override
								public void handleEvent(Event event) 
								   {
								      // 事件行数据对象
									  Object obj = event.item.getData();
									  if(obj instanceof SectorElementVaule )
									  {
										  // 行对象转为的  GroupAddrCommData 变量
										  SectorElementVaule rd = (SectorElementVaule)obj;
										  // 非内容差异之间的实际代码
										  if(rd.getAddress() >= 0)
										  {
											  // 首个元素的指令结果，根据结果调整行高
											  String command = rd.getAddressHexValue();
											  GC gc = new GC(Display.getDefault());
											  int y = gc.stringExtent(command).y;
											  // 基于结果更新高度 ，额外多 13个像素
											  event.height = y + 13;
											  gc.dispose();  
										  }
									  }
								   }
								});
							// 表格第一列 的内容 为 地址的结果
							TableColumn tblclmnAddress = new TableColumn(table, SWT.NONE);
							tblclmnAddress.setWidth(79);
							tblclmnAddress.setText(Messages.McuConfigDialogIDE_17);
							tblclmnAddress.setAlignment(SWT.CENTER);
							tblclmnAddress.setResizable(true);
							// 表格第二列的内容为
							TableColumn tblclmnCommand0 = new TableColumn(table, SWT.NONE);
							tblclmnCommand0.setWidth(106);
							tblclmnCommand0.setText(Messages.McuConfigDialogIDE_18);
							tblclmnCommand0.setResizable(true);
							
							TableColumn tblclmnCommand1 = new TableColumn(table, SWT.NONE);
							tblclmnCommand1.setWidth(70);
							tblclmnCommand1.setText(Messages.McuConfigDialogIDE_19);
							tblclmnCommand1.setResizable(true);
							
							TableColumn tblclmnCommand2 = new TableColumn(table, SWT.NONE);
							tblclmnCommand2.setWidth(104);
							tblclmnCommand2.setText(Messages.McuConfigDialogIDE_20);
							tblclmnCommand2.setResizable(true);
							
							TableColumn tblclmnCommand3 = new TableColumn(table, SWT.NONE);
							tblclmnCommand3.setWidth(113);
							tblclmnCommand3.setText(Messages.McuConfigDialogIDE_21);
							tblclmnCommand3.setResizable(true);
							
							tableViewer.setLabelProvider(new SectorTableLabelProvider());
							tableViewer.setContentProvider(new SectorContentProvider());
							//  列数量的设入
							UIUtil.setDefaultProperties(tableViewer);	
							
							// 单元格编辑器，对应8个地址的结果，根据GroupAddrCommData的修改方法进行结果进行处理
							CellEditor[] cellEditors = new CellEditor[5];
							cellEditors[0] = null;
							cellEditors[1] = null;
							cellEditors[2] = null;
							cellEditors[3] = new CheckboxCellEditor(table);
							cellEditors[4] = new CheckboxCellEditor(table);		
					
							tableViewer.setCellEditors(cellEditors);
							// 单元格行编辑器，检测修改事件监听
							tableViewer.setCellModifier(new SectorCellModifier(tableViewer));
							// 输入内容
							tableViewer.setInput(sector);	
		//==================================================扇区选择功能按钮全不选
		Button AllNotSelButton = new Button(pageConfigShell, SWT.NONE);
		AllNotSelButton.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent e) {
				for(int i=0;i<sector.length;i++)
				{
					sector[i].setIsCanRead(false);
					sector[i].setIsCanWrite(false);
				}
				tableViewer.setInput(sector);
			}
		});
		AllNotSelButton.setBounds(345, 243, 72, 22);
		AllNotSelButton.setText(Messages.McuConfigDialogIDE_22); // 全不选
		//==================================================扇区选择功能全选
		Button AllSelButton = new Button(pageConfigShell, SWT.NONE);
		AllSelButton.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent e) {
				for(int i=0;i<sector.length;i++)
				{
					sector[i].setIsCanRead(true);
					sector[i].setIsCanWrite(true);
				}
				tableViewer.setInput(sector);
			}
		});
		AllSelButton.setBounds(426, 243, 72, 22);
		AllSelButton.setText(Messages.McuConfigDialogIDE_23);
		//==================================================配置确认功能
		Button ConfigFire = new Button(containShell, SWT.CENTER);
		GridData gd_ConfigFire = new GridData(SWT.RIGHT, SWT.CENTER, true, true,1,1);
		gd_ConfigFire.heightHint = 38;
		gd_ConfigFire.widthHint = 133;
		ConfigFire.setLayoutData(gd_ConfigFire);
		ConfigFire.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent e) {
				//#########################配置字修改
				// 获取当前项目
//				final IProject project = UiActivator.getDefault().getCurrentProject();
				// 当前编辑器项目
//				final IProject editorProject = UiActivator.getDefault().getCurrentProjectFromEditor();		
				// -----------------------------------------------------------------------------------------------------------------				
				
				
				//###########################结束关窗
				containShell.close();
			}
		});		

		ConfigFire.setText(Messages.McuConfigDialogIDE_24);
		//==================================================退出配置功能
		Button CloseShellButton = new Button(containShell, SWT.CENTER);
		GridData gd_CloseShellButton = new GridData(SWT.LEFT, SWT.CENTER, true, true,1,1);
		gd_CloseShellButton.widthHint = 120;
		gd_CloseShellButton.heightHint = 42;
		CloseShellButton.setLayoutData(gd_CloseShellButton);
		CloseShellButton.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent e) {
				containShell.close();
			}
		});
		CloseShellButton.setText(Messages.McuConfigDialogIDE_25);

	
	}
}
