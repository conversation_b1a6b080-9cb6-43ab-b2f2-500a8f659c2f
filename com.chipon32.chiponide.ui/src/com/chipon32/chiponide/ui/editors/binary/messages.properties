BinEditorContributorClass.Copy=Copy
BinEditorContributorClass.Find=Find
SectionLabelProvider.Name=Name
BinOverviewPage.Title=Header
FromelfInputObserver.objImg=\ object/image 
FromelfInputObserver.NoTempFile=Failed to get Temp File
FromelfInputObserver.CancelledOp=\ cancelled
FromelfInputObserver.FailedOp=\ failed\n
FromElfPage.Refresh=Refresh
FromElfPage.Error=Error: 
SectionsPage.Title=Sections
SectionsPage.Number=Number
SectionsPage.NumberTooltip=The section number.
SectionsPage.Name=Name
SectionsPage.NameTooltip=The section name.
SectionsPage.Offset=ELF Offset
SectionsPage.OffsetTooltip=The offset of the section within the ELF image.
SectionsPage.Address=Address
SectionsPage.AddressTooltip=The section address.
SectionsPage.Size=Size (Bytes)
SectionsPage.SizeTooltip=The section size in bytes.
SectionsPage.Type=Type
SectionsPage.TypeTooltip=The section type.
SectionsPage.Flags=Flags
SectionsPage.FlagsTooltip=The section flags.
SegmentsPage.Title=Segments
SegmentsPage.Number=Number
SegmentsPage.NumberTooltip=The Segment number.
SegmentsPage.Offset=ELF Offset
SegmentsPage.OffsetTooltip=The offset of the Segment within the ELF image.
SegmentsPage.VirtualAddress=Virtual Address
SegmentsPage.VirtualAddressTooltip=The Virtual address at which the segment resides.
SegmentsPage.PhysicalAddress=Physical Address
SegmentsPage.PhysicalAddressTooltip=The Physical address at which the segment resides.
SegmentsPage.MemSize=Memory Size (Bytes)
SegmentsPage.MemSizeTooltip=The number of bytes in the memory image of the segment.
SegmentsPage.FileSize=File Size (Bytes)
SegmentsPage.FileSizeTooltip=The number of bytes in the file image of the segment.
SegmentsPage.Type=Type
SegmentsPage.TypeTooltip=The type of the segment.
SegmentsPage.Flags=Flags
SegmentsPage.FlagsTooltip=The flags applicable to the segment.
SymbolTableObserver.desc=Computing symbol table
SymbolTablePage.Size=Size
SymbolTablePage.Vis=Visibility
SymbolTablePage.Sect=Section
SymbolTablePage.Type=Type
SymbolTablePage.Bind=Binding
SymbolTablePage.Name=Name
SymbolTablePage.Addr=Address
SymbolTablePage.Num=Number
SymbolTablePage.title=Symbol Table
SymbolTablePage.FilterSections=Filter sections displayed
SymbolTablePage.FilterSymTab=Filter symbol table
SymbolTablePage.FilterMapping=Filter out mapping symbols
SymbolTablePage.Apply=Apply
