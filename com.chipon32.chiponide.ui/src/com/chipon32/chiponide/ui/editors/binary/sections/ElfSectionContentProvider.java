package com.chipon32.chiponide.ui.editors.binary.sections;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import org.eclipse.cdt.utils.elf.Elf;
import org.eclipse.cdt.utils.elf.ElfHelper;

import com.chipon32.chiponide.core.utils.parser.ChipOnElf;
import com.chipon32.chiponide.ui.editors.binary.BinContentProvider;

public class ElfSectionContentProvider extends BinContentProvider {

	public Object[] getElements(Object inputElement) {
		if (!(inputElement instanceof ElfHelper helper)) {
			return new Object[0];
		} else {
			try {
				ChipOnElf elf = (ChipOnElf) helper.getElf();

				boolean is32 = elf.getAttributes().getElfClass().equals(ChipOnElf.ELF32);
				int offsetSize = is32 ? 8 : 16;
				Elf.Section[] sections = elf.getSections();
				if (sections.length == 0) {
					return new Object[0];
				}

				List<SectionItem> items = new ArrayList<>();
				for (int i = 1; i < sections.length; i++) {
					Elf.Section section = sections[i];
					items.add(toSectionItem(offsetSize, section, i));
				}
				return items.toArray(new SectionItem[0]);
			} catch (IOException e) {
				return new Object[0];
			}
		}
	}

	private SectionItem toSectionItem(int byteSize, Elf.Section original, int index) {
		String name = original.toString();
		String offset = longToHexString(original.sh_offset, byteSize);
		String address = original.sh_addr.toHexAddressString();
		String size = String.valueOf(original.sh_size);
		String type = getSectionType((int) original.sh_type);
		String flags = getFlagsString(original.sh_flags);

		return new SectionItem(index, name, offset, address, size, type, flags);
	}

	// Standard section types
	private String getSectionType(long type) {
		return switch ((int)type) {
			case 0 -> "SHT_NULL";
			case 1 -> "SHT_PROGBITS";
			case 2 -> "SHT_SYMTAB";
			case 3 -> "SHT_STRTAB";
			case 4 -> "SHT_RELA";
			case 5 -> "SHT_HASH";
			case 6 -> "SHT_DYNAMIC";
			case 7 -> "SHT_NOTE";
			case 8 -> "SHT_NOBITS";
			case 9 -> "SHT_REL";
			case 10 -> "SHT_SHLIB";
			case 11 -> "SHT_DYNSYM";
			case 14 -> "SHT_INIT_ARRAY";
			case 15 -> "SHT_FINI_ARRAY";
			case 16 -> "SHT_PREINIT_ARRAY";
			case 17 -> "SHT_GROUP";
			case 18 -> "SHT_SYMTAB_SHNDX";
			default -> {
				if (type >= 0x60000000 && type <= 0x6FFFFFFF) {
					yield "OS Specific (" + type + ")";
				} else if (type >= 0x70000000 && type <= 0x7FFFFFFF) {
					yield "Processor Specific (" + type + ")";
				} else {
					yield "Unknown (" + type + ")";
				}
			}
		};
	}

	private static String getFlagsString(long value) {
		StringBuilder flags = new StringBuilder();
		String seperator = addFlag(flags, 2L, "SHF_ALLOC", "", value);
		seperator = addFlag(flags, 4L, "SHF_EXECINSTR", seperator, value);
		seperator = addFlag(flags, 1L, "SHF_WRITE", seperator, value);
		seperator = addFlag(flags, 512L, "SHF_GROUP", seperator, value);
		seperator = addFlag(flags, 64L, "SHF_INFO_LINK", seperator, value);
		seperator = addFlag(flags, 128L, "SHF_LINK_ORDER", seperator, value);
		seperator = addFlag(flags, 16L, "SHF_MERGE", seperator, value);
		seperator = addFlag(flags, 256L, "SHF_OS_NONCONFORMING", seperator, value);
		seperator = addFlag(flags, 32L, "SHF_STRINGS", seperator, value);
		addFlag(flags, 1024L, "SHF_TLS", seperator, value);
		return flags.toString();
	}

	private static String addFlag(StringBuilder sb, long flag, String flagName, String seperator, long value) {
		if ((value & flag) != 0L) {
			sb.append(seperator);
			sb.append(flagName);
			seperator = "+";
		}

		return seperator;
	}

}
