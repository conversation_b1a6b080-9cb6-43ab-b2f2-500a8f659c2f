package com.chipon32.chiponide.ui.editors.binary.segments;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import org.eclipse.cdt.utils.elf.Elf;
import org.eclipse.cdt.utils.elf.ElfHelper;

import com.chipon32.chiponide.core.utils.parser.ChipOnElf;
import com.chipon32.chiponide.ui.editors.binary.BinContentProvider;

public class ElfSegmentContentProvider extends BinContentProvider {

	public Object[] getElements(Object inputElement) {
		if (!(inputElement instanceof ElfHelper helper)) {
			return new Object[0];
		} else {
			try {
				ChipOnElf elf = (ChipOnElf) helper.getElf();

				Elf.PHdr[] pHdrs = elf.getPHdrs();
				if (pHdrs.length == 0) {
					return new Object[0];
				}

				List<SegmentItem> items = new ArrayList<>();
				for (int i = 0; i < pHdrs.length; i++) {
					Elf.PHdr segment = pHdrs[i];
					items.add(toSegmentItem(segment, i + 1));
				}
				return items.toArray(new SegmentItem[0]);
			} catch (IOException e) {
				return new Object[0];
			}
		}
	}

	private SegmentItem toSegmentItem(Elf.PHdr original, int index) {
		String type = getSegmentType(original.p_type);
		String virtualAddress = original.p_vaddr.toHexAddressString();
		String physicalAddress = original.p_paddr.toHexAddressString();
		String memSize = String.format("%s (%s)", original.p_memsz, longToHexString(original.p_memsz));
		String fileSize = String.format("%s (%s)", original.p_filesz, longToHexString(original.p_filesz));
		String flags = getSegmentFlags(original.p_flags);

		return new SegmentItem(original, index, type, virtualAddress, physicalAddress, memSize, fileSize, flags);
	}

	private String getSegmentType(long type) {
		return switch ((int) type) {
			case 0 -> "PT_NULL";
			case 1 -> "PT_LOAD";
			case 2 -> "PT_DYNAMIC";
			case 3 -> "PT_INTERP";
			case 4 -> "PT_NOTE";
			case 5 -> "PT_SHLIB";
			case 6 -> "PT_PHDR";
			case 0x70000001 -> "PT_ARM_EXIDX";
			default -> longToHexString(type, 8);
		};
	}

	private String getSegmentFlags(long flags) {
		StringBuilder result = new StringBuilder();
		String separator = "";

		if ((flags & 1L) != 0L) {
			result.append(separator).append("PF_X");
			separator = "+";
		}
		if ((flags & 2L) != 0L) {
			result.append(separator).append("PF_W");
			separator = "+";
		}
		if ((flags & 4L) != 0L) {
			result.append(separator).append("PF_R");
			separator = "+";
		}

		long otherFlags = flags & -8L;
		if (otherFlags != 0L) {
			result.append(separator).append(longToHexString(otherFlags));
		}

		return result.toString();
	}

}
