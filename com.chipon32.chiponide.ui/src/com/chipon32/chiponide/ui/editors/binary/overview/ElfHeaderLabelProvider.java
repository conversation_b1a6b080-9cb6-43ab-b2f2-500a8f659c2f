
package com.chipon32.chiponide.ui.editors.binary.overview;

import org.apache.commons.lang3.tuple.Pair;
import org.eclipse.jface.resource.JFaceResources;
import org.eclipse.jface.viewers.CellLabelProvider;
import org.eclipse.jface.viewers.ViewerCell;

public class ElfHeaderLabelProvider extends CellLabelProvider {

	@SuppressWarnings("unchecked")
	public void update(ViewerCell cell) {
		Pair<String, String> element = (Pair<String, String>) cell.getElement();
		if (cell.getColumnIndex() == 0) {
			cell.setText(element.getKey());
		} else {
			cell.setText(element.getValue());
		}

		cell.setFont(JFaceResources.getFont("org.eclipse.jface.textfont"));
	}
}
