package com.chipon32.chiponide.ui.editors.binary.sections;

import org.eclipse.jface.resource.JFaceResources;
import org.eclipse.jface.viewers.CellLabelProvider;
import org.eclipse.jface.viewers.ViewerCell;

public class SectionLabelProvider extends CellLabelProvider {

	public void update(ViewerCell cell) {
		SectionItem section = (SectionItem) cell.getElement();
		int column = cell.getColumnIndex();
		String text = switch (column) {
			case 0 -> Long.toString(section.number);
			case 1 -> section.name;
			case 2 -> section.offset;
			case 3 -> section.address;
			case 4 -> section.size;
			case 5 -> section.type;
			case 6 -> section.flags;
			default -> "";
		};

		cell.setText(text);
		cell.setFont(JFaceResources.getFont("org.eclipse.jface.textfont"));
	}

	public String getToolTipText(Object element) {
		if (element instanceof ViewerCell cell) {
			return cell.getText();
		} else {
			return null;
		}
	}
}
