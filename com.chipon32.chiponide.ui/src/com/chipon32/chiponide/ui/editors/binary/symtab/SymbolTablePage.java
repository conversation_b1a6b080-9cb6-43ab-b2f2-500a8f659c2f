package com.chipon32.chiponide.ui.editors.binary.symtab;

import org.eclipse.jface.layout.TableColumnLayout;
import org.eclipse.jface.resource.JFaceResources;
import org.eclipse.jface.viewers.CellLabelProvider;
import org.eclipse.jface.viewers.ColumnWeightData;
import org.eclipse.jface.viewers.IContentProvider;
import org.eclipse.jface.viewers.TableViewer;
import org.eclipse.jface.viewers.Viewer;
import org.eclipse.jface.viewers.ViewerCell;
import org.eclipse.jface.viewers.ViewerComparator;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.graphics.Font;
import org.eclipse.swt.widgets.Table;
import org.eclipse.swt.widgets.TableColumn;

import com.chipon32.chiponide.ui.editors.binary.BinEditor;
import com.chipon32.chiponide.ui.editors.binary.ElfViewerTablePage;
import com.chipon32.chiponide.ui.editors.binary.Messages;

public class SymbolTablePage extends ElfViewerTablePage {

	private static final String SIZE = Messages.getString("SymbolTablePage.Size");
	private static final String VISIBILITY = Messages.getString("SymbolTablePage.Vis");
	private static final String SECTION = Messages.getString("SymbolTablePage.Sect");
	private static final String TYPE = Messages.getString("SymbolTablePage.Type");
	private static final String BINDING = Messages.getString("SymbolTablePage.Bind");
	private static final String NAME = Messages.getString("SymbolTablePage.Name");
	private static final String ADDRESS = Messages.getString("SymbolTablePage.Addr");
	private static final String NUMBER = Messages.getString("SymbolTablePage.Num");
	private static final int NUMBER_INDEX = 0;
	private static final int ADDRESS_INDEX = 1;
	private static final int NAME_INDEX = 2;
	private static final int BIND_INDEX = 3;
	private static final int TYPE_INDEX = 4;
	private static final int SEC_INDEX = 5;
	private static final int VIS_INDEX = 6;
	private static final int SIZE_INDEX = 7;

	private final SymbolsComparator comparator = new SymbolsComparator();

	public SymbolTablePage(BinEditor editor, String id) {
		super(editor, id, Messages.getString("SymbolTablePage.title"), null);
	}

	protected IContentProvider getContentProvider() {
		return new SymbolTableContentProvider();
	}

	protected ViewerComparator getComparator() {
		return comparator;
	}

	protected void addColumns(TableViewer viewer, TableColumnLayout layout) {
		Table table = viewer.getTable();
		createColumn(table, NUMBER, "1234567", SWT.LEFT, NUMBER_INDEX);
		createColumn(table, ADDRESS, "0xDEADBEEF01234567", SWT.LEFT, ADDRESS_INDEX);
		createColumn(table, NAME, "This is a String indicative of symbol name length", SWT.LEFT, NAME_INDEX);
		createColumn(table, BINDING, "STB_GLOBAL", SWT.LEFT, BIND_INDEX);
		createColumn(table, TYPE, "STT_SECTION", SWT.LEFT, TYPE_INDEX);
		createColumn(table, SECTION, "SHN_COMMON", SWT.LEFT, SEC_INDEX);
		createColumn(table, VISIBILITY, "STV_PROTECTED", SWT.LEFT, VIS_INDEX);
		createColumn(table, SIZE, "0xDEADBEEF01234567", SWT.LEFT, SIZE_INDEX);
	}

	protected CellLabelProvider getLabelProvider() {
		return new CellLabelProvider() {
			public void update(ViewerCell cell) {
				SymbolItem symbol = (SymbolItem) cell.getElement();
				String text = switch (cell.getColumnIndex()) {
					case NUMBER_INDEX -> Integer.toString(symbol.number);
					case ADDRESS_INDEX -> symbol.address;
					case NAME_INDEX -> symbol.name;
					case BIND_INDEX -> symbol.getSymbolBinding();
					case TYPE_INDEX -> symbol.getSymbolType();
					case SEC_INDEX -> symbol.getSection();
					case VIS_INDEX -> symbol.getSymbolVisibility();
					case SIZE_INDEX -> String.valueOf(symbol.size);
					default -> "";
				};

				cell.setText(text);
				cell.setFont(JFaceResources.getFont("org.eclipse.jface.textfont"));
			}
		};
	}

	protected int getTableFlags() {
		return SWT.VIRTUAL;
	}

	private TableColumn createColumn(Table table, String name, String dummyInput, int style, int index) {
		TableColumn column = new TableColumn(table, style, index);
		Font oldFont = table.getFont();
		column.setText(dummyInput);
		table.setFont(JFaceResources.getFont("org.eclipse.jface.textfont"));
		TableColumnLayout layout = (TableColumnLayout) table.getParent()
			.getLayout();
		layout.setColumnData(column, new ColumnWeightData(10));
		column.pack();
		table.setFont(oldFont);
		column.setText(name);
		column.addSelectionListener(getSelectionAdapter(viewer, index));
		table.setSortColumn(column);
		return column;
	}

	protected SelectionAdapter getSelectionAdapter(final TableViewer viewer, final int index) {
		return new SelectionAdapter() {
			public void widgetSelected(SelectionEvent e) {
				comparator.setColumn(index);
				int dir = comparator.getDirection();
				Table table = viewer.getTable();
				table.setSortColumn(table.getColumn(index));
				table.setSortDirection(dir);
				// provider.sortSymbols();
				viewer.refresh();
			}
		};
	}

	public static class SymbolsComparator extends ViewerComparator {
		private int currentSortColumn = 0;
		private int direction = SWT.UP;

		public int getDirection() {
			return direction;
		}

		public void setColumn(int column) {
			if (column == currentSortColumn) {
				direction = direction == SWT.UP ? SWT.DOWN : SWT.UP;
			} else {
				currentSortColumn = column;
				direction = SWT.UP;
			}
		}

		public int compare(Viewer viewer, Object e1, Object e2) {
			SymbolItem symbol1 = (SymbolItem) e1;
			SymbolItem symbol2 = (SymbolItem) e2;
			int result = switch (currentSortColumn) {
				case NUMBER_INDEX -> Long.signum((long) (symbol1.number - symbol2.number));
				case ADDRESS_INDEX -> symbol1.address.compareTo(symbol2.address);
				case NAME_INDEX -> symbol1.name.compareTo(symbol2.name);
				case BIND_INDEX -> symbol1.getSymbolBinding()
					.compareTo(symbol2.getSymbolBinding());
				case TYPE_INDEX -> symbol1.getSymbolType()
					.compareTo(symbol2.getSymbolType());
				case SEC_INDEX -> symbol1.getSection()
					.compareTo(symbol2.getSection());
				case VIS_INDEX -> symbol1.getSymbolVisibility()
					.compareTo(symbol2.getSymbolVisibility());
				case SIZE_INDEX -> Long.signum(symbol1.size - symbol2.size);
				default -> 0;
			};

			if (direction == SWT.DOWN) {
				result = -result;
			}

			return result;
		}
	}
}
