package com.chipon32.chiponide.ui.editors.binary.segments;

import org.eclipse.jface.resource.JFaceResources;
import org.eclipse.jface.viewers.CellLabelProvider;
import org.eclipse.jface.viewers.ViewerCell;

public class SegmentLabelProvider extends CellLabelProvider {

	public static final int NUMBER_INDEX = 0;
	public static final int TYPE = 1;
	public static final int VIRTUAL_ADDRESS_INDEX = 2;
	public static final int PHYSICAL_ADDRESS_INDEX = 3;
	public static final int MEM_SIZE_INDEX = 4;
	public static final int FILE_SIZE_INDEX = 5;
	public static final int FLAGS = 6;

	public void update(ViewerCell cell) {
		SegmentItem segment = (SegmentItem) cell.getElement();
		int column = cell.getColumnIndex();

		String text = switch (column) {
			case NUMBER_INDEX -> String.valueOf(segment.number);
			case TYPE -> segment.type;
			case VIRTUAL_ADDRESS_INDEX -> segment.virtualAddress;
			case PHYSICAL_ADDRESS_INDEX -> segment.physicalAddress;
			case MEM_SIZE_INDEX -> segment.memSize;
			case FILE_SIZE_INDEX -> segment.fileSize;
			case FLAGS -> segment.flags;
			default -> "";
		};

		cell.setText(text);
		cell.setFont(JFaceResources.getFont("org.eclipse.jface.textfont"));
	}
}
