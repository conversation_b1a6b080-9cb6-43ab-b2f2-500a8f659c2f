BinEditorContributorClass.Copy=复制
BinEditorContributorClass.Find=查找
SectionLabelProvider.Name=名称
BinOverviewPage.Title=头部
FromelfInputObserver.objImg=\ 对象/映像 
FromelfInputObserver.NoTempFile=未能获取 Temp 文件
FromelfInputObserver.CancelledOp=\ 已取消
FromelfInputObserver.FailedOp=\ 失败\n
FromElfPage.Refresh=刷新
FromElfPage.Error=错误: 
SymbolTableObserver.desc=计算符号表
SymbolTablePage.Size=大小
SymbolTablePage.Vis=可见性
SymbolTablePage.Sect=节
SymbolTablePage.Type=类型
SymbolTablePage.Bind=绑定
SymbolTablePage.Name=名称
SymbolTablePage.Addr=地址
SymbolTablePage.Num=编号
SymbolTablePage.title=符号表
SymbolTablePage.FilterSections=过滤显示的节
SymbolTablePage.FilterSymTab=过滤符号表
SymbolTablePage.FilterMapping=过滤出映射符号
SymbolTablePage.Apply=应用
