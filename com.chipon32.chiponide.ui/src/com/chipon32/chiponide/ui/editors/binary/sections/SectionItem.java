package com.chipon32.chiponide.ui.editors.binary.sections;

import com.google.common.base.MoreObjects;

public class SectionItem {

    public final int number;
    public final String name;
    public final String offset;
    public final String address;
    public final String size;
    public final String type;
    public final String flags;

    public SectionItem(int number, String name, String offset, String address, String size, String type, String flags) {
        this.number = number;
        this.name = name;
        this.offset = offset;
        this.address = address;
        this.size = size;
        this.type = type;
        this.flags = flags;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("number", number)
            .add("name", name)
            .add("offset", offset)
            .add("address", address)
            .add("size", size)
            .add("type", type)
            .add("flags", flags)
            .toString();
    }
}
