
package com.chipon32.chiponide.ui.editors.binary.overview;

import java.io.IOException;
import java.util.ArrayList;

import org.apache.commons.lang3.tuple.Pair;
import org.eclipse.cdt.utils.elf.Elf;
import org.eclipse.cdt.utils.elf.ElfHelper;

import com.chipon32.chiponide.core.utils.parser.ChipOnElf;
import com.chipon32.chiponide.ui.editors.binary.BinContentProvider;

public class ElfHeaderContentProvider extends BinContentProvider {

	public Object[] getElements(Object inputElement) {
		if (!(inputElement instanceof ElfHelper helper) || !(helper.getElf() instanceof ChipOnElf elf)) {
			return new Object[0];
		}
		ArrayList<Pair<String, String>> list = new ArrayList<>();
		try {
			Elf.ELFhdr header = elf.getELFhdr();
			boolean is32 = elf.getAttributes().getElfClass().equals(ChipOnElf.ELF32);
			list.add(Pair.of("Machine class", elf.getAttributes().getElfClass()));
			list.add(Pair.of("Data encoding", getDateEncoding(elf)));
			list.add(Pair.of("Header version", getHeaderVersion(elf)));
			list.add(Pair.of("File type", getFileType(elf)));
			list.add(Pair.of("Machine", elf.getAttributes().getCPU()));

			list.add(Pair.of("Debug Info", getDebugInfo(elf)));
			list.add(Pair.of("e_entry", header.e_entry.toHexAddressString()));
			list.add(Pair.of("Flags", printLongHex(header.e_flags, is32)));
			list.add(Pair.of("Header Size", printIntHex(header.e_ehsize)));
			list.add(Pair.of("Segment header entry size", printIntHex(header.e_phentsize)));
			list.add(Pair.of("Section header entry size", printIntHex(header.e_shentsize)));
			list.add(Pair.of("Number of program headers", String.valueOf(header.e_phnum)));
			list.add(Pair.of("Number of section headers", String.valueOf(header.e_shnum)));
			list.add(Pair.of("Program header table offset", printLongHex(header.e_phoff, is32)));
			list.add(Pair.of("Section header table offset", printLongHex(header.e_shoff, is32)));
			list.add(Pair.of("String header string table index", String.valueOf(header.e_shstrndx)));
		} catch (IOException ignored) {
		}
		return list.toArray();
	}

	private String printIntHex(int v) {
		return v + " bytes (0x" + Integer.toHexString(v) + ")";
	}

	public static String printLongHex(long v, boolean is32) {
		long l = v;
		if (is32) {
			l = v & 0xFFFFFFFFL;
		}

		return "0x" + Long.toHexString(l);
	}

	private String getDateEncoding(ChipOnElf elf) throws IOException {
		if (elf.getAttributes().isLittleEndian()) {
			return "ELFDATA2LSB (Little endian)";
		} else {
			return "ELFDATA2MSB (Big endian)";
		}
	}

	private String getHeaderVersion(ChipOnElf elf) throws IOException {
		return switch ((int) elf.getELFhdr().e_version) {
			case 1 -> "EV_CURRENT (Current version)";
			default -> "EV_NONE (Invalid version)";
		};
	}

	private String getFileType(ChipOnElf elf) throws IOException {
		int type = elf.getELFhdr().e_type;
		return switch (type) {
			case 0 -> "ET_NONE (None) (0)";
			case 1 -> "ET_REL (Relocatable file) (1)";
			case 2 -> "ET_EXEC (Executable file) (2)";
			case 3 -> "ET_EXEC (Shared object file) (3)";
			case 4 -> "ET_EXEC (Core file) (4)";
			default -> "OS Specific (" + type + ")";
		};
	}

	private String getDebugInfo(ChipOnElf elf) throws IOException {
		return switch (elf.getAttributes().getDebugType()) {
			case Elf.Attribute.DEBUG_TYPE_STABS -> "STABS";
			case Elf.Attribute.DEBUG_TYPE_DWARF -> "DWARF";
			default -> "No debug info found";
		};
	}
}
