
package com.chipon32.chiponide.ui.editors.binary.sections;

import org.eclipse.jface.layout.TableColumnLayout;
import org.eclipse.jface.viewers.IBaseLabelProvider;
import org.eclipse.jface.viewers.IContentProvider;
import org.eclipse.jface.viewers.TableViewer;
import org.eclipse.jface.viewers.Viewer;
import org.eclipse.jface.viewers.ViewerComparator;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.widgets.Table;

import com.chipon32.chiponide.ui.editors.binary.BinEditor;
import com.chipon32.chiponide.ui.editors.binary.ElfViewerTablePage;
import com.chipon32.chiponide.ui.editors.binary.Messages;

public class SectionsPage extends ElfViewerTablePage {

	private static final String TITLE = Messages.getString("SectionsPage.Title");
	private static final String NUMBER = Messages.getString("SectionsPage.Number");
	private static final String NUMBER_TOOLTIP = Messages.getString("SectionsPage.NumberTooltip");
	private static final String NAME = Messages.getString("SectionsPage.Name");
	private static final String NAME_TOOLTIP = Messages.getString("SectionsPage.NameTooltip");
	private static final String OFFSET = Messages.getString("SectionsPage.Offset");
	private static final String OFFSET_TOOLTIP = Messages.getString("SectionsPage.OffsetTooltip");
	private static final String ADDRESS = Messages.getString("SectionsPage.Address");
	private static final String ADDRESS_TOOLTIP = Messages.getString("SectionsPage.AddressTooltip");
	private static final String SIZE = Messages.getString("SectionsPage.Size");
	private static final String SIZE_TOOLTIP = Messages.getString("SectionsPage.SizeTooltip");
	private static final String TYPE = Messages.getString("SectionsPage.Type");
	private static final String TYPE_TOOLTIP = Messages.getString("SectionsPage.TypeTooltip");
	private static final String FLAGS = Messages.getString("SectionsPage.Flags");
	private static final String FLAGS_TOOLTIP = Messages.getString("SectionsPage.FlagsTooltip");
	private final SectionsComparator comparator = new SectionsComparator();

	public SectionsPage(BinEditor editor, String id) {
		super(editor, id, TITLE, null);
	}

	protected IContentProvider getContentProvider() {
		return new ElfSectionContentProvider();
	}

	protected IBaseLabelProvider getLabelProvider() {
		return new SectionLabelProvider();
	}

	protected void addColumns(TableViewer viewer, TableColumnLayout layout) {
		addTableColumn(viewer, 0, NUMBER, NUMBER_TOOLTIP, 0);
		addTableColumn(viewer, 1, NAME, NAME_TOOLTIP, 40);
		addTableColumn(viewer, 2, OFFSET, OFFSET_TOOLTIP, 20);
		addTableColumn(viewer, 3, ADDRESS, ADDRESS_TOOLTIP, 20);
		addTableColumn(viewer, 4, SIZE, SIZE_TOOLTIP, 20);
		addTableColumn(viewer, 5, TYPE, TYPE_TOOLTIP, 20);
		addTableColumn(viewer, 6, FLAGS, FLAGS_TOOLTIP, 40);
	}

	protected ViewerComparator getComparator() {
		return comparator;
	}

	protected SelectionAdapter getSelectionAdapter(final TableViewer viewer, final int index) {
		return new SelectionAdapter() {
			public void widgetSelected(SelectionEvent e) {
				comparator.setColumn(index);
				int dir = comparator.getDirection();
				Table table = viewer.getTable();
				table.setSortColumn(table.getColumn(index));
				table.setSortDirection(dir);
				viewer.refresh();
			}
		};
	}

	private static class SectionsComparator extends ViewerComparator {
		private int currentSortColumn = 0;
		private int direction = SWT.UP;

		public SectionsComparator() {
		}

		public int getDirection() {
			return direction;
		}

		public void setColumn(int column) {
			if (column == currentSortColumn) {
				direction = direction == SWT.UP ? SWT.DOWN : SWT.UP;
			} else {
				currentSortColumn = column;
				direction = SWT.UP;
			}
		}

		public int compare(Viewer viewer, Object e1, Object e2) {
			SectionItem section1 = (SectionItem) e1;
			SectionItem section2 = (SectionItem) e2;
			int result = switch (currentSortColumn) {
				case 0 -> Long.signum(section1.number - section2.number);
				case 1 -> section1.name.compareTo(section2.name);
				case 2 -> section1.offset.compareTo(section2.offset);
				case 3 -> section1.address.compareTo(section2.address);
				case 4 -> Long.valueOf(section1.size).compareTo(Long.valueOf(section2.size));
				case 5 -> section1.type.compareTo(section2.type);
				case 6 -> section1.flags.compareTo(section2.flags);
				default -> 0;
			};

			if (direction == SWT.DOWN) {
				result = -result;
			}

			return result;
		}
	}
}
