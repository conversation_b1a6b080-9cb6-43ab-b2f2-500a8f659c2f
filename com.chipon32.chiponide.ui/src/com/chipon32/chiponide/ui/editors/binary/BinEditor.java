
package com.chipon32.chiponide.ui.editors.binary;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.cdt.utils.elf.ElfHelper;
import org.eclipse.core.databinding.Binding;
import org.eclipse.core.databinding.DataBindingContext;
import org.eclipse.core.databinding.observable.value.IObservableValue;
import org.eclipse.core.runtime.IPath;
import org.eclipse.core.runtime.IProgressMonitor;
import org.eclipse.jface.databinding.swt.DisplayRealm;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.ui.IEditorInput;
import org.eclipse.ui.IFileEditorInput;
import org.eclipse.ui.forms.widgets.Form;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.part.MultiPageEditorPart;

import com.chipon32.chiponide.ui.editors.binary.overview.BinOverviewPage;
import com.chipon32.chiponide.ui.editors.binary.sections.SectionsPage;
import com.chipon32.chiponide.ui.editors.binary.segments.SegmentsPage;
import com.chipon32.chiponide.ui.editors.binary.symtab.SymbolTablePage;

public class BinEditor extends MultiPageEditorPart {

	private static final String OVERVIEW_PAGE_ID = "com.chipon32.binary.overview";
	private static final String SECTIONS_PAGE_ID = "com.chipon32.binary.sections";
	private static final String SEGMENT_PAGE_ID = "com.chipon32.binary.segments";
	private static final String SYMTAB_PAGE_ID = "com.chipon32.binarysymtab";
	private static final String DISAS_PAGE_ID = "com.chipon32.binary.disas";
	private static final String SIZES_PAGE_ID = "com.chipon32.binary.sizes";

	private final EditorInputObserver editorInputObserver = new EditorInputObserver();
	private final List<ElfViewerPage> pages = new ArrayList<>();
	private BinResourceChangeListener resourceChangeListener;
	private IFileEditorInput finput;

	protected void pageChange(int newPageIndex) {
		ElfViewerPage requiredPage = pages.get(newPageIndex);
		if (!requiredPage.isActive()) {
			Composite container = getContainer();
			container.setLayoutData(new GridData(16384, 128, true, true));
			Form form = requiredPage.createForm(container, new FormToolkit(Display.getCurrent()));
			setControl(newPageIndex, form);
		}

		super.pageChange(newPageIndex);
	}

	public ElfViewerPage getElfPage(int index) {
		return pages.get(index);
	}

	protected void createPages() {
		pages.add(new BinOverviewPage(this, OVERVIEW_PAGE_ID));
		pages.add(new SectionsPage(this, SECTIONS_PAGE_ID));
		pages.add(new SegmentsPage(this, SEGMENT_PAGE_ID));
		pages.add(new SymbolTablePage(this, SYMTAB_PAGE_ID));

		for (int i = 0; i < pages.size(); ++i) {
			addPage(i, null);
			setPageText(i, pages.get(i).getTitle());
		}
	}

	public void doSave(IProgressMonitor monitor) {
	}

	public void doSaveAs() {
	}

	public boolean isSaveAsAllowed() {
		return false;
	}

	protected void setInput(IEditorInput input) {
		super.setInput(input);
		if (input instanceof IFileEditorInput) {
			finput = (IFileEditorInput) input;
			IPath path = finput.getFile().getFullPath();
			resourceChangeListener = new BinResourceChangeListener(path, this);
			finput.getFile().getWorkspace().addResourceChangeListener(resourceChangeListener);
		}

		setPartName(input.getName());
	}

	public void setDirty() {
		editorInputObserver.getRealm().exec(() -> editorInputObserver.setValue(getEditorInput()));
	}

	public void setClosed() {
		Display display = getSite().getShell().getDisplay();
		display.asyncExec(() -> getSite().getPage().closeEditor(this, false));
	}

	public Binding bindToEditor(IObservableValue<ElfHelper> observer) {
		DataBindingContext dbc = new DataBindingContext(DisplayRealm.getRealm(Display.getDefault()));
		return dbc.bindValue(observer, editorInputObserver);
	}

	public void dispose() {
		super.dispose();
		if (finput != null && resourceChangeListener != null) {
			finput.getFile().getWorkspace().removeResourceChangeListener(resourceChangeListener);
		}

		for (ElfViewerPage page : pages) {
			page.dispose();
		}
	}

}
