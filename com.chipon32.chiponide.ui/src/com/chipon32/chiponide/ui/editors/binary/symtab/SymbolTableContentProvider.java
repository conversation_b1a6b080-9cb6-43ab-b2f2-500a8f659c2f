package com.chipon32.chiponide.ui.editors.binary.symtab;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import org.eclipse.cdt.utils.elf.Elf;
import org.eclipse.cdt.utils.elf.ElfHelper;

import com.chipon32.chiponide.core.utils.parser.ChipOnElf;
import com.chipon32.chiponide.ui.editors.binary.BinContentProvider;

public class SymbolTableContentProvider extends BinContentProvider {

	public Object[] getElements(Object inputElement) {
		if (!(inputElement instanceof ElfHelper helper)) {
			return new Object[0];
		} else {
			ChipOnElf elf = (ChipOnElf) helper.getElf();
			try {
				elf.loadSymbols();
			} catch (IOException e) {
				e.printStackTrace();
				return new Object[0];
			}
			Elf.Symbol[] symbols = elf.getSymbols();

			List<SymbolItem> items = new ArrayList<>();
			for (int i = 0; i < symbols.length; i++) {
				Elf.Symbol symbol = symbols[i];
				items.add(toSymbolItem(symbol, i + 1));
			}
			return items.toArray(new SymbolItem[0]);

		}
	}

	private SymbolItem toSymbolItem(Elf.Symbol symbol, int index) {
		String address = symbol.st_value.toHexAddressString();
		String name = symbol.toString();
		return new SymbolItem(index, address, name, symbol.st_bind(), symbol.st_type(), symbol.st_shndx, symbol.st_other, symbol.st_size);
	}

}
