
package com.chipon32.chiponide.ui.editors.binary;

import org.eclipse.cdt.utils.elf.ElfHelper;
import org.eclipse.jface.viewers.IStructuredContentProvider;
import org.eclipse.jface.viewers.Viewer;
import org.eclipse.swt.widgets.Display;

public abstract class BinContentProvider implements IStructuredContentProvider {

	protected ElfHelper model;

	public void inputChanged(final Viewer viewer, Object oldInput, Object newInput) {
		if (newInput == null) {
			this.model = null;
		} else {
			this.model = (ElfHelper) newInput;
			Display.getDefault().asyncExec(() -> {
				viewer.refresh();
				viewer.getControl().redraw();
			});
		}
	}

	public static String longToHexString(long value) {
		return longToHexString(value, 0);
	}

	public static String longToHexString(long value, int minLength) {
		String hexStr = Long.toHexString(value).toUpperCase();
		StringBuilder result = new StringBuilder();
		result.append("0x");

		result.append("0".repeat(Math.max(0, minLength - hexStr.length())));

		result.append(hexStr);
		return result.toString();
	}

}
