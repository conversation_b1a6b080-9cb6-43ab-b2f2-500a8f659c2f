package com.chipon32.language.action;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.charset.Charset;

public class UTF8FileUtil {
//#######################################################################
  public static String readStr(String fileName) throws IOException {
    File f = new File(fileName);
    if(f.exists())
    {
    FileInputStream fs = new FileInputStream(f);
    byte[] b = new byte[fs.available()];
    fs.read(b);
    fs.close();
    return new String(b, Charset.forName("UTF-8"));
    }
    return "";
  }
//####################################################################### 
  public static byte[] readUtf8Bytes(String fileName) throws IOException {
    File f = new File(fileName);
    if(f.exists())
    {
	    FileInputStream fs = new FileInputStream(f);
	    byte[] b = new byte[fs.available()];
	    fs.read(b);
	    fs.close();
	    return b;
    }
    byte[] b = new byte[1];
    b[0]=0;
    return b;
  }
//####################################################################### 
  public static boolean writeStr(String fileName, String fileContent) throws IOException {
    File f = new File(fileName);
    if (f.exists())
      f.delete(); 
    
    FileOutputStream fs = new FileOutputStream(f);
    byte[] b = fileContent.getBytes(Charset.forName("UTF-8"));
    fs.write(b);
    fs.flush();
    fs.close();
    return true;
  }
  
  public static boolean writeStr2(String fileName, String fileContent) throws IOException {
    File f = new File(fileName);
    
    FileOutputStream fs = new FileOutputStream(f);
    byte[] b = fileContent.getBytes(Charset.forName("UTF-8"));
    fs.write(b);
    fs.flush();
    fs.close();
    return true;
  }
//#######################################################################  
  public static boolean writeUtf8Bytes(String fileName, byte[] bytecontent) throws IOException {
    File f = new File(fileName);
    if (f.exists())
      f.delete(); 
    
    FileOutputStream fs = new FileOutputStream(f);
    fs.write(bytecontent);
    fs.flush();
    fs.close();
    return true;
  }
//####################################################################### 
}
