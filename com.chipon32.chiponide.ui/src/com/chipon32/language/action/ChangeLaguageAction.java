package com.chipon32.language.action;


import org.eclipse.jface.action.IAction;
import org.eclipse.jface.viewers.ISelection;
import org.eclipse.ui.IWorkbenchWindow;
import org.eclipse.ui.IWorkbenchWindowActionDelegate;


//command handle
//	import org.eclipse.core.commands.AbstractHandler;
//	import org.eclipse.core.commands.ExecutionEvent;
//	import org.eclipse.core.commands.ExecutionException;
//
//
//
//	public class ChangeLaguageAction extends AbstractHandler {
//
//		@Override
//		public Object execute(ExecutionEvent event) throws ExecutionException {
//
//		    LanguagePage dialog = new LanguagePage();		
//		    dialog.open();
//			return null;
//		}
//
//	}
//action
public class ChangeLaguageAction implements IWorkbenchWindowActionDelegate {

	@Override
	public void run(IAction action) {
	    LanguagePage dialog = new LanguagePage();		
	    dialog.open();
	}

	@Override
	public void selectionChanged(IAction action, ISelection selection) {
		// TODO Auto-generated method stub
	}

	@Override
	public void dispose() {
		// TODO Auto-generated method stub
	}

	@Override
	public void init(IWorkbenchWindow window) {
		// TODO Auto-generated method stub
	}

}
