package com.chipon32.util.ui.drawChart;

import java.util.Arrays;

import org.eclipse.swt.events.PaintEvent;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.graphics.GC;

public class RectanglePaintTool {
	//定义长方图形的数据
	/**
	 * 
	 *   RectangleChart
		 监听：paintRectangleControl
		
		 控制信息：总数量大小,使用数量大小，1小数百分比，整数百分比0-100
		 百分比格式：
		 NumberFormat  .setMaximumFractionDigits(1);.setMinimumFractionDigits(1);
		 基于参数的创建 数据。RectangleData[]，即
		 RectanglePaintTool().drawnRectangChart的2个元素。即绘制分2个过程完成差异化颜色具有
		 	第一个总的信息，灰亮色，设计 200组件宽对应最大空间数量值。标记类型为2
			第二个使用信息，绿色或红色，百分占比组件宽对应使用空间数量值。标记类型为3.
		
		调用paintControl完成显示更新。
		绘图gc进行绘制，如颜色配置好的填充，和绘制文本。基于对象的内部指定地址点开始。
	 * @param maxValue	最大值
	 * @param value		已使用的值
	 * @param efficiencyValue	使用率
	 * @param range		进度条的zhan宽度
	 * 总数量大小,使用数量大小，1小数百分比，整数百分比0-100
	 * @return
	 */
	public RectangleData[] drawnRectangChart(String maxValue ,String value ,String efficiencyValue , int range ) {
		RectangleData[] oils = new RectangleData[2];
		//++++++++++++++++++++++++++++++++++++++++++++++++++++++
		oils[0] = new RectangleData();
		oils[0].setColor(new Color(null,GraphPalette.RGB_GREEN));  //RGB_LIGHT_GREY
		oils[0].setRange(200);	//200时两列，100时则一列显示总值
		oils[0].setCategory(2);
		oils[0].setValue(maxValue);  // 200对应的后台最大数据
		//++++++++++++++++++++++++++++++++++++++++++++++++++++++
		// 获取百分比的整数部分
		int i = efficiencyValue.indexOf(".");
		if(i==-1)i = efficiencyValue.indexOf("%");
		if(i==-1 || i==0) i=efficiencyValue.length();
//		String color = efficiencyValue.substring(0,i); //efficiencyValue.substring(0,efficiencyValue.indexOf("."))
//		int colorRestue=1;
//		try{
//			colorRestue= Integer.parseInt(color);
//		}catch (Exception e){			
//		}
		//++++++++++++++++++++++++++++++++++++++++++++++++++++++
		oils[1] = new RectangleData();
//		if(colorRestue>95){
			oils[1].setColor(new Color(null,GraphPalette.RGB_RED));
//		}else 
//		{
//			oils[1].setColor(new Color(null,GraphPalette.RGB_GREEN));
//		}	
		if(range<2 && (!efficiencyValue.equalsIgnoreCase("0.0%")) )
			oils[1].setRange(2);     // 为了一定时颜色使用的显示
		else
			oils[1].setRange(range); // 百分比占宽
		
		oils[1].setCategory(3);		
		oils[1].setValue(value); // 占宽对应的值
		oils[1].setEfficiencyValue(efficiencyValue); // 百分比值
		//++++++++++++++++++++++++++++++++++++++++++++++++++++++
		return oils;
	}
	
	//监听来绘制图形的方法
	public void paintRectangleControl(PaintEvent e) {
		Object data = e.widget.getData(RectangleChart.KEY);
		if (data instanceof RectangleData[]) {
			Arrays.sort((RectangleData[]) data);
			// 数据和绘制类型	
			RectangleData[] oils = (RectangleData[]) data;
			GC gc = e.gc;			
			paintControlDo(oils, gc);
		}
	}
	
	
	public void paintControlDo(RectangleData[] oils,GC gc)
	{		
//		gc.setBackground(Display.getCurrent().getSystemColor(SWT.COLOR_BLUE));
//		gc.setForeground(new Color(null,GraphPalette.RGB_RED));
//		gc.fillRectangle(0, 0, 100, 200);
//		gc.drawString("12345", 2, 2);
//		gc.dispose();		
		
		for (RectangleData oil : oils) {
			// 配置绘图对象的颜色，即其填充色
			gc.setBackground(oil.getColor());//			gc.setForeground(Display.getCurrent().getSystemColor(SWT.COLOR_BLUE));
			// 数据有效前提的解析处理
			if(oil!=null && oil.getValue()!=null && oil.getValue().length()>0){			
				 //#################################################################################################
				// 绘图宽度  和  绘图宽度下后台原始数据
				float range = oil.getRange();
				String resultValue = oil.getValue();
				//######################################
				// 使用情况下的值追加原始百分比信息。
				if(oil.getEfficiencyValue()!=null){
					resultValue = resultValue + "(" + oil.getEfficiencyValue() + ")                   ";
//					//resultValue="1048576(110.3%)";
				}
				//gc.drawText(resultValue, 0, 0);
				//######################################
				//######################################
				// 获取文本信息的占宽占高信息
//				//Point point= gc.textExtent(resultValue);  //
				int maxHeight= 22;
				//文本信息解析获取的 结果字符串位置  并设置居中
//				//int resultlength = point.x;				
//				//int stringHeight=(maxHeight-point.y)/2;//int stringHeight=(16-fontData[0].getHeight())/2;
				// 填充区域，即根据颜色进行区域的填充。首次全区域填充背景底层，二次占用区域填充背景中层，即灰色总结和使用的绿色或红色。
				gc.fillRectangle(0, 0, Math.round(range), maxHeight);
				//######################################
				// 根据区域宽度信息，选择文字在那个区间显示。 gc.drawText(显示文本,起始偏移x, 起始偏移y);RGB_LIGHT_GREY  VS RGB_RED | RGB_GREEN
				if(oil.getCategory()==3)
				{
					// 考虑设计宽度，文字放后面情况先存在显示不完全问题，即文字显示内容较长.
					//设计内容 补充空格让超过范围在背景色输出文字，即实现使用率的全色变更为下面的条示意空间占比。
					gc.setBackground(new Color(null,GraphPalette.RGB_LIGHT_GREY));  // red  redraw  light
					gc.drawText(resultValue, Math.round(0), 0);
					
//					//// 内容放在前面的保存颜色 红或绿  满足文本放置时，直接绘制文字
//					//if(Math.round(range)>resultlength){						
//					//	gc.drawText(resultValue, Math.round(range)-resultlength, stringHeight);
//					//}
//					//// 不满足文本放置时，放在后面的，因为继续绘图类，后续颜色使用背景底色
//					//else 
//					//{
//					//	gc.setBackground(new Color(null,GraphPalette.RGB_LIGHT_GREY));
//					//	gc.drawText(resultValue, Math.round(range), stringHeight);
//					//}
				}

			  //#################################################################################################
			}
			
		}
		gc.dispose();
	}
	
}

