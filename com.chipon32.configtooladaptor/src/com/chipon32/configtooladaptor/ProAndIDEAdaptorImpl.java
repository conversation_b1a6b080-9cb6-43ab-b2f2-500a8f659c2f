package com.chipon32.configtooladaptor;

import com.chipon32.configtool.adaptor.ProAndIDEAdaptor;
import com.chipon32.configtool.util.Constants;
import com.chipon32.configtool.util.FormatUtil;
import com.chipon32.hex.core.CheckAESCMAKCalculateLibrary;
import com.chipon32.util.communicate.ICSPOperationFactory;
import com.chipon32.util.communicate.WriteMessage;
import com.chipon32.util.frame.IFrame;
import com.chipon32.util.provider.MyToolConfig;

import java.util.List;


public class ProAndIDEAdaptorImpl implements ProAndIDEAdaptor {
	
	@Override
	public void printErrorMsg(String msg) {
		WriteMessage.getDefault().writeErrMessage(msg);
	}
	
	@Override
	public void printInfoMsg(String msg) {
		WriteMessage.getDefault().writeMessage(msg);
	}

	@Override
	public boolean powerOnBeforeWriteData(String chipType) {
		return ConfigToolUtils.downloadCFGBeforeOpera(chipType);
	}

	@Override
	public List<String> readData(String type, String startAddr, int readLen) {
		List<String> data = ICSPOperationFactory.read(type, startAddr.toUpperCase(), readLen);
		return data;
	}

	@Override
	public List<String> calcCMAK(List<String> key, Long startAddr, Long len, List<String> data) {
		CheckAESCMAKCalculateLibrary.setBufVector(
				FormatUtil.list2String(key),
				startAddr.intValue(),
				(int) (startAddr + len-1));
		List<String> CMAC_Result = CheckAESCMAKCalculateLibrary.process_cmak_cal_inner(
				data, 
				true, 
				len.intValue());
		return CMAC_Result;
	}

	@Override
	public boolean powerOff() {
		if(!ICSPOperationFactory.isPowerDownProgram()) {
			printErrorMsg(Messages.ProAndIDEAdaptorImpl_0);
			return false;
		}
		return true;
	}

	@Override
	public boolean writeData(List<String> data, String startAddr) {
		String eraseStartAddr = FormatUtil.getStartAddrByLine(startAddr);
		boolean res = ICSPOperationFactory.erase(IFrame.AimStorageCFGUser,	eraseStartAddr,	Integer.toHexString(Constants.ONE_KB_LEN));
		res &= ICSPOperationFactory.write(IFrame.AimStorageCFGUser,startAddr, data);
		if(res) {
			res = checkWriteData(data, startAddr, data.size());
		}
		
		return res;
	}

	/**
	 * 校验写到芯片中的值
	 * @param data
	 * @param writeLen 
	 * @param startAddr 
	 * @return
	 */
	private boolean checkWriteData(List<String> data, String startAddr, int writeLen) {
		List<String> readData = ICSPOperationFactory.read(IFrame.AimStorageCFGUser, startAddr, writeLen);
		if(readData!=null) {
			if(readData.equals(data)) {
				return true;
			}
		}
		
		return false;
	}

	@Override
	public String getAimStorageCFGUser() {
		return IFrame.AimStorageCFGUser;
	}

	@Override
	public String getAimStorageFlash() {
		// TODO Auto-generated method stub
		return IFrame.AimStorageFlash;
	}

	@Override
	public void rePower() {
		
	}

	@Override
	public void KDF(short[] in1, short[] in2, short[] out) {
		CheckAESCMAKCalculateLibrary.KDF(in1, in2, out);
	}

	@Override
	public void CBC(short[] in1, short[] key1, short[] IV, int inputlength, short[] out) {
		CheckAESCMAKCalculateLibrary.CBC(in1, key1, IV, inputlength, out);
		
	}

	@Override
	public void aes_cmac(short[] in, int length, short[] out, short[] key) {
		CheckAESCMAKCalculateLibrary.aes_cmac(in, length, out, key);
		
	}

	@Override
	public void aes_128_encrypt(short[] in, short[] out, short[] key) {
		CheckAESCMAKCalculateLibrary.aes_128_encrypt(in, out, key);
		
	}

	@Override
	public String getCipherDebugKey() {
		return MyToolConfig.cipherDebugKey;
	}

	@Override
	public void setDebugCipherKey(String debugKey) {
		MyToolConfig.cipherDebugKey = debugKey.toUpperCase();
		
	}
	

}
