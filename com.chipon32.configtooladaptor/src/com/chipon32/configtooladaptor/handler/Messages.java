package com.chipon32.configtooladaptor.handler;

import org.eclipse.osgi.util.NLS;

public class Messages extends NLS {
	private static final String BUNDLE_NAME = "com.chipon32.configtooladaptor.handler.messages"; //$NON-NLS-1$
	public static String LoadKeyHandler_0;
	public static String LoadKeyHandler_1;
	
	static {
		// initialize resource bundle
		NLS.initializeMessages(BUNDLE_NAME, Messages.class);
	}

	private Messages() {
	}
}
