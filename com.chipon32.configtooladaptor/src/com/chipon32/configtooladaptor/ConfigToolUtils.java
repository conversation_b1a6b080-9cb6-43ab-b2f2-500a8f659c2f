package com.chipon32.configtooladaptor;


import org.eclipse.jface.dialogs.MessageDialog;

import com.chipon32.hex.core.util.IConfigurationProvider;
import com.chipon32.util.communicate.DeviceManager;
import com.chipon32.util.communicate.ICSPOperationFactory;
import com.chipon32.util.provider.ConfigurationFactory;
import com.chipon32.util.provider.MyToolConfig;

public class ConfigToolUtils {

	/*private static String parameter;

	public static boolean powerOnBeforeWriteData() {
		parameter = "011000111100000100100001";// 01100011-11000001-00100001
		String chipName = getChiponName();			
		IConfigurationProvider chipxmlmessage = ConfigurationFactory.getProvider(chipName);
		if(ICSPOperationFactory.getMemory()==null) {
			ICSPOperationFactory.setMemory(new HexFileParser("", chipxmlmessage).getMemory()); //$NON-NLS-1$
		}
		ICSPOperationFactory.setChipxmlmessage(chipxmlmessage);
		parameter = CommunicateUtil.rewriteParameterFor11K(parameter, chipxmlmessage);
		ICSPOperationFactory.initCommand(parameter);
		boolean isNotFineErr = ICSPOperationFactory.all_Thread_Open_Set(parameter,false,false,false);
		if(!isNotFineErr) {
			return isNotFineErr;
		}
		isNotFineErr=ICSPOperationFactory.all_Thread_Check_ID_and_ReadTrim(true,true);
		if(!isNotFineErr) {
			return isNotFineErr;
		}
		isNotFineErr = ICSPOperationFactory.checkProFlag(isNotFineErr);
		if(!isNotFineErr) {
			return isNotFineErr;
		}
		isNotFineErr=ICSPOperationFactory.isPowerONProgram(true);
		if(!isNotFineErr) {
			return isNotFineErr;
		}
		isNotFineErr = CommunicateUtil.WriteDeviceTrimWraper(chipxmlmessage, ICSPOperationFactory.ChipTrimValue,ICSPOperationFactory.ChipCalibrationValue, true);
		if(!isNotFineErr) {
			return isNotFineErr;
		}
		isNotFineErr = ICSPOperationFactory.eraseCFG(false);
		List<String> result = ICSPOperationFactory.SelIPOrOtherFun(IFrame.AimStorageCFGUser, "0", "0", false, false, false);   //  鍖哄煙瀵瑰簲鐨勯粯璁? ip0
		if(result==null)
		{
			return false;
		}
		return isNotFineErr;
	} 
	
	public static boolean powerOff() {
		boolean isNotFineErr = true;
		if(!ICSPOperationFactory.writeConfigProtect()) {
			isNotFineErr=false;
		}
		ICSPOperationFactory.isPowerDownProgram();
		try{
			DeviceManager.close();
		}catch (Exception e) {

		}
		return isNotFineErr;
	}*/

	
	/**
	 * 写入配置或生成的秘钥信息前芯片环境准备
	 * @return
	 */
	public static boolean downloadCFGBeforeOpera(String chipType) {
		if(DeviceManager.portName==null || DeviceManager.portName.length()<2) {
			MessageDialog.openWarning(null, Messages.ConfigToolUtils_0, Messages.ConfigToolUtils_1);
			return false;
		}
		
		String commandParameter = "011000011100000100100001";// 01100011-11000001-00100001 //$NON-NLS-1$
		ICSPOperationFactory.initCommand(commandParameter);
		IConfigurationProvider chipxmlmessage = ConfigurationFactory.getProvider(chipType);
		ICSPOperationFactory.setChipxmlmessage(chipxmlmessage);
		
		boolean isNotFineErr = ICSPOperationFactory.all_Thread_Open_Set(commandParameter,true,false,false);
		if(isNotFineErr){
			isNotFineErr=ICSPOperationFactory.all_Thread_Check_ID_and_ReadTrim(true, true); //id  trim  cal  protect value, all in chip CFGUser Area. isWorkNeedID deal in function
		} 
		//加密状态识别
		if(isNotFineErr) {
			isNotFineErr = ICSPOperationFactory.checkProtectFlag(4, true);
		}
		
		if(isNotFineErr) {
			isNotFineErr = ICSPOperationFactory.validateDebugKey(MyToolConfig.debugKey);
		}
		//11KA02用户信息区、配置区读写解锁
		/*if(isNotFineErr) {
			isNotFineErr = ICSPOperationFactory.unlockUserAndCfg();
		}*/
		//----------------------------------------------------------------------------
		//上电重新加载trim值
		if(isNotFineErr) {
			isNotFineErr = ICSPOperationFactory.loadChipTrimValue(4, true);
		}
		
		return isNotFineErr;
		
	}
	
}
