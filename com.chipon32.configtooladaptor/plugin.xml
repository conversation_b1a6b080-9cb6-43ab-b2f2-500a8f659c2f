<?xml version="1.0" encoding="UTF-8"?>
<?eclipse version="3.4"?>
<plugin>
   <!--<extension
         point="org.eclipse.ui.handlers">
       <handler
             class="com.chipon32.configtooladaptor.handler.LoadKeyHandler"
             commandId="com.chipon32.configtool.importdebugkey">
       </handler>
   </extension>
   <extension
         point="org.eclipse.ui.commands">
      <command
            id="com.chipon32.configtool.importdebugkey"
            name="Load Key">
      </command>
   </extension>
   <extension
         point="org.eclipse.ui.menus">
      <menuContribution
            allPopups="false"
            locationURI="menu:sfile?after=start">
         <command
               commandId="com.chipon32.configtool.importdebugkey"
               id="com.chipon32.configtool.importdebugkey"
               label="%Menu_File_LoadKey.name"
               style="push">
         </command>
      </menuContribution>
   </extension>-->
   <extension
         point="com.chipon32.configtooladaptor.point">
      <adaptor
            class="com.chipon32.configtooladaptor.ProAndIDEAdaptorImpl"
            id="ProTool">
      </adaptor>
   </extension>

</plugin>
