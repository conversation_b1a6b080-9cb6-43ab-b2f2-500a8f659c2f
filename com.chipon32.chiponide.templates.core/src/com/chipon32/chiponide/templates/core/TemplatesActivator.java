package com.chipon32.chiponide.templates.core;

import java.net.URL;
import java.util.Enumeration;

import org.eclipse.ui.plugin.AbstractUIPlugin;
import org.osgi.framework.BundleContext;


/**
 * The activator class controls the plug-in life cycle
 */
public class TemplatesActivator extends AbstractUIPlugin {

	// The plug-in ID
	public static final String PLUGIN_ID = "com.chipon32.chiponide.templates.core"; //$NON-NLS-1$

	// The shared instance
	private static TemplatesActivator plugin;
	
	/**
	 * The constructor
	 */
	public TemplatesActivator() {
	}

	@Override
	public void start(BundleContext context) throws Exception {
		super.start(context);
		plugin = this;
	}

	@Override
	public void stop(BundleContext context) throws Exception {
		plugin = null;
		super.stop(context);
	}

	/**
	 * Returns the shared instance
	 *
	 * @return the shared instance
	 */
	public static TemplatesActivator getDefault() {
		return plugin;
	}
	
	/**
	   * Returns the absolut path of a entrie from the plugin's directory.
	   * 
	   * @return Returns the Relative path from the plugin.
	   */
	public static URL getRelativeFilePathFromPlugin(String path, String filePatten, boolean recurse){
		 URL url = null;
		 Enumeration<URL> enu = getDefault().getBundle().findEntries(path, filePatten, recurse);
		 if(enu == null){
		   return null;
		 }else{
			if (enu.hasMoreElements()) {
				url = enu.nextElement();
			}
		  }	
		 
		  return url;
	  }

}
