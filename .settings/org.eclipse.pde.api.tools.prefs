ANNOTATION_ELEMENT_TYPE_ADDED_CLASS_BOUND=Error
ANNOTATION_ELEMENT_TYPE_ADDED_FIELD=Error
ANNOTATION_ELEMENT_TYPE_ADDED_INTERFACE_BOUND=Error
ANNOTATION_ELEMENT_TYPE_ADDED_INTERFACE_BOUNDS=Error
ANNOTATION_ELEMENT_TYPE_ADDED_METHOD=Error
ANNOTATION_ELEMENT_TYPE_ADDED_METHOD_WITHOUT_DEFAULT_VALUE=Error
ANNOTATION_ELEMENT_TYPE_ADDED_TYPE_MEMBER=Error
ANNOTATION_ELEMENT_TYPE_ADDED_TYPE_PARAMETER=Error
ANNOTATION_ELEMENT_TYPE_CHANGED_CLASS_BOUND=Error
ANNOTATION_ELEMENT_TYPE_CHANGED_CONTRACTED_SUPERINTERFACES_SET=Error
ANNOTATION_ELEMENT_TYPE_CHANGED_INTERFACE_BOUND=Error
ANNOTATION_ELEMENT_TYPE_CHANGED_INTERFACE_BOUNDS=Error
ANNOTATION_ELEMENT_TYPE_CHANGED_RESTRICTIONS=Error
ANNOTATION_ELEMENT_TYPE_CHANGED_TO_CLASS=Error
ANNOTATION_ELEMENT_TYPE_CHANGED_TO_ENUM=Error
ANNOTATION_ELEMENT_TYPE_CHANGED_TO_INTERFACE=Error
ANNOTATION_ELEMENT_TYPE_CHANGED_TYPE_CONVERSION=Error
ANNOTATION_ELEMENT_TYPE_REMOVED_CLASS_BOUND=Error
ANNOTATION_ELEMENT_TYPE_REMOVED_FIELD=Error
ANNOTATION_ELEMENT_TYPE_REMOVED_INTERFACE_BOUND=Error
ANNOTATION_ELEMENT_TYPE_REMOVED_METHOD=Error
ANNOTATION_ELEMENT_TYPE_REMOVED_METHOD_WITHOUT_DEFAULT_VALUE=Error
ANNOTATION_ELEMENT_TYPE_REMOVED_METHOD_WITH_DEFAULT_VALUE=Error
ANNOTATION_ELEMENT_TYPE_REMOVED_TYPE_MEMBER=Error
ANNOTATION_ELEMENT_TYPE_REMOVED_TYPE_PARAMETER=Error
ANNOTATION_ELEMENT_TYPE_REMOVED_TYPE_PARAMETERS=Error
API_COMPONENT_ELEMENT_TYPE_REMOVED_API_TYPE=Error
API_COMPONENT_ELEMENT_TYPE_REMOVED_REEXPORTED_API_TYPE=Error
API_COMPONENT_ELEMENT_TYPE_REMOVED_REEXPORTED_TYPE=Error
API_COMPONENT_ELEMENT_TYPE_REMOVED_TYPE=Error
API_USE_SCAN_FIELD_SEVERITY=Error
API_USE_SCAN_METHOD_SEVERITY=Error
API_USE_SCAN_TYPE_SEVERITY=Error
CLASS_ELEMENT_TYPE_ADDED_CLASS_BOUND=Error
CLASS_ELEMENT_TYPE_ADDED_FIELD=Error
CLASS_ELEMENT_TYPE_ADDED_INTERFACE_BOUND=Error
CLASS_ELEMENT_TYPE_ADDED_INTERFACE_BOUNDS=Error
CLASS_ELEMENT_TYPE_ADDED_METHOD=Error
CLASS_ELEMENT_TYPE_ADDED_RESTRICTIONS=Error
CLASS_ELEMENT_TYPE_ADDED_TYPE_PARAMETER=Error
CLASS_ELEMENT_TYPE_CHANGED_CLASS_BOUND=Error
CLASS_ELEMENT_TYPE_CHANGED_CONTRACTED_SUPERCLASS_SET=Error
CLASS_ELEMENT_TYPE_CHANGED_CONTRACTED_SUPERINTERFACES_SET=Error
CLASS_ELEMENT_TYPE_CHANGED_DECREASE_ACCESS=Error
CLASS_ELEMENT_TYPE_CHANGED_INTERFACE_BOUND=Error
CLASS_ELEMENT_TYPE_CHANGED_NON_ABSTRACT_TO_ABSTRACT=Error
CLASS_ELEMENT_TYPE_CHANGED_NON_FINAL_TO_FINAL=Error
CLASS_ELEMENT_TYPE_CHANGED_RESTRICTIONS=Error
CLASS_ELEMENT_TYPE_CHANGED_SUPERCLASS=Error
CLASS_ELEMENT_TYPE_CHANGED_TO_ANNOTATION=Error
CLASS_ELEMENT_TYPE_CHANGED_TO_ENUM=Error
CLASS_ELEMENT_TYPE_CHANGED_TO_INTERFACE=Error
CLASS_ELEMENT_TYPE_CHANGED_TYPE_CONVERSION=Error
CLASS_ELEMENT_TYPE_REMOVED_CLASS_BOUND=Error
CLASS_ELEMENT_TYPE_REMOVED_CONSTRUCTOR=Error
CLASS_ELEMENT_TYPE_REMOVED_FIELD=Error
CLASS_ELEMENT_TYPE_REMOVED_INTERFACE_BOUND=Error
CLASS_ELEMENT_TYPE_REMOVED_INTERFACE_BOUNDS=Error
CLASS_ELEMENT_TYPE_REMOVED_METHOD=Error
CLASS_ELEMENT_TYPE_REMOVED_SUPERCLASS=Error
CLASS_ELEMENT_TYPE_REMOVED_TYPE_MEMBER=Error
CLASS_ELEMENT_TYPE_REMOVED_TYPE_PARAMETER=Error
CLASS_ELEMENT_TYPE_REMOVED_TYPE_PARAMETERS=Error
CONSTRUCTOR_ELEMENT_TYPE_ADDED_CLASS_BOUND=Error
CONSTRUCTOR_ELEMENT_TYPE_ADDED_INTERFACE_BOUND=Error
CONSTRUCTOR_ELEMENT_TYPE_ADDED_INTERFACE_BOUNDS=Error
CONSTRUCTOR_ELEMENT_TYPE_ADDED_TYPE_PARAMETER=Error
CONSTRUCTOR_ELEMENT_TYPE_CHANGED_CLASS_BOUND=Error
CONSTRUCTOR_ELEMENT_TYPE_CHANGED_DECREASE_ACCESS=Error
CONSTRUCTOR_ELEMENT_TYPE_CHANGED_INTERFACE_BOUND=Error
CONSTRUCTOR_ELEMENT_TYPE_CHANGED_NON_ABSTRACT_TO_ABSTRACT=Error
CONSTRUCTOR_ELEMENT_TYPE_CHANGED_NON_FINAL_TO_FINAL=Error
CONSTRUCTOR_ELEMENT_TYPE_CHANGED_NON_STATIC_TO_STATIC=Error
CONSTRUCTOR_ELEMENT_TYPE_CHANGED_STATIC_TO_NON_STATIC=Error
CONSTRUCTOR_ELEMENT_TYPE_CHANGED_TYPE_PARAMETER=Error
CONSTRUCTOR_ELEMENT_TYPE_CHANGED_VARARGS_TO_ARRAY=Error
CONSTRUCTOR_ELEMENT_TYPE_REMOVED_ANNOTATION_DEFAULT_VALUE=Error
CONSTRUCTOR_ELEMENT_TYPE_REMOVED_CLASS_BOUND=Error
CONSTRUCTOR_ELEMENT_TYPE_REMOVED_INTERFACE_BOUND=Error
CONSTRUCTOR_ELEMENT_TYPE_REMOVED_INTERFACE_BOUNDS=Error
CONSTRUCTOR_ELEMENT_TYPE_REMOVED_TYPE_PARAMETER=Error
CONSTRUCTOR_ELEMENT_TYPE_REMOVED_TYPE_PARAMETERS=Error
ENUM_ELEMENT_TYPE_CHANGED_CONTRACTED_SUPERINTERFACES_SET=Error
ENUM_ELEMENT_TYPE_CHANGED_RESTRICTIONS=Error
ENUM_ELEMENT_TYPE_CHANGED_TO_ANNOTATION=Error
ENUM_ELEMENT_TYPE_CHANGED_TO_CLASS=Error
ENUM_ELEMENT_TYPE_CHANGED_TO_INTERFACE=Error
ENUM_ELEMENT_TYPE_CHANGED_TYPE_CONVERSION=Error
ENUM_ELEMENT_TYPE_REMOVED_ENUM_CONSTANT=Error
ENUM_ELEMENT_TYPE_REMOVED_FIELD=Error
ENUM_ELEMENT_TYPE_REMOVED_METHOD=Error
ENUM_ELEMENT_TYPE_REMOVED_TYPE_MEMBER=Error
FIELD_ELEMENT_TYPE_ADDED_VALUE=Error
FIELD_ELEMENT_TYPE_CHANGED_DECREASE_ACCESS=Error
FIELD_ELEMENT_TYPE_CHANGED_FINAL_TO_NON_FINAL_STATIC_CONSTANT=Error
FIELD_ELEMENT_TYPE_CHANGED_NON_FINAL_TO_FINAL=Error
FIELD_ELEMENT_TYPE_CHANGED_NON_STATIC_TO_STATIC=Error
FIELD_ELEMENT_TYPE_CHANGED_STATIC_TO_NON_STATIC=Error
FIELD_ELEMENT_TYPE_CHANGED_TYPE=Error
FIELD_ELEMENT_TYPE_CHANGED_VALUE=Error
FIELD_ELEMENT_TYPE_REMOVED_TYPE_ARGUMENT=Error
FIELD_ELEMENT_TYPE_REMOVED_TYPE_ARGUMENTS=Error
FIELD_ELEMENT_TYPE_REMOVED_VALUE=Error
ILLEGAL_EXTEND=Warning
ILLEGAL_IMPLEMENT=Warning
ILLEGAL_INSTANTIATE=Warning
ILLEGAL_OVERRIDE=Warning
ILLEGAL_REFERENCE=Warning
INTERFACE_ELEMENT_TYPE_ADDED_CLASS_BOUND=Error
INTERFACE_ELEMENT_TYPE_ADDED_DEFAULT_METHOD=Error
INTERFACE_ELEMENT_TYPE_ADDED_FIELD=Error
INTERFACE_ELEMENT_TYPE_ADDED_INTERFACE_BOUND=Error
INTERFACE_ELEMENT_TYPE_ADDED_INTERFACE_BOUNDS=Error
INTERFACE_ELEMENT_TYPE_ADDED_METHOD=Error
INTERFACE_ELEMENT_TYPE_ADDED_RESTRICTIONS=Error
INTERFACE_ELEMENT_TYPE_ADDED_SUPER_INTERFACE_WITH_METHODS=Error
INTERFACE_ELEMENT_TYPE_ADDED_TYPE_MEMBER=Error
INTERFACE_ELEMENT_TYPE_ADDED_TYPE_PARAMETER=Error
INTERFACE_ELEMENT_TYPE_ADDED_TYPE_PARAMETERS=Error
INTERFACE_ELEMENT_TYPE_CHANGED_CLASS_BOUND=Error
INTERFACE_ELEMENT_TYPE_CHANGED_CONTRACTED_SUPERINTERFACES_SET=Error
INTERFACE_ELEMENT_TYPE_CHANGED_INTERFACE_BOUND=Error
INTERFACE_ELEMENT_TYPE_CHANGED_INTERFACE_BOUNDS=Error
INTERFACE_ELEMENT_TYPE_CHANGED_RESTRICTIONS=Error
INTERFACE_ELEMENT_TYPE_CHANGED_TO_ANNOTATION=Error
INTERFACE_ELEMENT_TYPE_CHANGED_TO_CLASS=Error
INTERFACE_ELEMENT_TYPE_CHANGED_TO_ENUM=Error
INTERFACE_ELEMENT_TYPE_CHANGED_TYPE_CONVERSION=Error
INTERFACE_ELEMENT_TYPE_REMOVED_CLASS_BOUND=Error
INTERFACE_ELEMENT_TYPE_REMOVED_FIELD=Error
INTERFACE_ELEMENT_TYPE_REMOVED_INTERFACE_BOUND=Error
INTERFACE_ELEMENT_TYPE_REMOVED_INTERFACE_BOUNDS=Error
INTERFACE_ELEMENT_TYPE_REMOVED_METHOD=Error
INTERFACE_ELEMENT_TYPE_REMOVED_TYPE_MEMBER=Error
INTERFACE_ELEMENT_TYPE_REMOVED_TYPE_PARAMETER=Error
INVALID_ANNOTATION=Ignore
INVALID_JAVADOC_TAG=Error
INVALID_REFERENCE_IN_SYSTEM_LIBRARIES=Warning
LEAK_EXTEND=Warning
LEAK_FIELD_DECL=Warning
LEAK_IMPLEMENT=Warning
LEAK_METHOD_PARAM=Warning
LEAK_METHOD_RETURN_TYPE=Warning
METHOD_ELEMENT_TYPE_ADDED_CLASS_BOUND=Error
METHOD_ELEMENT_TYPE_ADDED_INTERFACE_BOUND=Error
METHOD_ELEMENT_TYPE_ADDED_INTERFACE_BOUNDS=Error
METHOD_ELEMENT_TYPE_ADDED_RESTRICTIONS=Error
METHOD_ELEMENT_TYPE_ADDED_TYPE_PARAMETER=Error
METHOD_ELEMENT_TYPE_CHANGED_CLASS_BOUND=Error
METHOD_ELEMENT_TYPE_CHANGED_DECREASE_ACCESS=Error
METHOD_ELEMENT_TYPE_CHANGED_INTERFACE_BOUND=Error
METHOD_ELEMENT_TYPE_CHANGED_NON_ABSTRACT_TO_ABSTRACT=Error
METHOD_ELEMENT_TYPE_CHANGED_NON_FINAL_TO_FINAL=Error
METHOD_ELEMENT_TYPE_CHANGED_NON_STATIC_TO_STATIC=Error
METHOD_ELEMENT_TYPE_CHANGED_STATIC_TO_NON_STATIC=Error
METHOD_ELEMENT_TYPE_CHANGED_TYPE_PARAMETER=Error
METHOD_ELEMENT_TYPE_CHANGED_VARARGS_TO_ARRAY=Error
METHOD_ELEMENT_TYPE_REMOVED_ANNOTATION_DEFAULT_VALUE=Error
METHOD_ELEMENT_TYPE_REMOVED_CLASS_BOUND=Error
METHOD_ELEMENT_TYPE_REMOVED_INTERFACE_BOUND=Error
METHOD_ELEMENT_TYPE_REMOVED_INTERFACE_BOUNDS=Error
METHOD_ELEMENT_TYPE_REMOVED_TYPE_PARAMETER=Error
METHOD_ELEMENT_TYPE_REMOVED_TYPE_PARAMETERS=Error
MISSING_EE_DESCRIPTIONS=Warning
TYPE_PARAMETER_ELEMENT_TYPE_ADDED_CLASS_BOUND=Error
TYPE_PARAMETER_ELEMENT_TYPE_ADDED_INTERFACE_BOUND=Error
TYPE_PARAMETER_ELEMENT_TYPE_CHANGED_CLASS_BOUND=Error
TYPE_PARAMETER_ELEMENT_TYPE_CHANGED_INTERFACE_BOUND=Error
TYPE_PARAMETER_ELEMENT_TYPE_REMOVED_CLASS_BOUND=Error
TYPE_PARAMETER_ELEMENT_TYPE_REMOVED_INTERFACE_BOUND=Error
UNUSED_PROBLEM_FILTERS=Warning
automatically_removed_unused_problem_filters=false
changed_execution_env=Error
eclipse.preferences.version=1
incompatible_api_component_version=Error
incompatible_api_component_version_include_major_without_breaking_change=Disabled
incompatible_api_component_version_include_minor_without_api_change=Disabled
incompatible_api_component_version_report_major_without_breaking_change=Warning
incompatible_api_component_version_report_minor_without_api_change=Warning
invalid_since_tag_version=Error
malformed_since_tag=Error
missing_since_tag=Error
report_api_breakage_when_major_version_incremented=Disabled
report_resolution_errors_api_component=Warning
