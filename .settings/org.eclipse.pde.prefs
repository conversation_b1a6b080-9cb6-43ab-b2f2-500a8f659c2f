compilers.f.unresolved-features=1
compilers.f.unresolved-plugins=1
compilers.incompatible-environment=1
compilers.p.build=1
compilers.p.build.bin.includes=0
compilers.p.build.encodings=2
compilers.p.build.java.compiler=2
compilers.p.build.java.compliance=1
compilers.p.build.missing.output=2
compilers.p.build.output.library=1
compilers.p.build.source.library=1
compilers.p.build.src.includes=0
compilers.p.deprecated=1
compilers.p.discouraged-class=1
compilers.p.exec-env-too-low=1
compilers.p.internal=1
compilers.p.missing-packages=2
compilers.p.missing-version-export-package=2
compilers.p.missing-version-import-package=2
compilers.p.missing-version-require-bundle=2
compilers.p.no-required-att=0
compilers.p.no.automatic.module=1
compilers.p.not-externalized-att=1
compilers.p.service.component.without.lazyactivation=1
compilers.p.unknown-attribute=1
compilers.p.unknown-class=0
compilers.p.unknown-element=1
compilers.p.unknown-identifier=0
compilers.p.unknown-resource=0
compilers.p.unresolved-ex-points=0
compilers.p.unresolved-import=0
compilers.s.create-docs=false
compilers.s.doc-folder=doc
compilers.s.open-tags=1
compilers.use-project=true
eclipse.preferences.version=1
