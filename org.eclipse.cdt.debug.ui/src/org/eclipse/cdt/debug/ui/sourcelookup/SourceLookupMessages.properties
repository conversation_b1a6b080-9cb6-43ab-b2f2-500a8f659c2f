###############################################################################
# Copyright (c) 2003, 2005 QNX Software Systems and others.
#
# This program and the accompanying materials
# are made available under the terms of the Eclipse Public License 2.0
# which accompanies this distribution, and is available at
# https://www.eclipse.org/legal/epl-2.0/
#
# SPDX-License-Identifier: EPL-2.0
#
# Contributors:
#     QNX Software Systems - initial API and implementation
###############################################################################

DefaultSourceLocator.1=Unable to create memento for C/C++ source locator.
DefaultSourceLocator.2=Unable to restore prompting source locator - invalid format.
DefaultSourceLocator.3=Unable to restore prompting source locator - invalid format.
DefaultSourceLocator.4=Unable to restore prompting source locator - project {0} not found.
DefaultSourceLocator.5=Unable to restore prompting source locator - invalid format.
DefaultSourceLocator.6=Exception occurred initializing source locator.
DefaultSourceLocator.7=Selection needed
DefaultSourceLocator.8=Debugger has found multiple files with the same name.\nPlease select one associated with the selected stack frame.
DefaultSourceLocator.9=Project ''{0}'' does not exist.
