###############################################################################
# Copyright (c) 2006, 2009 Nokia and others.
#
# This program and the accompanying materials
# are made available under the terms of the Eclipse Public License 2.0
# which accompanies this distribution, and is available at
# https://www.eclipse.org/legal/epl-2.0/
#
# SPDX-License-Identifier: EPL-2.0
#
# Contributors:
#     Nokia - initial API and implementation
###############################################################################

ImportExecutableWizard_pageOneTitle=Import C/C++ Executable Files
ImportExecutableWizard_pageOneDescription=Select a file or a directory to search for C/C++ executable files.
ImportExecutablePageOne_SelectBinaryParser=Select binary parser:
ImportExecutablePageOne_SelectExecutable=Select executable:
ImportExecutablePageOne_SelectADirectory=Select a directory to search for C/C++ executable files.
ImportExecutablePageTwo_EnterProjectName=Enter a project name.
ImportExecutablePageOne_ProcessingResults=Processing results
ImportExecutablePageTwo_EnterLaunchConfig=Enter a launch configuration name.
ImportExecutablePageTwo_ProjectAlreadyExists=That project already exists, enter a new name.
ImportExecutablePageTwo_ExecutableAlreadyExists=Project already contains {0} executable(s). \
If you continue the file(s) will be replaced.
ImportExecutablePageTwo_DefaultProjectPrefix=Debug_
ImportExecutableWizard_executableListLabel=C/C++ Executable Files:
ImportExecutableWizard_fileDialogTitle=Choose an executable file
ImportExecutablePageOne_SearchDirectory=Search directory:
ImportExecutablePageTwo_ChooseExisting=Choose an existing project or create a new one.
ImportExecutablePageTwo_BadProjectName=Invalid project name.
ImportExecutablePageTwo_NewProjectName=New project name:
ImportExecutablePageTwo_ExistingProject=Existing project:
ImportExecutableWizard_AllFiles=All Files
ImportExecutableWizard_Applications=Applications
ImportExecutablePageOne_NoSuchFile=That file does not exist.
ImportExecutablePageOne_DeselectAll=Deselect All
ImportExecutablePageTwo_ChooseProject=Choose Project
ImportExecutablePageTwo_ProjectLabel=The new project will let you debug but not build the executable.
ImportExecutablePageTwo_CreateLaunch=Create a Launch Configuration:
ImportExecutableWizard_LIbaries=Libraries
ImportExecutablePageOne_Browse=Browse...
ImportExecutablePageTwo_Search=Search...
ImportExecutablePageTwo_Name=Name:
ImportExecutablePageOne_NoteAnEXE=Not an executable file.
ImportExecutablePageOne_SelectAll=Select All
ImportExecutablePageOne_Searching=Searching:
AbstractImportExecutableWizard_windowTitle=Import Executable
AbstractImportExecutableWizard_ErrorImporting=Error importing:\u0020
AbstractImportExecutableWizard_CreateLaunchConfiguration=Create Launch Configuration
