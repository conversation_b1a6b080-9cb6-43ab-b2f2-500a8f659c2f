###############################################################################
# Copyright (c) 2005, 2012 Nokia
#
# This program and the accompanying materials
# are made available under the terms of the Eclipse Public License 2.0
# which accompanies this distribution, and is available at
# https://www.eclipse.org/legal/epl-2.0/
#
# SPDX-License-Identifier: EPL-2.0
#
# Contributors:
#     Nokia - initial API and implementation
###############################################################################
ActionsPropertyPage.1=Actions for this breakpoint:
ActionsPropertyPage.2=Available actions:
ActionsPreferencePage.0=Actions available for any breakpoint in the workspace:
PreferenceInitializer.1=Default value
GlobalActionsList.0=Name
GlobalActionsList.1=Type
GlobalActionsList.2=Summary
GlobalActionsList.3=Attach
GlobalActionsList.4=New...
GlobalActionsList.5=Edit...
GlobalActionsList.6=Delete
ActionsList.0=Name
ActionsList.1=Type
ActionsList.2=Summary
ActionsList.3=Remove
ActionsList.4=Up
ActionsList.5=Down
ActionDialog.0=New Breakpoint Action
ActionDialog.1=Action name:
ActionDialog.2=Action type:
ActionDialog.ErrEmptyName=Action name cannot be empty
SoundActionComposite.4=Select a sound to play when the breakpoint is hit:
SoundActionComposite.5=Choose a sound file:
SoundActionComposite.6=Browse...
SoundActionComposite.7=Play Sound
SoundActionComposite.9=That sound file does not exist.
SoundAction.error.0=Missing sound file "{0}"
SoundAction.ActionTypeName=Sound Action
SoundAction.UntitledName=Untitled Sound Action
LogActionComposite.0=Message to log when the breakpoint is hit:
LogActionComposite.1=Evaluate as expression
LogAction.ConsoleTitle=Log Action Messages
LogAction.UntitledName=Untitled Log Action
LogAction.TypeName=Log Action
LogAction.error.0=Could not execute log action: "{0}".

ExternalToolActionComposite.ToolLabel=External Tool:
ExternalToolActionComposite.DialogTitle=External Tools
ExternalToolActionComposite.DialogMessage=Choose an External Tool to run
ExternalToolActionComposite.ChooseButtonTitle=Choose...
ExternalToolActionComposite.ExternalToolsButtonTitle=External Tools...
ExternalToolAction.Summary=Run {0}
ExternalToolAction.error.0=There is no launch configuration named "{0}".
ExternalToolAction.error.1=Could not launch "{0}".
ExternalToolAction.TypeName=External Tool Action
ResumeAction.UntitledName=Untitled Resume Action
ResumeAction.SummaryImmediately=Resume immediately
ResumeActionComposite.ResumeAfterLabel=Resume after\u0020
ResumeAction.SummaryResumeTime=Resume after {0} seconds
ResumeActionComposite.Seconds=seconds
ResumeAction.TypeName=Resume Action
ResumeAction.error.0=IResumeActionEnabler not registered in context.
ResumeAction.error.1=Could not resume.\u0020

ReverseDebugAction.UntitledName=Untitled Rev Debug Action
ReverseDebugAction.TypeName=Reverse Debug Action
ReverseDebugActionComposite.label=Select Reverse Debugging Action
ReverseDebugAction.Summary= reverse debugging
ReverseDebugAction.error.0=IReverseToggleEnabler not registered in context.
ReverseDebugAction.error.1=Could not do reverse debug operation
ReverseDebugAction.enable=Enable
ReverseDebugAction.disable=Disable
ReverseDebugAction.toggle=Toggle

CLICommandActionComposite.0=Debugger command(s) to run when the breakpoint is hit:
CLICommandAction.ConsoleTitle=Debugger Command Action Messages
CLICommandAction.UntitledName=Untitled Debugger Command Action
CLICommandAction.TypeName=Debugger Command Action
CLICommandAction.error.0=Could not execute debugger command action: "{0}".
CLICommandAction.NoSupport=Your debugger integration does not support direct execution of commands
