/*******************************************************************************
 * Copyright (c) 2007 Nokia and others.
 *
 * This program and the accompanying materials
 * are made available under the terms of the Eclipse Public License 2.0
 * which accompanies this distribution, and is available at
 * https://www.eclipse.org/legal/epl-2.0/
 *
 * SPDX-License-Identifier: EPL-2.0
 *
 * Contributors:
 *     Nokia - initial API and implementation
 *******************************************************************************/
package org.eclipse.cdt.debug.ui.breakpointactions;

/**
 * Constant definitions for plug-in preferences
 */
public class PreferenceConstants {

	public static final String P_PATH = "pathPreference"; //$NON-NLS-1$

	public static final String P_BOOLEAN = "booleanPreference"; //$NON-NLS-1$

	public static final String P_CHOICE = "choicePreference"; //$NON-NLS-1$

	public static final String P_STRING = "stringPreference"; //$NON-NLS-1$

}
