/*******************************************************************************
 * Copyright (c) 2014 <PERSON><PERSON> and others.
 *
 * This program and the accompanying materials
 * are made available under the terms of the Eclipse Public License 2.0
 * which accompanies this distribution, and is available at
 * https://www.eclipse.org/legal/epl-2.0/
 *
 * SPDX-License-Identifier: EPL-2.0
 *
 * Contributors:
 *   <PERSON> (<PERSON><PERSON>) - Initial API and implementation
 *******************************************************************************/
package org.eclipse.cdt.debug.internal.ui.actions.breakpoints;

import org.eclipse.osgi.util.NLS;

public class Messages extends NLS {

	public static String Default_LineDynamicPrintf_String;
	public static String Default_FunctionDynamicPrintf_String;

	static {
		NLS.initializeMessages(Messages.class.getName(), Messages.class);
	}

	private Messages() {
	}
}
