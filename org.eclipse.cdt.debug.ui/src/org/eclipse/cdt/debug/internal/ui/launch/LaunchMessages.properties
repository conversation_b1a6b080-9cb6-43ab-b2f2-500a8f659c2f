###############################################################################
# Copyright (c) 2002, 2009 QNX Software Systems and others.
#
# This program and the accompanying materials
# are made available under the terms of the Eclipse Public License 2.0
# which accompanies this distribution, and is available at
# https://www.eclipse.org/legal/epl-2.0/
#
# SPDX-License-Identifier: EPL-2.0
#
# Contributors:
#       QNX Software Systems - Initial API and implementation
# 		Monta Vista - Joanne Woo - Bug 87556
#       Nokia - Ken Ryall - Bug 118894
#       <PERSON> (CodeSourcery) - Bug 218366
#		IBM Corporation
###############################################################################
CApplicationLaunchShortcut.Application_Launcher=Application Launcher
CApplicationLaunchShortcut.ChooseConfigToDebug=Choose a debug configuration to debug
CApplicationLaunchShortcut.ChooseConfigToRun=Choose a configuration to run
CApplicationLaunchShortcut.CLocalApplication=C Local Application
CApplicationLaunchShortcut.ChooseLocalAppToDebug=Choose a local application to debug
CApplicationLaunchShortcut.ChooseLocalAppToRun=Choose a local application to run
CApplicationLaunchShortcut.Launch_failed_no_binaries=Launch failed. Binary not found.
CApplicationLaunchShortcut.LaunchFailed=Launch failed
CApplicationLaunchShortcut.LaunchDebugConfigSelection=Launch Debug Configuration Selection
CApplicationLaunchShortcut.LaunchConfigSelection=Launch Configuration Selection
CApplicationLaunchShortcut.Invalid_launch_mode_1=Invalid launch mode
CApplicationLaunchShortcut.Invalid_launch_mode_2=Invalid launch mode.
CApplicationLaunchShortcut.Invalid_launch_mode_3=Invalid launch mode.
CApplicationLaunchShortcut.ChooseLaunchConfigToDebug=Choose a launch configuration to debug
CApplicationLaunchShortcut.ChooseLaunchConfigToRun=Choose a launch configuration to run
CApplicationLaunchShortcut.Launch_failed_no_project_selected=Launch failed no project selected

Launch.common.BinariesColon=Binaries:
Launch.common.QualifierColon=Qualifier:

Launch.ILaunchable.Interface.Error=An attempt to instantiate an adapter factory for ILaunchable. By API specification this is not allowed. Use hasAdapter() to determine existense.

NewGenericTargetWizard_0=New Generic Target
NewGenericTargetWizardPage.Arch=CPU Architecture:
NewGenericTargetWizardPage.Desc=Enter name and properties for the target.
NewGenericTargetWizardPage.Name=Name:
NewGenericTargetWizardPage.OS=Operating System:
NewGenericTargetWizardPage.Title=Generic Target
