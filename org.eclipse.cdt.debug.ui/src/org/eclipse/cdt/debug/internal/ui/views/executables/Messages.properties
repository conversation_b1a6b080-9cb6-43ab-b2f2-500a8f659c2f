###############################################################################
# Copyright (c) 2008, 2010 Nokia and others.
#
# This program and the accompanying materials
# are made available under the terms of the Eclipse Public License 2.0
# which accompanies this distribution, and is available at
# https://www.eclipse.org/legal/epl-2.0/
#
# SPDX-License-Identifier: EPL-2.0
#
# Contributors:
#     Nokia - initial API and implementation
#     IBM Corporation
###############################################################################
ExecutablesContentProvider_FetchingExecutables=Fetching executables
ExecutablesView_Columns=Columns
ExecutablesView_ConfigureColumns=Configure Columns
ExecutablesView_ConfirmRemoveExe=Confirm Remove Executables
ExecutablesView_ConfirmRemoveSelected=Are you sure you want to remove the selected executables?
ExecutablesView_ExeData=Executable Date
ExecutablesView_ExeLocation=Executable Location
ExecutablesView_ExeName=Executable Name
ExecutablesView_ExeProject=Executable Project
ExecutablesView_ExeSize=Executable Size
ExecutablesView_ExeType=Executable Type
ExecutablesView_Finding_Sources_Job_Name=Finding source files in\u0020
ExecutablesView_Import=Import
ExecutablesView_ImportExe=Import an executable file
ExecutablesView_ImportExecutables=Import Executables
ExecutablesView_Refresh=Refresh
ExecutablesView_RefreshList=Refresh the list of executables
ExecutablesView_Remove=Remove
ExecutablesView_RemoveExes=Remove Executables
ExecutablesView_RemoveSelectedExes=Remove the selected executables
ExecutablesView_Select_Executable=Select Executable
ExecutablesView_SelectColumns=Select the columns to show
ExecutablesView_SelectExeFile=Select an executable file
ExecutablesView_SrcDate=Source File Date
ExecutablesView_SrcLocation=Source File Location
ExecutablesView_SrcName=Source File Name
ExecutablesView_SrcOrgLocation=Source File Original Location
ExecutablesView_SrcSize=Source File Size
ExecutablesView_SrcType=Source File Type
ExecutablesViewer_ExecutableName=Executable Name
ExecutablesViewer_Location=Location
ExecutablesViewer_Modified=Modified
ExecutablesViewer_Project=Project
ExecutablesViewer_RefreshExecutablesView=Refresh Executables View
ExecutablesViewer_Size=Size
ExecutablesViewer_Type=Type
SourceFilesContentProvider_NoFilesFound=No source files found in\u0020
SourceFilesContentProvider_ReadingDebugSymbolInformationLabel=Reading Debug Symbol Information:\u0020
SourceFilesContentProvider_Refreshing=Refreshing...\u0020
SourceFilesContentProvider_Canceled=Parse canceled. Hit refresh to restart.
SourceFilesViewer_RefreshSourceFiles=Refresh Source Files
SourceFilesViewer_Location=Location
SourceFilesViewer_Modified=Modified
SourceFilesViewer_Original=Original
SourceFilesViewer_Size=Size
SourceFilesViewer_SourceFileName=Source File Name
SourceFilesViewer_Type=Type
