###############################################################################
# Copyright (c) 2005, 2008 QNX Software Systems and others.
#
# This program and the accompanying materials
# are made available under the terms of the Eclipse Public License 2.0
# which accompanies this distribution, and is available at
# https://www.eclipse.org/legal/epl-2.0/
#
# SPDX-License-Identifier: EPL-2.0
#
# Contributors:
#     QNX Software Systems - initial API and implementation
#     Wind River Systems - adapted to work with platform Modules view (bug 210558)
###############################################################################
ModulesView.1=executable
ModulesView.10=Size:\u0020
ModulesView.11=\ (symbols loaded)
ModulesView.12=(symbols not loaded)
ModulesView.13=Select &All
ModulesView.16=&Copy
ModulesView.2=shared library
ModulesView.3=Type:\u0020
ModulesView.4=Symbols:\u0020
ModulesView.5=loaded
ModulesView.6=not loaded
ModulesView.7=Symbols file:\u0020
ModulesView.8=CPU:\u0020
ModulesView.9=Base address:\u0020
