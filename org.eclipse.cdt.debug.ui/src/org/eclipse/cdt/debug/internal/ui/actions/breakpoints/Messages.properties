#######################################################################################
# Copyright (c) 2014 <PERSON><PERSON> and others.
#
# This program and the accompanying materials
# are made available under the terms of the Eclipse Public License 2.0
# which accompanies this distribution, and is available at
# https://www.eclipse.org/legal/epl-2.0/
#
# SPDX-License-Identifier: EPL-2.0
#
# Contributors:
#   <PERSON> (<PERSON><PERSON>) - Initial API and implementation
#######################################################################################

# We use %d in the below string on purpose to show the user that it is possible
# Don't use %s with an in-line string, as it crashes GDB 7.5 and 7.6
# http://sourceware.org/bugzilla/show_bug.cgi?id=15433
Default_LineDynamicPrintf_String="Hit line %d of {0}\\n",{1}
Default_FunctionDynamicPrintf_String="Entered function: {1} of {0}\\n"
